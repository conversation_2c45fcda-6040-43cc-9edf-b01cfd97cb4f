import { danger, warn, fail, message } from "danger";

const pr = danger.github.pr;
const modifiedFiles = danger.git.modified_files;
const createdFiles = danger.git.created_files;
const touchedFiles = [...modifiedFiles, ...createdFiles];

// Rule 1: Error if PR title doesn't include ticket number SFLX-0000 format
const ticketPattern = /SF-\d{3,4}/;
if (!ticketPattern.test(pr.title)) {
  fail("PR title must include a ticket number in the format `SF-0000`.");
}

// Rule 2: Warn if src/components or src/pages are changed without screenshot
const uiFilesChanged = touchedFiles.some(
  (file) => file.startsWith("src/components/") || file.startsWith("src/pages/"),
);
const hasScreenshot =
  /!\[.*\]\(.*\)/.test(pr.body) || pr.body.toLowerCase().includes("screenshot");

if (uiFilesChanged && !hasScreenshot) {
  warn("UI changes detected—please add a screenshot to your PR description.");
}

// Rule 3: Error on console.log statements
const jsTsFiles = touchedFiles.filter(
  (file) =>
    /\.(js|jsx|ts|tsx)$/.exec(file) &&
    !file.toLowerCase().includes("dangerfile"), // Exclude dangerfile.ts/js
);

jsTsFiles.forEach(async (file) => {
  const diff = await danger.git.diffForFile(file);
  if (diff?.added.includes("console.log")) {
    fail(`Remove \`console.log\` statements from \`${file}\`.`);
  }
});

// Rule 4: Warn on changes to src/shared-state
const sharedStateChanged = touchedFiles.some((file) =>
  file.startsWith("src/shared-state/"),
);
if (sharedStateChanged) {
  warn(
    "Changes in `src/shared-state` detected—please review these changes carefully.",
  );
}

// Rule 5: Warn if PR is large (15 files changed)
const tooManyFiles = touchedFiles.length > 15;

if (tooManyFiles) {
  warn(
    `This PR changes ${touchedFiles.length} files—consider splitting into smaller PRs.`,
  );
}

// Rule 6: Warn if component modified without changing its story file
const componentFileRegex = /^src\/components\/(.+)\/([A-Za-z0-9]+)\.tsx$/;

modifiedFiles.forEach((file) => {
  const match = componentFileRegex.exec(file);
  if (match) {
    const componentDir = match[1];
    const componentName = match[2];
    const expectedStoryFile = `src/components/${componentDir}/${componentName}.stories.tsx`;

    if (!touchedFiles.includes(expectedStoryFile)) {
      warn(
        `You modified \`${componentName}.tsx\` but not its story file—consider updating \`${componentName}.stories.tsx\`.`,
      );
    }
  }
});

// Rule 7: Warn if a new component was added without accompanying story file
createdFiles.forEach((file) => {
  const match = componentFileRegex.exec(file);
  if (match) {
    const componentDir = match[1];
    const componentName = match[2];
    const expectedStoryFile = `src/components/${componentDir}/${componentName}.stories.tsx`;

    if (!touchedFiles.includes(expectedStoryFile)) {
      warn(
        `New component \`${componentName}.tsx\` should have an accompanying story file \`${componentName}.stories.tsx\`.`,
      );
    }
  }
});

// Rule 8: Welcome first-time contributors
const isFirstTimeContributor =
  pr.author_association === "FIRST_TIMER" ||
  pr.author_association === "FIRST_TIME_CONTRIBUTOR";
if (isFirstTimeContributor) {
  message("🎉 Thanks for your first PR! Welcome to the Sea-Flux project! 🚢");
}
