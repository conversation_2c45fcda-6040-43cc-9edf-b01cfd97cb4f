import { defineConfig } from 'eslint/config'
import globals from 'globals'
import tseslint from 'typescript-eslint'
import pluginReact from 'eslint-plugin-react'
import pluginReactHooks from 'eslint-plugin-react-hooks'
/// TO DO: Enable additional plugins when dependency conflicts are resolved
// import pluginReactNative from "eslint-plugin-react-native"
// import pluginImport from "eslint-plugin-import"
// import pluginJsxA11y from "eslint-plugin-jsx-a11y"

export default defineConfig([
  ...tseslint.configs.recommended,
  pluginReact.configs.flat['jsx-runtime'],
  // TO DO: Enable when plugin conflicts resolved
  // pluginJsxA11y.flatConfigs.recommended,
  // ...tseslint.configs.stylisticTypeChecked,
  {
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      globals: { ...globals.browser, __DEV__: 'readonly', process: 'readonly' },
      parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: import.meta.dirname,
      },
    },
    settings: {
      react: {
        version: '18.3.1',
        runtime: 'automatic',
      },
    },

    plugins: {
      react: pluginReact,
      'react-hooks': pluginReactHooks,
      // TO DO: Enable when able to address related issues
      // "react-native": pluginReactNative,
      // import: pluginImport,
    },

    rules: {
      // Rule exclusions - TO DO: Remove these when able to address issues
      '@typescript-eslint/no-require-imports': 'off',
      '@typescript-eslint/no-non-null-asserted-optional-chain': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/no-unused-expressions': 'off',
      '@typescript-eslint/no-unsafe-function-type': 'off',
      '@typescript-eslint/no-unnecessary-type-constraints': 'off',
      '@typescript-eslint/no-empty-object-type': 'off',

      // TypeScript rules
      '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_', varsIgnorePattern: '^_' }],
      '@typescript-eslint/consistent-type-definitions': 'off',
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-empty-function': 'off',
      '@typescript-eslint/consistent-type-definitions': 'off',
      '@typescript-eslint/prefer-nullish-coalescing': 'warn',
      '@typescript-eslint/prefer-optional-chain': 'warn',
      '@typescript-eslint/prefer-for-of': 'warn',
      '@typescript-eslint/prefer-includes': 'warn',

      // React rules
      'react/react-in-jsx-scope': 'off',
      // TO DO: Uncomment when able to address issues
      // "react/jsx-uses-react": "off",
      // "react/prop-types": "off",
      // "react/display-name": "warn",
      'react/jsx-key': 'error',

      /* 
        TO DO: Enable below rules when able to address related issues
      */

      // React Hooks
      // ...pluginReactHooks.configs.recommended.rules,

      // React Native
      // "react-native/no-unused-styles": "warn",
      // "react-native/split-platform-components": "off",
      // "react-native/no-inline-styles": "off",
      // "react-native/no-color-literals": "off",

      // Accessibility - Make more lenient for React Native
      // "jsx-a11y/no-static-element-interactions": "warn",
      // "jsx-a11y/click-events-have-key-events": "warn",
      // "jsx-a11y/no-noninteractive-element-interactions": "warn",
    },
  },
  {
    files: ['**/*.{js,mjs,cjs}'],
    languageOptions: {
      globals: { ...globals.node },
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    rules: {
      'no-unused-vars': 'warn',
      'no-console': 'off',
    },
  },
])
