name: DangerJS Checks

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  danger-check:
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Required for accurate git diff in DangerJS

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: 📦 Install dependencies
        run: |
          npm install
          npm install --save-dev danger

      - name: 🚦 Run DangerJS
        env:
          GITHUB_TOKEN: ${{ secrets.DANGER_GITHUB_API_TOKEN }}
        run: |
          yarn danger ci
