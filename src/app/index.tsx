import React, { useMemo } from 'react'
import { Redirect } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'

export default function Index() {
  // Shared Data
  const user = sharedState.user.use()
  const adminUser = sharedState.superAdmin.use()
  const appReadyState = sharedState.appReadyState.use()

  // Compute authentication state declaratively
  const isAuthenticated = useMemo(() => {
    // App is not ready yet - still determining authentication state
    if (!appReadyState?.isReady || appReadyState.notReadyMessage) {
      return undefined
    }

    // No user or admin user - not authenticated
    if (!user && !adminUser) {
      return false
    }

    // Check if user can auto-login
    const canAutoLogin = user
      ? user.isLoginDisabled || user.isDeactivated || user.state !== 'active'
        ? !!adminUser
        : true
      : true

    return canAutoLogin
  }, [appReadyState?.isReady, appReadyState?.notReadyMessage, user, adminUser])

  // Show loading state while app is initializing
  if (!appReadyState?.isReady || appReadyState.notReadyMessage) {
    return null // Let the native splash screen handle the loading state
  }

  // Handle authentication redirects
  if (isAuthenticated === true) {
    return <Redirect href={getRoutePath(Routes.FLEET_DASHBOARD)} />
  }

  if (isAuthenticated === false) {
    return <Redirect href={Routes.LOGIN} />
  }

  // Still determining authentication state
  return null
}
