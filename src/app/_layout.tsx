import React from 'react'
import { Providers } from '@src/providers'
import { Stack } from 'expo-router'
import { Routes } from '@src/navigation/constants'
import App from '@src/App'
import { Platform, StatusBar } from 'react-native'
import { colors } from '@src/theme/colors'
import { SafeAreaView } from 'react-native-safe-area-context'
import { onScreenClicked } from '@src/shared-state/General/debugView'

export default function RootLayout() {
  const RootLayoutNav = () => {
    // Do all things needed if the app is focused
    return (
      <App>
        <Stack
          screenOptions={{
            headerShown: false,
            gestureEnabled: false,
            animation: 'none',
          }}>
          <Stack.Screen name={Routes.INDEX} />
          <Stack.Screen name={Routes.AUTH} />
          <Stack.Screen name={Routes.HOME} />
          <Stack.Screen name={Routes.ADMIN} />
        </Stack>
      </App>
    )
  }

  return (
    <Providers>
      <SafeAreaView
        style={{
          flex: 1,
          minHeight: '100%',
          width: '100%',
          backgroundColor: colors.background.primary,
        }}
        edges={['top', 'left', 'right']}
        // Enable debugView.onScreenClicked to work on iOS/android
        onStartShouldSetResponder={e => Platform.OS !== 'web'}
        onResponderRelease={e => {
          onScreenClicked(e.nativeEvent.pageX, e.nativeEvent.pageY)
        }}>
        <StatusBar animated={true} />

        <RootLayoutNav />
      </SafeAreaView>
    </Providers>
  )
}
