import React from 'react'
import { Stack } from 'expo-router'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { Routes } from '@src/navigation/constants'

const Network = () => {
  return (
    <>
      <Stack.Screen
        name={Routes.NETWORK}
        options={{
          title: 'Oops!',
          headerShown: false,
        }}
      />
      <SeaTypography variant={'title'} containerStyle={{ backgroundColor: 'red' }}>
        NETWORK Error - TODO
      </SeaTypography>
    </>
  )
}

export default Network
