import React, { useEffect } from 'react'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { Sidebar } from '@src/navigation/Sidebar'
import { colors } from '@src/theme/globalStyle'
import { useGlobalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'

export default function HomeLayout() {
  const { vesselId: paramsVesselId } = useGlobalSearchParams()
  const stateVesselId = sharedState.vesselId.use()

  useEffect(() => {
    if (!!paramsVesselId && typeof paramsVesselId === 'string') {
      if (!stateVesselId || stateVesselId !== paramsVesselId) {
        /**
         * This is the only place where the VesselId should be set based on the URL.
         * Cases that will trigger it is as follows:
         *   - 1: onPress from FleetDashboard will trigger this
         *   - 2: Page refresh
         * */
        sharedState.vesselId.set(paramsVesselId)
      }
    }
  }, [stateVesselId, paramsVesselId])

  return (
    <GestureHandlerRootView style={{ flex: 1, backgroundColor: colors.white }}>
      <Sidebar />
    </GestureHandlerRootView>
  )
}
