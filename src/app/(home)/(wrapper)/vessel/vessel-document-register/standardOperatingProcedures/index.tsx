import React from 'react'
import { SOPs } from '@src/pages/VesselDocumentRegister/SOPs'
import { useVesselDocumentSubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'

const StandardOperatingProceduresPage = () => {
  const vesselId = sharedState.vesselId.use()

  return <SOPs headerSubNavigation={useVesselDocumentSubNav(vesselId, Routes.STANDARD_OPERATING_PROCEDURES)} />
}

export default StandardOperatingProceduresPage
