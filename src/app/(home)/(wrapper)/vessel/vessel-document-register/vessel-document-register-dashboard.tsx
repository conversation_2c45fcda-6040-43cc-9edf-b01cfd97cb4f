import React from 'react'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { Redirect, useLocalSearchParams } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

const VesselDocumentRegisterDashboardPage = () => {
  const { vesselId } = useLocalSearchParams()
  return <Redirect href={`${getRoutePath(Routes.VESSEL_CERTIFICATES)}?vesselId=${vesselId}`} />
}

export default VesselDocumentRegisterDashboardPage
