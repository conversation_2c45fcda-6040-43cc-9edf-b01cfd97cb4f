import React from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { useVesselDocumentSubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'
import { SurveyDocuments } from '@src/pages/VesselDocumentRegister/SurveyDocuments'

const SurveyDocumentsPage = () => {
  const vesselId = sharedState.vesselId.use()

  return <SurveyDocuments headerSubNavigation={useVesselDocumentSubNav(vesselId, Routes.SURVEY_DOCUMENTS)} />
}

export default SurveyDocumentsPage
