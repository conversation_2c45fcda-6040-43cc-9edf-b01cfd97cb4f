import { ViewUserDrills } from '@src/pages/Safety/Drills/ViewUserDrills'
import { useLocalSearchParams } from 'expo-router'
import React from 'react'
import { sharedState } from '@src/shared-state/shared-state'

const UserDrillsPage = () => {
  const vesselId = sharedState.vesselId.use()
  const { userId } = useLocalSearchParams()

  return <ViewUserDrills userId={userId as string} vesselId={vesselId} />
}

export default UserDrillsPage
