import React from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { useSafetySubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'
import { Drills } from '@src/pages/Safety/Drills/Drills'

const DrillsPage = () => {
  const vesselId = sharedState.vesselId.use()
  const headerSubNavigation = useSafetySubNav(vesselId, Routes.DRILLS)

  return (
    <>
      <Drills vesselId={vesselId} visible={true} headerSubNavigation={headerSubNavigation} />
    </>
  )
}

export default DrillsPage
