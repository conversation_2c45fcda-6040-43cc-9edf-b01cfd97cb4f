import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import SafetyEquipmentExpiries from '@src/pages/Safety/SafetyEquipmentExpiries/SafetyEquipmentExpiries'
import { useSafetySubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'

const SafetyEquipmentExpiriesListPage = () => {
  const vesselId = sharedState.vesselId.use()
  const headerSubNavigation = useSafetySubNav(vesselId, Routes.SAFETY_EQUIPMENT_EXPIRIES)
  return (
    <>
      {/*  TODO - Add wrapper component */}
      <SafetyEquipmentExpiries vesselId={vesselId} visible={true} headerSubNavigation={headerSubNavigation} />
    </>
  )
}

export default SafetyEquipmentExpiriesListPage
