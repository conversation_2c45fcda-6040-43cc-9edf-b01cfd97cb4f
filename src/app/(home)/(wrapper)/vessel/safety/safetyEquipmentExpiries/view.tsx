import React from 'react'
import ViewSafetyEquipmentExpiry from '@src/pages/Safety/SafetyEquipmentExpiries/ViewSafetyEquipmentExpiry'
import { useLocalSearchParams } from 'expo-router'

const SafetyEquipmentExpiriesViewPage = () => {
  const { itemId, vesselId } = useLocalSearchParams()
  return <ViewSafetyEquipmentExpiry vesselId={vesselId as string} itemId={itemId as string} />
}

export default SafetyEquipmentExpiriesViewPage
