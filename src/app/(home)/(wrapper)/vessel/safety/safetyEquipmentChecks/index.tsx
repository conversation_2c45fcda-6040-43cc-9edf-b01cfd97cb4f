import React from 'react'
import { SafetyChecks } from '@src/pages/Safety/SafetyChecks/SafetyChecks'
import { useSafetySubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'

const SafetyChecksListPage = () => {
  const vesselId = sharedState.vesselId.use()
  const headerSubNavigation = useSafetySubNav(vesselId, Routes.SAFETY_EQUIPMENT_CHECKS)

  return (
    <>
      <SafetyChecks vesselId={vesselId} visible={true} headerSubNavigation={headerSubNavigation} />
    </>
  )
}

export default SafetyChecksListPage
