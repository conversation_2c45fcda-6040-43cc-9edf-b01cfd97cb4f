import React from 'react'
import { Redirect, useLocalSearchParams } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

const SafetyDashboardPage = () => {
  const { vesselId } = useLocalSearchParams()
  return (
    <>
      <Redirect href={`${getRoutePath(Routes.SAFETY_EQUIPMENT_CHECKS)}?vesselId=${vesselId}`} />
    </>
  )
}

export default SafetyDashboardPage
