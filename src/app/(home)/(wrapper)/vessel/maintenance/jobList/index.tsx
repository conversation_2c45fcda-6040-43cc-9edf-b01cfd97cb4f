import React from 'react'
import { JobList } from '@src/pages/Maintenance/JobList'
import { sharedState } from '@src/shared-state/shared-state'
import { Routes } from '@src/navigation/constants'
import { useMaintenanceSubNav } from '@src/hooks/useSubNav'

const JobListPage = () => {
  const vesselId = sharedState.vesselId.use()
  return <JobList headerSubNavigation={useMaintenanceSubNav(vesselId, Routes.JOBLIST)} />
}

export default JobListPage
