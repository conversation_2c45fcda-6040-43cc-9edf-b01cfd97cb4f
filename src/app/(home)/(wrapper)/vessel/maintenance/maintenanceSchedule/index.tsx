import { MaintenanceSchedule } from '@src/pages/Maintenance/MaintenanceSchedule'
import React from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { Routes } from '@src/navigation/constants'
import { useMaintenanceSubNav } from '@src/hooks/useSubNav'

export default function MaintenanceScheduleList() {
  const vesselId = sharedState.vesselId.use()
  return <MaintenanceSchedule headerSubNavigation={useMaintenanceSubNav(vesselId, Routes.MAINTENANCE_SCHEDULE)} />
}
