import React from 'react'
import { EquipmentList } from '@src/pages/Maintenance/EquipmentList'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'
import { useMaintenanceSubNav } from '@src/hooks/useSubNav'

const EquipmentListPage = () => {
  const vesselId = sharedState.vesselId.use()

  return <EquipmentList headerSubNavigation={useMaintenanceSubNav(vesselId, Routes.EQUIPMENT_LIST)} />
}

export default EquipmentListPage
