import React from 'react'
import { SpareParts } from '@src/pages/Maintenance/SpareParts'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'
import { useMaintenanceSubNav } from '@src/hooks/useSubNav'

const SparePartsListPage = () => {
  const vesselId = sharedState.vesselId.use()
  return <SpareParts headerSubNavigation={useMaintenanceSubNav(vesselId, Routes.SPARE_PARTS_LIST)} />
}

export default SparePartsListPage
