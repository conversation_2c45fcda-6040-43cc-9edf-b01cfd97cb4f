import React from 'react'
import { Redirect, useLocalSearchParams } from 'expo-router'
import { Routes } from '@src/navigation/constants'
import { getRoutePath } from '@src/navigation/utils'

const MaintenanceDashboardPage = () => {
  const { vesselId } = useLocalSearchParams()
  return <Redirect href={`${getRoutePath(Routes.MAINTENANCE_SCHEDULE)}?vesselId=${vesselId}`} />
}

export default MaintenanceDashboardPage
