import { MaintenanceHistory } from '@src/pages/Maintenance/MaintenanceHistory'
import React from 'react'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'
import { useMaintenanceSubNav } from '@src/hooks/useSubNav'

const MaintenanceHistoryPage = () => {
  const vesselId = sharedState.vesselId.use()
  return <MaintenanceHistory headerSubNavigation={useMaintenanceSubNav(vesselId, Routes.MAINTENANCE_HISTORY)} />
}

export default MaintenanceHistoryPage
