import React from 'react'
import { EquipmentManual } from '@src/pages/Maintenance/EquipmentManual'
import { sharedState } from '@src/shared-state/shared-state'
import { Routes } from '@src/navigation/constants'
import { useMaintenanceSubNav } from '@src/hooks/useSubNav'

const EquipmentManualsPage = () => {
  const vesselId = sharedState.vesselId.use()
  return <EquipmentManual headerSubNavigation={useMaintenanceSubNav(vesselId, Routes.EQUIPMENT_MANUALS)} />
}

export default EquipmentManualsPage
