import { DangerousGoodsRegister } from '@src/pages/HealthSafety/DangerousGoodsRegister'
import { useHealthAndSafetySubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'

export default function DangerousGoodsRegisterPage() {
  const vesselId = sharedState.vesselId.use()

  return (
    <DangerousGoodsRegister headerSubNavigation={useHealthAndSafetySubNav(vesselId, Routes.DANGEROUS_GOODS_REGISTER)} />
  )
}
