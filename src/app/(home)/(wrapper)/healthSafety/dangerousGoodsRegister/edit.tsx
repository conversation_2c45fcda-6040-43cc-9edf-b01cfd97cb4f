import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { EditDangerousGoodsRegister } from '@src/components/_organisms/HealthSafety/DangerousGoodsRegister/EditDangerousGoodsRegister'

const DangerousGoodsRegisterEditPage = () => {
  const { dangerousGoodId } = useLocalSearchParams()

  const dangerousGoods = sharedState.dangerousGoods.use()
  const dangerousGood = dangerousGoods?.byId[dangerousGoodId as string]

  return <EditDangerousGoodsRegister dangerousGood={dangerousGood} />
}

export default DangerousGoodsRegisterEditPage
