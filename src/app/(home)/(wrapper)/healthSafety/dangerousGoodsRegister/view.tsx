import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { ViewDangerousGoodsRegister } from '@src/components/_organisms/HealthSafety/DangerousGoodsRegister/ViewDangerousGoodsRegister'

const DangerousGoodsRegisterViewPage = () => {
  const { dangerousGoodId } = useLocalSearchParams()

  const dangerousGoods = sharedState.dangerousGoods.use()
  const dangerousGood = dangerousGoods?.byId[dangerousGoodId as string]

  return <ViewDangerousGoodsRegister dangerousGood={dangerousGood} />
}

export default DangerousGoodsRegisterViewPage
