import React from 'react'
import { ViewIncident } from '@src/components/_organisms/HealthSafety/Incidents/ViewIncident'
import { useGlobalSearchParams, useLocalSearchParams, useRouter } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'

const IncidentReportViewPage = () => {
  const { incidentId } = useLocalSearchParams()

  const incidents = sharedState.incidents.use()
  const incident = incidents?.byId[incidentId as string]

  return <ViewIncident incident={incident} />
}

export default IncidentReportViewPage
