import { Incidents } from '@src/pages/HealthSafety/Incidents'
import { useHealthAndSafetySubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'

export default function IncidentsPage() {
  const vesselId = sharedState.vesselId.use()
  return <Incidents headerSubNavigation={useHealthAndSafetySubNav(vesselId, Routes.INCIDENT_REPORT)} />
}
