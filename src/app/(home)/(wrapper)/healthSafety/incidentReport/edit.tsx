import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { EditIncident } from '@src/components/_organisms/HealthSafety/Incidents/EditIncident'

const IncidentReportEditPage = () => {
  const { incidentId } = useLocalSearchParams()

  const incidents = sharedState.incidents.use()
  const incident = incidents?.byId[incidentId as string]

  return <EditIncident incident={incident} />
}

export default IncidentReportEditPage
