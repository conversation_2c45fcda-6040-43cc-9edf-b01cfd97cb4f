import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { ViewHealthSafetyMeetings } from '@src/components/_organisms/HealthSafety/HealthSafetyMeetings/ViewHealthSafetyMeetings'

const HealthSafetyMeetingsViewPage = () => {
  const { safetyMeetingReportId } = useLocalSearchParams()

  const safetyMeetingReports = sharedState.safetyMeetingReports.use()
  const safetyMeetingReport = safetyMeetingReports?.byId[safetyMeetingReportId as string]

  return <ViewHealthSafetyMeetings safetyMeetingReport={safetyMeetingReport} />
}

export default HealthSafetyMeetingsViewPage
