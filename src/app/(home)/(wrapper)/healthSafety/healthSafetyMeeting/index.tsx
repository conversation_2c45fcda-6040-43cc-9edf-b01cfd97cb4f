import { HealthSafetyMeetings } from '@src/pages/HealthSafety/HealthSafetyMeetings'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'
import { useHealthAndSafetySubNav } from '@src/hooks/useSubNav'

export default function HealthSafetyMeetingsPage() {
  const vesselId = sharedState.vesselId.use()
  return <HealthSafetyMeetings headerSubNavigation={useHealthAndSafetySubNav(vesselId, Routes.HEALTH_SAFETY_MEETING)} />
}
