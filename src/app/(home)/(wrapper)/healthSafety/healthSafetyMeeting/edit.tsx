import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { EditHealthSafetyMeetings } from '@src/components/_organisms/HealthSafety/HealthSafetyMeetings/EditHealthSafetyMeetings'

const HealthSafetyMeetingsEditPage = () => {
  const { safetyMeetingReportId } = useLocalSearchParams()

  const safetyMeetingReports = sharedState.safetyMeetingReports.use()
  const safetyMeetingReport = safetyMeetingReports?.byId[safetyMeetingReportId as string]

  return <EditHealthSafetyMeetings safetyMeetingReport={safetyMeetingReport} />
}

export default HealthSafetyMeetingsEditPage
