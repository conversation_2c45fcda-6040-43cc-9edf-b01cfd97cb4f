import { RiskRegister } from '@src/pages/HealthSafety/RiskRegister'
import { sharedState } from '@src/shared-state/shared-state'
import { useHealthAndSafetySubNav } from '@src/hooks/useSubNav'
import { Routes } from '@src/navigation/constants'

export default function RiskRegisterPage() {
  const vesselId = sharedState.vesselId.use()
  return <RiskRegister headerSubNavigation={useHealthAndSafetySubNav(vesselId, Routes.RISK_ASSESSMENT)} />
}
