import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { EditRiskRegister } from '@src/components/_organisms/HealthSafety/RiskRegister/EditRiskRegister'

const RiskAssessmentEditPage = () => {
  const { riskId } = useLocalSearchParams()

  const risks = sharedState.risks.use()
  const risk = risks?.byId[riskId as string]

  return <EditRiskRegister risk={risk} />
}

export default RiskAssessmentEditPage
