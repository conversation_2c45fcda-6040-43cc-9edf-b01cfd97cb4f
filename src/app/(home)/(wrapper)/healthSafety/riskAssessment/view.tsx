import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { ViewRegister } from '@src/components/_organisms/HealthSafety/RiskRegister/ViewRegister'

const RiskAssessmentViewPage = () => {
  const { riskId } = useLocalSearchParams()

  const risks = sharedState.risks.use()
  const risk = risks?.byId[riskId as string]

  return <ViewRegister risk={risk} />
}

export default RiskAssessmentViewPage
