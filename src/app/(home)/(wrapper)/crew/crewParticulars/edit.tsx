import React from 'react'
import { useLocalSearchParams } from 'expo-router'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { Crew } from '@src/pages/Crew/Crew'
import { CrewParticularsList } from '@src/pages/Crew/CrewParticularsList'
import { sharedState } from '@src/shared-state/shared-state'
import { renderFullName } from '@src/shared-state/Core/users'

const CrewParticularsEditPage = () => {
  const { crewId } = useLocalSearchParams()

  const users = sharedState.users.use()
  const crew = users?.byId[crewId as string]
  return (
    <>
      <SeaTypography variant={'title'}>Edit Crew Details Page - {renderFullName(crew)}</SeaTypography>
    </>
  )
}

export default CrewParticularsEditPage
