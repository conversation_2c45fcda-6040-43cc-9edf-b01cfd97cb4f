import React from 'react'
import { useGlobalSearchParams } from 'expo-router'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { sharedState } from '@src/shared-state/shared-state'
import { ScrollView } from 'react-native'

const CrewFormsPage = () => {
  const { crewId } = useGlobalSearchParams()

  const users = sharedState.users.use()
  const crew = users?.byId[crewId as string]

  return (
    <>
      <ScrollView key={`crew-${crew?.id}`} style={{ flex: 1 }}>
        <SeaTypography variant={'title'}>TODO - FORMS Page</SeaTypography>
      </ScrollView>
    </>
  )
}

export default CrewFormsPage
