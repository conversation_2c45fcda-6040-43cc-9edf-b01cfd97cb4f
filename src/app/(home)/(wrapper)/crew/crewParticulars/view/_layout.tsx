import React, { useCallback } from 'react'
import { Stack, useGlobalSearchParams, useLocalSearchParams, useRouter } from 'expo-router'
import { View } from 'react-native'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaSelector } from '@src/components/_atoms/SeaSelector/SeaSelector'
import { getRouteConfig, getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { renderFullName } from '@src/shared-state/Core/users'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaCard } from '@src/components/_atoms/SeaCard/SeaCard'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { useCrewParticularsSubNav } from '@src/hooks/useSubNav'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { formatDatetime } from '@src/lib/datesAndTime'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'

export default function CrewViewLayout() {
  const { styles } = useStyles(styleSheet)
  const { crewId, tab } = useGlobalSearchParams()

  const users = sharedState.users.use()
  const vesselId = sharedState.vesselId.use()
  const crew = users?.byId[crewId as string]

  const status = crew?.whenActivated
    ? `Activated ${formatDatetime(crew?.whenActivated, ' ')}`
    : crew?.whenWelcomed
      ? `Emailed ${formatDatetime(crew?.whenWelcomed, ' ')}`
      : `Added ${formatDatetime(crew?.whenAdded, ' ')}`

  return (
    <View style={styles.container}>
      <SeaPageCard
        titleComponent={
          <SeaPageCardTitle
            title={renderFullName(crew)}
            additionalElements={
              <>
                <SeaStack>
                  {crew?.isLicensee && (
                    <SeaTypography variant={'label'} color={'red'}>
                      LICENSEE{' '}
                    </SeaTypography>
                  )}
                  <SeaTypography variant={'value'} textStyle={{ fontStyle: 'italic', fontSize: 12 }}>
                    {status}
                  </SeaTypography>
                </SeaStack>
              </>
            }
          />
        }
        subNav={useCrewParticularsSubNav(
          vesselId,
          crewId as string,
          (tab as Routes) ?? Routes.CREW_PARTICULARS_VIEW_PROFILE
        )}
        primaryActionButton={
          <>
            {crew?.state !== 'archived' && (
              <RequirePermissions role="crewParticulars" level={permissionLevels.CREATE}>
                <SeaEditButton
                  key={'Edit'}
                  label={'Edit'}
                  variant={SeaButtonVariant.Primary}
                  onPress={() => alert('Coming soon!')}
                />
              </RequirePermissions>
            )}
          </>
        }
        secondaryActionButton={[
          <>
            {crew?.state !== 'archived' && (
              <RequirePermissions role="crewParticulars" level={permissionLevels.CREATE}>
                <SeaButton
                  key={'reset'}
                  label={'Reset Login'}
                  variant={SeaButtonVariant.Secondary}
                  onPress={() => alert('Coming soon!')}
                />
              </RequirePermissions>
            )}
          </>,
        ]}
      />

      <View style={styles.content}>
        <Stack screenOptions={{ headerShown: false }} />
      </View>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: theme.colors.background.primary,
  },
  header: {
    paddingBottom: 20,
    alignItems: 'flex-start',
  },
  content: {
    flex: 1,
  },
  card: {
    flex: 1,
    width: '100%',
    gap: 20,
  },
}))
