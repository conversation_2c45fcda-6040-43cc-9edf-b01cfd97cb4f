import React from 'react'
import { useGlobalSearchParams } from 'expo-router'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { sharedState } from '@src/shared-state/shared-state'
import { ScrollView } from 'react-native'

const CrewSeatimePage = () => {
  const { crewId } = useGlobalSearchParams()

  const users = sharedState.users.use()
  const crew = users?.byId[crewId as string]

  return (
    <>
      <ScrollView key={`crew-${crew?.id}`} style={{ flex: 1 }}>
        <SeaTypography variant={'title'}>TODO - Sea Time Page</SeaTypography>
      </ScrollView>
    </>
  )
}

export default CrewSeatimePage
