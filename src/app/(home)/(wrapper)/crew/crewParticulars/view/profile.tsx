import React from 'react'
import { useGlobalSearchParams } from 'expo-router'
import { ViewCrewParticularsProfile } from '@src/components/_organisms/Crew/CrewParticulars/View/ViewCrewParticularsProfile'
import { ScrollView } from 'react-native'

const CrewProfilePage = () => {
  const { crewId } = useGlobalSearchParams()

  return (
    <ScrollView key={`crew-${crewId}`} style={{ flex: 1 }}>
      <ViewCrewParticularsProfile crewId={crewId as string} />
    </ScrollView>
  )
}

export default CrewProfilePage
