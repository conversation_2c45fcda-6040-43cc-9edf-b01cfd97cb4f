import React, { useEffect, useState } from 'react'
import { attemptLogout } from '@src/App'
import { sharedState } from '@src/shared-state/shared-state'

const LogoutPage = () => {
  // Shared Data
  const user = sharedState.user.use()
  const adminUser = sharedState.superAdmin.use()
  const appReadyState = sharedState.appReadyState.use()

  // Hooks
  useEffect(() => {
    if (appReadyState?.isReady && !appReadyState.notReadyMessage) {
      if (user || adminUser) {
        attemptLogout()
      }
      /** Else:
       * - Do nothing because the Auth provider will handle the redirect to Login
       */
    }
  }, [appReadyState?.isReady, appReadyState?.notReadyMessage])

  return <></>
}

export default LogoutPage
