import { CreateableDocument, SharedStateConfig, UpdateableDocument, sharedState } from '../shared-state'
import { renderFullNameForUserId } from '../Core/users'
import { registerFiles, registerSignature } from '../FileSyncSystem/filesToCache'
import { canDelete, canView } from '../../shared-state/Core/userPermissions'
import {
  onSnapshot,
  query,
  collection,
  where,
  orderBy,
  QueryDocumentSnapshot,
  DocumentData,
} from '@src/lib/firebase/services/firestore.service'
import { firestore } from '@src/lib/firebase/services/firestore.service'
import { setupArrayQueryListener } from '@src/lib/firebase'
import { QueryOrderByConstraint } from 'firebase/firestore'
//
// Load contacts
// Note: all contacts are always loaded regardless of permission
//

export type Injury = {
  whoInjured?: string
  typeIds?: string[]
  locationIds?: string[]
  outcome?: keyof InjuryOutcomes
}

export enum IncidentWhoInvolvedTypes {
  employee = 'Employee(s)',
  contractor = 'Contractor(s)',
  trainnee = 'Trainnee(s)',
  volunteer = 'Volunteer(s)',
  guest = 'Guest(s)',
  public = 'Member(s) of the public',
}

export const authoritiesNotified: Record<string, string> = {
  n: "No, it's not requried",
  y: 'Yes',
  unsure: "I'm unsure if this is a notifiable event",
  amsa: "Yes, I've notified AMSA",
  whs: "Yes, I've notified Work Health and Safety",
  mnz: "Yes, I've notified Maritime New Zealand",
  ws: "Yes, I've notified Work Safe",
  mca: "Yes, I've notified MCA",
  hse: "Yes, I've notified HSE",
  miab: "Yes, I've notified MAIB",
}

export enum InjuryOutcomes {
  none = 'None',
  basicFirstAid = 'Basic First Aid',
  doctor = 'Doctor',
  hospital = 'Hospitalisation',
}

export enum IncidentState {
  active = 'active',
  completed = 'completed',
  deleted = 'deleted',
  draft = 'draft',
  forReview = 'forReview',
  inReview = 'inReview',
}

export enum IncidentType {
  incident = 'Incident',
  injury = 'Injury',
  nearMiss = 'Near Miss',
  illness = 'Illness',
  event = 'Event (General)',
}

export interface Incident extends CreateableDocument, UpdateableDocument {
  authority?: string
  categoryId?: string
  causeIds: string[]
  completedBy?: string
  conditions?: string
  deletedBy?: string
  description?: string
  files: string[]
  initialActions?: string
  injuries?: Injury[]
  isSensitive?: 'y' | 'n'
  licenseeId: string
  location?: string
  name: string
  notifiedAuthorities?: string
  prevention?: string
  propertyDamage?: string
  reportNum: string
  reportedBy?: string
  role?: string
  searchText?: string
  signature?: string
  // TODO: Sree - double check if this works across all the places
  // state: "draft" | "forReview" | "inReview" | "completed";
  state: IncidentState
  // TODO: Sree - double check if this works across all the places
  // type: string;
  type: keyof IncidentType
  vesselId: string
  whenAccident?: number
  whenCompleted?: number
  whenDeleted?: number
  whenEvent: number
  whoInvolved?: string
  whoInvolvedTypes: [keyof IncidentWhoInvolvedTypes]
  witnesses?: string
}

export type IncidentsData = {
  all: Incident[] // Ordered by whenEvent descending
  byId: Record<string, Incident>
  byVesselId: Record<string, Incident[]>
}

export const incidentsConfig: SharedStateConfig<IncidentsData> = {
  isAlwaysActive: false,
  dependencies: ['userId', 'vesselIds', 'licenseeSettings', 'userPermissions'],
  countLiveDocs: () => sharedState.incidents.current?.all?.length ?? 0,
  run: (done, set, clear) => {
    clear()
    const userId = sharedState.userId.current
    const vesselIds = sharedState.vesselIds.current
    if (userId && vesselIds && vesselIds.length > 0 && sharedState.licenseeSettings.current?.hasIncidents) {
      const searchText = (incident: Incident) => {
        let searchText = incident.name
        if (incident.location) {
          searchText += incident.location
        }
        if (incident.reportedBy) {
          searchText += renderFullNameForUserId(incident.reportedBy)
        }
        if (incident.role) {
          searchText += incident.role
        }
        if (incident.whoInvolved) {
          searchText += renderFullNameForUserId(incident.whoInvolved)
        }
        if (incident.witnesses) {
          searchText += incident.witnesses
        }
        if (incident.conditions && incident.conditions !== 'N/A') {
          searchText += incident.conditions
        }
        if (incident.propertyDamage) {
          searchText += incident.propertyDamage
        }
        if (incident.description) {
          searchText += incident.description
        }
        if (incident.initialActions) {
          searchText += incident.initialActions
        }
        if (incident.prevention) {
          searchText += incident.prevention
        }
        return searchText.toLowerCase()
      }

      const makeAll = (docs: QueryDocumentSnapshot<DocumentData>[]) => {
        return docs.map(doc => {
          return {
            ...(doc.data() as Incident),
            id: doc.id,
            searchText: searchText(doc.data() as Incident),
          } as Incident
        })
      }

      const sortAll = (all: Incident[]) => {
        all.sort((a, b) => {
          return b.whenEvent - a.whenEvent
        })
      }

      const processAll = (all: Incident[]) => {
        const byId = {} as Record<string, Incident>
        const byVesselId = {} as Record<string, Incident[]>
        all.forEach(incident => {
          byId[incident.id] = incident
          if (byVesselId[incident.vesselId] === undefined) {
            byVesselId[incident.vesselId] = []
          }
          byVesselId[incident.vesselId].push(incident)
          registerFiles(incident.files, 'incidents', incident)
          registerSignature(incident.signature, 'incidents', incident)
        })
        set({
          all,
          byId,
          byVesselId,
        })
      }

      if (canDelete('incidentAccidentMedicalRegister')) {
        //
        // Users with delete permission can view ALL reports (with relevant vessel access)
        //

        return setupArrayQueryListener(
          'incidents', // what
          collection(firestore, 'incidents'),
          [where('state', 'in', ['draft', 'forReview', 'inReview', 'completed'])], // baseConstraints
          'vesselId',
          'in',
          vesselIds,
          [orderBy('whenEvent', 'desc') as QueryOrderByConstraint],
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            done()
            const all = makeAll(docs)
            if (isCombined) {
              // Need to sort by whenAccident
              sortAll(all)
            }
            processAll(all)
          },
          error => {
            // onError
            done()
          }
        )
      } else if (canView('incidentAccidentMedicalRegister')) {
        //
        // Users with view/edit permissions can view ALL non-draft reports (with relevant vessel access)
        // as well as all their own reports (we'll need to combine two query listener results)
        //

        let myDrafts: Incident[]
        let nonDrafts: Incident[]

        const combine = () => {
          if (myDrafts !== undefined && nonDrafts !== undefined) {
            const all = [...myDrafts, ...nonDrafts]
            sortAll(all)
            processAll(all)
          }
        }

        // Gather drafts created by me
        const myDraftsCleanup = onSnapshot(
          query(
            collection(firestore, 'incidents'),
            where('addedBy', '==', userId),
            where('state', '==', 'draft'),
            orderBy('whenEvent', 'desc')
          ),
          snap => {
            done()
            myDrafts = makeAll(snap.docs)
            combine()
          },
          error => {
            done()
            console.log(`Error getting incidents for user ${userId}`, error)
          }
        )

        // Gather all non draft reports (that I have access via vessel to)
        const nonDraftsCleanup = setupArrayQueryListener(
          'incidents', // what
          collection(firestore, 'incidents'),
          [where('state', 'in', ['forReview', 'inReview', 'completed'])], // baseConstraints
          'vesselId',
          'in',
          vesselIds,
          [orderBy('whenEvent', 'desc') as QueryOrderByConstraint],
          (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
            // processDocs
            nonDrafts = makeAll(docs)
            combine()
          }
        )

        // Return cleanup function
        return () => {
          myDraftsCleanup()
          nonDraftsCleanup()
        }
      } else {
        //
        // Users with no view permissions still see their own reports
        //

        return onSnapshot(
          query(
            collection(firestore, 'incidents'),
            where('addedBy', '==', userId),
            where('state', 'in', ['draft', 'forReview', 'inReview', 'completed']),
            orderBy('whenEvent', 'desc')
          ),
          snap => {
            done()
            const all = makeAll(snap.docs)
            processAll(all)
          },
          error => {
            done()
            console.log(`Error getting incidents for user ${userId}`, error.message, error)
          }
        )
      }
    }
  },
}
