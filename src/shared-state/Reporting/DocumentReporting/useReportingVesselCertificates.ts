import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { sharedState } from '@src/shared-state/shared-state'
import { useEffect, useState } from 'react'
import { getDayOffset, getDayOffsetMillis, MIN_DATE, warnDays } from '@src/lib/datesAndTime'
import { VesselCertificate } from '../../VesselDocuments/vesselCertificates'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'

export const useReportingVesselCertificates = (
  isActive: boolean,
  selectedVesselIds: string[],
  selectedStatuses: string[] | undefined
) => {
  const today = sharedState.today.use()!
  const [reportingVesselCertificates, setReportingVesselCertificates] = useState<VesselCertificate[]>()

  useEffect(() => {
    if ((selectedStatuses?.length || 0) === 0 || (selectedVesselIds.length || 0) === 0) {
      // Nothing to load
      setReportingVesselCertificates([])
      return
    }
    setReportingVesselCertificates(undefined)
    if (isActive && selectedVesselIds?.length && selectedStatuses) {
      const includeOverdue = selectedStatuses.includes('overdue')
      const includeUpcoming = selectedStatuses.includes('upcoming')
      if (!includeOverdue && !includeUpcoming) {
        return
      }

      const baseConstraints = [where('state', '==', 'active')]
      if (includeOverdue && includeUpcoming) {
        baseConstraints.push(where('dateExpires', '<', getDayOffset(warnDays.vesselCertificates[0])))
      } else if (includeOverdue) {
        // Include overdue only
        baseConstraints.push(where('dateExpires', '<', today))
      } else {
        // Include upcoming only
        baseConstraints.push(
          where('dateExpires', '<', getDayOffsetMillis(warnDays.vesselCertificates[0])),
          where('dateExpires', '>=', today)
        )
      }

      return setupArrayQueryListener(
        'vesselCertificates', // what
        collection(firestore, 'vesselCertificates'),
        baseConstraints,
        'vesselId',
        'in',
        selectedVesselIds,
        [orderBy('dateExpires', 'asc') as QueryOrderByConstraint],
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          const all = [] as VesselCertificate[]
          docs.forEach(doc => {
            const certificate = {
              id: doc.id,
              ...doc.data(),
            } as VesselCertificate
            registerFiles(certificate.files, 'vesselCertificates', certificate)
            all.push(certificate)
          })

          if (isCombined) {
            all.sort((a, b) => {
              //return (a.dateExpires ?? 0) - (b.dateExpires ?? 0);
              return (a.dateExpires ?? MIN_DATE).localeCompare(b.dateExpires ?? MIN_DATE)
            })
          }

          setReportingVesselCertificates(all)
        }
      )
    }
  }, [isActive, selectedVesselIds, selectedStatuses, today])

  return reportingVesselCertificates
}
