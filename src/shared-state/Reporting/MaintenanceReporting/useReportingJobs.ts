import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { ReportingEquipmentByIdData } from './useReportingEquipmentById'
import { Job } from '../../VesselMaintenance/jobs'
import { registerFiles } from '@src/shared-state/FileSyncSystem/filesToCache'
import { MAX_DATE } from '@src/lib/datesAndTime'

export const useReportingJobs = (
  isActive: boolean,
  selectedVesselIds: string[],
  selectedJobPriorities: string[] | undefined,
  equipmentById: ReportingEquipmentByIdData | undefined,
  equipmentCriticality: string
) => {
  const [reportingJobs, setReportingJobs] = useState<Job[]>()

  useEffect(() => {
    if (!selectedJobPriorities || selectedJobPriorities.length === 0 || selectedVesselIds.length === 0) {
      // Nothing to load
      setReportingJobs([])
      return
    }
    setReportingJobs(undefined)
    if (isActive && selectedVesselIds?.length) {
      return setupArrayQueryListener(
        'jobs', // what
        collection(firestore, 'jobs'),
        [where('state', '==', 'active'), where('priority', 'in', selectedJobPriorities)],
        'vesselId',
        'in',
        selectedVesselIds,
        [orderBy('task', 'asc') as QueryOrderByConstraint],
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          let all = [] as Job[]

          docs.forEach(doc => {
            const job = {
              id: doc.id,
              ...doc.data(),
            } as Job
            registerFiles(job.files, 'jobs', job)
            all.push(job)
          })

          if (equipmentById) {
            if (equipmentCriticality === 'critical') {
              all = all.filter(item => item.equipmentId && equipmentById[item.equipmentId]?.isCritical)
            } else if (equipmentCriticality === 'nonCritical') {
              all = all.filter(item => item.equipmentId && !equipmentById[item.equipmentId]?.isCritical)
            }
          }

          all.sort((a, b) => {
            if (a.priority !== b.priority) {
              // sort first by priority
              return b.priority.localeCompare(a.priority)
            } else if (a.dateDue !== b.dateDue) {
              // next sort by dateDue (may be undefined)
              //return (a.dateDue ? a.dateDue : Number.MAX_SAFE_INTEGER) - (b.dateDue ? b.dateDue : Number.MAX_SAFE_INTEGER);
              return (a.dateDue ? a.dateDue : MAX_DATE).localeCompare(b.dateDue ? b.dateDue : MAX_DATE)
            } else {
              // sort by task
              return a.task.localeCompare(b.task)
            }
          })

          setReportingJobs(all)
        }
      )
    }
  }, [isActive, selectedVesselIds, selectedJobPriorities, equipmentById, equipmentCriticality])

  return reportingJobs
}
