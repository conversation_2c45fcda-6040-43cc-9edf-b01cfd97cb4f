import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { SafetyCheckItem } from '../../VesselSafety/safetyCheckItems'
import { getDayOffset, warnDays } from '@src/lib/datesAndTime'
import { sharedState } from '@src/shared-state/shared-state'
import { makeSafetyItems, sortSafetyItems } from './reportingSafetyFunctions'

export const useReportingDueSafetyChecks = (
  isActive: boolean,
  selectedVesselIds: string[],
  selectedSafetyCheckStatuses: string[]
) => {
  const today = sharedState.today.use()!
  const [reportingDueSafetyChecks, setReportingDueSafetyChecks] = useState<SafetyCheckItem[]>()

  useEffect(() => {
    setReportingDueSafetyChecks(undefined)
    if (isActive && selectedVesselIds?.length) {
      const includeOverdue = selectedSafetyCheckStatuses.includes('overdue')
      const includeUpcoming = selectedSafetyCheckStatuses.includes('upcoming')
      if (!includeOverdue && !includeUpcoming) {
        return
      }

      const baseConstraints = [where('state', '==', 'active')]
      baseConstraints.push(where('hasFault', '==', false))
      if (includeOverdue && includeUpcoming) {
        baseConstraints.push(where('dateDue', '<', getDayOffset(warnDays.safetyEquipmentChecks[0])))
      } else if (includeOverdue) {
        // Include overdue only
        baseConstraints.push(where('dateDue', '<', today))
      } else {
        // Include upcoming only
        baseConstraints.push(
          where('dateDue', '<', getDayOffset(warnDays.safetyEquipmentChecks[0])),
          where('dateDue', '>=', today)
        )
      }

      return setupArrayQueryListener(
        'safetyCheckItems', // what
        collection(firestore, 'safetyCheckItems'),
        baseConstraints,
        'vesselId',
        'in',
        selectedVesselIds,
        [orderBy('dateDue', 'asc') as QueryOrderByConstraint],
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          const all = makeSafetyItems(docs) as SafetyCheckItem[]
          if (isCombined) {
            sortSafetyItems(all)
          }
          setReportingDueSafetyChecks(all)
        }
      )
    }
  }, [isActive, selectedVesselIds, selectedSafetyCheckStatuses, today])

  return reportingDueSafetyChecks
}
