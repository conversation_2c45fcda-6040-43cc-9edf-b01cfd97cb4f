import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { useEffect, useState } from 'react'
import { getDayOffset, warnDays } from '@src/lib/datesAndTime'
import { sharedState } from '@src/shared-state/shared-state'
import { makeSafetyItems, sortSafetyItems } from './reportingSafetyFunctions'
import { ReportingSafetyItemById } from './useReportingSafetyItemsById'
import { SafetyEquipmentItem } from '../../VesselSafety/safetyEquipmentItems'

export const useReportingSafetyEquipments = (
  isActive: boolean,
  selectedVesselIds: string[],
  selectedSafetyEquipmentStatuses: string[],
  vesselSafetyItemsById: ReportingSafetyItemById | undefined,
  safetyCriticality: string
) => {
  const today = sharedState.today.use()!
  const [reportingSafetyEquipment, setReportingSafetyEquipment] = useState<SafetyEquipmentItem[]>()

  useEffect(() => {
    if (selectedSafetyEquipmentStatuses.length === 0 || selectedVesselIds.length === 0) {
      // Nothing to load
      setReportingSafetyEquipment([])
      return
    }
    setReportingSafetyEquipment(undefined)
    if (isActive && selectedVesselIds?.length) {
      const includeOverdue = selectedSafetyEquipmentStatuses.includes('overdue')
      const includeUpcoming = selectedSafetyEquipmentStatuses.includes('upcoming')
      if (!includeOverdue && !includeUpcoming) {
        return
      }

      const baseConstraints = [where('state', '==', 'active')]
      if (includeOverdue && includeUpcoming) {
        baseConstraints.push(where('dateDue', '<', getDayOffset(warnDays.safetyEquipmentExpiries[0])))
      } else if (includeOverdue) {
        // Include overdue only
        baseConstraints.push(where('dateDue', '<', today))
      } else {
        // Include upcoming only
        baseConstraints.push(
          where('dateDue', '<', getDayOffset(warnDays.safetyEquipmentExpiries[0])),
          where('dateDue', '>=', today)
        )
      }

      return setupArrayQueryListener(
        'safetyEquipmentItems', // what
        collection(firestore, 'safetyEquipmentItems'),
        baseConstraints,
        'vesselId',
        'in',
        selectedVesselIds,
        [orderBy('dateDue', 'asc') as QueryOrderByConstraint],
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          let all = makeSafetyItems(docs) as SafetyEquipmentItem[]

          // Filter by criticality
          if (vesselSafetyItemsById) {
            if (safetyCriticality === 'critical') {
              all = all.filter(item => vesselSafetyItemsById[item.itemId]?.isCritical)
            } else if (safetyCriticality === 'nonCritical') {
              all = all.filter(item => !vesselSafetyItemsById[item.itemId]?.isCritical)
            }
          }

          if (isCombined) {
            sortSafetyItems(all)
          }
          setReportingSafetyEquipment(all)
        }
      )
    }
  }, [isActive, selectedVesselIds, selectedSafetyEquipmentStatuses, vesselSafetyItemsById, safetyCriticality, today])

  return reportingSafetyEquipment
}
