import {
  DocumentData,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  collection,
  orderBy,
  where,
} from '@src/lib/firebase/services/firestore.service'
import { firestore, setupArrayQueryListener } from '@src/lib/firebase'
import { sharedState } from '@src/shared-state/shared-state'
import { useEffect, useState } from 'react'
import { UserType } from '@src/shared-state/Core/user'
//import { useReportSettings } from "../../../pages/Reporting/Reporting";
import { ActionLogEntry } from '../../General/actionLog'
import { addInterval, makeDateTime } from '@src/lib/datesAndTime'

export const useReportingActionLog = (
  isActive: boolean,
  selectedVesselIds: string[],
  filteredUsers:
    | {
        list: UserType[]
        byId: Record<string, UserType>
      }
    | undefined
) => {
  const licenseeId = sharedState.licenseeId.use()
  //const { dateRange } = useReportSettings();
  const dateRange = { from: '20250101', to: '20260101' } // Dummy Value. Todo! Use useReportSettings hook once reporting filters are created.
  const [reportingActionLog, setReportingActionLog] = useState<ActionLogEntry[]>()

  useEffect(() => {
    if (filteredUsers?.list?.length === 0) {
      // Nothing to load
      setReportingActionLog([])
      return
    }
    setReportingActionLog(undefined)
    if (isActive && filteredUsers?.list) {
      const userIds = filteredUsers.list.map(user => {
        return user.id
      }) as string[]
      return setupArrayQueryListener(
        'actionLog', // what
        collection(firestore, 'actionLog'),
        [
          where('licenseeId', '==', licenseeId),
          where('when', '>=', makeDateTime(dateRange.from).toMillis()),
          where('when', '<', addInterval(dateRange.to, '1d').toMillis()),
        ],
        'userId',
        'in',
        userIds,
        [orderBy('when', 'desc') as QueryOrderByConstraint], // orderBy
        (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => {
          // processDocs
          const array = docs.map(doc => {
            return {
              id: doc.id,
              ...doc.data(),
            } as ActionLogEntry
          })
          if (isCombined) {
            array.sort((a, b) => {
              return b.when - a.when
            })
          }
          setReportingActionLog(array)
        }
      )
    }
  }, [isActive, licenseeId, filteredUsers, selectedVesselIds, dateRange])

  return reportingActionLog
}
