import { doc, docExists, onSnapshot } from '@src/lib/firebase/services/firestore.service'
import { firestore, analytics, auth } from '@src/lib/firebase'
import {
  logEvent as logAnalyticsEvent,
  setUserProperties as setAnalyticsUserProperties,
} from '@src/lib/firebase/services/analytics.service'
import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { hasArrayChanged, haveObjectsChanged } from '@src/lib/util'
import {
  createUserWithEmailAndPassword,
  NativeAuth,
  signInWithCustomToken,
  signOut,
} from '@src/lib/firebase/services/auth.service'
import { Platform } from 'react-native'

//
// Loads user data based on userId.
// Is also responsible to set user properties for Analytics.
// Note: The user data may not match the authenticated user if a superadmin has logged in as someone.
//

export type UserType = {
  id?: string
  firstName?: string
  lastName?: string
  position?: string
  roleIds?: string[]
  isSuperAdmin?: boolean
  isLicensee?: boolean
  licenseeId?: string
  vesselIds?: string[]
  crewVesselIds?: string[]
  isStaff?: boolean
  whenRoleChanged?: number
  isLoginDisabled?: boolean
  contactNumber?: string
  emailMe?: string[]
  emailMeVesselIds?: string[]
  //dateOfBirth?: string,
  whenAdded?: number
  whenWelcomed?: number
  whenActivated?: number
  dateInducted?: string
  //passportNumber?: string,
  //address?: string,
  //nextOfKin?: {
  //    name?: string,
  //    relationship?: string,
  //    phone?: string,
  //},
  //bankAccountNumber?: string,
  //irdNumber?: string,
  //contractFiles?: string[],
  //inductionFiles?: string[],
  //medicalDoctor?: {
  //    name?: string,
  //    phone?: string,
  //},
  //currentMedicalIssues?: string,
  //currentMedication?: string,
  //previousInjuryOrSurgery?: string,
  //allergies?: string,
  //bloodType?: string,
  // emailMe?: string[],
  //weeklyReport?: string[],
  state?: string
  timezone?: string
  whenUpdated?: string
  whenArchived?: string
  whenDeleted?: string
  deletedBy?: string
  addedBy?: string
  updatedBy?: string
  isDeactivated?: boolean
  // licensee
  companyName?: string
  //companyAddress?: string,
  //skipperInductionFiles?: string[],
  //crewInductionFiles?: string[],
  //stripeCreditCardDetails?: any,
  //numVessels?: number,
}

export type LicenseeUserType = UserType & {
  id: string
  uid?: string
  dateOfBirth?: string
  passportNumber?: string
  address?: string
  nextOfKin?: {
    name?: string
    relationship?: string
    phone?: string
  }
  bankAccountNumber?: string
  irdNumber?: string
  contractFiles?: string[]
  inductionFiles?: string[]
  medicalDoctor?: {
    name?: string
    phone?: string
  }
  currentMedicalIssues?: string
  currentMedication?: string
  previousInjuryOrSurgery?: string
  allergies?: string
  bloodType?: string
  weeklyReport?: string[]
  companyAddress?: string
  skipperInductionFiles?: string[]
  crewInductionFiles?: string[]
  stripeCreditCardDetails?: any
  numVessels?: number
  licenseeNumber?: number
}

export const userConfig: SharedStateConfig<UserType> = {
  isAlwaysActive: true,
  dependencies: ['userId'],
  countLiveDocs: () => (sharedState.user.current ? 1 : 0),
  run: (done, set, clear) => {
    const userId = sharedState.userId.current
    if (userId) {
      return onSnapshot(
        doc(firestore, 'users', userId),
        { includeMetadataChanges: true },
        doc => {
          done()
          if (!docExists(doc)) {
            console.log('No user matching userId! ' + userId)
            clear()
            sharedState.vesselIds.clear()

            // Fix the "quick restart" problem which takes you to the login screen
            if (
              Platform.OS !== 'web' &&
              !sharedState.onlineStatus.current?.isOnline //&&
              //window.location.href.indexOf("/restart?reason=offlineFail") === -1
            ) {
              //window.location.href = "/restart?reason=offlineFail";
              //
              // Note: If the "quick restart" problem still exists we'll need to
              // take the user to a restart screen that says something like...
              //
              // There was a problem authenticating your account while offline.
              // Please restart the app to try again.
              //
              console.error('Possible quick restart problem has occurred!')
            }
          } else {
            const data = {
              id: doc.id,
              ...doc.data()!,
            } as UserType

            if (haveObjectsChanged(data, sharedState.user.current)) {
              set(data)
            }

            // Update userFromCache
            sharedState.userFromCache.set(doc.metadata.fromCache)

            // Update licenseeId if changed and notify fileSync system
            if (sharedState.licenseeId.current !== data?.licenseeId) {
              sharedState.licenseeId.set(data?.licenseeId)
            }

            // Update vesselIds if they changed
            if (hasArrayChanged(data.vesselIds, sharedState.vesselIds.current)) {
              sharedState.vesselIds.set(data.vesselIds)
            }

            // User properties for analytics
            if (sharedState.superAdmin.current === undefined) {
              let type = 'nonStaff'
              if (data?.isLicensee) {
                type = 'licensee'
              } else if (data?.isStaff) {
                type = 'staff'
              } else {
                type = 'nonStaff'
              }
              setAnalyticsUserProperties(analytics, {
                // Try to set it to `undefined`
                isSuperAdmin: 'false',
                isLicensee: data?.isLicensee ? 'true' : 'false',
                isStaff: data?.isStaff ? 'true' : 'false',
                state: data?.state,
                type,
              })
            }
          }
          // sharedState.userPending.set(false); <-- Actually, let's keep userPending=true until userPermissions have loaded
        },
        error => {
          done()
          clear()
          sharedState.userPending.set(false)
          console.log(`error getting user ${userId}`, error)
        }
      )
    }
  },
}

// export const signupUser = (email: string, password: string) => {
//     return createUserWithEmailAndPassword(auth, email || '', password || ''); // returns Promise
// };

export const loginUser = (token: string) => {
  return signInWithCustomToken(auth, token) // returns Promise
}

// export const loginUser = async (token: string) => {
//     try {
//         return await signInWithCustomToken(auth, token);
//     } catch (error) {
//         console.error("Error in loginUser:", error);
//         throw error;
//     }
// };

export const logoutUser = async () => {
  // returns Promise
  if (Platform.OS === 'web') {
    const { auth } = await import('@src/lib/firebase/config/firebase.web')
    return signOut(auth).then(() => {
      logAnalyticsEvent(analytics!, 'logout')
      sharedState.superAdmin.clear()
      sharedState.user.clear()
      return Promise.resolve()
    })
  } else {
    const { auth } = await import('@src/lib/firebase/config/firebase.native')

    return auth()
      .signOut()
      .then(() => {
        logAnalyticsEvent(analytics!, 'logout')
        sharedState.superAdmin.clear()
        sharedState.user.clear()
        return Promise.resolve()
      })
  }
}
