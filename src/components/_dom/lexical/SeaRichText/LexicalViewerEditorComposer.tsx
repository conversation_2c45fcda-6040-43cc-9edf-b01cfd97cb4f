'use dom'

import React from 'react'
import { LexicalComposer } from '@lexical/react/LexicalComposer'
import { globalActiveEditor, lexicalTheme, lexNodes } from '@src/components/_dom/lexical/SeaRichText/SeaRichTextUtils'
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin'
import { ContentEditable } from '@lexical/react/LexicalContentEditable'
import LexicalErrorBoundary from '@lexical/react/LexicalErrorBoundary'
import { ListPlugin } from '@lexical/react/LexicalListPlugin'
import { TablePlugin } from '@lexical/react/LexicalTablePlugin'
import LexTableCellResizer from '@src/components/_dom/lexical/plugins/LexTableCellResizer/LexTableCellResizer'
import LexImages from '@src/components/_dom/lexical/plugins/LexImages/LexImages'
import LexPagePlugin, { INSERT_PAGE_BREAK_COMMAND } from '@src/components/_dom/lexical/plugins/LexPage/LexPage'
import { HorizontalRulePlugin } from '@lexical/react/LexicalHorizontalRulePlugin'
import { TabIndentationPlugin } from '@lexical/react/LexicalTabIndentationPlugin'
import { LexicalEditor } from 'lexical'

// Styling
import './SeaRichText.css'
import LexToolBar from '@src/components/_dom/lexical/plugins/LexToolBar/LexToolBar'
import { View } from 'react-native'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'

const LexicalViewerEditorComposer = ({ children, isEditable = false }) => {
  const onLexicalError = (error: Error, editor: LexicalEditor) => {
    console.error('Lexical editor', error)
  }

  return (
    <View
      style={{
        flex: 1,
        height: '100%',
      }}>
      <LexicalComposer
        initialConfig={{
          // TODO: Change back
          editable: isEditable,
          namespace: 'LexEditor',
          theme: lexicalTheme,
          nodes: lexNodes,
          onError: onLexicalError,
          // editorState: content
        }}>
        <>
          {/** Toolbar */}
          {isEditable && (
            <View>
              <LexToolBar level={1} />
            </View>
          )}
          {/* <LexDragDropPaste /> */}
          {/* <AutoFocusPlugin /> */}

          {/** Text Document */}
          <ScrollablePageLayout
            style={[
              {
                height: '100%',
              },
              isEditable
                ? { borderColor: '#DCDDE1', borderLeftWidth: 1, borderRightWidth: 1, borderBottomWidth: 1 }
                : {},
            ]}
            showsVerticalScrollIndicator={true}
            contentContainerStyle={{
              paddingHorizontal: 20,
            }}>
            <div className="document">
              <div className="sfdoc white-zone">
                <RichTextPlugin
                  contentEditable={
                    <>
                      {/*<div className="lex-scroller">*/}
                      {/* <div className="lex-editor" ref={onRef}> */}

                      <div className="lex-editor">
                        <ContentEditable className="lex-content-editable-root" />
                      </div>
                      {/*</div>*/}
                    </>
                  }
                  placeholder={null}
                  ErrorBoundary={LexicalErrorBoundary}
                />

                <ListPlugin />
                <TablePlugin />
                <LexTableCellResizer />
                <LexImages />
                <LexPagePlugin />
                <HorizontalRulePlugin />
                <TabIndentationPlugin />

                {children}

                {/* <HistoryPlugin /> */}
                {/* <TreeViewPlugin /> */}
                {isEditable && (
                  <SeaStack align={'center'} justify={'center'} style={{ paddingBottom: 20 }}>
                    <SeaAddButton
                      variant={'primary'}
                      onPress={() => {
                        globalActiveEditor.dispatchCommand(INSERT_PAGE_BREAK_COMMAND, 'end')
                      }}
                    />
                  </SeaStack>
                )}
              </div>
            </div>
          </ScrollablePageLayout>
        </>
      </LexicalComposer>
    </View>
  )
}

export default LexicalViewerEditorComposer
