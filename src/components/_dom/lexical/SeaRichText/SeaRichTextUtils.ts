import { LexicalEditor, LexicalNode } from 'lexical'
import { ListItemNode, ListNode } from '@lexical/list'
import { AutoLinkNode, LinkNode } from '@lexical/link'
import { HeadingNode, QuoteNode } from '@lexical/rich-text'
import { TableCellNode, TableNode, TableRowNode } from '@lexical/table'
import { ImageNode } from '@src/components/_dom/lexical/nodes/ImageNode/ImageNode'
import { PageNode } from '@src/components/_dom/lexical/nodes/PageNode/PageNode'
import { HorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode'
import { SeaSectionNode } from '@src/components/_dom/lexical/nodes/SeaSectionNode/SeaSectionNode'

export const lexContentWidth = 760 // pixels that cover 21cm of A4 paper
export const lexMarginX = 28
export const lexMarginTop = 20
export const lexMarginBottom = 40
//export const lexNodes: Array<Klass<LexicalNode>> = [
export const lexNodes: any[] = [
  //HeadingNode,
  ListNode,
  ListItemNode,
  QuoteNode,
  // CodeNode,
  // NewTableNode,
  TableNode,
  TableCellNode,
  TableRowNode,
  // HashtagNode,
  // CodeHighlightNode,
  AutoLinkNode,
  LinkNode,
  // OverflowNode,
  // PollNode,
  // StickyNode,
  ImageNode,
  // MentionNode,
  // EmojiNode,
  // ExcalidrawNode,
  // EquationNode,
  // AutocompleteNode,
  // KeywordNode,
  PageNode,
  HorizontalRuleNode,
  // TweetNode,
  // YouTubeNode,
  // FigmaNode,
  // MarkNode,
  // CollapsibleContainerNode,
  // CollapsibleContentNode,
  // CollapsibleTitleNode,
  SeaSectionNode,
  {
    replace: HeadingNode,
    with: (node: HeadingNode): SeaSectionNode => {
      if (node.__tag === 'h1' || node.__tag === 'h2') {
        return new SeaSectionNode(node.__tag)
      } else {
        return node
      }
    },
  },
]
export const lexicalTheme = {
  ltr: 'lex-ltr',
  rtl: 'lex-rtl',
  placeholder: 'lex-placeholder',
  paragraph: 'lex-paragraph',
  quote: 'lex-quote',
  heading: {
    h1: 'lex-heading-h1',
    h2: 'lex-heading-h2',
    h3: 'lex-heading-h3',
    h4: 'lex-heading-h4',
    h5: 'lex-heading-h5',
    h6: 'lex-heading-h6',
  },
  list: {
    nested: {
      listitem: 'lex-nested-listitem',
    },
    ol: 'lex-list-ol',
    ul: 'lex-list-ul',
    listitem: 'lex-listItem',
    listitemChecked: 'lex-listItemChecked',
    listitemUnchecked: 'lex-listItemUnchecked',
  },
  hashtag: 'lex-hashtag',
  image: 'lex-image',
  link: 'lex-link',
  text: {
    bold: 'lex-textBold',
    code: 'lex-textCode',
    italic: 'lex-textItalic',
    strikethrough: 'lex-textStrikethrough',
    subscript: 'lex-textSubscript',
    superscript: 'lex-textSuperscript',
    underline: 'lex-textUnderline',
    underlineStrikethrough: 'lex-textUnderlineStrikethrough',
  },
  code: 'lex-code',
  codeHighlight: {
    atrule: 'lex-tokenAttr',
    attr: 'lex-tokenAttr',
    boolean: 'lex-tokenProperty',
    builtin: 'lex-tokenSelector',
    cdata: 'lex-tokenComment',
    char: 'lex-tokenSelector',
    class: 'lex-tokenFunction',
    'class-name': 'lex-tokenFunction',
    comment: 'lex-tokenComment',
    constant: 'lex-tokenProperty',
    deleted: 'lex-tokenProperty',
    doctype: 'lex-tokenComment',
    entity: 'lex-tokenOperator',
    function: 'lex-tokenFunction',
    important: 'lex-tokenVariable',
    inserted: 'lex-tokenSelector',
    keyword: 'lex-tokenAttr',
    namespace: 'lex-tokenVariable',
    number: 'lex-tokenProperty',
    operator: 'lex-tokenOperator',
    prolog: 'lex-tokenComment',
    property: 'lex-tokenProperty',
    punctuation: 'lex-tokenPunctuation',
    regex: 'lex-tokenVariable',
    selector: 'lex-tokenSelector',
    string: 'lex-tokenSelector',
    symbol: 'lex-tokenProperty',
    tag: 'lex-tokenProperty',
    url: 'lex-tokenOperator',
    variable: 'lex-tokenVariable',
  },
}

export let globalActiveEditor: LexicalEditor

export const debugNodeTree = (node: LexicalNode | null | undefined, level = 0) => {
  while (node) {
    let s = ''
    for (let i = 0; i < level; i++) {
      s = '    ' + s
    }
    console.log(s + node.getType(), node)
    if (node.getFirstChild?.()) {
      debugNodeTree(node.getFirstChild(), level + 1)
    }
    node = node.getNextSibling()
  }
}
