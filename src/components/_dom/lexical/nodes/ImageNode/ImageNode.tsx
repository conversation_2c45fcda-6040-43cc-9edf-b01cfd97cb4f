/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import type {
  DOMConversionMap,
  DOMConversionOutput,
  DOMExportOutput,
  EditorConfig,
  LexicalNode,
  NodeKey,
  SerializedLexicalNode,
  Spread,
} from 'lexical'

import { $applyNodeReplacement, DecoratorNode } from 'lexical'
import * as React from 'react'
import { Suspense } from 'react'

const ImageComponent = React.lazy(
  // @ts-ignore
  () => import('./ImageComponent')
)

export interface ImagePayload {
  altText: string
  //caption?: LexicalEditor;
  aspectRatio?: 'auto' | number
  opt?: boolean
  key?: NodeKey
  //maxWidth?: number;
  //showCaption?: boolean;
  src: string
  width?: number
  //captionsEnabled?: boolean;
}

function convertImageElement(domNode: Node): null | DOMConversionOutput {
  if (domNode instanceof HTMLImageElement) {
    const { alt: altText, src, width, aspectRatio } = domNode as any
    const node = $createImageNode({ altText, aspectRatio, src, width })
    return { node }
  }
  return null
}

export type SerializedImageNode = Spread<
  {
    altText: string
    //caption: SerializedEditor;
    aspectRatio?: 'auto' | number
    opt?: boolean
    //maxWidth: number;
    //showCaption: boolean;
    src: string
    width?: number
    type: 'image'
    version: 1
  },
  SerializedLexicalNode
>

export class ImageNode extends DecoratorNode<JSX.Element> {
  __src: string
  __altText: string
  __width: 'inherit' | number
  __aspectRatio: 'auto' | number
  __opt: boolean
  //__maxWidth: number;
  //__showCaption: boolean;
  //__caption: LexicalEditor;
  // Captions cannot yet be used within editor cells
  //__captionsEnabled: boolean;

  static getType(): string {
    return 'image'
  }

  static clone(node: ImageNode): ImageNode {
    return new ImageNode(
      node.__src,
      node.__altText,
      //node.__maxWidth,
      node.__width,
      node.__aspectRatio,
      node.__opt,
      //node.__showCaption,
      //node.__caption,
      //node.__captionsEnabled,
      node.__key
    )
  }

  static importJSON(serializedNode: SerializedImageNode): ImageNode {
    //const {altText, aspectRatio, width, caption, src, showCaption} =
    const { altText, aspectRatio, opt, width, src } = serializedNode
    const node = $createImageNode({
      altText,
      aspectRatio,
      opt,
      // showCaption,
      src,
      width,
    })
    // const nestedEditor = node.__caption;
    // const editorState = nestedEditor.parseEditorState(caption.editorState);
    // if (!editorState.isEmpty()) {
    //   nestedEditor.setEditorState(editorState);
    // }
    return node
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElement('img')
    element.setAttribute('src', this.__src)
    element.setAttribute('alt', this.__altText)
    element.setAttribute('width', this.__width.toString())
    element.setAttribute('aspectRatio', this.__aspectRatio.toString())
    return { element }
  }

  static importDOM(): DOMConversionMap | null {
    return {
      img: (node: Node) => ({
        conversion: convertImageElement,
        priority: 0,
      }),
    }
  }

  constructor(
    src: string,
    altText: string,
    //maxWidth: number,
    width?: 'inherit' | number,
    aspectRatio?: 'auto' | number,
    opt?: boolean,
    // showCaption?: boolean,
    // caption?: LexicalEditor,
    // captionsEnabled?: boolean,
    key?: NodeKey
  ) {
    super(key)
    this.__src = src
    this.__altText = altText
    //this.__maxWidth = maxWidth;
    this.__width = width || 'inherit'
    this.__aspectRatio = aspectRatio || 'auto'
    this.__opt = opt || false
    // this.__showCaption = showCaption || false;
    // this.__caption = caption || createEditor();
    // this.__captionsEnabled = captionsEnabled || captionsEnabled === undefined;
  }

  exportJSON(): SerializedImageNode {
    return {
      altText: this.getAltText(),
      //caption: this.__caption.toJSON(),
      aspectRatio: this.__aspectRatio,
      opt: this.__opt,
      //maxWidth: this.__maxWidth,
      //maxWidth: this.__width === 'inherit' ? this.__maxWidth : Math.max(this.__width, this.__maxWidth),
      //showCaption: this.__showCaption,
      src: this.getSrc(),
      type: 'image',
      version: 1,
      width: this.__width === 'inherit' ? 0 : this.__width,
    }
  }

  setWidthAndAspectRatio(width: 'inherit' | number, aspectRatio: 'auto' | number): void {
    const writable = this.getWritable()
    writable.__width = width
    writable.__aspectRatio = aspectRatio
  }

  // setShowCaption(showCaption: boolean): void {
  //   const writable = this.getWritable();
  //   writable.__showCaption = showCaption;
  // }

  // View

  createDOM(config: EditorConfig): HTMLElement {
    const span = document.createElement('span')
    const theme = config.theme
    const className = theme.image
    if (className !== undefined) {
      span.className = className
    }
    return span
  }

  updateDOM(): false {
    return false
  }

  getSrc(): string {
    return this.__src
  }

  getAltText(): string {
    return this.__altText
  }

  decorate(): JSX.Element {
    return (
      <Suspense fallback={null}>
        <ImageComponent
          src={this.__src}
          altText={this.__altText}
          width={this.__width}
          aspectRatio={this.__aspectRatio}
          //maxWidth={this.__maxWidth}
          nodeKey={this.getKey()}
          // showCaption={this.__showCaption}
          // caption={this.__caption}
          // captionsEnabled={this.__captionsEnabled}
          resizable={true}
        />
      </Suspense>
    )
  }
}

export function $createImageNode({
  altText,
  aspectRatio,
  opt,
  //maxWidth, // = 500,
  //captionsEnabled,
  src,
  width,
  //showCaption,
  //caption,
  key,
}: ImagePayload): ImageNode {
  return $applyNodeReplacement(
    new ImageNode(
      src,
      altText,
      //maxWidth,
      width,
      aspectRatio,
      opt,
      // showCaption,
      // caption,
      // captionsEnabled,
      key
    )
  )
}

export function $isImageNode(node: LexicalNode | null | undefined): node is ImageNode {
  return node instanceof ImageNode
}
