import { CLICK_COMMAND, COMMAND_PRIORITY_LOW, NodeKey } from 'lexical'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { useLexicalNodeSelection } from '@lexical/react/useLexicalNodeSelection'
import { Suspense, useEffect } from 'react'
import { mergeRegister } from '@lexical/utils'

export default function PageComponent({ nodeKey }: { nodeKey: NodeKey }): JSX.Element | null {
  const [editor] = useLexicalComposerContext()
  const [isSelected, setSelected, clearSelection] = useLexicalNodeSelection(nodeKey)

  /*
    const onDelete = useCallback(event => {
        if (isSelected && $isNodeSelection($getSelection())) {
            event.preventDefault();
            const node = $getNodeByKey(nodeKey);
    
            if ($isPageNode(node)) {
                node.remove();
            }
            //setSelected(false);
        }
        return false;
    }, [isSelected, nodeKey]);
    */

  useEffect(() => {
    return mergeRegister(
      editor.registerCommand(
        CLICK_COMMAND,
        event => {
          const pageElement = editor.getElementByKey(nodeKey)

          if (event.target === pageElement) {
            if (!event.shiftKey) {
              clearSelection()
            }

            setSelected(!isSelected)
            return true
          }

          return false
        },
        COMMAND_PRIORITY_LOW
      )
      // editor.registerCommand(
      //     KEY_DELETE_COMMAND,
      //     (event) => {
      //         const pageElement = editor.getElementByKey(nodeKey);
      //         return false;
      //     },
      //     COMMAND_PRIORITY_LOW,
      // ),
      // editor.registerCommand(
      //     KEY_BACKSPACE_COMMAND,
      //     onDelete,
      //     COMMAND_PRIORITY_LOW,
      // ),
    )
  }, [clearSelection, editor, isSelected, nodeKey, setSelected])

  useEffect(() => {
    const hrElem = editor.getElementByKey(nodeKey)

    if (hrElem !== null) {
      hrElem.className = isSelected ? 'selected' : ''
    }
  }, [editor, isSelected, nodeKey])

  return (
    <Suspense fallback={null}>
      <div style={{ backgroundColor: 'pink', padding: '4px' }}>PageComponent ({editor._editable ? 'E' : 'V'})</div>
    </Suspense>
  )
}
