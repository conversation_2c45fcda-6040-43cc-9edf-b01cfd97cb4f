
.lex-page-control {
    /*display: none;*/
    /*width: 32px;*/
    /*height: 32px;*/
    /*position: absolute;*/
    top: -400px;
    text-align: center;
    font-weight: 400;
    color: #888;
    font-size: 20px;
    border-radius: 18px;
    cursor: pointer;
    /*user-select: none;*/
    /*text-decoration: none;*/

    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0);
    background-color: rgba(255,255,255,0);
    transition: all 0.2s;
}
.lex-page-control.plus {
    line-height: 32px;
    left: calc(50% - 25px);
}
.lex-page-control.options {
    right: 8px;
    font-size: 34px;
    line-height: 14px;
    box-shadow: none;
    background-color: rgba(255,255,255,0);
}