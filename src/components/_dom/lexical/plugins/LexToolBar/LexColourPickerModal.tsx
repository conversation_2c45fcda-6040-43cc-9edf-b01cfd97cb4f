import React, { useEffect, useState } from 'react'
import { View, StyleSheet, TouchableOpacity } from 'react-native'
import { SeaModal } from '@src/components/_atoms/SeaModal/SeaModal'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import { LexicalEditor } from 'lexical'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import SeaLabel from '@src/components/_legacy/SeaLabel/SeaLabel'

export interface LexColourPickerModalProps {
  visible: boolean
  onClose: () => void
  editor: LexicalEditor
  title: string
  setColor: (color: string) => void
  color?: string
}

const lexColorSwatches = [
  '#000000',
  '#2b2b2b',
  '#555555',
  '#808080',
  '#aaaaaa',
  '#d5d5d5',
  '#ffffff',
  '#92d051',
  '#fdf054',
  '#fbbe43',
  '#fb4322',
  '#9bc2e6',
  '#be80fe',
  '#fe80e3',
]

export const LexColourPickerModal = ({
  visible,
  onClose,
  editor,
  title,
  color: colorProp,
  setColor,
}: LexColourPickerModalProps) => {
  const [currentColour, setCurrentColour] = useState('#ffffff')

  useEffect(() => {
    if (colorProp) {
      setCurrentColour(colorProp)
    }
  }, [colorProp])

  return (
    <SeaModal visible={visible} onClose={onClose} title={title} style={{ maxWidth: '60%' }}>
      <SeaStack direction={'column'} align={'start'} justify={'center'} gap={10}>
        <SeaStack direction={'column'} align={'start'} width={'100%'}>
          <SeaLabel>Colour Chosen</SeaLabel>
          <View
            key={currentColour}
            style={[
              styles.colourContainer,
              {
                width: '100%',
                backgroundColor: currentColour,
              },
            ]}
          />
        </SeaStack>

        <SeaStack direction={'column'} align={'start'} width={'100%'}>
          <SeaLabel>Swatches</SeaLabel>
          <SeaStack
            direction={'row'}
            gap={2}
            width={'100%'}
            align={'start'}
            style={{
              flexWrap: 'wrap',
            }}>
            {lexColorSwatches.map(lexColor => (
              <TouchableOpacity key={lexColor} onPress={() => setCurrentColour(lexColor)}>
                <View style={[styles.colourContainer, { backgroundColor: lexColor }]}></View>
              </TouchableOpacity>
            ))}
          </SeaStack>
        </SeaStack>
        <SeaStack direction={'column'} align={'start'} width={'100%'}>
          <SeaLabel>Customise</SeaLabel>
          {/*<HuePicker*/}
          {/*  color={currentColor}*/}
          {/*  onChange={(result: ColorResult) => {*/}
          {/*    setCurrentColor(result.hex);*/}
          {/*  }}*/}
          {/*  height="50px"*/}
          {/*  width="100%"*/}
          {/*/>*/}
          {/*{!isGrayscale(currentColor) && (*/}
          {/*  <>*/}
          {/*    <div style={{ height: "22px" }}></div>*/}
          {/*    <SeaLabel>Saturation</SeaLabel>*/}
          {/*    <div className="swatches-container">*/}
          {/*      {[12.5, 25, 37.5, 50, 62.5, 75, 87.5].map(*/}
          {/*        (saturation: number) => {*/}
          {/*          return (*/}
          {/*            <div*/}
          {/*              key={saturation}*/}
          {/*              className="sea-input sea-colour swatch"*/}
          {/*              style={{*/}
          {/*                backgroundColor: applySaturationToHexColor(*/}
          {/*                  currentColor,*/}
          {/*                  saturation,*/}
          {/*                ),*/}
          {/*              }}*/}
          {/*              onClick={(e) => {*/}
          {/*                setCurrentColor(*/}
          {/*                  applySaturationToHexColor(currentColor, saturation),*/}
          {/*                );*/}
          {/*              }}*/}
          {/*            ></div>*/}
          {/*          );*/}
          {/*        },*/}
          {/*      )}*/}
          {/*    </div>*/}
          {/*    <div style={{ height: "22px" }}></div>*/}
          {/*    <SeaLabel>Brightness</SeaLabel>*/}
          {/*    <div className="swatches-container">*/}
          {/*      {[0.1428, 0.2857, 0.4285, 0.5714, 0.7142, 0.8571, 1].map(*/}
          {/*        (value: number) => {*/}
          {/*          return (*/}
          {/*            <div*/}
          {/*              key={value}*/}
          {/*              className="sea-input sea-colour swatch"*/}
          {/*              style={{*/}
          {/*                backgroundColor: applyValueToHexColor(*/}
          {/*                  currentColor,*/}
          {/*                  value,*/}
          {/*                ),*/}
          {/*              }}*/}
          {/*              onClick={(e) => {*/}
          {/*                setCurrentColor(*/}
          {/*                  applyValueToHexColor(currentColor, value),*/}
          {/*                );*/}
          {/*              }}*/}
          {/*            ></div>*/}
          {/*          );*/}
          {/*        },*/}
          {/*      )}*/}
          {/*    </div>*/}
          {/*  </>*/}
          {/*)}*/}
        </SeaStack>

        <SeaButton
          label={'Choose Colour'}
          onPress={() => {
            setColor(currentColour)
            onClose()
          }}
        />
      </SeaStack>
    </SeaModal>
  )
}

const styles = StyleSheet.create({
  colourContainer: {
    height: 40,
    width: 40,
    backgroundColor: 'white',
    borderColor: '#E2E2E2',
    borderRadius: 8,
    borderWidth: 1,
    // display: "flex",
    alignItems: 'center',
    justifyContent: 'center',
    // flex: 1,
    // flexWrap: "wrap",
  },
})
