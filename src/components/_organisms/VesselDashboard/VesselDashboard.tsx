import { View } from 'react-native'
import React from 'react'

import {
  HealthAndSafetyModule,
  MaintenanceModule,
  SafetyCheckModule,
  VesselRegisterModule,
} from '@src/components/_organisms/VesselDashboard/modules'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaDashboardCard } from './SeaDashboardCard'

export function VesselDashboardModules() {
  const { isDesktopWidth } = useDeviceWidth()
  const { styles } = useStyles(styleSheet)

  return (
    <SeaDashboardCard
      title="Vessel Dashboard"
      style={
        isDesktopWidth
          ? {
              flex: 1,
            }
          : {}
      }>
      <View style={styles.moduleWrapper}>
        <SafetyCheckModule />
      </View>
      <View style={styles.moduleWrapper}>
        <MaintenanceModule />
      </View>
      <View style={styles.moduleWrapper}>
        <VesselRegisterModule />
      </View>
      <View style={styles.moduleWrapper}>
        <HealthAndSafetyModule />
      </View>
    </SeaDashboardCard>
  )
}

const styleSheet = createStyleSheet(theme => ({
  moduleWrapper: {
    borderBottomWidth: 12,
    borderColor: theme.colors.background.primary,
  },
}))
