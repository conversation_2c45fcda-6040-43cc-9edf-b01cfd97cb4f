import { SeaStatusBattery } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBattery'
import { StatusBatteryModule } from '@src/components/_molecules/StatusBatteryModule/StatusBatteryModule'
import { useMaintenanceCount } from '@src/hooks/useMaintenanceCount'
import { usePermission } from '@src/hooks/usePermission'
import { buildStatuses, emptyStatusBattery, isStatusBatteryEmpty, StatusBatteryCount } from '@src/lib/statusBattery'

import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import React, { useMemo } from 'react'

const MaintenanceSchedule = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Maintenance Schedule" statuses={statuses} />
}

const JobList = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Job List" statuses={statuses} />
}

const SpareParts = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Spare Parts" statuses={statuses} />
}

const MaintenanceModulePermissions = {
  maintenanceSchedule: { level: permissionLevels.VIEW },
  jobList: { level: permissionLevels.VIEW },
  sparePartsList: { level: permissionLevels.VIEW },
}

export const MaintenanceModule = () => {
  const { jobListCount, maintenanceScheduleCount, sparePartsCount } = useMaintenanceCount()
  const modulePermissions = usePermission<typeof MaintenanceModulePermissions>({
    modules: MaintenanceModulePermissions,
  })

  return (
    <StatusBatteryModule
      title="Maintenance"
      iconOptions={{
        icon: 'build',
      }}
      subModules={[
        ...(modulePermissions.maintenanceSchedule && !isStatusBatteryEmpty(maintenanceScheduleCount)
          ? [<MaintenanceSchedule key="maintenanceSchedule" count={maintenanceScheduleCount} />]
          : []),
        ...(modulePermissions.jobList && !isStatusBatteryEmpty(jobListCount)
          ? [<JobList key="jobList" count={jobListCount} />]
          : []),
        ...(modulePermissions.sparePartsList && !isStatusBatteryEmpty(sparePartsCount)
          ? [<SpareParts key="sparePartsList" count={sparePartsCount} />]
          : []),
      ]}
    />
  )
}
