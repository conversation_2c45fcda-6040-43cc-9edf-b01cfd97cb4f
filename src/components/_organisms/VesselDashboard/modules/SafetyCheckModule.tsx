import { SeaStatusBattery } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBattery'
import { StatusBatteryModule } from '@src/components/_molecules/StatusBatteryModule/StatusBatteryModule'
import { useSafetyCount } from '@src/hooks/useSafetyCount'
import { buildStatuses, emptyStatusBattery, isStatusBatteryEmpty, StatusBatteryCount } from '@src/lib/statusBattery'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { sharedState } from '@src/shared-state/shared-state'
import React, { useMemo } from 'react'
import { usePermission } from '@src/hooks/usePermission'

const SafetyCheck = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Safety Check" statuses={statuses} />
}

const SafetyEquipmentExpires = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Safety Equipment Expires" statuses={statuses} />
}

const Drill = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Drills" statuses={statuses} />
}

const SafetyCheckModulePermissions = {
  safetyEquipmentChecks: { level: permissionLevels.VIEW },
  safetyEquipmentList: { level: permissionLevels.VIEW },
  drills: { level: permissionLevels.VIEW },
}

export const SafetyCheckModule = () => {
  const vesselId = sharedState.vesselId.use()
  const modulePermissions = usePermission<typeof SafetyCheckModulePermissions>({
    modules: SafetyCheckModulePermissions,
  })

  const { drillCount, safetyCheckCount, safetyEquipmentExpiryCount } = useSafetyCount({ vesselId })

  return (
    <StatusBatteryModule
      title="Safety"
      iconOptions={{
        icon: 'support',
      }}
      subModules={[
        ...(modulePermissions.safetyEquipmentChecks && !isStatusBatteryEmpty(safetyCheckCount)
          ? [<SafetyCheck key="safety-check" count={safetyCheckCount} />]
          : []),
        ...(modulePermissions.safetyEquipmentList && !isStatusBatteryEmpty(safetyEquipmentExpiryCount)
          ? [<SafetyEquipmentExpires key="safety-equipment-expires" count={safetyEquipmentExpiryCount} />]
          : []),
        ...(modulePermissions.drills && !isStatusBatteryEmpty(drillCount)
          ? [<Drill key="drill" count={drillCount} />]
          : []),
      ]}
    />
  )
}
