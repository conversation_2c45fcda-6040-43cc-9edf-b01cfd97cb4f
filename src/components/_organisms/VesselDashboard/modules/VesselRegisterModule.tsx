import { SeaStatusBattery } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBattery'
import { StatusBatteryModule } from '@src/components/_molecules/StatusBatteryModule/StatusBatteryModule'
import { usePermission } from '@src/hooks/usePermission'
import { useVesselRegisterCount } from '@src/hooks/useVesselRegisterCount'
import { buildStatuses, emptyStatusBattery, isStatusBatteryEmpty, StatusBatteryCount } from '@src/lib/statusBattery'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import React, { useMemo } from 'react'

const VesselCertificates = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Vessel Certificates" statuses={statuses} />
}

const VesselDocuments = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Vessel Documents" statuses={statuses} />
}

const SOPs = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Standard Operating Procedures" statuses={statuses} />
}

const VesselRegisterModulePermissions = {
  vesselCertificates: { level: permissionLevels.VIEW },
  vesselDocuments: { level: permissionLevels.VIEW },
  standardOperatingProcedures: { level: permissionLevels.VIEW },
}

export const VesselRegisterModule = () => {
  const { SOPCount, vesselCertificatesCount, vesselDocumentsCount } = useVesselRegisterCount()

  const modulePermissions = usePermission<typeof VesselRegisterModulePermissions>({
    modules: VesselRegisterModulePermissions,
  })

  return (
    <StatusBatteryModule
      title="Vessel Register"
      iconOptions={{
        icon: 'description',
      }}
      subModules={[
        ...(modulePermissions.vesselCertificates && !isStatusBatteryEmpty(vesselCertificatesCount)
          ? [<VesselCertificates key="vesselCertificates" count={vesselCertificatesCount} />]
          : []),
        ...(modulePermissions.vesselDocuments && !isStatusBatteryEmpty(vesselDocumentsCount)
          ? [<VesselDocuments key="vesselDocuments" count={vesselDocumentsCount} />]
          : []),
        ...(modulePermissions.standardOperatingProcedures && !isStatusBatteryEmpty(SOPCount)
          ? [<SOPs key="standardOperatingProcedures" count={SOPCount} />]
          : []),
      ]}
    />
  )
}
