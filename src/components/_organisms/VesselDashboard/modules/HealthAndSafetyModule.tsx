import { SeaStatusBattery } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBattery'
import { StatusBatteryModule } from '@src/components/_molecules/StatusBatteryModule/StatusBatteryModule'
import { useHealthAndSafetyCount } from '@src/hooks/useHealthAndSafetyCount'
import { usePermission } from '@src/hooks/usePermission'
import { buildStatuses, emptyStatusBattery, isStatusBatteryEmpty, StatusBatteryCount } from '@src/lib/statusBattery'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { sharedState } from '@src/shared-state/shared-state'
import React, { useMemo } from 'react'

const IncidentOrEvents = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )
  return <SeaStatusBattery label="Incident / Events" statuses={statuses} />
}

const RiskAssessment = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Risk Assessments" statuses={statuses} />
}

const DangerousGoods = ({ count }: { count?: StatusBatteryCount }) => {
  const statuses = useMemo(
    () =>
      buildStatuses(count ?? emptyStatusBattery, {
        Critical: {
          onPress: () => alert('Critical'),
        },
        Error: { onPress: () => alert('Error') },
        Warning: { onPress: () => alert('Warning') },
        Attention: { onPress: () => alert('Attention') },
        Minor: { onPress: () => alert('Minor') },
        Ok: { onPress: () => alert('Ok') },
      }),
    [count]
  )

  return <SeaStatusBattery label="Dangerous Goods" statuses={statuses} />
}

const HealthAndSafetyPermissions = {
  dangerousGoodsRegister: { level: permissionLevels.VIEW },
  hasIncidents: { requireLicensee: true },
  hazardRegister: { level: permissionLevels.VIEW },
}

export const HealthAndSafetyModule = () => {
  const vesselId = sharedState.vesselId.use()

  const { dangerousGoodsCount, incidentCount, riskAssessmentCount } = useHealthAndSafetyCount({ vesselId })

  const healthAndSafetyPermissions = usePermission<typeof HealthAndSafetyPermissions>({
    modules: HealthAndSafetyPermissions,
  })

  return (
    <StatusBatteryModule
      title="Health & Safety"
      iconOptions={{
        icon: 'health_and_safety',
      }}
      subModules={[
        ...(healthAndSafetyPermissions.hasIncidents
          ? [<IncidentOrEvents key="incidentOrEvents" count={incidentCount} />]
          : []),
        ...(healthAndSafetyPermissions.hazardRegister
          ? [<RiskAssessment key="riskAssessment" count={riskAssessmentCount} />]
          : []),
        ...(healthAndSafetyPermissions.dangerousGoodsRegister
          ? [<DangerousGoods key="dangerousGoods" count={dangerousGoodsCount} />]
          : []),
      ]}
    />
  )
}
