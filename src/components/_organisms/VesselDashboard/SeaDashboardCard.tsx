import { View, Text, ViewStyle, StyleSheet } from 'react-native'
import React from 'react'
import { SeaCard, SeaCardBody, SeaCardContent, SeaCardHeader } from '@src/components/_atoms/SeaCard/SeaCard'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'

interface SeaDashboardCardProps {
  title: string
  children: React.ReactNode
  style?: ViewStyle
  contentStyle?: ViewStyle
  onPress?: () => void
}
export function SeaDashboardCard({ title, children, style, contentStyle, onPress }: SeaDashboardCardProps) {
  const { styles } = useStyles(styleSheet)
  return (
    <SeaCard style={style}>
      <SeaCardHeader style={styles.cardHeader}>
        <SeaStack align="center" justify="between" direction="row" gap={8}>
          <SeaTypography variant="body" fontWeight="semiBold">
            {title}
          </SeaTypography>
          {onPress && (
            <SeaButton
              variant={SeaButtonVariant.Link}
              iconOptions={{
                icon: 'north_east',
                size: 20,
                fill: false,
              }}
              viewStyle={{
                height: 'auto',
              }}
              onPress={onPress}
            />
          )}
        </SeaStack>
      </SeaCardHeader>

      <SeaCardBody style={StyleSheet.flatten([styles.cardBody, contentStyle])}>
        <SeaCardContent>{children}</SeaCardContent>
      </SeaCardBody>
    </SeaCard>
  )
}

const styleSheet = createStyleSheet(theme => ({
  cardHeader: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  cardBody: {
    borderRadius: 12,
    borderTopWidth: 1,
    borderColor: theme.colors.borderColor,
  },
}))
