import { View, Text, ScrollView } from 'react-native'
import React, { useMemo } from 'react'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaMetricCard } from '@src/components/_atoms/SeaMetricCard/SeaMetricCard'
import { formatDateSimplify } from '@src/lib/datesAndTime'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { useStyles } from '@src/theme/styles'
import { useSafetyCount } from '@src/hooks/useSafetyCount'
import { useMaintenanceCount } from '@src/hooks/useMaintenanceCount'
import { useVesselRegisterCount } from '@src/hooks/useVesselRegisterCount'
import { useHealthAndSafetyCount } from '@src/hooks/useHealthAndSafetyCount'
import { sharedState } from '@src/shared-state/shared-state'
import { useRouter } from 'expo-router'
import { DateTime } from 'luxon'
import { useDeviceWidth } from '@src/hooks/useDevice'

export function Metrics() {
  const vessel = sharedState.vessel.use()
  const engines = sharedState.engines.use()

  const router = useRouter()
  const { theme } = useStyles()
  const { isLargeDesktopWidth } = useDeviceWidth()

  const { drillCount, safetyCheckCount, safetyEquipmentExpiryCount } = useSafetyCount({ vesselId: vessel?.id })
  const { jobListCount, maintenanceScheduleCount, sparePartsCount } = useMaintenanceCount()
  const { SOPCount, vesselCertificatesCount, vesselDocumentsCount } = useVesselRegisterCount()
  const { dangerousGoodsCount, incidentCount, riskAssessmentCount } = useHealthAndSafetyCount({ vesselId: vessel?.id })

  const vesselStatusPercent = useMemo(() => {
    const countObjects = [
      drillCount,
      safetyCheckCount,
      safetyEquipmentExpiryCount,
      jobListCount,
      maintenanceScheduleCount,
      sparePartsCount,
      SOPCount,
      vesselCertificatesCount,
      vesselDocumentsCount,
      dangerousGoodsCount,
      incidentCount,
      riskAssessmentCount,
    ]

    const totalTasks = countObjects.reduce((sum, countObj) => {
      return (
        sum +
        Object.values(countObj).reduce((objSum, val) => {
          const number = Number(val)
          return objSum + (isNaN(number) ? 0 : number)
        }, 0)
      )
    }, 0)

    const completedTasks = countObjects.reduce((sum, countObj) => {
      return sum + countObj.Ok
    }, 0)

    const percent = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0

    return `${percent.toFixed(1)}%`
  }, [
    drillCount,
    safetyCheckCount,
    safetyEquipmentExpiryCount,
    jobListCount,
    maintenanceScheduleCount,
    sparePartsCount,
    SOPCount,
    vesselCertificatesCount,
    vesselDocumentsCount,
    dangerousGoodsCount,
    incidentCount,
    riskAssessmentCount,
  ])

  const engineLastUpdated = useMemo(() => {
    if (!engines?.whenLastUpdated) return ''

    const lastUpdated = DateTime.fromMillis(engines.whenLastUpdated)
    const now = DateTime.now()
    const daysDiff = Math.floor(now.diff(lastUpdated, 'days').days)

    if (daysDiff === 0) {
      return '(Updated today)'
    }
    return `(Updated ${daysDiff} days ago)`
  }, [engines])

  return (
    <ScrollView
      style={{ width: '100%', flex: 1 }}
      contentContainerStyle={{
        gap: 20,
        ...(isLargeDesktopWidth ? { justifyContent: 'space-around', flex: 1 } : {}),
      }}
      horizontal={true}
      showsHorizontalScrollIndicator={false}
      scrollEnabled>
      <SeaMetricCard
        title="Vessel Status"
        metricValue={vesselStatusPercent}
        iconOptions={{
          icon: 'planner_review',
          size: 24,
        }}
        onPress={() =>
          router.navigate({
            pathname: getRoutePath(Routes.VESSEL_STATUS),
            params: {
              vesselId: vessel?.id,
            },
          })
        }
      />

      {!vessel?.isShoreFacility && (
        <SeaMetricCard
          title="Engine / Equipment Hours"
          metricValue={engines?.whenLastUpdated ? formatDateSimplify(engines.whenLastUpdated) : 'N/A'}
          additionalInfo={engineLastUpdated}
          iconOptions={{
            icon: 'timer',
            size: 24,
            fill: false,
          }}
          onPress={() =>
            router.navigate({
              pathname: getRoutePath(Routes.ENGINE_HOURS),
              params: {
                vesselId: vessel?.id,
              },
            })
          }
        />
      )}

      <SeaMetricCard
        title="Fuel"
        metricValue="625L"
        additionalInfo="6 July"
        iconOptions={{
          icon: 'local_gas_station',
          size: 24,
          fill: false,
        }}
        onPress={() =>
          router.navigate({
            pathname: getRoutePath(Routes.FUEL),
            params: {
              vesselId: vessel?.id,
            },
          })
        }
      />

      <SeaMetricCard
        title="Survey Status"
        metricValue="18 June"
        additionalInfo="251 days"
        additionalInfoColor={theme.colors.status.okPrimary}
        iconOptions={{
          icon: 'check',
          size: 24,
          fill: false,
          color: theme.colors.status.okPrimary,
        }}
        onPress={() =>
          router.navigate({
            pathname: getRoutePath(Routes.SURVEY_STATUS),
            params: {
              vesselId: vessel?.id,
            },
          })
        }
      />
    </ScrollView>
  )
}
