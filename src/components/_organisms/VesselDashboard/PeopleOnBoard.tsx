import React from 'react'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaDashboardCard } from './SeaDashboardCard'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaAvatar } from '@src/components/_atoms/SeaAvatar/SeaAvatar'
import { SeaStatusType } from '@src/types/Common'

export default function PeopleOnBoard() {
  const people = [
    { id: 1, status: SeaStatusType.Ok },
    { id: 2, status: SeaStatusType.Error },
    { id: 3, status: SeaStatusType.Ok },
    { id: 4, status: SeaStatusType.Error },
    { id: 5, status: SeaStatusType.Attention },
    { id: 6, status: SeaStatusType.Error },
    { id: 7, status: SeaStatusType.Ok },
    { id: 8, status: SeaStatusType.Error },
    { id: 9, status: SeaStatusType.Ok },
    { id: 10, status: SeaStatusType.Attention },
    { id: 11, status: SeaStatusType.Ok },
    { id: 12, status: SeaStatusType.Ok },
    { id: 13, status: SeaStatusType.Ok },
    { id: 14, status: SeaStatusType.Ok },
    { id: 15, status: SeaStatusType.Ok },
    { id: 16, status: SeaStatusType.Ok },
    { id: 17, status: SeaStatusType.Ok },
    { id: 18, status: SeaStatusType.Ok },
    { id: 19, status: SeaStatusType.Ok },
    { id: 20, status: SeaStatusType.Ok },
    { id: 21, status: SeaStatusType.Ok },
    { id: 22, status: SeaStatusType.Ok },
    { id: 23, status: SeaStatusType.Ok },
    { id: 24, status: SeaStatusType.Ok },
  ]

  const visiblePeople = people.slice(0, 9)
  const remainingCount = people.length - visiblePeople.length

  return (
    <SeaDashboardCard title="People on Board (24)" style={{ flex: 1 }}>
      <SeaStack justify="between" direction="row" gap={8}>
        <SeaTypography variant="body" fontWeight="semiBold">
          Crew (9)
        </SeaTypography>
        <SeaTypography variant="body" fontWeight="semiBold">
          Passengers (15)
        </SeaTypography>
      </SeaStack>

      <SeaStack
        align="center"
        justify="start"
        direction="row"
        gap={22}
        style={{
          flexWrap: 'wrap',
          marginTop: 20,
        }}>
        {visiblePeople.map(person => (
          <SeaAvatar key={person.id} size={45} status={person.status} />
        ))}

        {remainingCount > 0 && <SeaAvatar size={45} label={`+${remainingCount}`} />}
      </SeaStack>
    </SeaDashboardCard>
  )
}
