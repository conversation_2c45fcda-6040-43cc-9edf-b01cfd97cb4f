import React, { useMemo } from 'react'
import { SeaDashboardCard } from './SeaDashboardCard'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'
import { ActionLog } from '@src/components/_molecules/ActionLog/ActionLog'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { useActionLog } from '@src/shared-state/General/useActionLog'
import { maxOfflineActionLogDays } from '@src/shared-state/General/actionLog'

export function ActionLogs() {
  const router = useRouter()
  const vesselId = sharedState.vesselId.use()

  const actionLog =
    useActionLog(
      vesselId ? true : false, // This should only be true if component is visible
      maxOfflineActionLogDays,
      vesselId
    ) ?? []

  const { isDesktopWidth } = useDeviceWidth()
  const maxItemsToShow = useMemo(() => (isDesktopWidth ? 10 : 4), [isDesktopWidth])

  const actionLogData = useMemo(() => {
    return actionLog.slice(0, maxItemsToShow)
  }, [actionLog, vesselId, maxItemsToShow])

  return (
    <SeaDashboardCard
      title="Action Logs"
      onPress={() =>
        router.navigate({
          pathname: getRoutePath(Routes.ACTION_LOG),
          params: {
            vesselId: vesselId,
          },
        })
      }>
      {actionLogData.length > 0 ? (
        actionLogData?.map(log => <ActionLog key={log.id} log={log} />)
      ) : (
        <SeaTypography variant="body">No recent activity</SeaTypography>
      )}
    </SeaDashboardCard>
  )
}
