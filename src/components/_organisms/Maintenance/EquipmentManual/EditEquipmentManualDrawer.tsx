import { View, Text } from 'react-native'
import React, { useCallback, useMemo, useState } from 'react'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { EquipmentManualDocument } from '@src/shared-state/VesselDocuments/equipmentManualDocuments'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { useFormik } from 'formik'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions, SimpleSelectionData } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { getFileNameWithExtension } from '@src/lib/files'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import Yup from '@src/lib/yup'
import { preventMultiTap } from '@src/lib/util'
import {
  UpdateEquipmentManualsDto,
  UpdateEquipmentManualsUseCase,
} from '@src/domain/use-cases/maintenance/UpdateEquipmentManualsUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import {
  CreateEquipmentManualsDto,
  CreateEquipmentManualsUseCase,
} from '@src/domain/use-cases/maintenance/CreateEquipmentManualsUseCase'
import { SeaFile } from '@src/lib/fileImports'

const validationSchema = Yup.object({
  title: Yup.string().max(500).required(),
})

export interface EditEquipmentManualDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: EquipmentManualDocument
  type?: 'new' | 'edit'
}

export function EditEquipmentManualDrawer({
  selectedItem,
  visible,
  onClose,
  type = 'edit',
  style,
}: EditEquipmentManualDrawerProps) {
  const equipment = sharedState.equipment.use()
  const userId = sharedState.userId.use(visible ? 1 : 500)
  const licenseeId = sharedState.licenseeId.use(visible ? 1 : 500)
  const vesselId = sharedState.vesselId.use(visible ? 1 : 500)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return {
      title: selectedItem?.title ?? '',
      equipment: selectedItem?.equipmentIds ?? [],
    }
  }, [selectedItem])

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  const handleSubmit = useCallback(
    (values: any) => {
      if (preventMultiTap('equipmentManual')) {
        return
      }

      if (!vesselId || !userId || !licenseeId) {
        console.error('Vessel ID, user ID or licensee ID is not defined')
        return
      }

      const commonDto = {
        title: values.title,
        files: files,
        vesselId,
      }

      const equipmentIdsToRemove: string[] = []
      const equipmentIdsToAdd: string[] = []

      equipment?.all.forEach(item => {
        const itemHasManual = item.equipmentDocumentIds?.includes(selectedItem?.id ?? '')

        if (values.equipment && !values.equipment.includes(item.id)) {
          if (!itemHasManual) {
            equipmentIdsToAdd.push(item.id)
          }
        } else {
          if (itemHasManual && type === 'edit') {
            equipmentIdsToRemove.push(item.id)
          }
        }
      })

      if (type === 'new') {
        const equipmentManualsDto: CreateEquipmentManualsDto = {
          ...commonDto,
          equipmentIdsToAdd,
        }

        const createEquipmentManuals = services.get(CreateEquipmentManualsUseCase)

        createEquipmentManuals
          .execute(equipmentManualsDto as CreateEquipmentManualsDto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Maintenance History\n ${err.message}`))
      } else {
        const equipmentManualsDto: UpdateEquipmentManualsDto = {
          ...commonDto,
          equipmentIdsToAdd,
          equipmentIdsToRemove,
          id: selectedItem?.id ?? '',
        }
        const updateEquipmentManuals = services.get(UpdateEquipmentManualsUseCase)

        updateEquipmentManuals
          .execute(equipmentManualsDto as UpdateEquipmentManualsDto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Maintenance History\n ${err.message}`))
      }
    },
    [selectedItem, equipment, vesselId, userId, licenseeId, type, files]
  )

  const equipmentOptions = useMemo(() => {
    if (equipment) {
      const options: SimpleSelectionData = []
      equipment.all.forEach(item => {
        options.push({
          value: item.id,
          label: item.equipment,
        })
      })
      return options
    }
    return []
  }, [equipment])

  const handleEquipmentSelect = useCallback(
    async (action: CheckBoxActions, changedValue: string) => {
      switch (action) {
        case CheckBoxActions.SELECT: {
          const newIds = [...formik.values.equipment]
          newIds.push(changedValue)
          formik.setFieldValue('equipment', newIds)
          return
        }
        case CheckBoxActions.DESELECT: {
          const newIds = formik.values.equipment.filter((id: string) => id !== changedValue)
          formik.setFieldValue('equipment', newIds)
          return
        }

        default:
          return
      }
    },
    [formik.values.equipment]
  )

  return (
    <SeaDrawer
      title={type === 'new' ? 'Add New Equipment Manual Document' : `Edit - ${selectedItem?.title}`}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        <SeaButton
          label={'Save Equipment Manual'}
          variant={SeaButtonVariant.Primary}
          key="Save Equipment Manual"
          onPress={formik.handleSubmit}
        />
      }>
      <SeaStack direction="column" gap={10} justify="start" align="start">
        <SeaTextInput
          label={'Document Title'}
          value={formik.values.title}
          onChangeText={formik.handleChange('title')}
          hasError={Boolean(formik.errors.title)}
          errorText={formik.errors.title}
        />

        <SeaSelectInput
          label="Equipment"
          data={equipmentOptions}
          isMulti
          style={{
            width: '100%',
          }}
          onItemSelect={handleEquipmentSelect}
          selectedItemValues={formik.values.equipment}
        />
        <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
      </SeaStack>
    </SeaDrawer>
  )
}
