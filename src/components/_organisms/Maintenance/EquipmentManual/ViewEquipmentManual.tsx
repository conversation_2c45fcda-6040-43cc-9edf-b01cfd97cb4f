import { ScrollView } from 'react-native'
import React, { useMemo, useState } from 'react'
import { useGlobalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { getFileNameWithExtension } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { EditEquipmentManualDrawer } from './EditEquipmentManualDrawer'
import { ViewEquipmentSectionCompact } from '@src/components/_organisms/Equipment/ViewEquipmentSectionCompact'

export function ViewEquipmentManuals() {
  const { manualId } = useGlobalSearchParams()
  const equipmentManualDocuments = sharedState.equipmentManualDocuments.use()

  const { styles } = useStyles(styleSheet)
  const { isLargeDesktopWidth } = useDeviceWidth()

  const [showEditDrawer, setShowEditDrawer] = useState(false)

  const selectedItem = useMemo(() => {
    if (!manualId || !equipmentManualDocuments) {
      return undefined
    }

    return equipmentManualDocuments.byId[Array.isArray(manualId) ? manualId[0] : manualId]
  }, [manualId, equipmentManualDocuments])

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return []

    return selectedItem?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('TODO')} />],
    })) as MediaCardFile[]
  }, [selectedItem])

  return (
    <RequirePermissions role="maintenanceHistory" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container}>
        <SeaPageCard
          secondaryActionButton={[
            <SeaEditButton key={'Edit'} onPress={() => setShowEditDrawer(true)} />,
            <SeaDeleteButton key={'Delete'} onPress={() => alert('TODO: Delete Completed Schedule')} />,
          ]}
          titleComponent={<SeaPageCardTitle title={selectedItem?.title} />}>
          <SeaPageCardContentSection>
            <SeaStack direction={'row'} gap={20} align={'start'} style={{ flex: 1 }}>
              <SeaStack direction="column" align={'start'} width={isLargeDesktopWidth ? '70%' : '100%'}>
                <ViewEquipmentSectionCompact equipmentIds={selectedItem?.equipmentIds} />
              </SeaStack>
              {uploadedFiles.length > 0 && <SeaMedia type="manuals" title="Manuals" files={uploadedFiles} />}
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>
      </ScrollView>
      {showEditDrawer && selectedItem && (
        <EditEquipmentManualDrawer
          visible={showEditDrawer}
          onClose={() => setShowEditDrawer(false)}
          selectedItem={selectedItem}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  content: {
    marginBottom: 20,
  },
}))
