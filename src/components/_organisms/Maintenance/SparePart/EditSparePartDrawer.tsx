import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { FormikValues, useFormik } from 'formik'
import { sharedState } from '@src/shared-state/shared-state'
import { toFloat } from '@src/lib/util'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'

import { renderCategoryName } from '@src/lib/categories'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { UpdateSparePartDto, UpdateSparePartUseCase } from '@src/domain/use-cases/maintenance/UpdateSparePartUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { CreateSparePartDto, CreateSparePartUseCase } from '@src/domain/use-cases/maintenance/CreateSparePartUseCase'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { SeaEquipment } from '../../SeaEquipment/SeaEquipment'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'

import { SeaFile } from '@src/lib/fileImports'
import Yup from '@src/lib/yup'

const validationSchema = Yup.object({
  item: Yup.string().max(500).required(),
  partNumber: Yup.string().max(200).required(),
  description: Yup.string().max(5000),
  manufacturer: Yup.string().max(200),
  supplier: Yup.string().max(200),
  cost: Yup.number().min(0),
  location: Yup.string().max(200),
  minStock: Yup.number().min(0),
  currentStock: Yup.number().min(0),
  categoryId: Yup.string(),
})

export interface EditSparePartDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  sparePartId?: string
  type: 'create' | 'edit'
}

export function EditSparePartDrawer({ sparePartId, visible, onClose, style, type }: EditSparePartDrawerProps) {
  const spareParts = sharedState.spareParts.use(visible)
  const vesselSystems = sharedState.vesselSystems.use(visible)
  const contacts = sharedState.contacts.use(visible)
  const userId = sharedState.userId.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const equipment = sharedState.equipment.use(visible)
  const vesselLocations = sharedState.vesselLocations.use(visible)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const selectedSparePart = useMemo(() => {
    return spareParts?.byId[sparePartId!]
  }, [sparePartId, spareParts])

  const initialValues = useMemo(() => {
    return {
      item: selectedSparePart?.item ?? '',
      locationDescription: selectedSparePart?.locationDescription ?? '',
      quantity: toFloat(selectedSparePart?.quantity) as number,
      minQuantity:
        selectedSparePart?.minQuantity && !isNaN(selectedSparePart?.minQuantity)
          ? (toFloat(selectedSparePart?.minQuantity) as number)
          : undefined,
      manufacturer: selectedSparePart?.manufacturer ?? '',
      model: selectedSparePart?.model ?? '',
      partNum: selectedSparePart?.partNum ?? '',
      unitPrice: selectedSparePart?.unitPrice ?? 0,
      notes: selectedSparePart?.notes ?? '',
      systemId: selectedSparePart?.systemId ?? '',
      equipmentIds: selectedSparePart?.equipmentIds ?? [],
      locationId: selectedSparePart?.locationId ?? '',
      contactIds: selectedSparePart?.contactIds ?? [],
    }
  }, [selectedSparePart])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId) {
        console.error('Vessel ID, Licensee ID, or User ID is not available')
        return
      }

      const commonDto = {
        vesselId,
        item: values.item,
        systemId: values.systemId,
        equipmentIds: values.equipmentIds ?? undefined,
        locationId: values.locationId ?? undefined,
        locationDescription: values.locationDescription ?? undefined,
        quantity: toFloat(values.quantity, 0),
        minQuantity: toFloat(values.minQuantity, 0),
        manufacturer: values.manufacturer ?? undefined,
        model: values.model ?? undefined,
        partNum: values.partNum ?? undefined,
        unitPrice: values.unitPrice ?? undefined,
        notes: values.notes ?? undefined,
        files: files,
        contactIds: values.contactIds ?? [],
      }

      if (type === 'create') {
        const dto: CreateSparePartDto = {
          ...commonDto,
        }

        const createSparePart = services.get(CreateSparePartUseCase)

        createSparePart.execute(dto, userId, licenseeId).then(() => {
          onClose()
        })
      } else {
        const dto: UpdateSparePartDto = {
          ...commonDto,
          id: sparePartId ?? '',
        }

        const updateSparePart = services.get(UpdateSparePartUseCase)

        updateSparePart.execute(dto, userId, licenseeId).then(() => {
          onClose()
        })
      }
    },
    [sparePartId, spareParts, vesselId, licenseeId, userId, type, files]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  const categoryOptions = useMemo(() => {
    if (!vesselSystems?.ids) return []

    const options = vesselSystems.ids
      .filter(id => vesselSystems.byId[id].state === 'active')
      .map(id => ({
        label: renderCategoryName(id, vesselSystems),
        value: id,
      }))

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...options,
    ]
  }, [vesselSystems])

  const contactOptions = useMemo(() => {
    if (!contacts?.all) return []
    return contacts?.all?.map(contact => {
      return {
        value: contact.id,
        label: contact.company ? `${contact.company}, ${contact.name}` : contact.name,
      }
    })
  }, [contacts])

  const equipmentList = useMemo(() => {
    if (!equipment?.all) {
      return []
    }

    // Return empty array if systemId is invalid
    if (formik.values?.systemId && !equipment.bySystemId[formik.values.systemId]) {
      return []
    }

    const list: Equipment[] = formik.values?.systemId
      ? equipment.bySystemId[formik.values.systemId] || []
      : equipment.all

    return list.map(item => ({
      label: `${item.equipment}${item.state === 'deleted' ? ' (Deleted)' : ''}`,
      value: item.id,
    }))
  }, [equipment, formik.values])

  const locationOptions = useMemo(() => {
    if (!vesselLocations?.ids) return []

    const options = vesselLocations.ids
      .filter(id => vesselLocations.byId[id].state === 'active')
      .map(id => ({
        label: renderCategoryName(id, vesselLocations),
        value: id,
      }))

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...options,
    ]
  }, [vesselLocations])

  return (
    <SeaDrawer
      title={type === 'create' ? 'Add New Spare Part' : 'Edit Spare Part'}
      visible={visible}
      onClose={onClose}
      style={style}
      level={2}
      primaryAction={
        <SeaButton
          variant={SeaButtonVariant.Primary}
          onPress={formik.handleSubmit}
          label={type === 'create' ? 'Save New Part' : 'Update Part'}
        />
      }>
      <SeaStack direction={'column'} align="start" gap={20}>
        <SeaStack direction={'column'} gap={20} style={{ width: '100%' }}>
          <SeaTextInput
            label={'Item'}
            value={formik.values.item}
            onChangeText={formik.handleChange('item')}
            hasError={Boolean(formik.errors.item)}
            errorText={formik.errors.item}
          />
        </SeaStack>
        <SeaStack direction="row" gap={8} justify="start" align="start" width={'100%'}>
          <SeaDropdown
            label={'System'}
            items={categoryOptions}
            onSelect={value => formik.setFieldValue('systemId', value)}
            value={formik.values.systemId}
            style={{ flex: 1 }}
          />
          <SeaSelectInput
            label={'Select Equipment'}
            isMulti={true}
            showSelectAllOption={false}
            data={equipmentList}
            selectedItemValues={formik.values.equipmentIds}
            style={{ width: '100%' }}
            disabled={!formik.values.systemId}
            onItemSelect={useCallback((action: CheckBoxActions, changedValue: string) => {
              switch (action) {
                case CheckBoxActions.SELECT: {
                  const newIds = [...formik.values.equipmentIds]

                  newIds.push(changedValue)

                  formik.setFieldValue('equipmentIds', newIds)
                  return
                }
                case CheckBoxActions.DESELECT: {
                  const newIds = formik.values.equipmentIds.filter(id => id !== changedValue)

                  formik.setFieldValue('equipmentIds', newIds)

                  return
                }
                default:
                  return
              }
            }, [])}
          />
        </SeaStack>

        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaDropdown
            label={'Location'}
            items={locationOptions}
            onSelect={value => formik.setFieldValue('locationId', value)}
            value={formik.values.locationId}
            style={{ flex: 1, width: '100%' }}
          />
        </SeaStack>
        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaTextInput
            label={'Location Description'}
            value={formik.values.locationDescription}
            onChangeText={formik.handleChange('locationDescription')}
          />
        </SeaStack>

        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaTextInput
            label={'Quantity'}
            value={formik.values.quantity.toString()}
            inputMode="numeric"
            keyboardType="numeric"
            onChangeText={formik.handleChange('quantity')}
            style={{ flex: 1 }}
          />

          <SeaTextInput
            label={'Minimum Quantity'}
            value={formik.values.minQuantity?.toString() ?? ''}
            inputMode="numeric"
            keyboardType="numeric"
            onChangeText={formik.handleChange('minQuantity')}
            style={{ flex: 1 }}
          />

          <SeaTextInput
            label={'Manufacturer'}
            value={formik.values.manufacturer}
            onChangeText={formik.handleChange('manufacturer')}
            style={{ flex: 1 }}
          />
        </SeaStack>

        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaTextInput
            label={'Model'}
            value={formik.values.model}
            onChangeText={formik.handleChange('model')}
            style={{ flex: 1 }}
          />

          <SeaTextInput
            label={'Part Number'}
            value={formik.values.partNum}
            onChangeText={formik.handleChange('partNum')}
            style={{ flex: 1 }}
          />
        </SeaStack>

        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaTextInput
            label={'Unit Price'}
            value={formik.values.unitPrice.toString()}
            onChangeText={formik.handleChange('unitPrice')}
            inputMode={'decimal'}
            keyboardType={'decimal-pad'}
            placeholder={'0.00'}
            prefixIcon={<SeaIcon icon={'attach_money'} size={16} />}
            style={{ flex: 1 }}
          />
        </SeaStack>

        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaTextInput
            label={'Notes'}
            value={formik.values.notes}
            multiLine={true}
            onChangeText={formik.handleChange('notes')}
          />
        </SeaStack>

        <SeaFileUploader initialFiles={selectedSparePart?.files} files={files} setFiles={setFiles} />

        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaSelectInput
            label={'Connect Contact'}
            isMulti={true}
            showSelectAllOption={false}
            data={contactOptions}
            selectedItemValues={formik.values.contactIds}
            style={{ width: '100%' }}
            onItemSelect={useCallback((action: CheckBoxActions, changedValue: string) => {
              switch (action) {
                case CheckBoxActions.SELECT: {
                  const newIds = [...formik.values.contactIds]

                  newIds.push(changedValue)

                  formik.setFieldValue('contactIds', newIds)
                  return
                }
                case CheckBoxActions.DESELECT: {
                  const newIds = formik.values.contactIds.filter(id => id !== changedValue)

                  formik.setFieldValue('contactIds', newIds)

                  return
                }
                default:
                  return
              }
            }, [])}
          />
        </SeaStack>
      </SeaStack>
    </SeaDrawer>
  )
}
