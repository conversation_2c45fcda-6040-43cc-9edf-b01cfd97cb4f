import { View, Text } from 'react-native'
import React, { useMemo, useState } from 'react'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { sharedState } from '@src/shared-state/shared-state'
import { useGlobalSearchParams } from 'expo-router'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatValue } from '@src/lib/util'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { getFileNameWithExtension } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { renderCategoryName } from '@src/lib/categories'
import { EditSparePartDrawer } from './EditSparePartDrawer'
import { ScrollView } from 'react-native-gesture-handler'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { ContactMediaCards } from '@src/components/_organisms/MediaCards/ContactMediaCards'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'
import { ViewEquipmentSection } from '@src/components/_organisms/Equipment/ViewEquipmentSection'
import { ViewEquipmentSectionCompact } from '@src/components/_organisms/Equipment/ViewEquipmentSectionCompact'
import { SeaSectionSubtitle } from '@src/components/_molecules/SeaSectionSubtitle/SeaSectionSubtitle'

const DESKTOP_ITEMS_WIDTH = '100%'

export function ViewSparePart() {
  const vesselSystems = sharedState.vesselSystems.use()
  const vesselLocations = sharedState.vesselLocations.use()
  const equipment = sharedState.equipment.use()
  const spareParts = sharedState.spareParts.use()

  const { sparePartId } = useGlobalSearchParams()
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)

  const selectedSparePart = useMemo(() => {
    return spareParts?.byId[sparePartId as string]
  }, [sparePartId, spareParts])

  const equipmentList = useMemo(() => {
    if (!selectedSparePart?.equipmentIds)
      return {
        equipment: '',
        criticalEquipment: '',
      }

    const availableEquipment = selectedSparePart.equipmentIds.reduce((acc, id: string) => {
      const item = equipment?.byId[id]
      if (item) {
        if (item.state === 'deleted') {
          item.equipment += ' (deleted)'
        }

        acc.push(item)
      }

      return acc
    }, [] as Equipment[])

    const criticalEquipment = availableEquipment
      .filter(item => item.isCritical)
      .map(item => item.equipment)
      .join(', ')

    const equipmentNames = availableEquipment.map(item => item.equipment).join(', ')

    return {
      equipment: equipmentNames,
      criticalEquipment: criticalEquipment.length > 0 ? criticalEquipment : '-',
    }
  }, [selectedSparePart, equipment])

  const uploadedFiles = useMemo(() => {
    if (!selectedSparePart?.files) return []

    return selectedSparePart?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('TODO')} />],
    })) as MediaCardFile[]
  }, [selectedSparePart])

  return (
    <RequirePermissions role="sparePartsList" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView key={`incident-${selectedSparePart?.id}`} style={{ flex: 1 }}>
        <SeaPageCard
          secondaryActionButton={[
            <SeaEditButton key={'Edit'} onPress={() => setIsVisibleEditDrawer(true)} />,
            <SeaDeleteButton
              key={'Delete'}
              // onPress={() => alert("TODO: Delete Completed Schedule")}
            />,
          ]}
          titleComponent={
            <SeaPageCardTitle title={`Item - ${selectedSparePart?.item}`} files={selectedSparePart?.files} />
          }>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Quantity'} value={formatValue(selectedSparePart?.quantity)} />
                    <SeaLabelValue label={'Minimum Quantity'} value={formatValue(selectedSparePart?.minQuantity)} />
                  </SeaStack>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Order Quantity'} value={formatValue(selectedSparePart?.orderQuantity)} />
                    <SeaLabelValue
                      iconOptions={{
                        icon: 'attach_money',
                      }}
                      showIcon
                      label={'Unit Price'}
                      value={formatValue(selectedSparePart?.unitPrice)}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Part Number'} value={formatValue(selectedSparePart?.partNum)} />
                    <SeaLabelValue
                      iconOptions={{
                        icon: 'location_on',
                      }}
                      showIcon
                      label={'Location'}
                      value={formatValue(renderCategoryName(selectedSparePart?.locationId, vesselLocations))}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={'50%'}>
                    <SeaLabelValue label={'Model'} value={formatValue(selectedSparePart?.model)} />
                  </SeaStack>
                </SeaStack>
                <SeaStack isCollapsible={true} direction={'column'} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue
                    label={'Description'}
                    value={formatValue(selectedSparePart?.locationDescription)}
                    layout={'vertical'}
                  />
                </SeaStack>

                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} direction={'column'}>
                  <SeaLabelValue label={'Notes'} value={formatValue(selectedSparePart?.notes)} layout={'vertical'} />
                </SeaStack>

                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} direction={'column'}>
                  <SeaLabelValue label={'Manufacturer'} value={selectedSparePart?.manufacturer} layout={'vertical'} />
                </SeaStack>

                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} direction={'column'}>
                  <ContactMediaCards title={'Supplier Contact'} contactIds={selectedSparePart?.contactIds} />
                </SeaStack>
              </SeaStack>
              {uploadedFiles.length > 0 && <SeaMedia type="manuals" title="Files" files={uploadedFiles} />}
            </SeaStack>
          </SeaPageCardContentSection>
          <SeaEmptyDivider />
          <SeaPageCardContentSection>
            <ViewEquipmentSectionCompact equipmentIds={selectedSparePart?.equipmentIds} />
          </SeaPageCardContentSection>
        </SeaPageCard>
      </ScrollView>
      {isVisibleEditDrawer && (
        <EditSparePartDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          sparePartId={Array.isArray(sparePartId) ? sparePartId[0] : sparePartId}
          visible={isVisibleEditDrawer}
          type="edit"
        />
      )}
    </RequirePermissions>
  )
}
