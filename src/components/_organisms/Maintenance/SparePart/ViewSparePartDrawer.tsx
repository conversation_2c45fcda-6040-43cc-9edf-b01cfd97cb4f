import { View, Text, StyleSheet } from 'react-native'
import React, { useMemo } from 'react'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SparePart } from '@src/shared-state/VesselMaintenance/spareParts'
import { SeaCard } from '@src/components/_atoms/SeaCard/SeaCard'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatValue } from '@src/lib/util'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { renderCategoryName } from '@src/lib/categories'
import { sharedState } from '@src/shared-state/shared-state'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'

export interface ViewSparePartDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedSparePart: SparePart
}

export function ViewSparePartDrawer({ selectedSparePart, visible, onClose, style }: ViewSparePartDrawerProps) {
  const vesselSystems = sharedState.vesselSystems.use(visible)
  const vesselLocations = sharedState.vesselLocations.use(visible)
  const contacts = sharedState.contacts.use(visible)
  const equipment = sharedState.equipment.use(visible)

  const equipmentList = useMemo(() => {
    if (!selectedSparePart?.equipmentIds)
      return {
        equipment: '',
        criticalEquipment: '',
      }

    const availableEquipment = selectedSparePart.equipmentIds.reduce((acc, id: string) => {
      const item = equipment?.byId[id]
      if (item) {
        if (item.state === 'deleted') {
          item.equipment += ' (deleted)'
        }

        acc.push(item)
      }

      return acc
    }, [] as Equipment[])

    const criticalEquipment = availableEquipment
      .filter(item => item.isCritical)
      .map(item => item.equipment)
      .join(', ')

    const equipmentNames = availableEquipment.map(item => item.equipment).join(', ')

    return {
      equipment: equipmentNames,
      criticalEquipment: criticalEquipment.length > 0 ? criticalEquipment : '-',
    }
  }, [selectedSparePart, equipment])

  return (
    <>
      <SeaDrawer
        title={`Item - ${selectedSparePart?.item}`}
        visible={visible}
        onClose={onClose}
        style={style}
        headerActions={[
          <SeaDeleteButton
            key={'delete'}
            onPress={() => alert('This functionality is not implemented yet')} // TODO
          />,
          <SeaEditButton
            key={'edit'}
            onPress={() => alert('TODO')} // TODO
          />,
        ]}>
        <SeaCard style={styles.card}>
          <SeaStack
            gap={20}
            align={'start'}
            style={{
              width: '100%',
            }}>
            {/* Schedule Details */}
            <SeaStack
              gap={20}
              direction={'column'}
              align={'start'}
              style={{
                width: '70%',
              }}>
              <SeaStack gap={30} style={styles.fullRow} align={'start'}>
                <SeaLabelValue
                  label={'Quantity'}
                  value={formatValue(selectedSparePart.quantity)}
                  style={styles.oneThirdColumn}
                />
                <SeaLabelValue
                  label={'Minimum Quantity'}
                  value={formatValue(selectedSparePart.minQuantity)}
                  style={styles.oneThirdColumn}
                />
                <SeaLabelValue
                  label={'Order Quantity'}
                  value={formatValue(selectedSparePart.orderQuantity)}
                  style={styles.oneThirdColumn}
                />
              </SeaStack>

              <SeaStack gap={30} style={styles.fullRow} align={'start'}>
                <SeaLabelValue
                  label={'Unit Price'}
                  value={formatValue(selectedSparePart.unitPrice)}
                  style={styles.oneThirdColumn}
                />
                <SeaLabelValue
                  label={'Part Number'}
                  value={formatValue(selectedSparePart.partNum)}
                  style={styles.oneThirdColumn}
                />
                <SeaLabelValue
                  label={'System'}
                  value={formatValue(renderCategoryName(selectedSparePart.systemId, vesselSystems))}
                  style={styles.oneThirdColumn}
                />
              </SeaStack>
              <SeaStack gap={30} style={styles.fullRow} align={'start'}>
                <SeaLabelValue label={'Equipment'} value={equipmentList.equipment} style={styles.oneThirdColumn} />
                <SeaLabelValue
                  label={'Location'}
                  value={formatValue(renderCategoryName(selectedSparePart.locationId, vesselLocations))}
                  style={styles.oneThirdColumn}
                />
              </SeaStack>
              <SeaStack gap={30} style={styles.fullRow} align={'start'}>
                <SeaLabelValue
                  label={'Critical Equipment'}
                  value={equipmentList.criticalEquipment}
                  style={styles.column}
                />
                <SeaLabelValue label={'Model'} value={formatValue(selectedSparePart.model)} style={styles.column} />
              </SeaStack>
              <View style={styles.fullRow}>
                <SeaLabelValue
                  label={'Manufacturer'}
                  value={selectedSparePart.manufacturer}
                  style={styles.oneThirdColumn}
                />
              </View>
              <View style={styles.fullRow}>
                <SeaLabelValue
                  label={'Description'}
                  value={formatValue(selectedSparePart.locationDescription)}
                  style={styles.fullRow}
                />
              </View>
              <View style={styles.fullRow}>
                <SeaLabelValue
                  label={'Supplier Contact'}
                  value={
                    contacts?.byId && selectedSparePart?.contactIds && selectedSparePart?.contactIds?.length > 0
                      ? selectedSparePart.contactIds
                          .map((contactId: string) => {
                            const contact = contacts.byId[contactId]
                            return `${contact.name}${contact.company ? ` (${contact.company})` : ''}${contact.number ? `, ${contact.number}` : ''}${contact.email ? `, ${contact.email}` : ''}`
                          })
                          .join('\n \n')
                      : '-'
                  }
                  style={styles.fullRow}
                />
              </View>
              <View style={styles.fullRow}>
                <SeaLabelValue
                  label={'Supplier Contact'}
                  value={formatValue(selectedSparePart?.notes)}
                  style={styles.fullRow}
                />
              </View>
            </SeaStack>
            {/*  Image goes here*/}
            <SeaFileImage files={selectedSparePart.files} size="medium" showOthers />
          </SeaStack>
        </SeaCard>
      </SeaDrawer>
    </>
  )
}

const styles = StyleSheet.create({
  card: {
    padding: 20,
    width: '100%',
    gap: 40,
    flexDirection: 'column',
  },
  fullRow: {
    width: '100%',
  },
  column: {
    width: '50%',
  },
  oneThirdColumn: {
    width: '33%',
  },
})
