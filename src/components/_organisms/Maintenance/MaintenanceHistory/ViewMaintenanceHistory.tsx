import { ScrollView } from 'react-native'
import React, { useMemo, useState } from 'react'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatDatetime, formatSparePartsList, formatValue, renderCamelCase } from '@src/lib/util'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { getEngineName } from '@src/shared-state/Core/vessel'
import { renderCategoryName } from '@src/lib/categories'
import { formatShortTimeDurationHrsMinsView } from '@src/lib/datesAndTime'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { sharedState } from '@src/shared-state/shared-state'
import { useGlobalSearchParams, useRouter } from 'expo-router'
import { SparePart } from '@src/shared-state/VesselMaintenance/spareParts'
import { Job } from '@src/shared-state/VesselMaintenance/jobs'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'

import { getFileNameWithExtension } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { EditMaintenanceHistoryDrawer } from './EditMaintenanceHistoryDrawer'
import { ViewEquipmentSection } from '@src/components/_organisms/Equipment/ViewEquipmentSection'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'

const DESKTOP_ITEMS_WIDTH = '100%'

export function ViewMaintenanceHistory() {
  const { styles } = useStyles(styleSheet)
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  const vesselSystems = sharedState.vesselSystems.use()
  const vesselLocations = sharedState.vesselLocations.use()
  const maintenanceTasksCompleted = sharedState.maintenanceTasksCompleted.use()
  const scheduledMaintenanceTasks = sharedState.scheduledMaintenanceTasks.use()
  const jobs = sharedState.jobs.use()
  const spareParts = sharedState.spareParts.use()

  const { taskId } = useGlobalSearchParams()

  const [showEditDrawer, setShowEditDrawer] = useState(false)
  const selectedItem = useMemo(() => {
    if (!taskId || !maintenanceTasksCompleted) {
      return undefined
    }

    return maintenanceTasksCompleted.all.find(item => item.id === taskId)
  }, [taskId, maintenanceTasksCompleted])

  const scheduledTask = useMemo(() => {
    if (selectedItem?.maintenanceTaskId && scheduledMaintenanceTasks) {
      return scheduledMaintenanceTasks.byId[selectedItem?.maintenanceTaskId]
    } else if (selectedItem?.jobId && jobs) {
      return jobs?.byId.all[selectedItem?.jobId]
    }
  }, [jobs, scheduledMaintenanceTasks, selectedItem?.jobId, selectedItem?.maintenanceTaskId])

  const equipmentSpareParts = useMemo(() => {
    if (selectedItem && spareParts) {
      const _spareParts: SparePart[] = []
      spareParts.all.forEach(sparePart => {
        if (sparePart.equipmentIds) {
          for (const equipmentId of sparePart.equipmentIds) {
            if (equipmentId === selectedItem?.equipmentId) {
              _spareParts.push(sparePart)
              break
            }
          }
        }
      })
      return _spareParts
    }
    return undefined
  }, [selectedItem, spareParts])

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return []

    return selectedItem?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('TODO')} />],
    })) as MediaCardFile[]
  }, [selectedItem])

  return (
    <RequirePermissions role="maintenanceHistory" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container}>
        <SeaPageCard
          secondaryActionButton={[
            <SeaEditButton key={'Edit'} onPress={() => setShowEditDrawer(true)} />,
            <SeaDeleteButton key={'Delete'} onPress={() => alert('TODO: Delete Completed Schedule')} />,
          ]}
          titleComponent={
            <SeaPageCardTitle title={`Completed Maintenance Task: ${selectedItem?.equipment?.equipment}`} />
          }>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 10 : 0}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" gap={isDesktopWidth ? 20 : 10} align={'start'} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaStack
                    isCollapsible={true}
                    width={DESKTOP_ITEMS_WIDTH}
                    direction={'column'}
                    style={isDesktopWidth ? { paddingBottom: 5 } : {}}>
                    <SeaLabelValue label={'Task'} value={formatValue(selectedItem?.task)} layout={'vertical'} />
                  </SeaStack>

                  <SeaStack
                    isCollapsible={true}
                    width={DESKTOP_ITEMS_WIDTH}
                    direction={'column'}
                    style={isDesktopWidth ? { paddingBottom: 5 } : {}}>
                    <SeaLabelValue
                      label={'Description'}
                      value={formatValue(selectedItem?.description)}
                      layout={'vertical'}
                    />
                  </SeaStack>
                </SeaStack>

                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{
                        icon: 'calendar_month',
                      }}
                      showIcon
                      label={'When Completed'}
                      value={formatDatetime(selectedItem?.whenCompleted, ', ')}
                    />

                    <SeaLabelValue
                      iconOptions={{
                        icon: 'person',
                      }}
                      showIcon
                      label={'Completed By'}
                      value={renderFullNameForUserId(selectedItem?.completedBy)}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Type'}
                      value={formatValue(
                        selectedItem?.type === 'unscheduled' ? 'Job' : renderCamelCase(selectedItem?.type ?? '')
                      )}
                    />

                    {(isDesktopWidth || selectedItem?.engineId) && (
                      <SeaLabelValue
                        label={
                          selectedItem?.engineHours
                            ? `${selectedItem?.engineId && getEngineName(selectedItem?.engineId)} Hours`
                            : ''
                        }
                        value={selectedItem?.engineHours ? selectedItem?.engineHours.toString() : ''}
                      />
                    )}
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>

          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <ViewEquipmentSection vesselIds={[]} equipment={selectedItem?.equipment} />
          </SeaPageCardContentSection>

          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Parts Used'}
                    value={
                      selectedItem?.spareParts
                        ? formatValue(formatSparePartsList(selectedItem?.spareParts, equipmentSpareParts))
                        : '-'
                    }
                  />
                  <SeaLabelValue
                    label={'Maintenance Tags'}
                    value={formatValue(selectedItem?.maintenanceTags?.join(', '))}
                  />

                  {selectedItem?.type === 'job' && (
                    <SeaStack
                      isCollapsible={true}
                      width={DESKTOP_ITEMS_WIDTH}
                      direction={'row'}
                      gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue label={'Job#'} value={formatValue((scheduledTask as Job)?.jobNum)} />

                      <SeaLabelValue label={'Job Tags'} value={formatValue((scheduledTask as Job)?.tags?.join(', '))} />
                    </SeaStack>
                  )}

                  <SeaStack
                    isCollapsible={true}
                    width={DESKTOP_ITEMS_WIDTH}
                    direction={'row'}
                    gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Actual Time'}
                      value={formatValue(
                        selectedItem?.actualTime ? formatShortTimeDurationHrsMinsView(selectedItem?.actualTime) : ''
                      )}
                    />
                  </SeaStack>
                </SeaStack>
                <SeaStack
                  isCollapsible={true}
                  width={DESKTOP_ITEMS_WIDTH}
                  direction={'row'}
                  gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue label={'Notes'} value={formatValue(selectedItem?.notes)} layout={'vertical'} />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>

          {/*</SeaStack>*/}
          {uploadedFiles.length > 0 && <SeaMedia type="manuals" title="Files" files={uploadedFiles} />}
          {/*</SeaStack>*/}
        </SeaPageCard>
      </ScrollView>
      {showEditDrawer && selectedItem && (
        <EditMaintenanceHistoryDrawer
          visible={showEditDrawer}
          onClose={() => setShowEditDrawer(false)}
          selectedItem={selectedItem}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  content: {
    marginBottom: 20,
  },
  card: {
    padding: 20,
    width: '100%',
    gap: 40,
    flexDirection: 'column',
  },
}))
