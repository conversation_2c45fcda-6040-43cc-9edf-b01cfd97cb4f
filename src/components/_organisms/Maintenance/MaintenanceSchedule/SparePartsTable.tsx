import React, { useMemo } from 'react'
import { ScheduledMaintenanceTask } from '@src/shared-state/VesselMaintenance/maintenanceSchedule'
import { formatValue } from '@src/lib/util'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { sharedState } from '@src/shared-state/shared-state'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { SparePart } from '@src/shared-state/VesselMaintenance/spareParts'
import { CategoriesData, renderCategoryName } from '@src/lib/categories'
import { useLicenseeVesselLocations } from '@src/shared-state/VesselMaintenance/useLicenseeVesselLocations'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconLocation } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

const seaTableColumns = [
  {
    label: '',
    width: 60,
    render: item => <SeaTableImage files={item.files} />,
    compactModeOptions: {
      isThumbnail: true,
    },
  },
  {
    label: 'Item',
    value: item => formatValue(item.item),
    compactModeOptions: {
      rowPosition: CompactRowPosition.Title,
    },
  },
  {
    label: 'Quantity',
    value: item => formatValue(item.quantity),
    compactModeOptions: {
      label: {
        show: true,
        valueWidth: CompactValueWidth.Medium,
      },
    },
  },
  {
    label: 'Part #',
    value: item => formatValue(item.partNum),
    compactModeOptions: {
      label: {
        show: true,
        valueWidth: CompactValueWidth.Medium,
      },
    },
  },
] as SeaTableColumn<SparePart>[]

interface SparePartsTableProps {
  selectedSchedule: ScheduledMaintenanceTask
  vesselIds: string[]
}

export const SparePartsTable = ({ selectedSchedule, vesselIds }: SparePartsTableProps) => {
  const spareParts = sharedState.spareParts.use()

  const vesselLocations = useLicenseeVesselLocations(vesselIds)

  const equipmentSpareParts = useMemo(() => {
    if (!selectedSchedule?.equipmentId || !spareParts) return []

    return spareParts.all.filter(sparePart => sparePart.equipmentIds?.includes(selectedSchedule.equipmentId))
  }, [selectedSchedule, spareParts])

  /**
   * This creates an array of the columns those are conditionally added at specific position or
   * have dependencies from the shared state.
   */
  const updatedColumns = useMemo(() => {
    let newColumns = [...seaTableColumns]

    const quantityIndex = newColumns.findIndex(col => col.label === 'Quantity')
    const locationIndex = newColumns.findIndex(col => col.label === 'Location')

    // Add "Actual time" after "Parts Used" if it does not exist
    if (quantityIndex !== -1 && locationIndex === -1) {
      newColumns = [
        ...newColumns.slice(0, quantityIndex + 1),
        {
          label: 'Location',
          icon: () => <SeaTableIconLocation />,
          value: item =>
            vesselLocations
              ? formatValue(renderCategoryName(item.locationId, vesselLocations as unknown as CategoriesData))
              : '-',
          compactModeOptions: {
            label: {
              show: true,
              valueWidth: CompactValueWidth.Medium,
            },
          },
        },
        ...newColumns.slice(quantityIndex + 1),
      ]
    }

    return newColumns
  }, [seaTableColumns, selectedSchedule])

  return (
    <RequirePermissions role={'sparePartsList'} level={permissionLevels.VIEW}>
      <SeaTable columns={updatedColumns} rows={buildRows(equipmentSpareParts, item => alert('TODO'))} />
    </RequirePermissions>
  )
}

const buildRows = (items: SparePart[], onPress: (item: SparePart) => void) => {
  return items.map(item => ({
    data: item,
    onPress: (item: SparePart) => onPress(item),
  }))
}
