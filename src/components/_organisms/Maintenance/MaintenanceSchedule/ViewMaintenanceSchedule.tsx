import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { EquipmentHistoryTable } from '@src/components/_organisms/Maintenance/MaintenanceSchedule/EquipmentHistoryTable'
import { SparePartsTable } from '@src/components/_organisms/Maintenance/MaintenanceSchedule/SparePartsTable'
import { TaskHistoryTable } from '@src/components/_organisms/Maintenance/MaintenanceSchedule/TaskHistoryTable'
import { usePermission } from '@src/hooks/usePermission'
import { formatInterval, formatShortTimeDurationHrsMinsView, warnDays } from '@src/lib/datesAndTime'
import { formatValue } from '@src/lib/util'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { sharedState } from '@src/shared-state/shared-state'
import { useEnginesFromVessels } from '@src/shared-state/VesselMaintenance/useEnginesFromVessels'
import { useLicenseeVesselLocations } from '@src/shared-state/VesselMaintenance/useLicenseeVesselLocations'
import { useGlobalSearchParams } from 'expo-router'
import React, { useMemo, useState } from 'react'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { ScrollView } from 'react-native-gesture-handler'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { ModifyMaintenanceScheduleDrawer } from '@src/components/_organisms/Maintenance/MaintenanceSchedule/ModifyMaintenanceScheduleDrawer'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { CompleteMaintenanceScheduleDrawer } from './CompleteMaintenanceScheduleDrawer'
import { WhenDueHours } from '@src/components/_molecules/WhenDueHours/WhenDueHours'
import { useItemLinks } from '@src/shared-state/General/useItemLinks'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'
import { ViewEquipmentSection } from '@src/components/_organisms/Equipment/ViewEquipmentSection'

const DESKTOP_ITEMS_WIDTH = '100%'
type TabValues = 'task-history' | 'equipment-history' | 'spare-parts'

const MaintenanceScheduleDrawerPermissions = {
  maintenanceHistory: { level: permissionLevels.VIEW },
  sparePartsList: { level: permissionLevels.VIEW },
}

export function ViewMaintenanceSchedule() {
  const scheduledMaintenanceTasks = sharedState.scheduledMaintenanceTasks.use()
  const licenseeSettings = sharedState.licenseeSettings.use()

  const { maintenanceScheduleId } = useGlobalSearchParams()
  const { styles, theme } = useStyles(styleSheet)

  const selectedSchedule = useMemo(() => {
    if (!maintenanceScheduleId || !scheduledMaintenanceTasks) {
      return undefined
    }

    return scheduledMaintenanceTasks.byId[
      Array.isArray(maintenanceScheduleId) ? maintenanceScheduleId[0] : maintenanceScheduleId
    ]
  }, [maintenanceScheduleId, scheduledMaintenanceTasks])

  const links = useItemLinks(selectedSchedule?.id)

  const vesselIds = useMemo(
    () => (selectedSchedule?.vesselId ? [selectedSchedule?.vesselId] : undefined),
    [selectedSchedule?.vesselId]
  )
  const engines = useEnginesFromVessels(vesselIds)

  const modulePermissions = usePermission<typeof MaintenanceScheduleDrawerPermissions>({
    modules: MaintenanceScheduleDrawerPermissions,
  })

  const [activeTab, setActiveTab] = useState<TabValues>('task-history')
  const [showEditDrawer, setShowEditDrawer] = useState(false)
  const [showCompletedModal, setShowCompletedModal] = useState(false)

  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  const interval = useMemo(() => {
    let intervalText = ''

    if (selectedSchedule?.intervalType === 'weekMonth' || selectedSchedule?.intervalType === 'weekMonthAndHours') {
      intervalText += formatInterval(selectedSchedule?.intervalWeekMonth)
    }

    if (selectedSchedule?.intervalType === 'engineHours' || selectedSchedule?.intervalType === 'weekMonthAndHours') {
      intervalText += ` (${selectedSchedule?.intervalEngineHours} Hours)`
    }

    return intervalText
  }, [selectedSchedule])

  const engineState = useMemo(() => {
    const engineId = selectedSchedule?.engineId
    if (!engineId) return undefined

    const engine = engines?.byId[engineId]

    if (!engine) return undefined

    return engine.state
  }, [engines?.byId, selectedSchedule?.engineId])

  return (
    <RequirePermissions role="maintenanceSchedule" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container}>
        <SeaPageCard
          primaryActionButton={
            <SeaButton
              key={'Complete'}
              label="Complete Task"
              iconOptions={{ icon: 'check' }}
              variant={SeaButtonVariant.Primary}
              onPress={() => setShowCompletedModal(true)} // TODO
            />
          }
          secondaryActionButton={[
            <SeaDeleteButton key={'Delete'} onPress={() => alert('TODO: Delete Completed Schedule')} />,
            <SeaEditButton key={'Edit'} onPress={() => setShowEditDrawer(true)} />,
          ]}
          subNav={[
            ...(modulePermissions.maintenanceHistory
              ? [
                  {
                    title: 'Task History',
                    onPress: () => setActiveTab('task-history'),
                    isActive: activeTab === 'task-history',
                  },
                  {
                    title: 'Equipment History',
                    onPress: () => setActiveTab('equipment-history'),
                    isActive: activeTab === 'equipment-history',
                  },
                ]
              : []),
            ...(modulePermissions.sparePartsList
              ? [
                  {
                    title: 'Spare Parts',
                    onPress: () => setActiveTab('spare-parts'),
                    isActive: activeTab === 'spare-parts',
                  },
                ]
              : []),
          ]}
          titleComponent={
            <SeaPageCardTitle
              title={selectedSchedule?.equipment?.equipment}
              additionalElements={
                selectedSchedule?.useHours && selectedSchedule?.engineHoursLeft ? (
                  <WhenDueHours engineHoursLeft={selectedSchedule.engineHoursLeft} />
                ) : selectedSchedule?.dateDue ? (
                  <WhenDueStatus
                    whenDue={selectedSchedule.dateDue}
                    warnDaysThreshold={warnDays.maintenanceSchedule[0]}
                    hasFault={selectedSchedule?.hasFault ?? false}
                  />
                ) : undefined
              }
              files={selectedSchedule?.equipment?.files}
            />
          }>
          <SeaPageCardContentSection>
            {engineState && engineState !== 'active' && (
              <SeaTypography
                variant={'body'}
                textStyle={{
                  fontWeight: 'bold',
                  paddingBottom: 20,
                  color: theme.colors.status.errorPrimary,
                }}>
                WARNING: This task is connected to an engine that has been{' '}
                {engineState === 'archived' ? 'archived' : 'deleted'}!
              </SeaTypography>
            )}
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack
                    isCollapsible={true}
                    width={DESKTOP_ITEMS_WIDTH}
                    direction={'column'}
                    style={isDesktopWidth ? { paddingBottom: 5 } : {}}>
                    <SeaLabelValue label={'Task Title'} value={selectedSchedule?.task} layout={'vertical'} />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    {licenseeSettings?.hasMaintenanceTaskTime && (
                      <SeaLabelValue
                        iconOptions={{
                          icon: 'timer',
                        }}
                        showIcon
                        label={'Estimated Time'}
                        value={formatValue(
                          selectedSchedule?.estimatedTime
                            ? formatShortTimeDurationHrsMinsView(selectedSchedule?.estimatedTime)
                            : ''
                        )}
                      />
                    )}
                    <SeaLabelValue
                      iconOptions={{
                        icon: 'schedule',
                      }}
                      showIcon
                      label={'Interval'}
                      value={interval}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={'50%'}>
                    <SeaLabelValue
                      iconOptions={{
                        icon: 'label',
                      }}
                      showIcon
                      label={'Maintenance Tags'}
                      value={formatValue(selectedSchedule?.maintenanceTags?.join(', '))}
                    />
                  </SeaStack>
                </SeaStack>

                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} direction={'column'}>
                  <SeaLabelValue
                    label={'Description'}
                    value={formatValue(selectedSchedule?.description)}
                    layout={'vertical'}
                  />
                </SeaStack>
              </SeaStack>

              {isLargeDesktopWidth && <SeaMedia title="Links" files={[]} type="links" />}
            </SeaStack>
          </SeaPageCardContentSection>
          <SeaEmptyDivider />
          <SeaPageCardContentSection>
            <ViewEquipmentSection vesselIds={vesselIds} equipment={selectedSchedule?.equipment} />
          </SeaPageCardContentSection>

          {!isLargeDesktopWidth && (
            <>
              <SeaEmptyDivider />
              <SeaPageCardContentSection>
                <SeaStack direction="column" align="start" gap={20} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaMedia title="Links" files={[]} type="links" />
                </SeaStack>
              </SeaPageCardContentSection>
            </>
          )}
        </SeaPageCard>

        {/* TODO: Move them to their on routes */}
        {activeTab === 'task-history' && selectedSchedule && <TaskHistoryTable selectedSchedule={selectedSchedule} />}
        {activeTab === 'equipment-history' && selectedSchedule && (
          <EquipmentHistoryTable selectedSchedule={selectedSchedule} />
        )}
        {activeTab === 'spare-parts' && selectedSchedule && (
          <SparePartsTable selectedSchedule={selectedSchedule} vesselIds={vesselIds ?? []} />
        )}

        <SeaSpacer height={16} />
      </ScrollView>
      {showEditDrawer && (
        <ModifyMaintenanceScheduleDrawer
          visible={showEditDrawer}
          onClose={() => setShowEditDrawer(false)}
          selectedScheduleId={selectedSchedule?.id ?? ''}
          mode="edit"
        />
      )}
      {showCompletedModal && (
        <CompleteMaintenanceScheduleDrawer
          visible={showCompletedModal}
          onClose={() => setShowCompletedModal(false)}
          selectedScheduleId={selectedSchedule?.id ?? ''}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  card: {
    padding: 20,
    width: '100%',
    gap: 20,
  },
  fullRow: {
    width: '100%',
  },
  column: {
    width: '50%',
  },
}))
