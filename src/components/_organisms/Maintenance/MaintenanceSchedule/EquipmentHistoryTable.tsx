import React, { useMemo } from 'react'
import { ScheduledMaintenanceTask } from '@src/shared-state/VesselMaintenance/maintenanceSchedule'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { sharedState } from '@src/shared-state/shared-state'
import { MaintenanceTaskCompleted } from '@src/shared-state/VesselMaintenance/maintenanceTasksCompleted'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { formatDateShort, formatShortTimeDurationHrsMinsView } from '@src/lib/datesAndTime'
import { formatValue, renderCamelCase } from '@src/lib/util'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconCalendar, SeaTableIconPerson } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

const seaTableColumns = [
  {
    label: 'Date',
    icon: () => <SeaTableIconCalendar />,
    value: item => formatDateShort(item.whenCompleted),
    widthPercentage: 0.1,
    compactModeOptions: {
      rowPosition: CompactRowPosition.Title,
      label: {
        show: true,
        valueWidth: CompactValueWidth.Medium,
      },
    },
  },
  {
    label: 'Task',
    value: item => formatValue(item.task),
    widthPercentage: 0.2,
    compactModeOptions: {
      label: {
        show: true,
        valueWidth: CompactValueWidth.Medium,
      },
    },
  },
  {
    label: 'Notes',
    value: item => item.notes ?? '',
    widthPercentage: 0.3,
    compactModeOptions: {
      label: {
        show: true,
        valueWidth: CompactValueWidth.Medium,
      },
    },
  },
  {
    label: 'Type',
    value: item => formatValue(item.type === 'unscheduled' ? 'Job' : renderCamelCase(item.type)),
    widthPercentage: 0.1,
    compactModeOptions: {
      label: {
        show: true,
        valueWidth: CompactValueWidth.Medium,
      },
    },
  },
  {
    label: 'Completed By',
    icon: () => <SeaTableIconPerson />,
    value: item => renderFullNameForUserId(item.completedBy) || '',
    widthPercentage: 0.2,
    compactModeOptions: {
      label: {
        show: true,
        valueWidth: CompactValueWidth.Medium,
        name: 'By',
      },
    },
  },
  {
    label: '',
    width: 60,
    render: item => <SeaTableImage files={item.files} />,
    compactModeOptions: {
      isThumbnail: true,
    },
  },
] as SeaTableColumn<MaintenanceTaskCompleted>[]

interface EquipmentHistoryTableProps {
  selectedSchedule: ScheduledMaintenanceTask
}

export const EquipmentHistoryTable = ({ selectedSchedule }: EquipmentHistoryTableProps) => {
  const maintenanceTasksCompleted = sharedState.maintenanceTasksCompleted.use()
  const licenseeSettings = sharedState.licenseeSettings.use()

  const equipmentTasksCompleted = useMemo(() => {
    if (selectedSchedule && maintenanceTasksCompleted) {
      const tasksCompleted: MaintenanceTaskCompleted[] = []
      maintenanceTasksCompleted.all.forEach(taskCompleted => {
        if (taskCompleted.equipmentId === selectedSchedule.equipmentId) {
          tasksCompleted.push(taskCompleted)
        }
      })
      tasksCompleted.sort((a, b) => {
        return b.whenCompleted - a.whenCompleted
      })
      return tasksCompleted
    }
    return []
  }, [selectedSchedule, maintenanceTasksCompleted])

  const hasTimeTrackingEnabled = useMemo(() => {
    return licenseeSettings?.hasMaintenanceTaskTime ?? false
  }, [licenseeSettings])

  /**
   * This creates an array of the columns those are conditionally added at specific position or
   * have dependencies from the shared state.
   */
  const updatedColumns = useMemo(() => {
    let newColumns = [...seaTableColumns]

    const partsUsedIndex = newColumns.findIndex(col => col.label === 'Completed By')
    const actualTimeIndex = newColumns.findIndex(col => col.label === 'Actual time')

    // Add "Actual time" after "Parts Used" if it does not exist
    if (hasTimeTrackingEnabled && partsUsedIndex !== -1 && actualTimeIndex === -1) {
      newColumns = [
        ...newColumns.slice(0, partsUsedIndex + 1),
        {
          label: 'Actual time',
          value: item => formatValue(item.actualTime ? formatShortTimeDurationHrsMinsView(item.actualTime) : '-'),
          compactModeOptions: {
            label: {
              show: true,
              valueWidth: CompactValueWidth.Medium,
            },
          },
        },
        ...newColumns.slice(partsUsedIndex + 1),
      ]
    }

    return newColumns
  }, [seaTableColumns, hasTimeTrackingEnabled, selectedSchedule])

  return (
    <RequirePermissions role={'maintenanceHistory'} level={permissionLevels.VIEW}>
      <SeaTable columns={updatedColumns} rows={buildRows(equipmentTasksCompleted, item => alert('TODO'))} />
    </RequirePermissions>
  )
}

const buildRows = (items: MaintenanceTaskCompleted[], onPress: (item: MaintenanceTaskCompleted) => void) => {
  return items.map(item => ({
    data: item,
    onPress: (item: MaintenanceTaskCompleted) => onPress(item),
  }))
}
