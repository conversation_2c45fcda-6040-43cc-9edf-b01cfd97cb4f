import { StyleSheet, Text, View } from 'react-native'
import React, { useMemo, useState } from 'react'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { SeaCard } from '@src/components/_atoms/SeaCard/SeaCard'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { renderCategoryName } from '@src/lib/categories'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { sharedState } from '@src/shared-state/shared-state'
import { formatValue } from '@src/lib/util'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSelector, SeaSelectorItem } from '@src/components/_atoms/SeaSelector/SeaSelector'
import { usePermission } from '@src/hooks/usePermission'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { TaskHistoryTable } from './TaskHistoryTable'
import { EquipmentHistoryTable } from './EquipmentHistoryTable'
import { SparePartsTable } from './SparePartsTable'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'

type TabValues = 'task-history' | 'equipment-history' | 'spare-parts'

const MaintenanceDrawerPermissions = {
  maintenanceSchedule: { level: permissionLevels.VIEW },
  maintenanceHistory: { level: permissionLevels.VIEW },
  sparePartsList: { level: permissionLevels.VIEW },
}

const tabs: Omit<SeaSelectorItem, 'onPress'>[] = [
  { value: 'task-history', label: 'Task History' },
  { value: 'equipment-history', label: 'Equipment History' },
  { value: 'spare-parts', label: 'Spare Parts' },
]

export interface ViewSparePartDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedEquipment: Equipment
}

export default function ViewEquipmentDrawer({ selectedEquipment, visible, onClose, style }: ViewSparePartDrawerProps) {
  const vessel = sharedState.vessel.use(visible)
  const vesselSystems = sharedState.vesselSystems.use(visible)
  const vesselLocations = sharedState.vesselLocations.use(visible)
  const contacts = sharedState.contacts.use(visible)
  const equipmentManualDocuments = sharedState.equipmentManualDocuments.use(visible)

  const modulePermissions = usePermission<typeof MaintenanceDrawerPermissions>({
    modules: MaintenanceDrawerPermissions,
  })

  const [activeTab, setActiveTab] = useState<TabValues>('task-history')

  const equipmentManualDocumentsList = useMemo(() => {
    if (!selectedEquipment.equipmentDocumentIds || !equipmentManualDocuments) return []

    return selectedEquipment.equipmentDocumentIds.reduce(
      (acc, manualId) => {
        const manual = equipmentManualDocuments.byId[manualId]

        if (manual) {
          const data = {
            title: manual.title,
            files: manual.files,
          }

          acc.push(data)
        }

        return acc
      },
      [] as { title: string; files: string[] }[]
    )
  }, [equipmentManualDocuments])

  return (
    <>
      <SeaDrawer
        title={selectedEquipment.equipment}
        visible={visible}
        onClose={onClose}
        style={style}
        headerActions={[
          <SeaDeleteButton
            key={'delete'}
            onPress={() => alert('This functionality is not implemented yet')} // TODO
          />,
          <SeaEditButton
            key={'edit'}
            onPress={() => alert('TODO')} // TODO
          />,
        ]}>
        <SeaCard style={styles.card}>
          <SeaStack
            gap={20}
            align={'start'}
            style={{
              width: '100%',
            }}>
            {/* Schedule Details */}
            <SeaStack
              gap={20}
              direction={'column'}
              align={'start'}
              style={{
                width: '70%',
              }}>
              <SeaStack gap={30} style={styles.fullRow} align={'start'}>
                <SeaLabelValue
                  label={'System'}
                  value={renderCategoryName(selectedEquipment?.systemId, vesselSystems)}
                  style={styles.column}
                />
                <SeaLabelValue
                  label={'Location'}
                  value={formatValue(renderCategoryName(selectedEquipment.locationId, vesselLocations))}
                  style={styles.column}
                />
              </SeaStack>

              <SeaStack gap={30} style={styles.fullRow} align={'start'}>
                <SeaLabelValue
                  label={'Critical Equipment'}
                  value={formatValue(selectedEquipment?.isCritical ? 'Yes' : 'No')}
                  style={styles.column}
                />
                <SeaLabelValue label={'Make'} value={formatValue(selectedEquipment.make)} style={styles.column} />
              </SeaStack>
              <SeaStack gap={30} style={styles.fullRow} align={'start'}>
                <SeaLabelValue label={'Model'} value={formatValue(selectedEquipment.model)} style={styles.column} />
                <SeaLabelValue label={'Serial'} value={formatValue(selectedEquipment.serial)} style={styles.column} />
              </SeaStack>
              <SeaStack gap={30} style={styles.fullRow} align={'start'}>
                <SeaLabelValue
                  label={'Equipment Notes'}
                  value={formatValue(selectedEquipment?.notes)}
                  style={styles.fullRow}
                />
              </SeaStack>
              <View style={styles.fullRow}>
                <SeaLabelValue
                  label={'Supplier Contact'}
                  value={
                    contacts?.byId && selectedEquipment?.contactIds && selectedEquipment?.contactIds?.length > 0
                      ? selectedEquipment.contactIds
                          .map((contactId: string) => {
                            const contact = contacts.byId[contactId]
                            return `${contact.name}${contact.company ? ` (${contact.company})` : ''}${contact.number ? `, ${contact.number}` : ''}${contact.email ? `, ${contact.email}` : ''}`
                          })
                          .join('\n \n')
                      : '-'
                  }
                  style={styles.fullRow}
                />
              </View>

              {vessel?.isShoreFacility && (
                <View style={styles.fullRow}>
                  <SeaLabelValue
                    label={'Manuals'}
                    value={
                      !selectedEquipment?.equipmentDocumentIds || selectedEquipment.equipmentDocumentIds.length === 0
                        ? '-'
                        : undefined
                    }
                  />
                  <SeaStack gap={10} justify={'start'} align={'start'}>
                    {equipmentManualDocuments?.byId &&
                      (selectedEquipment.equipmentDocumentIds ?? []).map(manualId => {
                        const manual = equipmentManualDocuments.byId[manualId]
                        if (manual) {
                          return (
                            <SeaStack gap={10} key={manualId} align={'center'} justify={'start'}>
                              <View
                                style={{
                                  height: 45,
                                  width: 45,
                                  borderRadius: 16,
                                }}>
                                {/*TODO: Fix the pdf icon path issue in SeaFileImage*/}
                                <SeaFileImage files={manual.files} size={'tiny'} showOthers />
                              </View>
                              <SeaTypography variant={'input'}>{manual.title}</SeaTypography>
                            </SeaStack>
                          )
                        }
                      })}
                  </SeaStack>
                </View>
              )}
            </SeaStack>
            {/*  Image goes here*/}
            <SeaFileImage files={selectedEquipment.files} size="medium" showOthers />
          </SeaStack>
        </SeaCard>

        <SeaStack direction={'row'} justify={'between'} gap={10} style={styles.fullRow}>
          <SeaSelector
            items={tabs
              .filter(tab => {
                switch (tab.value) {
                  case 'task-history':
                    return modulePermissions.maintenanceSchedule
                  case 'equipment-history':
                    return modulePermissions.maintenanceHistory
                  case 'spare-parts':
                    return modulePermissions.sparePartsList
                  default:
                    return false
                }
              })
              .map(tab => ({
                ...tab,
                onPress: () => setActiveTab(tab.value as TabValues),
              }))}
            selectedValue={activeTab}
          />
        </SeaStack>
        {activeTab === 'task-history' && selectedEquipment && (
          <>
            <RequirePermissions role="maintenanceSchedule" level={permissionLevels.CREATE}>
              <View style={[styles.fullRow, { padding: 20, alignItems: 'flex-end', marginBottom: 20 }]}>
                <SeaStack gap={20}>
                  <SeaAddButton label="Add Job List Task" onPress={() => alert('TODO')} />
                  <SeaAddButton label="Add Scheduled Task" onPress={() => alert('TODO')} />
                </SeaStack>
              </View>
            </RequirePermissions>
            <TaskHistoryTable selectedEquipment={selectedEquipment} />
          </>
        )}
        {activeTab === 'equipment-history' && selectedEquipment && (
          <EquipmentHistoryTable selectedEquipment={selectedEquipment} />
        )}
        {activeTab === 'spare-parts' && selectedEquipment && <SparePartsTable selectedEquipment={selectedEquipment} />}

        <SeaSpacer height={16} />
      </SeaDrawer>
    </>
  )
}

const styles = StyleSheet.create({
  card: {
    padding: 20,
    width: '100%',
    gap: 40,
    flexDirection: 'column',
  },
  fullRow: {
    width: '100%',
  },
  column: {
    width: '50%',
  },
  oneThirdColumn: {
    width: '33%',
  },
})
