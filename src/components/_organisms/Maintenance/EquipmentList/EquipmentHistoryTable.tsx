import React, { useMemo } from 'react'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { useLicenseeSettings } from '@src/hooks/useLicenseeSettings'
import { MaintenanceTaskCompleted } from '@src/shared-state/VesselMaintenance/maintenanceTasksCompleted'
import { formatDateShort, formatShortTimeDurationHrsMinsView } from '@src/lib/datesAndTime'
import { formatValue, renderCamelCase } from '@src/lib/util'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconCalendar, SeaTableIconPerson } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { View } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

interface EquipmentHistoryTableProps {
  selectedEquipment: Equipment
}

export function EquipmentHistoryTable({ selectedEquipment }: EquipmentHistoryTableProps) {
  const { hasTimeTrackingEnabled } = useLicenseeSettings()

  const maintenanceTasksCompleted = sharedState.maintenanceTasksCompleted.use(!!selectedEquipment)

  const associatedTasksCompleted = useMemo(() => {
    if (!selectedEquipment || !maintenanceTasksCompleted?.all) return []

    return maintenanceTasksCompleted.all
      .filter(taskCompleted => taskCompleted.equipmentId === selectedEquipment.id)
      .sort((a, b) => b.whenCompleted - a.whenCompleted)
  }, [selectedEquipment, maintenanceTasksCompleted])

  return (
    <View>
      <View style={{ paddingHorizontal: 10, paddingVertical: 10 }}>
        <SeaTypography variant="cardTitle">History</SeaTypography>
      </View>
      <SeaTable
        columns={buildColumn(hasTimeTrackingEnabled)}
        rows={buildRow(associatedTasksCompleted, () => alert('TODO View Task'))}
        style={{
          marginBottom: 50,
        }}
      />
    </View>
  )
}

const buildRow = (items: MaintenanceTaskCompleted[], onPress: (item: MaintenanceTaskCompleted) => void) => {
  return items.map(item => ({
    data: item,
    onPress: (item: MaintenanceTaskCompleted) => onPress(item),
  }))
}

const buildColumn = (hasTimeTrackingEnabled: boolean) => {
  return [
    {
      label: 'Date',
      icon: () => <SeaTableIconCalendar />,
      value: item => formatDateShort(item.whenCompleted),
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Task',
      value: item => formatValue(item.task),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Notes',
      value: item => formatValue(item.notes),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Type',
      value: item => formatValue(item.type === 'unscheduled' ? 'Job' : renderCamelCase(item.type)),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Completed By',
      icon: () => <SeaTableIconPerson />,
      value: item => renderFullNameForUserId(item.completedBy),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
          name: 'Completed',
        },
      },
    },
    ...(hasTimeTrackingEnabled
      ? [
          {
            label: 'Actual Time',
            value: (item: MaintenanceTaskCompleted) =>
              formatValue(item.actualTime ? formatShortTimeDurationHrsMinsView(item.actualTime) : '-'),
            compactModeOptions: {
              label: {
                show: true,
                valueWidth: CompactValueWidth.Medium,
              },
            },
          },
        ]
      : []),
    {
      label: '',
      width: 60,
      render: item => <SeaTableImage files={item.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
  ] as SeaTableColumn<MaintenanceTaskCompleted>[]
}
