import React, { useMemo } from 'react'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { SparePart } from '@src/shared-state/VesselMaintenance/spareParts'
import { formatValue } from '@src/lib/util'
import { CategoriesData, renderCategoryName } from '@src/lib/categories'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconLocation } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { View } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

interface SparePartsTableProps {
  selectedEquipment: Equipment
}

export function SparePartsTable({ selectedEquipment }: SparePartsTableProps) {
  const vesselLocations = sharedState.vesselLocations.use(!!selectedEquipment)
  const spareParts = sharedState.spareParts.use(!!selectedEquipment)

  const equipmentSpareParts = useMemo(() => {
    if (!selectedEquipment || !spareParts?.all) return []

    return spareParts.all.filter(sparePart => sparePart.equipmentIds?.some(id => id === selectedEquipment.id))
  }, [selectedEquipment, spareParts])

  return (
    <View>
      <View style={{ paddingHorizontal: 10, paddingVertical: 10 }}>
        <SeaTypography variant="cardTitle">List</SeaTypography>
      </View>
      <SeaTable
        columns={buildColumn(vesselLocations)}
        rows={buildRow(equipmentSpareParts, () => alert('TODO View Task'))}
        style={{
          marginBottom: 50,
        }}
      />
    </View>
  )
}

const buildRow = (items: SparePart[], onPress: (item: SparePart) => void) => {
  return items.map(item => ({
    data: item,
    onPress: (item: SparePart) => onPress(item),
  }))
}

const buildColumn = (vesselLocations?: CategoriesData) => {
  return [
    {
      label: '',
      width: 60,
      render: item => <SeaTableImage files={item.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },

    {
      label: 'Item',
      value: item => formatValue(item.item),
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Quantity',
      value: item => formatValue(item.quantity),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
    {
      label: 'Location',
      icon: () => <SeaTableIconLocation />,
      value: item => formatValue(renderCategoryName(item.locationId, vesselLocations)),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
    {
      label: 'Part #',
      value: item => formatValue(item.partNum),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
  ] as SeaTableColumn<SparePart>[]
}
