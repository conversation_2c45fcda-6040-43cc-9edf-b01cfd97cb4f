import { View } from 'react-native'
import React, { useMemo, useState } from 'react'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'

import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { usePermission } from '@src/hooks/usePermission'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { sharedState } from '@src/shared-state/shared-state'
import { useGlobalSearchParams } from 'expo-router'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { TaskHistoryTable } from './TaskHistoryTable'
import { EquipmentHistoryTable } from './EquipmentHistoryTable'
import { SparePartsTable } from './SparePartsTable'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { renderCategoryName } from '@src/lib/categories'
import { formatValue } from '@src/lib/util'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { getFileNameWithExtension } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { ScrollView } from 'react-native-gesture-handler'
import { EditEquipmentListDrawer } from './EditEquipmentListDrawer'
import { EditJobListDrawer } from '../JobList/EditJobListDrawer'
import { ModifyMaintenanceScheduleDrawer } from '../MaintenanceSchedule/ModifyMaintenanceScheduleDrawer'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { ViewEquipmentSection } from '@src/components/_organisms/Equipment/ViewEquipmentSection'

const DESKTOP_ITEMS_WIDTH = '100%'

type TabValues = 'task-history' | 'equipment-history' | 'spare-parts'

const MaintenanceDrawerPermissions = {
  maintenanceSchedule: { level: permissionLevels.VIEW },
  maintenanceHistory: { level: permissionLevels.VIEW },
  sparePartsList: { level: permissionLevels.VIEW },
}

export function ViewEquipmentList() {
  const equipment = sharedState.equipment.use()

  const modulePermissions = usePermission<typeof MaintenanceDrawerPermissions>({
    modules: MaintenanceDrawerPermissions,
  })
  const { equipmentId } = useGlobalSearchParams()
  const { isMobileWidth, isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  const [activeTab, setActiveTab] = useState<TabValues>('task-history')
  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)
  const [isVisibleJobListDrawer, setIsVisibleJobListDrawer] = useState(false)
  const [isVisibleScheduleDrawer, setIsVisibleScheduleDrawer] = useState(false)

  const selectedEquipment = useMemo(() => {
    return equipment?.byId[equipmentId as string]
  }, [equipment, equipmentId])

  const uploadedFiles = useMemo(() => {
    if (!selectedEquipment?.files) return []

    return selectedEquipment?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('TODO')} />],
    })) as MediaCardFile[]
  }, [selectedEquipment])

  return (
    <RequirePermissions role="maintenanceSchedule" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={{ flex: 1 }}>
        <SeaPageCard
          secondaryActionButton={
            activeTab === 'task-history'
              ? [
                  <SeaEditButton key={'Edit'} onPress={() => setIsVisibleEditDrawer(true)} />,
                  <RequirePermissions role="maintenanceSchedule" level={permissionLevels.CREATE} key={'Add Job'}>
                    <SeaAddButton
                      label={isMobileWidth ? 'Job' : 'Job List Task'}
                      onPress={() => setIsVisibleJobListDrawer(true)}
                      variant={SeaButtonVariant.Primary}
                    />
                  </RequirePermissions>,
                  <RequirePermissions role="maintenanceSchedule" level={permissionLevels.CREATE} key={'Add Schedule'}>
                    <SeaAddButton
                      label="Scheduled Task"
                      onPress={() => setIsVisibleScheduleDrawer(true)}
                      variant={SeaButtonVariant.Primary}
                    />
                  </RequirePermissions>,
                ]
              : ([<SeaEditButton key={'Edit'} onPress={() => setIsVisibleEditDrawer(true)} />] as [React.ReactElement])
          }
          subNav={[
            ...(modulePermissions.maintenanceHistory
              ? [
                  {
                    title: 'Task History',
                    onPress: () => setActiveTab('task-history'),
                    isActive: activeTab === 'task-history',
                  },
                  {
                    title: 'Equipment History',
                    onPress: () => setActiveTab('equipment-history'),
                    isActive: activeTab === 'equipment-history',
                  },
                ]
              : []),
            ...(modulePermissions.sparePartsList
              ? [
                  {
                    title: 'Spare Parts',
                    onPress: () => setActiveTab('spare-parts'),
                    isActive: activeTab === 'spare-parts',
                  },
                ]
              : []),
          ]}
          titleComponent={<SeaPageCardTitle title={selectedEquipment?.equipment} files={selectedEquipment?.files} />}>
          <SeaPageCardContentSection>
            <ViewEquipmentSection equipment={selectedEquipment} />
          </SeaPageCardContentSection>
        </SeaPageCard>

        {/* TODO: Move them to their on routes */}
        {activeTab === 'task-history' && selectedEquipment && (
          <TaskHistoryTable selectedEquipment={selectedEquipment} />
        )}
        {activeTab === 'equipment-history' && selectedEquipment && (
          <EquipmentHistoryTable selectedEquipment={selectedEquipment} />
        )}
        {activeTab === 'spare-parts' && selectedEquipment && <SparePartsTable selectedEquipment={selectedEquipment} />}
      </ScrollView>
      {isVisibleEditDrawer && (
        <EditEquipmentListDrawer
          equipmentId={selectedEquipment?.id}
          visible={isVisibleEditDrawer}
          onClose={() => setIsVisibleEditDrawer(false)}
          type="edit"
        />
      )}

      {isVisibleJobListDrawer && (
        <EditJobListDrawer
          equipmentId={selectedEquipment?.id}
          visible={isVisibleJobListDrawer}
          onClose={() => setIsVisibleJobListDrawer(false)}
          type="create"
        />
      )}

      {isVisibleScheduleDrawer && (
        <ModifyMaintenanceScheduleDrawer
          equipmentId={selectedEquipment?.id}
          visible={isVisibleScheduleDrawer}
          onClose={() => setIsVisibleScheduleDrawer(false)}
          mode="create"
        />
      )}
    </RequirePermissions>
  )
}
