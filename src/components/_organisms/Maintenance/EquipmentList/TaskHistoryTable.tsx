import React, { useMemo } from 'react'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { sharedState } from '@src/shared-state/shared-state'
import { ScheduledMaintenanceTask } from '@src/shared-state/VesselMaintenance/maintenanceSchedule'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { formatValue } from '@src/lib/util'
import { formatDateShort, formatInterval, formatShortTimeDurationHrsMinsView, warnDays } from '@src/lib/datesAndTime'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { useLicenseeSettings } from '@src/hooks/useLicenseeSettings'
import { Text, View } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { Job } from '@src/shared-state/VesselMaintenance/jobs'
import {
  SeaTableIconCalendar,
  SeaTableIconInterval,
  SeaTableIconPerson,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

interface TaskHistoryTableProps {
  selectedEquipment: Equipment
}

export function TaskHistoryTable({ selectedEquipment }: TaskHistoryTableProps) {
  const scheduledMaintenanceTasks = sharedState.scheduledMaintenanceTasks.use(!!selectedEquipment)
  const jobs = sharedState.jobs.use(!!selectedEquipment)

  const { hasTimeTrackingEnabled } = useLicenseeSettings()

  const associatedScheduledTasks = useMemo(() => {
    if (!selectedEquipment || !scheduledMaintenanceTasks?.byId) return undefined

    const tasks = Object.keys(scheduledMaintenanceTasks.byId).reduce((acc, key) => {
      if (scheduledMaintenanceTasks.byId[key]?.equipmentId === selectedEquipment.id) {
        acc.push(scheduledMaintenanceTasks.byId[key])
      }
      return acc
    }, [] as ScheduledMaintenanceTask[])

    return tasks.sort((a, b) => {
      if (!a.task && !b.task) return 0
      if (!a.task) return 1
      if (!b.task) return -1

      return a.task.localeCompare(b.task)
    })
  }, [selectedEquipment, scheduledMaintenanceTasks])

  const associatedJobList = useMemo(() => {
    if (!selectedEquipment || !jobs?.byId) return undefined

    const activeJobs = Object.keys(jobs.byId.active).reduce((acc, key) => {
      if (jobs.byId.active[key]?.equipmentId === selectedEquipment.id) {
        acc.push(jobs.byId.active[key])
      }
      return acc
    }, [] as Job[])

    return activeJobs.sort((a, b) => {
      if (!a.task && !b.task) return 0
      if (!a.task) return 1
      if (!b.task) return -1

      return a.task.localeCompare(b.task)
    })
  }, [selectedEquipment, jobs])

  return (
    <View>
      <View style={{ paddingHorizontal: 10, paddingVertical: 10 }}>
        <SeaTypography variant="cardTitle">Scheduled Task</SeaTypography>
      </View>
      <SeaTable
        columns={buildScheduleTaskColumns(hasTimeTrackingEnabled) as SeaTableColumn<Job | ScheduledMaintenanceTask>[]}
        rows={buildRow(associatedScheduledTasks ?? [], () => alert('TODO View Task'))}
        style={{
          marginBottom: 10,
        }}
      />

      <View style={{ paddingHorizontal: 10, paddingVertical: 10 }}>
        <SeaTypography variant="cardTitle">Active Job List Item</SeaTypography>
      </View>
      <SeaTable
        columns={buildJobsColumns(hasTimeTrackingEnabled)}
        rows={buildRow(associatedJobList ?? [], () => alert('TODO Active job List Task'))}
        style={{
          marginBottom: 50,
        }}
      />
    </View>
  )
}

const buildRow = <T extends ScheduledMaintenanceTask | Job>(items: T[], onPress: (item: T) => void) => {
  return items.map(item => ({
    data: item,
    onPress: (item: T) => onPress(item),
  }))
}

const buildScheduleTaskColumns = (hasTimeTrackingEnabled: boolean) => {
  return [
    {
      label: 'Task',
      value: item => formatValue(item.task),
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Interval',
      icon: () => <SeaTableIconInterval />,
      value: (item, isCompactView) => {
        let intervalText = ''

        if (item.intervalType === 'weekMonth' || item.intervalType === 'weekMonthAndHours') {
          intervalText += formatInterval(item.intervalWeekMonth)
        }

        if (item.intervalType === 'weekMonthAndHours') {
          intervalText += '\n' // Equivalent to <br /> in text format
        }

        if (item.intervalType === 'engineHours' || item.intervalType === 'weekMonthAndHours') {
          if (isCompactView) {
            intervalText += ` - (${item.intervalEngineHours} Hours)`
          } else {
            intervalText += `${item.intervalEngineHours} Hours`
          }
        }

        return intervalText
      },
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Next Due',
      icon: () => <SeaTableIconCalendar />,
      value: (item, isCompactView) => {
        let nextDueText = ''

        if (item.intervalType === 'weekMonth' || item.intervalType === 'weekMonthAndHours') {
          nextDueText += formatDateShort(item.dateDue)
        }

        if (item.intervalType === 'weekMonthAndHours') {
          nextDueText += '\n' // Equivalent to <br /> in text format
        }

        if (item.intervalType === 'engineHours' || item.intervalType === 'weekMonthAndHours') {
          if (isCompactView) {
            nextDueText += ` - (${item.engineHoursDue} Hours)`
          } else {
            nextDueText += `${item.engineHoursDue} Hours`
          }
        }

        return nextDueText
      },
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    ...(hasTimeTrackingEnabled
      ? [
          {
            label: 'Est Time',
            value: (item: ScheduledMaintenanceTask) =>
              formatValue(item.estimatedTime ? formatShortTimeDurationHrsMinsView(item.estimatedTime) : '-'),
            compactModeOptions: {
              label: {
                show: true,
                valueWidth: CompactValueWidth.Medium,
              },
            },
          },
        ]
      : []),
    {
      label: 'Status',
      render: (item, isCompactView) =>
        item.useHours ? (
          <SeaStatusPill label={'TODO'} variant={SeaStatusType.Ok} compact={isCompactView} />
        ) : (
          <WhenDueStatus
            whenDue={item.dateDue!}
            warnDaysThreshold={warnDays.maintenanceSchedule[0]}
            hasFault={item.hasFault ?? false}
            compact={isCompactView}
          />
        ),
      compactModeOptions: {
        rowPosition: CompactRowPosition.TopRightCorner,
      },
    },
  ] as SeaTableColumn<ScheduledMaintenanceTask>[]
}

const buildJobsColumns = (hasTimeTrackingEnabled: boolean) => {
  return [
    {
      label: 'Job',
      value: item => formatValue(item.task),
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Priority',
      value: item => item?.priority && item?.priority.charAt(1).toUpperCase() + item?.priority.slice(2),
      compactModeOptions: {
        rowPosition: CompactRowPosition.TopRightCorner,
      },
    },
    {
      label: 'Date Added',
      icon: () => <SeaTableIconCalendar />,
      value: item => formatDateShort(item.whenAdded),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    ...(hasTimeTrackingEnabled
      ? [
          {
            label: 'Est Time',
            value: (item: ScheduledMaintenanceTask) =>
              formatValue(item.estimatedTime ? formatShortTimeDurationHrsMinsView(item.estimatedTime) : '-'),
          },
        ]
      : []),
    {
      label: 'Assigned To',
      icon: () => <SeaTableIconPerson />,
      value: item => formatValue(item.assignedTo?.name) + (item.assignedTo?.contactId ? ' (Contact)' : ''),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
  ] as SeaTableColumn<Job>[]
}
