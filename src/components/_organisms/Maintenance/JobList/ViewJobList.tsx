import React, { useMemo, useState } from 'react'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { ScrollView } from 'react-native-gesture-handler'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { useGlobalSearchParams } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { getFileNameWithExtension } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatCurrency, formatValue } from '@src/lib/util'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaCheckButton } from '@src/components/_molecules/IconButtons/SeaCheckButton'
import { renderCategoryName } from '@src/lib/categories'
import { formatDate, formatEmailReminder } from '@src/lib/datesAndTime'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { EditJobListDrawer } from './EditJobListDrawer'
import { CompleteTaskDrawer } from './CompleteTaskDrawer'
import { UpdateTaskDrawer } from './UpdateTaskDrawer'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'
import { ViewEquipmentSection } from '@src/components/_organisms/Equipment/ViewEquipmentSection'

const DESKTOP_ITEMS_WIDTH = '100%'

export function ViewJobList() {
  const jobs = sharedState.jobs.use()
  const licenseeSettings = sharedState.licenseeSettings.use()

  const { styles } = useStyles(styleSheet)
  const { isMobileWidth, isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const { jobId } = useGlobalSearchParams()

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)
  const [isVisibleCompleteDrawer, setIsVisibleCompleteDrawer] = useState(false)
  const [isVisibleUpdateDrawer, setIsVisibleUpdateDrawer] = useState(false)

  const selectedItem = useMemo(() => {
    if (!jobId || !jobs) {
      return undefined
    }

    return jobs.byId.all[jobId as string]
    // return jobs.byId.all[jobId as string];
  }, [jobId, jobs])

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return []

    return selectedItem?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('TODO')} />],
    })) as MediaCardFile[]
  }, [selectedItem])

  return (
    <RequirePermissions role="jobList" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container}>
        <SeaPageCard
          secondaryActionButton={[
            <SeaEditButton key={'Edit'} onPress={() => setIsVisibleEditDrawer(true)} />,
            <SeaDeleteButton key={'Delete'} onPress={() => alert('TODO: Delete Completed Schedule')} />,
            <SeaButton
              label="Update Task"
              onPress={() => setIsVisibleUpdateDrawer(true)}
              variant={SeaButtonVariant.Tertiary}
              key={'Update Task'}
            />,
          ]}
          primaryActionButton={
            <SeaCheckButton
              label={isMobileWidth ? 'Complete' : 'Mark Job as Complete'}
              onPress={() => setIsVisibleCompleteDrawer(true)}
              variant={SeaButtonVariant.Primary}
            />
          }
          titleComponent={<SeaPageCardTitle title={'Job Item'} files={selectedItem?.files} />}>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction={'column'}
                gap={isDesktopWidth ? 20 : 10}
                align="start"
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue label={'Task'} value={selectedItem?.task} />

                  <SeaLabelValue label={'Job #'} value={formatValue(selectedItem?.jobNum)} />
                </SeaStack>
                <SeaStack direction="column" align={'start'} gap={isDesktopWidth ? 5 : 0} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} direction={'column'}>
                    <SeaLabelValue
                      label={'Description'}
                      value={formatValue(selectedItem?.description)}
                      layout={'vertical'}
                    />
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>

          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction={'column'}
                gap={isDesktopWidth ? 20 : 10}
                align="start"
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" gap={isDesktopWidth ? 5 : 0} align={'start'} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Priority'}
                      value={
                        selectedItem?.priority &&
                        selectedItem?.priority.charAt(1).toUpperCase() + selectedItem?.priority.slice(2)
                      }
                    />

                    <SeaLabelValue
                      iconOptions={{
                        icon: 'person',
                      }}
                      showIcon
                      label={'Assigned To'}
                      value={`${formatValue(selectedItem?.assignedTo?.name)} ${selectedItem?.assignedTo?.contactId ? ' (Contact)' : ''}`}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={'50%'}>
                    <SeaLabelValue label={'Job Tags'} value={formatValue(selectedItem?.tags?.join(', '))} />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{
                        icon: 'calendar_month',
                      }}
                      showIcon
                      label={'Due Date'}
                      value={selectedItem?.dateDue ? formatDate(selectedItem?.dateDue) : '-'}
                    />

                    <SeaLabelValue
                      iconOptions={{
                        icon: 'mail',
                      }}
                      showIcon
                      label={'Email Reminder'}
                      value={formatValue(formatEmailReminder(selectedItem?.emailReminder))}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{
                        icon: 'attach_money',
                      }}
                      showIcon
                      label={'Estimated Job Cost'}
                      value={
                        selectedItem?.estimatedCost
                          ? formatCurrency(selectedItem.estimatedCost, 2, licenseeSettings?.region)
                          : '-'
                      }
                    />

                    <SeaLabelValue
                      iconOptions={{
                        icon: 'person',
                      }}
                      label={'Added By'}
                      showIcon
                      value={selectedItem?.addedBy ? formatValue(renderFullNameForUserId(selectedItem.addedBy)) : '-'}
                    />
                  </SeaStack>
                </SeaStack>
                {uploadedFiles.length > 0 && <SeaMedia type="manuals" title="Files" files={uploadedFiles} />}
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>

          <SeaEmptyDivider />
          <SeaPageCardContentSection>
            <ViewEquipmentSection equipment={selectedItem?.equipment} />
          </SeaPageCardContentSection>
        </SeaPageCard>
      </ScrollView>
      {isVisibleEditDrawer && (
        <EditJobListDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          jobId={Array.isArray(jobId) ? jobId[0] : jobId}
          visible={isVisibleEditDrawer}
          type="edit"
        />
      )}
      {isVisibleCompleteDrawer && (
        <CompleteTaskDrawer
          onClose={() => setIsVisibleCompleteDrawer(false)}
          jobId={Array.isArray(jobId) ? jobId[0] : jobId}
          visible={isVisibleCompleteDrawer}
        />
      )}
      {isVisibleUpdateDrawer && (
        <UpdateTaskDrawer
          onClose={() => setIsVisibleUpdateDrawer(false)}
          jobId={Array.isArray(jobId) ? jobId[0] : jobId}
          visible={isVisibleUpdateDrawer}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  content: {
    marginBottom: 20,
  },
}))
