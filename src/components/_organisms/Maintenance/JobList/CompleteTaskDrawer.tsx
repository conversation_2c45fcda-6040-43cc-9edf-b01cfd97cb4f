import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { sharedState } from '@src/shared-state/shared-state'
import { useFormik } from 'formik'
import { DateTime } from 'luxon'
import React, { useMemo, useState } from 'react'
import { View } from 'react-native'

import { SeaFile } from '@src/lib/fileImports'
import Yup from '@src/lib/yup'

interface CompleteTaskDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  jobId: string
  editHistory?: boolean
}

const validationSchema = Yup.object({
  task: Yup.string().max(500).required(),
  description: Yup.string().max(5000),
  whenCompleted: Yup.date().required(),
  actualTime: Yup.string(),
  notes: Yup.string().max(5000),
  maintenanceTags: Yup.array().of(Yup.string()),
})

export const CompleteTaskDrawer = ({
  visible,
  onClose,
  style,
  jobId,
  editHistory = false,
}: CompleteTaskDrawerProps) => {
  const jobs = sharedState.jobs.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeSettings = sharedState.licenseeSettings.use()

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const selectedJob = useMemo(() => {
    if (!jobId || !jobs) {
      return undefined
    }

    return jobs.byId.all[jobId as string]
  }, [jobId, jobs])

  const handleSubmit = async (values: any) => {
    console.debug('CompleteTaskDrawer:handleSubmit', values)
    console.debug('CompleteTaskDrawer:handleSubmit:files', files)
    alert('TODO: Complete Task')
  }

  const initialValues = useMemo(() => {
    //TODO: Edit history
    if (editHistory) {
      return {}
    } else {
      return {
        vesselId: vesselId,
        jobId: selectedJob?.id,
        equipmentId: selectedJob?.equipmentId,
        location: selectedJob?.location,
        isCritical: selectedJob?.isCritical ?? false,
        task: selectedJob?.task,
        description: selectedJob?.description,
        notes: '',
        //TODO: Add spare parts logic
        spareParts: selectedJob?.spareParts,
        jobNum: selectedJob?.jobNum,
        actualTime: undefined,
        whenCompleted: DateTime.now().toISO(),
      }
    }
  }, [editHistory])

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  return (
    <SeaDrawer
      title={editHistory ? `TODO: Edit Completed Task: ${selectedJob?.task}` : `Complete Task: ${selectedJob?.task}`}
      visible={visible}
      onClose={onClose}
      style={style}
      level={1}
      primaryAction={
        !editHistory ? (
          <SeaButton
            variant={SeaButtonVariant.Primary}
            onPress={formik.handleSubmit}
            label={editHistory ? 'Save Changes' : 'Submit'}
          />
        ) : undefined
      }>
      <SeaStack direction="column" gap={20} style={{ width: '100%' }}>
        <SeaStack
          direction={'column'}
          justify={'center'}
          align={'start'}
          style={{
            width: '100%',
          }}>
          <SeaDateTimeInput
            value={
              typeof formik.values.whenCompleted === 'string'
                ? DateTime.fromISO(formik.values.whenCompleted)
                : (formik.values.whenCompleted ?? DateTime.now())
            }
            label={'When Completed'}
            onChange={value => formik.setFieldValue('whenCompleted', value)}
            type={'date'}
            style={{
              width: '100%',
            }}
            hasError={Boolean(errors.whenCompleted && touched.whenCompleted)}
            errorText={errors.whenCompleted}
          />
        </SeaStack>

        {licenseeSettings?.hasMaintenanceTaskTime && (
          <SeaStack
            direction={'column'}
            justify={'center'}
            align={'start'}
            style={{
              width: '100%',
            }}>
            <SeaDateTimeInput
              value={
                typeof formik.values.actualTime === 'string'
                  ? DateTime.fromISO(formik.values.actualTime)
                  : (formik.values.actualTime ?? DateTime.now())
              }
              label={'Actual Time'}
              onChange={value => formik.setFieldValue('actualTime', value)}
              type={'datetime'}
              style={{
                width: '100%',
              }}
              hasError={Boolean(errors.actualTime && touched.actualTime)}
              errorText={errors.actualTime}
            />
          </SeaStack>
        )}

        <SeaStack
          direction={'column'}
          justify={'center'}
          align={'start'}
          style={{
            width: '100%',
          }}>
          <SeaTextInput
            label={'Notes'}
            multiLine={true}
            value={formik.values.notes ?? ''}
            onChangeText={formik.handleChange('notes')}
            hasError={Boolean(errors.notes && touched.notes)}
            errorText={errors.notes}
          />
        </SeaStack>

        <SeaFileUploader initialFiles={selectedJob?.files} files={files} setFiles={setFiles} />

        {/* TODO: Add Spareparts */}
      </SeaStack>
    </SeaDrawer>
  )
}
