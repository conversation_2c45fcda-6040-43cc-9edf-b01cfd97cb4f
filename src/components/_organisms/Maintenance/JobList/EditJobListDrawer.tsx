import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { StyleSheet } from 'react-native'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { ScrollView } from 'react-native-gesture-handler'
import { Job, jobPriorities } from '@src/shared-state/VesselMaintenance/jobs'
import { cleanupStringArray, formatCurrency, formatDp, toFloat } from '@src/lib/util'
import {
  formatShortTimeDurationHrsMinsOnly,
  hours24ToMillis,
  makeDateTime,
  subtractInterval,
  toMillis,
} from '@src/lib/datesAndTime'
import { FormikValues, useFormik } from 'formik'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaTagsInput } from '@src/components/_atoms/_inputs/SeaTagsInput/SeaTagsInput'
import { SeaSelectCrewOrContact } from '@src/components/_atoms/SeaSelectCrewOrContact/SeaSelectCrewOrContact'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { sharedState } from '@src/shared-state/shared-state'
import { renderFullName } from '@src/shared-state/Core/users'
import { SeaEquipment } from '../../SeaEquipment/SeaEquipment'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { SeaEmailReminderDropdown } from '@src/components/_atoms/SeaEmailReminderDropdown/SeaEmailReminderDropdown'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { UpdateJobListDto, UpdateJobListUseCase } from '@src/domain/use-cases/maintenance/UpdateJoblistUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { renderCategoryName } from '@src/lib/categories'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { CreateJobListDto, CreateJobListUseCase } from '@src/domain/use-cases/maintenance/CreateJobListUseCase'
import { SeaCurrencyInput } from '@src/components/_atoms/_inputs/SeaCurrencyInput/SeaCurrencyInput'
import { SeaFile } from '@src/lib/fileImports'
import Yup from '@src/lib/yup'

const validationSchema = Yup.object({
  task: Yup.string().max(500).required(),
  description: Yup.string().max(5000),
  priority: Yup.string().required(),
  assignedTo: Yup.object().nullable(),
  jobTags: Yup.array().of(Yup.string()),
  emailNotifications: Yup.array().of(Yup.string()),
  maintenanceTags: Yup.array().of(Yup.string()),
})

export interface EditJobListDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  jobId?: string
  type: 'create' | 'edit'
  equipmentId?: string
}

export const EditJobListDrawer = ({ jobId, visible, onClose, style, type, equipmentId }: EditJobListDrawerProps) => {
  const jobs = sharedState.jobs.use(visible)
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const users = sharedState.users.use(visible)
  const userDetails = sharedState.userDetails.use(visible)
  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const vesselSystems = sharedState.vesselSystems.use(visible)

  const user = sharedState.user.current

  // Hooks
  const services = useServiceContainer()
  const [files, setFiles] = useState<SeaFile[]>([])
  const [equipmentData, setEquipmentData] = useState<Partial<Equipment>>({})

  const selectedJob = useMemo(() => {
    if (!jobId || !jobs) {
      return undefined
    }

    return jobs.byId.all[jobId as string]
    // return jobs.byId.all[jobId as string];
  }, [jobId, jobs])

  const initialValues = useMemo(() => {
    return {
      task: selectedJob?.task ?? '',
      description: selectedJob?.description ?? '',
      priority: selectedJob?.priority ?? '',
      jobTags: selectedJob?.tags ?? [],
      assignedTo: selectedJob?.assignedTo ?? {
        userId: '',
        name: '',
      },
      dueDate: selectedJob?.dateDue ?? '',
      emailReminder: selectedJob?.emailReminder ?? '',
      estimatedCost: selectedJob?.estimatedCost ?? '',
      estimatedTime: selectedJob?.estimatedTime ? formatShortTimeDurationHrsMinsOnly(selectedJob.estimatedTime) : '',
      maintenanceTags: selectedJob?.maintenanceTags ?? [],
      emailNotifications: [] as string[],
    }
  }, [selectedJob])

  useEffect(() => {
    if (!selectedJob) return

    setEquipmentData({
      id: selectedJob?.equipmentId,
      locationId: selectedJob?.locationId,
    })
  }, [selectedJob])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId || !vessel) {
        console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
        return
      }

      let dateToRemind: string | undefined = undefined
      if (values.dateDue && values.emailReminder) {
        const result = subtractInterval(values.dateDue, values.emailReminder)
        dateToRemind = result ? (result.toISODate() ?? undefined) : undefined
      }

      const newMaintenanceTags = values.maintenanceTags.reduce((acc: string[], tag: string) => {
        if (!vessel?.possibleMaintenanceTags?.includes(tag)) {
          acc.push(tag)
        }

        return acc
      }, [] as string[])

      const newTags = values.jobTags.reduce((acc: string[], tag: string) => {
        if (!vessel?.possibleTags?.includes(tag)) {
          acc.push(tag)
        }

        return acc
      }, [] as string[])

      const commonDto = {
        vesselId,
        licenseeId,
        task: values.task,
        description: values.description,
        priority: values.priority,
        assignedTo: {
          userId: values.assignedTo.userId ?? undefined,
          contactId: values.assignedTo.contactId ?? undefined,
          name: values.assignedTo.name ?? undefined,
        },
        tags: values.jobTags ? cleanupStringArray(values.jobTags) : undefined,
        maintenanceTags: values.maintenanceTags ? cleanupStringArray(values.maintenanceTags) : undefined,
        dateDue: values.dueDate ? toMillis(values.dueDate) : undefined,
        equipmentId: equipmentData.id ?? undefined,
        locationId: equipmentData.locationId ?? undefined,
        emailReminder: values.emailReminder ?? undefined,

        dateToRemind,
        files: files,

        estimatedTime: hours24ToMillis(values.estimatedTime),
        newMaintenanceTags,
        newTags,

        jobNum: selectedJob?.jobNum ?? undefined,
        systemName: renderCategoryName(equipmentData.systemId, vesselSystems),
        equipmentName: equipmentData.equipment,
        locationName: renderCategoryName(equipmentData.locationId, vesselSystems),
        isCritical: equipmentData?.isCritical,
        userName: renderFullName(user),
        estimatedCost: toFloat(values.estimatedCost, undefined),
      }

      if (type === 'create') {
        const dto: CreateJobListDto = {
          ...commonDto,
        }

        const createJobList = services.get(CreateJobListUseCase)

        createJobList
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating Job List\n ${err.message}`))
      } else {
        const dto: UpdateJobListDto = {
          ...commonDto,
          id: selectedJob?.id,
        }

        const updateJobList = services.get(UpdateJobListUseCase)

        updateJobList
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating Job List\n ${err.message}`))
      }
    },
    [type, vesselId, licenseeId, userId, vessel, selectedJob, equipmentData, vesselSystems, files]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  const handleEmailToSelect = useCallback(
    async (action: CheckBoxActions, changedValue: string) => {
      switch (action) {
        case CheckBoxActions.SELECT: {
          const newIds = [...formik.values.emailNotifications]
          newIds.push(changedValue)
          formik.setFieldValue('emailNotifications', newIds)
          return
        }
        case CheckBoxActions.DESELECT: {
          const newIds = formik.values.emailNotifications.filter((id: string) => id !== changedValue)
          formik.setFieldValue('emailNotifications', newIds)
          return
        }

        default:
          return
      }
    },
    [formik.values.emailNotifications]
  )

  const emailToOptions = useMemo(() => {
    if (!users || !formik.values.priority) return { options: [], userIds: [] }

    const formattedPriority = 'jobs' + formik.values.priority.charAt(1).toUpperCase() + formik.values.priority.slice(2)

    const userIds: string[] = []

    const options = users.all
      .filter(user => {
        const userHasAccess =
          user.id && user.state === 'active' && userDetails?.byId[user.id] && user.vesselIds?.includes(vesselId!)

        return userHasAccess
      })
      .map(user => {
        const details = userDetails?.byId[user.id!]

        let selected = false
        let required = false

        if (
          (details?.emailMe?.includes(formattedPriority) && details?.emailMe.includes('jobsUpdated')) ||
          (!selectedJob && details?.emailMe?.includes('jobsCreated'))
        ) {
          selected = true
          required = true
          userIds.push(user.id!)
          formik.setFieldValue('emailNotifications', [user.id])
        } else if (user.id === formik.values.assignedTo?.userId) {
          selected = true
          userIds.push(user.id!)
          formik.setFieldValue('emailNotifications', [user.id])
        }

        return {
          value: user.id!,
          label: renderFullName(user),
          ...(selected && { selected }),
          ...(required && { required }),
        }
      })

    return { options, userIds }
  }, [vesselId, users, formik.values.priority, formik.values.assignedTo?.userId, selectedJob, userDetails?.byId])
  return (
    <SeaDrawer
      title={type === 'create' ? 'Add New Job' : 'Edit Job'}
      visible={visible}
      onClose={onClose}
      style={style}
      level={2}
      primaryAction={
        <SeaButton
          variant={SeaButtonVariant.Primary}
          onPress={formik.handleSubmit}
          label={type === 'create' ? 'Save New Job' : 'Update Job'}
        />
      }>
      <SeaStack direction={'column'} align="start" gap={20}>
        <SeaStack direction={'column'} gap={20} style={{ width: '100%' }}>
          <SeaTextInput
            label={'Task'}
            value={formik.values.task}
            onChangeText={formik.handleChange('task')}
            hasError={Boolean(errors.task && touched.task)}
            errorText={errors.task}
          />

          <SeaTextInput
            label={'Description'}
            value={formik.values.description}
            multiLine={true}
            onChangeText={formik.handleChange('description')}
            hasError={Boolean(errors.description && touched.description)}
            errorText={errors.description}
          />
        </SeaStack>

        <SeaStack direction="row" gap={8} justify="start" align="start" width={'100%'}>
          <SeaDropdown
            label={'Priority'}
            items={Object.keys(jobPriorities).map(priorityId => ({
              label: jobPriorities[priorityId as keyof typeof jobPriorities],
              value: priorityId,
            }))}
            value={formik.values.priority}
            onSelect={value => formik.setFieldValue('priority', value)}
            hasError={Boolean(errors.priority && touched.priority)}
            errorText={errors.priority}
          />

          <SeaSelectCrewOrContact
            crew={users?.byVesselId[vesselId!]}
            value={formik.values.assignedTo}
            setValue={value =>
              formik.setFieldValue('assignedTo', {
                userId: value?.userId,
                contactId: value?.contactId,
                name: value?.name,
              })
            }
            label={'Assigned to'}
          />
        </SeaStack>
        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaTagsInput
            label={'Job Tags'}
            tags={formik.values?.jobTags ?? []}
            setTags={tags => formik.setFieldValue('jobTags', tags)}
            options={vessel?.possibleTags}
          />
        </SeaStack>

        {/* TODO: To save the Email Notificaiton */}
        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaSelectInput
            label={'Email Notifications'}
            data={emailToOptions.options}
            selectedItemValues={formik.values.emailNotifications}
            onItemSelect={handleEmailToSelect}
          />
        </SeaStack>

        <SeaFileUploader initialFiles={selectedJob?.files} files={files} setFiles={setFiles} />

        <SeaTypography variant="label">TODO: Add more information Accordion</SeaTypography>

        {/* TODO: Edit equipment */}
        <SeaEquipment
          equipmentId={equipmentData.id ?? equipmentId ?? ''}
          locationId={equipmentData.locationId}
          onChange={equipment => setEquipmentData(equipment)}
        />

        <SeaStack direction="row" gap={8} justify="start" align="start" width={'100%'}>
          <SeaTagsInput
            options={vessel?.possibleMaintenanceTags}
            tags={formik.values.maintenanceTags}
            setTags={tags => formik.setFieldValue('maintenanceTags', tags)}
            label={'Maintenance Tags'}
            style={{ flex: 1 }}
          />

          <SeaDateTimeInput
            value={makeDateTime(formik.values.dueDate)}
            onChange={date => formik.setFieldValue('dueDate', date)}
            type={'date'}
            label={'Due Date'}
            style={{ flex: 1 }}
          />

          <SeaEmailReminderDropdown
            label="Email Reminder"
            value={formik.values.emailReminder}
            onSelect={value => formik.setFieldValue('emailReminder', value)}
            style={{ flex: 1 }}
          />
        </SeaStack>

        <SeaStack gap={20} style={{ width: '100%' }}>
          <SeaCurrencyInput
            label={'Estimated Cost'}
            value={formik.values.estimatedCost}
            currency={'NZD'}
            onChange={value => formik.setFieldValue('estimatedCost', value)}
            hasError={false}
          />
          {/*<SeaTextInput*/}
          {/*  label={"Estimated Cost"}*/}
          {/*  value={formik.values.estimatedCost}*/}
          {/*  onChangeText={(value) =>*/}
          {/*    formik.setFieldValue("estimatedCost", value)*/}
          {/*  }*/}
          {/*  inputMode={"decimal"}*/}
          {/*  keyboardType={"decimal-pad"}*/}
          {/*  placeholder={"0.00"}*/}
          {/*  icon={<SeaIcon icon={"attach_money"} size={16} />}*/}
          {/*/>*/}
        </SeaStack>
      </SeaStack>
    </SeaDrawer>
  )
}

const styles = StyleSheet.create({})
