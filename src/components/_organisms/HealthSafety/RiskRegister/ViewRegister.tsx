import React, { useState } from 'react'
import { Risk } from '@src/shared-state/HealthSafety/risks'
import { sharedState } from '@src/shared-state/shared-state'
import { ScrollView, StyleSheet, View } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatDate, formatValue, formatVessels } from '@src/lib/util'
import { renderCategoryName } from '@src/lib/categories'
import { RiskReviewed } from '@src/shared-state/HealthSafety/useRisksReviewed'
import { usePageLimiter } from '@src/hooks/usePageLimiter'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { RiskRatingStatus } from '@src/components/_molecules/RiskRatingStatus/RiskRatingStatus'
import { formatInterval } from '@src/lib/datesAndTime'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaCheckButton } from '@src/components/_molecules/IconButtons/SeaCheckButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { useRouter } from 'expo-router'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { canAccessAllVessels, canDelete, permissionLevels } from '@src/shared-state/Core/userPermissions'
import { EditRiskRegister } from '@src/components/_organisms/HealthSafety/RiskRegister/EditRiskRegister'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'
import { useDeviceWidth } from '@src/hooks/useDevice'

export interface ControlType {
  name: string
  description: string
}

interface ViewRegisterProps {
  risk?: Risk
}

const DESKTOP_ITEMS_WIDTH = '100%'
export const ViewRegister = ({ risk }: ViewRegisterProps) => {
  // Shared Data
  const licenseeSettings = sharedState.licenseeSettings.use()
  const vessels = sharedState.vessels.use()
  const today = sharedState.today.use()!
  const riskCategories = sharedState.riskCategories.use()
  // TODO: looks broken
  // const risksReviewed = useRisksReviewed(risk);
  // const links = useItemLinks(selectedRisk?.id);

  // Hooks
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const [editRiskVisible, setEditRiskVisible] = useState(false)

  const { limitTriggerElement, mapArrayWithLimit, resetPageLimit } = usePageLimiter()
  const [showReviewItemModal, setShowReviewItemModal] = useState(false)
  const [historyItemToUpdate, setHistoryItemToUpdate] = useState<RiskReviewed>()
  // TODO: Add Export
  // const [exportType, setExportType] = useState<ExportType>();

  // Render
  if (!risk) {
    return <></>
  }

  return (
    <>
      <ScrollView key={`risk-${risk.id}`} style={styles.container}>
        <SeaPageCard
          secondaryActionButton={[
            <RequirePermissions key="edit-hazardRegister" role="hazardRegister" level={permissionLevels.EDIT}>
              <SeaEditButton key={'Edit'} onPress={() => setEditRiskVisible(true)} />
            </RequirePermissions>,
            // TODO: Add export pdf
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not implemented yet')} />,

            <RequirePermissions key="delete-hazardRegister" role="hazardRegister" level={permissionLevels.EDIT}>
              {canDelete('hazardRegister') && risk?.vesselIds && canAccessAllVessels(risk.vesselIds) && (
                <SeaDeleteButton key={'delete'} onPress={() => alert('This functionality is not implemented yet')} />
              )}
            </RequirePermissions>,
          ]}
          titleComponent={<SeaPageCardTitle title={risk.name || 'Risk Assessment'} files={risk.files} />}>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'directions_boat_filled' }}
                      showIcon={true}
                      label={'Vessel/Facility'}
                      value={formatVessels(risk?.vesselIds, vessels)}
                    />
                    <SeaLabelValue
                      label={'Category'}
                      value={formatValue(renderCategoryName(risk?.categoryId, riskCategories))}
                    />
                  </SeaStack>
                </SeaStack>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Risks associated with this hazard'}
                    value={formatValue(risk?.risks)}
                    style={styles.label}
                    layout={'vertical'}
                  />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '100%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack direction={'row'} align={'start'} gap={2}>
                    <SeaTypography variant={'subtitle'}>Pre Controls Risk</SeaTypography>
                    <SeaIcon icon={'help'} size={20} variant={'sharp'} />
                  </SeaStack>
                  <>
                    <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue
                        label={'Likelihood'}
                        value={
                          (licenseeSettings?.riskRegister.likelihoods[`L${risk.preControls.likelihood}`] as ControlType)
                            ?.name ?? '-'
                        }
                      />
                      <SeaLabelValue
                        label={'Consequence'}
                        value={
                          (
                            licenseeSettings?.riskRegister.consequences[
                              `C${risk.preControls.consequence}`
                            ] as ControlType
                          )?.name ?? '-'
                        }
                      />

                      <SeaLabelValue
                        label={'Risk Rating'}
                        value={
                          risk.preControls?.likelihood ? (
                            <RiskRatingStatus
                              likelihoodId={risk.preControls.likelihood}
                              consequenceId={risk.preControls.consequence}
                            />
                          ) : (
                            '-'
                          )
                        }
                      />
                    </SeaStack>
                  </>
                </SeaStack>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue label={'Risk Controls'} value={formatValue(risk?.controls)} layout={'vertical'} />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '100%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack direction={'row'} align={'start'} gap={2}>
                    <SeaTypography variant={'subtitle'}>Post Controls Risk</SeaTypography>
                    <SeaIcon icon={'help'} size={20} variant={'sharp'} />
                  </SeaStack>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Likelihood'}
                      value={
                        (licenseeSettings?.riskRegister.likelihoods[`L${risk.postControls.likelihood}`] as ControlType)
                          ?.name ?? '-'
                      }
                    />
                    <SeaLabelValue
                      label={'Consequence'}
                      value={
                        (
                          licenseeSettings?.riskRegister.consequences[
                            `C${risk.postControls.consequence}`
                          ] as ControlType
                        )?.name ?? '-'
                      }
                    />
                    <SeaLabelValue
                      label={'Risk Rating'}
                      value={
                        risk.postControls?.likelihood ? (
                          <RiskRatingStatus
                            likelihoodId={risk.postControls.likelihood}
                            consequenceId={risk.postControls.consequence}
                          />
                        ) : (
                          '-'
                        )
                      }
                    />
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '100%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'person' }}
                      showIcon={true}
                      label={'Person/s Responsible'}
                      value={formatValue(risk.whoResponsible)}
                    />
                    <SeaLabelValue
                      iconOptions={{ icon: 'update' }}
                      showIcon={true}
                      label={'Review Interval'}
                      value={formatValue(formatInterval(risk.interval))}
                    />
                    <SeaLabelValue
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon={true}
                      label={'Review Date'}
                      value={formatDate(risk.dateDue)}
                    />
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
          <SeaEmptyDivider />

          <RequirePermissions role="hazardRegister" level={permissionLevels.EDIT}>
            <SeaPageCardContentSection>
              <SeaStack direction={'row'} align={'start'}>
                <SeaTypography variant={'subtitle'}>Review History</SeaTypography>
                <SeaCheckButton
                  label={'Review Hazard / Risk'}
                  onPress={() => alert('This functionality is not implemented yet')}
                />
              </SeaStack>
            </SeaPageCardContentSection>
          </RequirePermissions>
        </SeaPageCard>
      </ScrollView>

      {editRiskVisible && (
        <EditRiskRegister risk={risk} visible={editRiskVisible} onClose={() => setEditRiskVisible(false)} />
      )}
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    padding: 20,
    width: '100%',
    gap: 20,
  },
  column: {
    flex: 1,
    width: '50%',
  },
  row: {
    width: '100%',
    paddingBottom: '5%',
  },
  label: {
    // flex: 1,
  },
})
