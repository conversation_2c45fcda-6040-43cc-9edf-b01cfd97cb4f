import React, { useMemo, useState } from 'react'
import { Risk } from '@src/shared-state/HealthSafety/risks'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { SeaDrawer } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { StyleSheet } from 'react-native'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { useFormik } from 'formik'
import { preventMultiTap } from '@src/lib/util'
import { addInterval, formatSeaDate, toMillis } from '@src/lib/datesAndTime'
import { RiskCategoryDropdown } from '@src/components/_molecules/RiskCategoryDropdown/RiskCategoryDropdown'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import {
  RiskRatingConsequenceDropdown,
  RiskRatingLikelihoodDropdown,
} from '@src/components/_molecules/RiskRatingDropdown/RiskRatingDropdown'
import { RiskRatingStatus } from '@src/components/_molecules/RiskRatingStatus/RiskRatingStatus'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { DateTime } from 'luxon'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { IntervalDropdown } from '@src/components/_molecules/IntervalDropdown/IntervalDropdown'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { CreateRiskDto, CreateRiskUseCase, RiskDto } from '@src/domain/use-cases/risks/CreateRiskUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { sharedState } from '@src/shared-state/shared-state'
import { UpdateRiskDto, UpdateRiskUseCase } from '@src/domain/use-cases/risks/UpdateRiskUseCase'
import Yup, { notTooOld } from '@src/lib/yup'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaLineBreak } from '@src/components/_atoms/SeaDividers/SeaLineBreak'

import { SeaFile } from '@src/lib/fileImports'

interface EditRiskRegisterProps {
  visible?: boolean
  risk?: Risk
  onClose?: () => void
  isNew?: boolean
}

const validationSchema = Yup.object({
  name: Yup.string().max(500).required(),
  categoryId: Yup.string().max(500).required(),
  risks: Yup.string().max(5000),
  preLikelihood: Yup.number().max(1000).required(),
  preConsequence: Yup.number().max(1000).required(),
  postLikelihood: Yup.number().max(1000).required(),
  postConsequence: Yup.number().max(1000).required(),
  controls: Yup.string().max(5000).required(),
  whoResponsible: Yup.string().max(500),
  interval: Yup.string().max(4).required(),
  dateLastReviewed: Yup.date()
    .max(formatSeaDate())
    .required()
    .min(...notTooOld),
  shouldReportToManagement: Yup.boolean(),
})

export const EditRiskRegister = ({
  visible = false,
  risk,
  onClose = () => {},
  isNew = false,
}: EditRiskRegisterProps) => {
  if (!risk) {
    return <></>
  }
  // Shared Data
  const user = sharedState.user.use()
  const licenseeId = sharedState.licenseeId.use()

  // Hooks
  const [vesselIds, setVesselIds] = useState<string[]>(risk.vesselIds)
  const [files, setFiles] = useState<SeaFile[]>([])

  // Services
  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return {
      vesselIds: risk.vesselIds,
      name: risk.name ?? '',
      categoryId: risk.categoryId ?? '',
      risks: risk.risks ?? '',
      preLikelihood: risk.preControls?.likelihood ?? '',
      preConsequence: risk.preControls?.consequence ?? '',
      postLikelihood: risk.postControls?.likelihood ?? '',
      postConsequence: risk.postControls?.consequence ?? '',
      controls: risk.controls ?? '',
      whoResponsible: risk.whoResponsible ?? '',
      interval: risk.interval ?? '',
      dateLastReviewed: risk.dateLastReviewed ?? '',
      shouldReportToManagement: !!risk.shouldReportToManagement,
    }
  }, [])

  const onSubmit = (values: typeof initialValues) => {
    if (preventMultiTap('riskAssessment')) {
      return
    }

    // TODO: Cater for:
    //  - signature

    const riskDto: RiskDto = {
      vesselIds: vesselIds,
      categoryId: values.categoryId,
      controls: values.controls,
      dateLastReviewed: toMillis(values.dateLastReviewed) ?? '',
      dateDue: addInterval(values.dateLastReviewed, values.interval).toISODate() ?? '',
      files: files,
      interval: values.interval,
      name: values.name ?? '',
      // TODO: Make a new category ID
      preControls: {
        likelihood: values.preLikelihood,
        consequence: values.preConsequence,
      },
      postControls: {
        likelihood: values.postLikelihood,
        consequence: values.postConsequence,
      },
      risks: values.risks,
      shouldReportToManagement: values.shouldReportToManagement ?? false,
      whoResponsible: values.whoResponsible,
    }

    if (isNew) {
      /** Create New Risk */
      const createRiskDto: CreateRiskDto = {
        ...riskDto,
        state: 'active',
      }

      const createRiskUseCase = services.get(CreateRiskUseCase)
      createRiskUseCase
        .execute(createRiskDto, user?.id, licenseeId)
        .then(() => {
          alert('Risk created successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    } else {
      /** Update Risk */
      const updateRiskDto: UpdateRiskDto = {
        ...riskDto,
        id: risk.id,
        state: risk.state,
      }

      const updateRiskUseCase = services.get(UpdateRiskUseCase)
      updateRiskUseCase
        .execute(updateRiskDto, user?.id, licenseeId)
        .then(() => {
          alert('Risk Updated successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    }
  }

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => onSubmit(values),
  })

  const { errors, touched } = formik

  return (
    <SeaDrawer
      title={`${isNew ? 'Create' : 'Edit'} Risk Assessment`}
      visible={visible}
      onClose={() => onClose()}
      primaryAction={
        <SeaButton
          variant={SeaButtonVariant.Primary}
          onPress={() => formik.handleSubmit()}
          label={`${isNew ? 'Create' : 'Update'} Risk Assessment`}
        />
      }>
      <SeaStack direction={'column'} align={'start'} gap={8} width={'100%'}>
        {/** Row 1 */}
        <SeaStack direction={'row'} align={'start'} style={styles.row}>
          <VesselSelectInput vesselIds={vesselIds} setVesselIds={setVesselIds} />
        </SeaStack>

        {/** Row 2 */}
        <SeaStack direction={'row'} align={'start'} style={styles.row} gap={8} isCollapsible={true}>
          <SeaStack style={styles.column}>
            <SeaTextInput
              label={'Hazard/Risk Name'}
              value={formik.values.name}
              onChangeText={formik.handleChange('name')}
              hasError={Boolean(errors.name && touched.name)}
              errorText={errors.name}
            />
          </SeaStack>
          <RiskCategoryDropdown
            visible={true}
            categoryId={formik.values.categoryId}
            onSelect={value => formik.setFieldValue('categoryId', value)}
            style={styles.column}
          />
        </SeaStack>

        {/** Row 3 */}
        <SeaStack direction={'row'} align={'start'} style={styles.row}>
          <SeaTextInput
            label={'Risks associated with this hazard'}
            multiLine={true}
            value={formik.values.risks}
            onChangeText={formik.handleChange('risks')}
            hasError={Boolean(errors.risks && touched.risks)}
            errorText={errors.risks}
          />
        </SeaStack>

        <SeaLineBreak style={styles.lineBreak} />

        {/** Row 4 */}
        <SeaStack direction={'row'} align={'start'} gap={2}>
          <SeaTypography variant={'subtitle'}>Pre Controls Risk</SeaTypography>
          <SeaIcon icon={'help'} size={20} variant={'sharp'} />
        </SeaStack>
        <SeaStack direction={'row'} isCollapsible={true} width={'100%'} gap={8}>
          <RiskRatingLikelihoodDropdown
            value={formik.values.preLikelihood}
            onSelect={formik.handleChange('preLikelihood')}
          />

          <RiskRatingConsequenceDropdown
            value={formik.values.preConsequence}
            onSelect={formik.handleChange('preConsequence')}
          />

          <SeaStack direction={'column'} align={'start'} style={styles.column}>
            <SeaTypography variant={'label'}>Risk Rating</SeaTypography>
            <RiskRatingStatus likelihoodId={formik.values.preLikelihood} consequenceId={formik.values.preConsequence} />
          </SeaStack>
        </SeaStack>

        {/** Row 5 */}
        <SeaStack direction={'row'} align={'start'} style={styles.row}>
          <SeaTextInput
            label={'Risk Controls'}
            value={formik.values.controls}
            onChangeText={formik.handleChange('controls')}
            multiLine={true}
            hasError={Boolean(errors.controls && touched.controls)}
            errorText={errors.controls}
          />
        </SeaStack>

        <SeaLineBreak style={styles.lineBreak} />

        {/** Row 6 */}
        <SeaStack direction={'row'} align={'start'} gap={2}>
          <SeaTypography variant={'subtitle'}>Post Controls Risk</SeaTypography>
          <SeaIcon icon={'help'} size={20} variant={'sharp'} />
        </SeaStack>
        <SeaStack direction={'row'} align={'start'} style={styles.row} isCollapsible={true} gap={8}>
          <RiskRatingLikelihoodDropdown
            value={formik.values.postLikelihood}
            onSelect={formik.handleChange('postLikelihood')}
          />

          <RiskRatingConsequenceDropdown
            value={formik.values.postConsequence}
            onSelect={formik.handleChange('postConsequence')}
          />

          <SeaStack direction={'column'} align={'start'} style={styles.column}>
            <SeaTypography variant={'label'}>Risk Rating - TODO Onclick</SeaTypography>
            <RiskRatingStatus
              likelihoodId={formik.values.postLikelihood}
              consequenceId={formik.values.postConsequence}
            />
          </SeaStack>
        </SeaStack>

        {/** Row 7 */}
        <SeaStack direction={'row'} align={'start'} style={styles.row} isCollapsible={true} gap={8}>
          <>
            <SeaTextInput
              label={'Person/s Responsible'}
              value={formik.values.whoResponsible}
              onChangeText={formik.handleChange('whoResponsible')}
              style={styles.column}
              hasError={Boolean(errors.whoResponsible && touched.whoResponsible)}
              errorText={errors.whoResponsible}
            />
            <IntervalDropdown
              label={`Review Interval`}
              value={formik.values.interval}
              onChange={formik.handleChange('interval')}
              style={styles.column}
              hasError={Boolean(errors.interval && touched.interval)}
              errorText={errors.interval}
            />
            <SeaDateTimeInput
              label={'Date last Reviewed'}
              value={DateTime.fromISO(formatSeaDate(formik.values.dateLastReviewed))}
              onChange={(date: DateTime) => {
                formik.setFieldValue('dateLastReviewed', date)
              }}
              type={'date'}
              style={styles.column}
              hasError={Boolean(errors.dateLastReviewed && touched.dateLastReviewed)}
              errorText={errors.dateLastReviewed}
            />
          </>
        </SeaStack>

        <SeaLineBreak style={styles.lineBreak} />

        {/** Row 8 */}
        <SeaStack direction={'column'} align={'start'} style={styles.row}>
          <SeaFileUploader initialFiles={risk.files} files={files} setFiles={setFiles} />
        </SeaStack>

        {/** Row 9 */}
        <SeaStack direction={'row'} align={'start'} style={styles.row}>
          <SeaCheckbox
            label={'Report new Hazard/Risk to relevant crew ?(ICON)'}
            value={formik.values.shouldReportToManagement}
            onChange={value => formik.setFieldValue('shouldReportToManagement', value)}
            hasError={Boolean(errors.shouldReportToManagement && touched.shouldReportToManagement)}
          />
        </SeaStack>

        {/** Row 10 */}
        <SeaStack direction={'row'} align={'start'} style={styles.row}>
          {/* TODO: Show LINKS */}
          <SeaAddButton label={'Add Link'} variant={SeaButtonVariant.Link} />
        </SeaStack>

        {/** Row 11 */}
        <SeaStack direction={'row'} align={'start'}>
          <SeaTypography variant={'link'}>TODO: Show Links</SeaTypography>
        </SeaStack>
      </SeaStack>
    </SeaDrawer>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  column: {
    flex: 1,
    width: '100%',
  },
  row: {
    width: '100%',
  },
  lineBreak: {
    width: '100%',
    marginHorizontal: 0,
    marginVertical: 10,
  },
})
