import React, { useState } from 'react'
import { DangerousGood } from '@src/shared-state/HealthSafety/dangerousGoods'
import { formatDate } from '@src/lib/datesAndTime'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { ScrollView, StyleSheet } from 'react-native'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatValue } from '@src/lib/util'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { EditDangerousGoodsRegister } from '@src/components/_organisms/HealthSafety/DangerousGoodsRegister/EditDangerousGoodsRegister'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { getFileNameWithExtension } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'

interface ViewDangerousGoodsRegisterProps {
  dangerousGood?: DangerousGood
}

const DESKTOP_ITEMS_WIDTH = '100%'
export const ViewDangerousGoodsRegister = ({ dangerousGood }: ViewDangerousGoodsRegisterProps) => {
  if (!dangerousGood) {
    return <></>
  }

  // Hooks
  const [editModalVisible, setEditModalVisible] = useState(false)
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  return (
    <>
      <ScrollView key={`dangerousGood-${dangerousGood.id}`} style={styles.container}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={dangerousGood.name} files={dangerousGood?.imageFiles} />}
          secondaryActionButton={[
            <RequirePermissions
              key="dangerousGoodsRegister-edit"
              role="dangerousGoodsRegister"
              level={permissionLevels.EDIT}>
              <SeaEditButton key={'Edit'} onPress={() => setEditModalVisible(true)} />
            </RequirePermissions>,
            <RequirePermissions
              key="dangerousGoodsRegister-delete"
              role="dangerousGoodsRegister"
              level={permissionLevels.FULL}>
              <SeaDeleteButton
                key={'delete'}
                onPress={() => alert('This functionality is not implemented yet')} // TODO
              />
            </RequirePermissions>,
          ]}>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  {/** Row 1 */}
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Quantity'} value={formatValue(dangerousGood?.quantity)} />
                    <SeaLabelValue label={'Hazardous'} value={dangerousGood.isHazardous ? 'Yes' : 'No'} />
                  </SeaStack>

                  {/** Row 2 */}
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'location_on' }}
                      showIcon={true}
                      label={'Location'}
                      value={formatValue(dangerousGood?.location)}
                    />
                    <SeaLabelValue label={'DG Class'} value={formatValue(dangerousGood?.class)} />
                  </SeaStack>

                  {/** Row 3 */}
                  <SeaStack isCollapsible={true} width={'50%'} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon={true}
                      label={'Renewal Date'}
                      value={dangerousGood?.dateExpires ? formatDate(dangerousGood.dateExpires) : '-'}
                    />
                  </SeaStack>
                </SeaStack>
                {/** Row 4 */}
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'SDS Document'}
                    value={
                      dangerousGood.msdsFiles?.length > 0 ? (
                        <SeaFileImage files={dangerousGood.msdsFiles} size="tiny" showOthers={true} />
                      ) : (
                        '-'
                      )
                    }
                    layout={'vertical'}
                  />
                </SeaStack>

                {/** Row 5 */}
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Image / Files'}
                    value={
                      dangerousGood.imageFiles?.length > 0 ? (
                        <SeaFileImage files={dangerousGood.imageFiles} size="tiny" showOthers={true} />
                      ) : (
                        '-'
                      )
                    }
                    layout={'vertical'}
                  />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>
      </ScrollView>
      {editModalVisible && (
        <EditDangerousGoodsRegister
          visible={editModalVisible}
          dangerousGood={dangerousGood}
          onClose={() => setEditModalVisible(false)}
        />
      )}
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
})
