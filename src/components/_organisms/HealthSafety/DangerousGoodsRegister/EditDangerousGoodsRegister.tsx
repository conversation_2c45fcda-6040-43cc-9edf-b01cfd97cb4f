import React, { useMemo, useState } from 'react'
import { DangerousGood } from '@src/shared-state/HealthSafety/dangerousGoods'
import { StyleSheet } from 'react-native'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { preventMultiTap } from '@src/lib/util'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { useFormik } from 'formik'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { DateTime } from 'luxon'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDrawer } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { sharedState } from '@src/shared-state/shared-state'
import Yup, { notTooOld } from '@src/lib/yup'
import {
  CreateDangerousGoodDto,
  CreateDangerousGoodsUseCase,
  DangerousGoodDto,
} from '@src/domain/use-cases/dangerousGoods/CreateDangerousGoodsUseCase'
import {
  UpdateDangerousGoodDto,
  UpdateDangerousGoodsUseCase,
} from '@src/domain/use-cases/dangerousGoods/UpdateDangerousGoodsUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { formatSeaDate } from '@src/lib/datesAndTime'
import { SeaFile } from '@src/lib/fileImports'

interface EditDangerousGoodsRegisterProps {
  visible?: boolean
  isNew?: boolean
  dangerousGood?: DangerousGood
  onClose?: () => void
}

const validationSchema = Yup.object({
  name: Yup.string().max(500).required(),
  quantity: Yup.string().max(200),
  location: Yup.string().max(500),
  class: Yup.string().max(500),
  isHazardous: Yup.boolean().required(),
  dateExpires: Yup.date().when('type', {
    is: 'renewable',
    then: schema => schema.required().min(...notTooOld),
  }),
})

export const EditDangerousGoodsRegister = ({
  visible = false,
  isNew = false,
  dangerousGood,
  onClose = () => {},
}: EditDangerousGoodsRegisterProps) => {
  if (!dangerousGood) {
    return <></>
  }

  // Shared Data
  const user = sharedState.user.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)

  // Hooks
  const [vesselIds, setVesselIds] = useState<string[]>(dangerousGood.vesselIds)
  const [imageFiles, setImageFiles] = useState<SeaFile[]>([])
  const [msdsFiles, setMsdsFiles] = useState<SeaFile[]>([])

  // Services
  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return {
      name: dangerousGood.name,
      quantity: dangerousGood.quantity,
      location: dangerousGood?.location,
      isHazardous: dangerousGood.isHazardous,
      class: dangerousGood.class,
      dateExpires: dangerousGood?.dateExpires,
      imageFiles: dangerousGood.imageFiles,
      msdsFiles: dangerousGood.msdsFiles,
    }
  }, [])

  const onSubmit = (values: typeof initialValues) => {
    if (preventMultiTap('dangerousGood')) {
      return
    }

    // TODO: Cater for:
    //  - signature

    const dangerousGoodDto: DangerousGoodDto = {
      vesselIds: vesselIds,
      name: values.name,
      quantity: values.quantity,
      location: values.location,
      isHazardous: values.isHazardous,
      class: values.class,
      dateExpires: values.dateExpires,
      imageFiles: imageFiles,
      msdsFiles: msdsFiles,
    }

    if (isNew) {
      /** Create New Dangerous Good */
      const createDangerousGoodDto: CreateDangerousGoodDto = {
        ...dangerousGoodDto,
        state: 'active',
      }

      const createDangerousGoodUseCase = services.get(CreateDangerousGoodsUseCase)
      createDangerousGoodUseCase
        .execute(createDangerousGoodDto, user?.id, licenseeId)
        .then(() => {
          alert('Dangerous Good created successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    } else {
      /** Update Dangerous Good */
      const updateDangerousGoodDto: UpdateDangerousGoodDto = {
        ...dangerousGoodDto,
        id: dangerousGood.id,
        state: dangerousGood.state,
      }

      const updateDangerousGoodUseCase = services.get(UpdateDangerousGoodsUseCase)
      updateDangerousGoodUseCase
        .execute(updateDangerousGoodDto, user?.id, licenseeId)
        .then(() => {
          alert('Dangerous Good Updated successfully')
          onClose()
        })
        .catch(err => {
          const errorMessage = 'ERROR: ' + err.message
          alert(errorMessage)
          console.log('Failed', errorMessage)
        })
    }
  }

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => onSubmit(values),
  })

  const { errors, touched } = formik

  return (
    <SeaDrawer
      title={isNew ? 'Create Dangerous Good' : `Edit - ${dangerousGood.name}`}
      visible={visible}
      onClose={() => onClose()}
      primaryAction={
        <SeaButton
          variant={SeaButtonVariant.Primary}
          onPress={() => formik.handleSubmit()}
          label={`${isNew ? 'Create' : 'Update'} Dangerous Good`}
        />
      }>
      {/** Row 1 */}
      <SeaStack direction={'row'} align={'start'} style={styles.row}>
        <SeaStack style={styles.column}>
          <VesselSelectInput vesselIds={vesselIds} setVesselIds={setVesselIds} />
        </SeaStack>

        <SeaStack style={styles.column}>
          <SeaTextInput
            label={'Chemical Name'}
            value={formik.values.name}
            onChangeText={formik.handleChange('name')}
            hasError={Boolean(errors.name)}
            errorText={errors.name}
          />
        </SeaStack>
      </SeaStack>

      {/** Row 2 */}
      <SeaStack direction={'row'} align={'start'} style={styles.row}>
        <SeaStack style={styles.column}>
          <SeaTextInput
            label={'Quantity'}
            value={formik.values.quantity}
            onChangeText={formik.handleChange('quantity')}
          />
        </SeaStack>

        <SeaStack style={styles.column}>
          <SeaTextInput
            label={'Location'}
            value={formik.values.location}
            onChangeText={formik.handleChange('location')}
          />
        </SeaStack>
      </SeaStack>

      {/** Row 3 */}
      <SeaStack direction={'row'} align={'start'} style={styles.row}>
        <SeaDropdown
          label={'Hazardous Substance'}
          value={formik.values.isHazardous}
          items={[
            { label: 'Not Set', value: undefined },
            { label: 'Yes', value: true },
            { label: 'No', value: false },
          ]}
          hasError={Boolean(errors.isHazardous)}
          errorText={errors.isHazardous}
          onSelect={value => formik.setFieldValue('isHazardous', value)}
          style={styles.column}
        />

        <SeaStack style={styles.column}>
          <SeaTextInput label={'DG Class'} value={formik.values.class} onChangeText={formik.handleChange('class')} />
        </SeaStack>
      </SeaStack>

      {/** Row 4 */}
      <SeaStack direction={'row'} align={'start'} style={styles.row}>
        <SeaDateTimeInput
          label={'Renewal Date'}
          value={DateTime.fromISO(formatSeaDate(formik.values.dateExpires ?? ''))}
          onChange={(date: DateTime) => {
            formik.setFieldValue('dateExpires', date)
          }}
          type={'date'}
        />
      </SeaStack>

      {/** Row 5 */}
      <SeaStack direction={'column'} align={'start'} gap={20}>
        <SeaFileUploader
          label={'Image / Files'}
          initialFiles={dangerousGood.imageFiles}
          files={imageFiles}
          setFiles={setImageFiles}
        />
        <SeaFileUploader
          label={'SDS Document'}
          initialFiles={dangerousGood.msdsFiles}
          files={msdsFiles}
          setFiles={setMsdsFiles}
        />
      </SeaStack>
    </SeaDrawer>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  column: {
    flex: 1,
    width: '50%',
  },
  row: {
    width: '100%',
    paddingBottom: '5%',
  },
})
