import React, { useMemo } from 'react'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { Incident, IncidentState } from '@src/shared-state/HealthSafety/incidents'
import { StyleSheet } from 'react-native'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { dayDifferenceBetweenDates } from '@src/lib/datesAndTime'
import { formatValue } from '@src/lib/util'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { AuthorAction, SeaAuthorship } from '@src/components/_atoms/SeaAuthorship/SeaAuthorship'
import {
  addedToRiskRegister,
  authoritiesNotifiedForReview,
  controlStrategies,
  incidentSeverities,
  injuryConclusions,
  lostTimes,
  yesNoNa,
} from '@src/shared-state/HealthSafety/incidentReviews'
import { renderCategoryNames } from '@src/lib/categories'
import { sharedState } from '@src/shared-state/shared-state'
import { canView } from '@src/shared-state/Core/userPermissions'
import { SeaSignatureImage } from '@src/components/_atoms/SeaSignatureImage/SeaSignatureImage'
import { useDeviceWidth } from '@src/hooks/useDevice'

export interface ViewIncidentReviewProps {
  itemId?: string
  itemState?: IncidentState
  incident: Incident
  // review?: IncidentReview;
}

const DESKTOP_ITEMS_WIDTH = '100%'
export const ViewIncidentReview = ({
  itemId,
  itemState,
  // TODO: Sree - POSSIBLY remove
  incident,
}: ViewIncidentReviewProps) => {
  // Shared Data
  const incidentReviews = sharedState.incidentReviews.use()
  const incidentCauses = sharedState.incidentCauses.use()
  const licenseeSettings = sharedState.licenseeSettings.use()

  const { isDesktopWidth } = useDeviceWidth()
  const incidentReview = useMemo(() => {
    if (incidentReviews && itemId && incidentReviews?.byId[itemId]) {
      return incidentReviews.byId[itemId]
    }
    return undefined
  }, [incidentReviews, itemId])

  // Render
  const status = itemState === IncidentState.inReview && ' (Incomplete)'

  return (
    <>
      <SeaTypography variant={'subtitle'}>Review{status}</SeaTypography>
      <>
        {/** ForReview state */}
        {itemState === IncidentState.forReview && (
          <SeaTypography variant={'label'}>
            {(() => {
              const whenReport = incident.whenUpdated ?? incident.whenAdded
              const countDays = dayDifferenceBetweenDates(whenReport, Date.now()) - 1
              if (countDays > 1) {
                return `This report has been waiting to be reviewed for ${countDays} days.`
              } else if (countDays === 1) {
                return 'This report has been waiting to be reviewed since yesterday.'
              }
              return 'This report is waiting to be reviewed.'
            })()}
          </SeaTypography>
        )}

        {/** Completed state and there is no review */}
        {itemState === IncidentState.completed && !incidentReview && (
          <SeaTypography variant={'label'}>
            This report was never reviewed as it was created before the reviewing feature was added to Sea-Flux in
            August 2023.
          </SeaTypography>
        )}

        {incidentReview && (
          <>
            <SeaAuthorship item={incidentReview} createdAction={AuthorAction.REVIEW_CREATED} />
            <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
              <SeaLabelValue
                label={'Severity of Event'}
                value={formatValue(incidentSeverities[incidentReview.severity])}
                style={styles.label}
              />
              <SeaLabelValue
                label={'Notified to Authorities?'}
                value={formatValue(
                  incidentReview?.notifiedAuthorities &&
                    authoritiesNotifiedForReview[incidentReview?.notifiedAuthorities]
                )}
                style={styles.label}
              />
            </SeaStack>
            {/** Row 2 */}
            <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
              <SeaLabelValue
                label={'Causes of this incident / event'}
                value={formatValue(renderCategoryNames(incidentReview?.causeIds, incidentCauses))}
                style={styles.label}
              />
              <SeaLabelValue
                label={'Control Strategies'}
                value={formatValue(
                  incidentReview?.strategies
                    ?.map((strategy: string) => {
                      return controlStrategies[strategy]
                    })
                    .join(', ')
                )}
                style={styles.label}
              />
            </SeaStack>

            {/** Row 3 */}
            <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
              <SeaLabelValue
                label={'Added to risk assessments?'}
                value={formatValue(
                  incidentReview?.addedToRiskRegister && addedToRiskRegister[incidentReview?.addedToRiskRegister]
                )}
                style={styles.label}
              />
              <SeaLabelValue
                label={'External investigation required?'}
                value={formatValue(incidentReview?.externalRequired && yesNoNa[incidentReview?.externalRequired])}
                style={styles.label}
              />
            </SeaStack>

            {/** Row 4 and Injury Incident */}
            <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
              <SeaLabelValue
                label={'Injury Conclusion'}
                value={formatValue(
                  incidentReview?.injuryConclusions
                    ?.map((conclusion: string) => {
                      return injuryConclusions[conclusion]
                    })
                    .join(', ')
                )}
                style={styles.label}
              />
              <SeaLabelValue
                label={'Time Lost to Injury'}
                value={formatValue(incidentReview?.lostTime && lostTimes[incidentReview?.lostTime])}
                style={styles.label}
              />
            </SeaStack>

            {/** Row 4 and Analysis of Review */}
            <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
              <SeaLabelValue
                label={'Analysis of Review'}
                value={formatValue(incidentReview?.analysis)}
                style={styles.label}
              />
            </SeaStack>

            {/** Row 5 and Corrective Actions */}
            {(!licenseeSettings?.hasCorrectiveActions || !canView('correctiveActions')) && (
              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                {/* TODO: Add permission check */}
                <SeaLabelValue
                  label={'Corrective actions or tasks required'}
                  value={formatValue(incidentReview?.prevention)}
                  style={styles.label}
                />
              </SeaStack>
            )}

            {/** Row 6 and Hidden section */}
            {/** TODO: Build Corrective Actions History table */}
            {/** TODO: Build Corrective Actions History table */}
            {/** TODO: Build Corrective Actions History table */}

            {/* Row 7 Signature */}
            {/* TODO: Signature details */}
            {/* TODO: Signature details */}
            {/* TODO: Signature details */}

            <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
              <SeaSignatureImage />
            </SeaStack>
          </>
        )}
      </>
    </>
  )
}

const styles = StyleSheet.create({
  label: {
    flex: 1,
  },
})
