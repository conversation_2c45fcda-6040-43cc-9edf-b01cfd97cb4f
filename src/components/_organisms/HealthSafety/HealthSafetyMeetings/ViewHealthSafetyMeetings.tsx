import React, { useState } from 'react'
import { SafetyMeetingReport } from '@src/shared-state/HealthSafety/safetyMeetingReports'
import { ScrollView, StyleSheet, View } from 'react-native'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatDatetime } from '@src/lib/datesAndTime'
import { sharedState } from '@src/shared-state/shared-state'
import { formatValue, formatVessels } from '@src/lib/util'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { useRouter } from 'expo-router'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { canAccessAllVessels, permissionLevels } from '@src/shared-state/Core/userPermissions'
import { EditHealthSafetyMeetings } from '@src/components/_organisms/HealthSafety/HealthSafetyMeetings/EditHealthSafetyMeetings'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { ContactMediaCards } from '@src/components/_organisms/MediaCards/ContactMediaCards'
import { getFileNameWithExtension } from '@src/lib/files'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'

interface ViewHealthSafetyMeetingsProps {
  safetyMeetingReport?: SafetyMeetingReport
}

const DESKTOP_ITEMS_WIDTH = '100%'
export const ViewHealthSafetyMeetings = ({ safetyMeetingReport }: ViewHealthSafetyMeetingsProps) => {
  // Shared Data
  const safetyMeetingJobs = sharedState.safetyMeetingJobs.use()
  const vessels = sharedState.vessels.use()

  // Hooks
  const [editModalVisible, setEditModalVisible] = useState(false)
  // TODO: Export pdf
  // const [exportType, setExportType] = useState<ExportType>();
  const router = useRouter()
  const { isMobileWidth, isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  // TODO: Signature
  // const signature = safetySafetyMeetingReport?.signature
  //   ? getImgSrcFromString(safetySafetyMeetingReport.signature, "sig")
  //   : undefined;

  // Render
  if (!safetyMeetingReport) {
    return <></>
  }

  let hasJobs = false

  return (
    <>
      <ScrollView key={`safetySafetyMeetingReport-${safetyMeetingReport.id}`} style={styles.container}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={'Meeting Report'} />}
          secondaryActionButton={[
            <RequirePermissions key="healthSafetyMeetings" role="healthSafetyMeetings" level={permissionLevels.EDIT}>
              {safetyMeetingReport.vesselIds && canAccessAllVessels(safetyMeetingReport.vesselIds) && (
                <SeaEditButton key={'Edit'} onPress={() => setEditModalVisible(true)} />
              )}
            </RequirePermissions>,
            // TODO: Add export pdf
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not implemented yet')} />,
            <RequirePermissions key="healthSafetyMeetings" role="healthSafetyMeetings" level={permissionLevels.FULL}>
              <SeaDeleteButton key={'delete'} onPress={() => alert('This functionality is not implemented yet')} />
            </RequirePermissions>,
          ]}>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  {/** Row 1 */}
                  <SeaStack
                    direction={'row'}
                    align={'start'}
                    isCollapsible={true}
                    width={DESKTOP_ITEMS_WIDTH}
                    gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon={true}
                      label={'Date'}
                      value={formatDatetime(safetyMeetingReport?.whenMeeting, ' ')}
                      style={styles.label}
                    />
                    <SeaLabelValue
                      iconOptions={{ icon: 'directions_boat_filled' }}
                      showIcon={true}
                      label={'Vessel/Facility'}
                      value={formatVessels(safetyMeetingReport?.vesselIds, vessels)}
                      style={styles.label}
                    />
                  </SeaStack>
                </SeaStack>

                {/** Row 2 */}
                <SeaStack
                  direction={'row'}
                  align={'start'}
                  isCollapsible={true}
                  width={DESKTOP_ITEMS_WIDTH}
                  gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Personnel Present'}
                    value={safetyMeetingReport?.personnelPresentIds
                      ?.map(id => renderFullNameForUserId(id) || '-')
                      ?.join(', ')}
                    layout={'vertical'}
                  />
                </SeaStack>

                {/** Row 3 */}
                <SeaStack
                  direction={'row'}
                  align={'start'}
                  isCollapsible={true}
                  width={DESKTOP_ITEMS_WIDTH}
                  gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Notes from meeting'}
                    value={formatValue(safetyMeetingReport?.notes)}
                    layout={'vertical'}
                  />
                </SeaStack>

                {/** Row 4 */}
                <SeaStack
                  direction={'row'}
                  align={'start'}
                  isCollapsible={true}
                  width={DESKTOP_ITEMS_WIDTH}
                  gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Jobs Created'}
                    layout={'vertical'}
                    value={
                      <SeaStack direction={'row'} align={'start'}>
                        {/* TODO: Sree - will need to change - also duplicated in `HealthSafetyMeetings` */}
                        {safetyMeetingReport?.jobIds?.map((jobId: string) => {
                          if (safetyMeetingJobs?.byId[jobId]) {
                            hasJobs = true
                            return (
                              <View key={jobId}>
                                <SeaTypography variant={'link'}>{safetyMeetingJobs.byId[jobId].task}</SeaTypography>
                              </View>
                            )
                          }
                        })}
                        {!hasJobs && <SeaTypography variant={'input'}>-</SeaTypography>}
                      </SeaStack>
                    }
                  />
                </SeaStack>
                {/** Row 5 */}
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Image / Files'}
                    value={
                      safetyMeetingReport?.files?.length > 0 ? (
                        <SeaFileImage files={safetyMeetingReport?.files} size="tiny" showOthers={true} />
                      ) : (
                        '-'
                      )
                    }
                    layout={'vertical'}
                  />
                </SeaStack>

                {/** Row 6 */}
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Signed Off by'}
                    value={'TODO Replace with IMAGE'}
                    style={styles.label}
                    layout={'vertical'}
                  />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>
      </ScrollView>
      {editModalVisible && (
        <EditHealthSafetyMeetings
          visible={editModalVisible}
          safetyMeetingReport={safetyMeetingReport}
          onClose={() => setEditModalVisible(false)}
        />
      )}
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  label: {
    flex: 1,
  },
})
