import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { renderCategoryName } from '@src/lib/categories'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'
import { SeaCard, SeaCardBody, SeaCardContent, SeaCardHeader } from '@src/components/_atoms/SeaCard/SeaCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { getFileNameWithExtension } from '@src/lib/files'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'

interface SeaEquipmentProps {
  equipmentId?: string
  locationId?: string
  onChange?: (equipment: Partial<Equipment>) => void
}

export const SeaEquipment = ({ equipmentId, locationId, onChange }: SeaEquipmentProps) => {
  const vesselSystems = sharedState.vesselSystems.use()
  const vesselLocations = sharedState.vesselLocations.use()
  const equipment = sharedState.equipment.use()

  const { styles } = useStyles(styleSheet)

  const [localEquipment, setLocalEquipment] = useState<Partial<Equipment>>({})

  const categoryOptions = useMemo(() => {
    if (!vesselSystems?.ids) return []

    const options = vesselSystems.ids
      .filter(id => vesselSystems.byId[id].state === 'active')
      .map(id => ({
        label: renderCategoryName(id, vesselSystems),
        value: id,
      }))

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...options,
    ]
  }, [vesselSystems])

  const locationOptions = useMemo(() => {
    if (!vesselLocations?.ids) return []

    const options = vesselLocations.ids
      .filter(id => vesselLocations.byId[id].state === 'active')
      .map(id => ({
        label: renderCategoryName(id, vesselLocations),
        value: id,
      }))

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...options,
    ]
  }, [vesselLocations])

  const equipmentList = useMemo(() => {
    if (!equipment?.all) {
      return []
    }

    // Return empty array if systemId is invalid
    if (localEquipment?.systemId && !equipment.bySystemId[localEquipment.systemId]) {
      return []
    }

    let list: Equipment[] = localEquipment?.systemId
      ? equipment.bySystemId[localEquipment.systemId] || []
      : equipment.all

    // Only add the deleted equipment if it exists and matches current systemId
    if (equipmentId && !list.some(item => item.id === equipmentId)) {
      const deletedItem = equipment.byId[equipmentId]
      if (deletedItem && (!localEquipment.systemId || deletedItem.systemId === localEquipment.systemId)) {
        list = [deletedItem, ...list]
      }
    }

    return list.map(item => ({
      label: `${item.equipment}${item.state === 'deleted' ? ' (Deleted)' : ''}`,
      value: item.id,
    }))
  }, [equipmentId, equipment, localEquipment])

  const onSystemChanged = useCallback(
    (newSystemId: string) => {
      // Clear equipment selection if system changes and equipment doesn't belong to new system
      if (equipmentId && equipment) {
        const equipmentBelongsToNewSystem = equipment.byId[equipmentId]?.systemId === newSystemId
        if (!equipmentBelongsToNewSystem) {
          setLocalEquipment({})
        }
      }

      const data = {
        systemId: newSystemId,
        equipment: '',
        id: '',
      }
      setLocalEquipment(data)

      if (onChange) {
        onChange(data)
      }
    },
    [equipment, equipmentId]
  )

  const onEquipmentChanged = useCallback(
    (newEquipmentId: string) => {
      if (!newEquipmentId || newEquipmentId === '') {
        setLocalEquipment({})
        return
      }

      const item = equipment?.byId[newEquipmentId]
      if (!item) {
        setLocalEquipment({})
        return
      }

      setLocalEquipment(item)
      if (onChange) {
        onChange(item)
      }
    },
    [equipment, setLocalEquipment]
  )

  const locationChanged = useCallback(
    (locationId: string) => {
      setLocalEquipment(prev => {
        const updatedEquipment = {
          ...prev,
          locationId: locationId !== '' ? locationId : undefined,
        }

        if (onChange) {
          onChange(updatedEquipment)
        }

        return updatedEquipment
      })
    },
    [setLocalEquipment, onChange]
  )

  useEffect(() => {
    if (!equipmentId || !equipment) {
      setLocalEquipment({})
      return
    }

    const equipmentItem = equipment.byId[equipmentId]
    setLocalEquipment(equipmentItem)

    if (onChange) {
      onChange(equipmentItem)
    }
  }, [equipmentId, equipment])

  const system = useMemo(() => {
    if (!localEquipment?.systemId) return null
    return vesselSystems?.byId[localEquipment.systemId]
  }, [categoryOptions, localEquipment])

  const location = useMemo(() => {
    if (!localEquipment?.locationId) return null
    return vesselLocations?.byId[localEquipment.locationId]
  }, [localEquipment, vesselLocations])

  const manuals = useMemo(() => {
    if (!localEquipment?.files?.length) return undefined

    return localEquipment.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
    })) as MediaCardFile[]
  }, [localEquipment])

  return (
    <SeaStack direction={'column'} gap={20} justify={'start'} align={'start'} width={'100%'}>
      <SeaStack direction={'row'} gap={20} justify={'start'} align={'center'} width={'100%'} isCollapsible>
        <SeaDropdown
          label={'System'}
          items={categoryOptions}
          onSelect={value => onSystemChanged(value)}
          value={localEquipment.systemId}
          style={{ flex: 1, width: '100%' }}
        />

        <SeaDropdown
          label={'Equipment'}
          items={equipmentList}
          onSelect={value => onEquipmentChanged(value)}
          value={localEquipment.id ?? equipmentId ?? ''}
          style={{ flex: 1, width: '100%' }}
        />
      </SeaStack>

      <SeaStack direction={'row'} gap={20} justify={'start'} align={'center'} width={'100%'} isCollapsible>
        <SeaDropdown
          label={'Location'}
          items={locationOptions}
          onSelect={value => locationChanged(value)}
          value={locationId ?? localEquipment.locationId}
          style={{ flex: 1, width: '100%' }}
        />
      </SeaStack>
      <SeaCard>
        <SeaCardHeader style={styles.cardHeader}>
          <SeaTypography variant="subtitle">{localEquipment.equipment}</SeaTypography>
          <SeaButton
            label="Edit"
            variant={SeaButtonVariant.Link}
            iconOptions={{
              icon: 'edit',
              size: 18,
              fill: false,
            }}
            onPress={() => alert('Edit Equipment: Coming Soon')}
          />
        </SeaCardHeader>
        <SeaCardBody style={styles.cardBody}>
          <SeaCardContent>
            <SeaStack direction="row" gap={20} justify="start" align="start" width={'100%'} isCollapsible>
              <SeaStack direction="column" gap={5} style={{ flex: 1 }} width={'100%'}>
                <SeaLabelValue
                  label={'System'}
                  value={system?.name ?? ''}
                  iconOptions={{
                    icon: 'dns',
                    size: 18,
                  }}
                  showIcon
                />
                <SeaLabelValue
                  label={'Location'}
                  value={location?.name ?? ''}
                  iconOptions={{
                    icon: 'pin_drop',
                    size: 18,
                  }}
                  showIcon
                />
                <SeaLabelValue
                  label={'Critical'}
                  value={localEquipment.isCritical ? 'Yes' : 'No'}
                  iconOptions={{
                    icon: 'flag',
                    size: 18,
                    fill: true,
                  }}
                  showIcon
                />

                {/* TODO: Make sure if this is required or can remove it */}
                {/* {manuals && (
                <SeaMedia files={manuals} title="Manuals" type="manuals" />
              )} */}
              </SeaStack>
              <SeaStack direction="column" gap={5} style={{ flex: 1 }} width={'100%'}>
                <SeaLabelValue label={'Make'} value={localEquipment?.make ?? '-'} showIcon />
                <SeaLabelValue label={'Model'} value={localEquipment?.model ?? '-'} showIcon />
                <SeaLabelValue label={'Serial'} value={localEquipment?.serial ?? '-'} showIcon />
              </SeaStack>
            </SeaStack>
          </SeaCardContent>
        </SeaCardBody>
      </SeaCard>
    </SeaStack>
  )
}

const styleSheet = createStyleSheet(theme => ({
  cardHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  cardBody: {
    backgroundColor: 'transparent',
  },
}))
