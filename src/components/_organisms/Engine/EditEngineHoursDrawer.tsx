import { View, Text } from 'react-native'
import React, { useCallback, useMemo } from 'react'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { useFormik } from 'formik'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import {
  EngineDto,
  UpdateEngineHoursDto,
  UpdateEngineHoursUseCase,
} from '@src/domain/use-cases/engine/UpdateEngineHoursUseCase'
import { toInt } from '@src/lib/util'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaFile } from '@src/lib/fileImports'
import Yup from '@src/lib/yup'

interface FormikEngineData {
  initialHours: string
  hours: string
  name: string
}

const validationSchema = Yup.object({
  engineHours: Yup.number().min(0).required(),
  date: Yup.date().required(),
  notes: Yup.string().max(5000),
  engineId: Yup.string().required(),
})

export interface EditEngineHoursDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {}

export function EditEngineHoursDrawer({ visible, onClose, style }: EditEngineHoursDrawerProps) {
  const engines = sharedState.engines.use(visible)
  const userId = sharedState.userId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const vesselId = sharedState.vesselId.use(visible)

  const services = useServiceContainer()

  const initialValues = useMemo(() => {
    return (
      engines?.all.reduce(
        (acc, engine) => {
          acc[engine.id] = {
            initialHours: engine.hours.toString(),
            hours: '',
            name: engine.name,
          }
          return acc
        },
        {} as Record<string, FormikEngineData>
      ) ?? {}
    )
  }, [engines])

  const handleSubmit = useCallback(
    (values: any) => {
      if (!userId || !licenseeId || !vesselId) {
        throw 'Missing userId or licenseeId or vesselId'
      }

      const updatedEngines: EngineDto[] = Object.entries(values)
        .filter(([_, value]) => {
          const data = value as FormikEngineData
          return data.hours !== '' && data.hours !== data.initialHours
        })
        .map(([id, value]) => {
          const data = value as FormikEngineData

          return {
            engineId: id,
            engineName: data.name,
            engineHours: toInt(data.hours, 0),
          }
        })

      const dto: UpdateEngineHoursDto = {
        vesselId: vesselId ?? '',
        engines: updatedEngines,
      }

      const createJobList = services.get(UpdateEngineHoursUseCase)

      createJobList
        .execute(dto, userId, licenseeId)
        .then(() => onClose())
        .catch(err => console.error(`Error updating Engine/Equipment hours\n ${err.message}`))
    },
    [userId, licenseeId, vesselId]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  const { errors, touched } = formik

  return (
    <SeaDrawer
      title="Edit Engine/Equipment Hours"
      visible={visible}
      onClose={onClose}
      style={style}
      level={2}
      primaryAction={
        <SeaButton variant={SeaButtonVariant.Primary} onPress={formik.handleSubmit} label={'Submit New Hours'} />
      }>
      <SeaStack direction="column" gap={20} align="start" justify="start">
        {engines?.all.map(engine => (
          <SeaTextInput
            label={engine.name}
            value={
              formik.values[engine.id].hours !== ''
                ? formik.values[engine.id].hours
                : formik.values[engine.id].initialHours
            }
            key={engine.id}
            onChangeText={value => {
              formik.setFieldValue(engine.id, {
                ...formik.values[engine.id],
                hours: value,
              })
            }}
          />
        ))}

        <SeaTypography variant="body">
          {engines?.all
            ? 'Note: Engines/generators can be edited in '
            : 'You currently have no engines/generators. You can add engines/generators in '}
          Vessel Settings
        </SeaTypography>
      </SeaStack>
    </SeaDrawer>
  )
}
