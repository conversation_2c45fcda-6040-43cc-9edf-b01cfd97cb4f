import React from 'react'
import { SeaCard, SeaCardBody, SeaCardContent, SeaCardHeader } from '@src/components/_atoms/SeaCard/SeaCard'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { Pressable, ScrollView } from 'react-native-gesture-handler'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaIcon, SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { createStyleSheet, useStyles } from '@src/theme/styles'

const FORM_FIELDS: Omit<FormElementProps, 'onPress'>[] = [
  {
    id: 'heading',
    label: 'Heading',
    iconOptions: {
      icon: 'h_mobiledata_badge',
    },
  },
  {
    id: 'text',
    label: 'Paragraph',
    iconOptions: {
      icon: 'match_case',
    },
  },
  {
    id: 'input',
    label: 'Text Field',
    iconOptions: {
      icon: 'text_fields_alt',
    },
  },
  {
    id: 'textarea',
    label: 'Text Area',
    iconOptions: {
      icon: 'subject',
    },
  },
  {
    id: 'checkbox',
    label: 'Checkbox',
    iconOptions: {
      icon: 'check_box',
    },
  },
  {
    id: 'dropdown',
    label: 'Dropdown',
    iconOptions: {
      icon: 'expand_circle_down',
    },
  },
  {
    id: 'radio',
    label: 'Single Choice',
    iconOptions: {
      icon: 'radio_button_checked',
    },
  },
  {
    id: 'teamMember',
    label: 'Team Member',
    iconOptions: {
      icon: 'person',
    },
  },
  {
    id: 'files',
    label: 'File Upload',
    iconOptions: {
      icon: 'upload',
    },
  },
  {
    id: 'signature',
    label: 'Signature',
    iconOptions: {
      icon: 'signature',
    },
  },
  {
    id: 'date',
    label: 'Date',
    iconOptions: {
      icon: 'calendar_month',
    },
  },
  {
    id: 'checks',
    label: 'Pass | Fail | NA',
    iconOptions: {
      icon: 'fact_check',
    },
  },
  {
    id: 'yesno',
    label: 'Yes | No',
    iconOptions: {
      icon: 'rule',
    },
  },
  {
    id: 'spacer',
    label: 'Spacer',
    iconOptions: {
      icon: 'expand',
    },
  },
  {
    id: 'line',
    label: 'Line',
    iconOptions: {
      icon: 'check_indeterminate_small',
    },
  },
]

interface AddFormElementsProps {
  onAddElement: (id: string) => void
}

export function AddFormElements({ onAddElement }: AddFormElementsProps) {
  const { theme } = useStyles(styleSheet)

  return (
    <SeaCard style={{ flex: 1 }}>
      <SeaCardHeader style={{ padding: 10 }}>
        <SeaTypography variant="body">Form Elements</SeaTypography>
      </SeaCardHeader>
      <ScrollView>
        <SeaCardBody>
          <SeaCardContent>
            <SeaStack direction="column" gap={5}>
              {FORM_FIELDS.map((field, index) => (
                <FormElement
                  id={field.id}
                  key={index}
                  label={field.label}
                  iconOptions={{ ...field.iconOptions, size: 24, fill: false, color: theme.colors.text.body }}
                  onPress={() => onAddElement(field.id)}
                />
              ))}
            </SeaStack>
          </SeaCardContent>
        </SeaCardBody>
      </ScrollView>
    </SeaCard>
  )
}

interface FormElementProps {
  id: string
  label: string
  iconOptions: SeaIconProps
  onPress: () => void
}

function FormElement({ label, iconOptions, onPress }: FormElementProps) {
  const { styles } = useStyles(styleSheet)
  const { color: iconColor, ...restIconOptions } = { ...iconOptions }

  return (
    <Pressable style={styles.formElement} onPress={onPress}>
      <SeaIcon color={iconColor} {...restIconOptions} />
      <SeaTypography variant="body">{label}</SeaTypography>
    </Pressable>
  )
}

const styleSheet = createStyleSheet(() => ({
  formElement: {
    width: '100%',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 4,
    backgroundColor: '#F1F2F5',
    gap: 10,
  },
}))
