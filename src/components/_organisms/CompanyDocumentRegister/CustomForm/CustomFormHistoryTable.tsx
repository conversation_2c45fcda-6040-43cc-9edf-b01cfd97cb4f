import { View, Text } from 'react-native'
import React, { useMemo } from 'react'
import { CustomForm } from '@src/shared-state/CompanyDocuments/CustomForms/customForms'
import {
  CustomFormCompleted,
  useCustomFormsCompleted,
} from '@src/shared-state/CompanyDocuments/CustomForms/useCustomFormsCompleted'
import { SeaTable, SeaTableColumn } from '@src/components/_atoms/SeaTable/SeaTable'
import { DateTimeAcceptable, formatDate, formatDateShort, formatDatetime } from '@src/lib/datesAndTime'
import { truncateText } from '@src/lib/util'
import { renderCrewList, renderFullNameForUserId } from '@src/shared-state/Core/users'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { renderVesselsList } from '@src/shared-state/Core/vessels'
import { sharedState } from '@src/shared-state/shared-state'
import { CustomFormVersion } from '@src/shared-state/CompanyDocuments/CustomForms/customFormVersions'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { useRouter } from 'expo-router'

const formatElementValue = (value: DateTimeAcceptable, id: string) => {
  switch (id) {
    case 'date':
      return formatDate(value)
    case 'datetime':
      return formatDatetime(value)
    case 'checkbox':
      return value ? 'Yes' : 'No'
  }
  return '' + value
}

interface CompanyDocumentHistoryTableProps {
  selectedItem: CustomForm
  setSelectedHistoryItem: (item: CustomFormCompleted) => void
}

export function CustomFormHistoryTable({ selectedItem, setSelectedHistoryItem }: CompanyDocumentHistoryTableProps) {
  const customFormVersions = sharedState.customFormVersions.use()
  const vesselId = sharedState.vesselId.use()
  const completedCustomFormsBeforeSearch = useCustomFormsCompleted(selectedItem?.id)
  const { styles } = useStyles(styleSheet)
  const router = useRouter()

  const forVessels = useMemo(() => {
    return selectedItem?.forVesselIds && selectedItem.forVesselIds[0] !== 'none'
  }, [selectedItem])

  const forCrew = useMemo(() => {
    return selectedItem?.forCrew
  }, [selectedItem])

  const latestVersion = useMemo(() => {
    return customFormVersions?.byFormIdAndVersion[selectedItem.id][selectedItem.latestVersion]
  }, [customFormVersions])

  const elementToShow = useMemo(() => {
    if (latestVersion?.historyElementN && latestVersion.form?.[`e${latestVersion.historyElementN}`]) {
      return latestVersion.form[`e${latestVersion.historyElementN}`]
    }
    return undefined
  }, [latestVersion, customFormVersions])

  const handlePress = (item: CustomFormCompleted) => {
    setSelectedHistoryItem(item)
  }

  const data = useMemo(() => {
    if (!completedCustomFormsBeforeSearch) return []

    return completedCustomFormsBeforeSearch.all
  }, [completedCustomFormsBeforeSearch])

  const columns = useMemo(
    () =>
      buildColumns({
        forVessels,
        forCrew,
        allowMultiple: selectedItem?.vesselsElement?.allowMultiple ?? false,
        elementToShow,
        latestVersion,
      }),
    []
  )
  const rows = useMemo(() => buildRows(data, item => handlePress(item)), [data, completedCustomFormsBeforeSearch])

  return (
    <View style={styles.tableView}>
      <SeaTable columns={columns} rows={rows} />
    </View>
  )
}

const buildColumns = ({
  forVessels = false,
  forCrew = false,
  allowMultiple = false,
  elementToShow = undefined,
  latestVersion = undefined,
}: {
  forVessels?: boolean
  forCrew?: boolean
  allowMultiple?: boolean
  elementToShow?: any
  latestVersion?: CustomFormVersion
}) => {
  return [
    {
      label: 'Date',
      value: item => formatDateShort(item.whenAdded),
      width: 120,
    },
    {
      label: 'Completed By',
      value: item => renderFullNameForUserId(item.addedBy),
    },
    ...(elementToShow
      ? [
          {
            label: elementToShow.label,
            value: (item: CustomFormCompleted) =>
              item?.data?.[`e${latestVersion?.historyElementN}`]
                ? formatElementValue(item.data[`e${latestVersion?.historyElementN}`], elementToShow.id)
                : '-',
          },
        ]
      : []),
    {
      label: 'Version',
      value: item => formatDatetime(item.version),
    },
    ...(forVessels
      ? [
          {
            label: allowMultiple ? 'Vessels / Facilities' : 'Vessel / Facility',
            value: (item: CustomFormCompleted) => renderVesselsList(item.vesselIds),
          },
        ]
      : []),
    ...(forCrew
      ? [
          {
            label: 'Personnel',
            value: (item: CustomFormCompleted) => renderCrewList(item.personnelIds ?? []),
          },
        ]
      : []),
    {
      label: 'Status',
      value: item => (item.isDraft ? <SeaStatusPill primaryLabel="DRAFT" variant={SeaStatusType.Attention} /> : <></>),
      width: 140,
    },
  ] as SeaTableColumn<CustomFormCompleted>[]
}

const buildRows = (items: CustomFormCompleted[], onPress: (item: CustomFormCompleted) => void) => {
  return items.map(item => ({
    data: item,
    onPress: (item: CustomFormCompleted) => onPress(item),
  }))
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}))
