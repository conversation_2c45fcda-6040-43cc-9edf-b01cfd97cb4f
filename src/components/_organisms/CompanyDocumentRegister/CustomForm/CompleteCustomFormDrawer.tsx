import { View } from 'react-native'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { <PERSON>Drawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { CustomForm } from '@src/shared-state/CompanyDocuments/CustomForms/customForms'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { renderFullName, renderFullNameForUserId } from '@src/shared-state/Core/users'
import { calculateWidth, makeFormElements } from '@src/lib/customForms'
import {
  CustomFormElementType,
  SeaCustomFormElement,
} from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { FormikValues, useFormik } from 'formik'
import Yup from '@src/lib/yup'
import { CustomFormVersion } from '@src/shared-state/CompanyDocuments/CustomForms/customFormVersions'
import {
  CompleteCustomFormDto,
  CompleteCustomFormUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/CompleteCustomFormUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { formatDatetime, toMillis } from '@src/lib/datesAndTime'

import { SaveStateAction, setSavingStateManager } from '@src/managers/SavingStateManager/SavingStateManager'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { CustomFormCompleted } from '@src/shared-state/CompanyDocuments/CustomForms/useCustomFormsCompleted'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { useStyles } from '@src/theme/styles'
import {
  UpdateCompletedCustomFormDto,
  UpdateCompletedCustomFormUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/UpdateCompletedCustomFormUseCase'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { canAccessAllVessels, canComplete, canDelete, canEdit, canView } from '@src/shared-state/Core/userPermissions'

type InitialValues = {
  formData: Record<string, any>
  vesselIds?: string[]
  personnel?: string[]
  isDraft: boolean // Default value for isDraft
}

export interface CompleteCustomFormDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  customForm?: CustomForm
  selectedCompletedForm?: CustomFormCompleted
  type: 'complete' | 'edit' | 'view'
  toggleEdit?: () => void
}

export function CompleteCustomFormDrawer({
  customForm,
  selectedCompletedForm,
  visible,
  onClose,
  style,
  type,
  toggleEdit,
}: CompleteCustomFormDrawerProps) {
  const users = sharedState.users.use(visible)
  const customFormVersions = sharedState.customFormVersions.use(visible)
  const userId = sharedState.userId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const user = sharedState.user.use(visible)

  const [vesselIds, setVesselIds] = useState<string[]>([])
  const [personnelIds, setPersonnelIds] = useState<string[]>([])
  const [formElements, setFormElements] = useState<CustomFormElementType[]>([])
  const [selectedVersion, setSelectedVersion] = useState<CustomFormVersion | undefined>()

  const services = useServiceContainer()
  const { isMobileWidth } = useDeviceWidth()
  const { theme } = useStyles()

  const userCanEdit = useMemo(() => {
    if (!selectedCompletedForm) return false
    const isCustomFormEditor =
      canEdit('customForms') &&
      (selectedCompletedForm?.vesselIds[0] === 'none' || canAccessAllVessels(selectedCompletedForm?.vesselIds))

    const isCustomFormCompleter = canComplete('customForms') && selectedCompletedForm.isDraft

    const isVoyageEditor = selectedCompletedForm.attachTo === 'voyage' && canEdit('logbook')

    const isTrainingViewer = selectedCompletedForm.attachTo === 'trainingTaskReport' && canView('crewTraining')

    return isCustomFormEditor || isCustomFormCompleter || isVoyageEditor || isTrainingViewer
  }, [selectedCompletedForm])

  const userCanDelete = useMemo(() => {
    if (!selectedCompletedForm) return false

    const isCustomFormDeleter = canDelete('customForms')

    const isCustomFormCompleterDeleter =
      canComplete('customForms') && selectedCompletedForm.isDraft && selectedCompletedForm.addedBy === user?.id

    const isVoyageEditor = selectedCompletedForm.attachTo === 'voyage' && canEdit('logbook')

    const isTrainingEditor = selectedCompletedForm.attachTo === 'trainingTaskReport' && canEdit('crewTraining')

    return isCustomFormDeleter || isCustomFormCompleterDeleter || isVoyageEditor || isTrainingEditor
  }, [selectedCompletedForm, user, canDelete, canComplete, canEdit])

  const secondaryActionButton = useMemo(() => {
    const actions = []

    if (userCanEdit) {
      actions.push(
        <SeaButton
          key="edit"
          label={`Edit ${selectedCompletedForm?.isDraft ? 'Draft' : 'Completed Form'} `}
          variant={SeaButtonVariant.Secondary}
          onPress={toggleEdit}
        />
      )
    }

    if (userCanDelete) {
      actions.push(
        <SeaDeleteButton
        // onPress={onDelete}
        />
      )
    }

    return actions
  }, [userCanEdit, userCanDelete, selectedCompletedForm])

  const initialValues = useMemo(() => {
    const formData = formElements.reduce(
      (acc, el) => {
        if (el.n !== undefined) {
          acc[`e${el.n}`] = el.value
        }
        return acc
      },
      {} as Record<string, any>
    )

    return {
      formData,
      ...(customForm?.forVesselIds && customForm.forVesselIds[0] !== 'none'
        ? {
            vesselIds: !selectedCompletedForm?.vesselIds.includes('none') ? selectedCompletedForm?.vesselIds : [],
          }
        : {}),
      ...(customForm?.forCrew
        ? {
            personnel: selectedCompletedForm?.personnelIds ?? [],
          }
        : {}),
      isDraft: false, // Default value for isDraft
    } as InitialValues
  }, [formElements, customForm, selectedCompletedForm])

  const validationSchema = useMemo(() => {
    const formDataShape: Record<string, any> = {}

    formElements.forEach(el => {
      if (el.n === undefined) return

      // Use a base shape, will apply .when later
      let validator: any = Yup.string()

      if (el.type === 'number') {
        validator = Yup.number().typeError('Must be a number')
      }

      if (el.id === 'textarea') {
        validator = Yup.string().max(500, 'Max 500 characters')
      }

      if (el.id === 'checks') {
        validator = Yup.array().of(Yup.string().nullable()).nullable()
      } else if (el.id === 'files') {
        validator = Yup.array().of(Yup.object().nullable()).nullable()
      }

      // Now apply .when if required
      if (el.required) {
        validator = validator.when('$isDraft', {
          is: false,
          then: (schema: any) => schema.required('This field is required'),
          otherwise: (schema: any) => schema.nullable(),
        })
      } else {
        validator = validator.nullable()
      }

      formDataShape[`e${el.n}`] = validator
    })

    const shape: Record<string, any> = {
      formData: Yup.object().shape(formDataShape),
    }

    if (customForm?.forVesselIds && customForm.forVesselIds[0] !== 'none') {
      shape.vesselIds = Yup.array().when('$isDraft', {
        is: false,
        then: (schema: any) =>
          schema.of(Yup.string().required('Vessel ID required')).min(1, 'At least one vessel must be selected'),
        otherwise: (schema: any) => schema.of(Yup.string()).nullable(),
      })
    }

    if (customForm?.forCrew) {
      shape.personnel = Yup.array().when('$isDraft', {
        is: false,
        then: (schema: any) =>
          schema.of(Yup.string().required('Personnel ID required')).min(1, 'At least one person must be selected'),
        otherwise: (schema: any) => schema.of(Yup.string()).nullable(),
      })
    }

    return Yup.object().shape(shape)
  }, [formElements, customForm])

  const handleSubmit = useCallback(
    (values: FormikValues): void => {
      if (!customForm || !licenseeId || !userId || !selectedVersion) {
        console.error('Custom Form, Licensee ID, User ID, or Selected Version is not available')
        return
      }

      setSavingStateManager({
        action: SaveStateAction.SAVING,
      })

      const formTitle = customForm?.title ? `${customForm?.title} ${values.isDraft ? '(DRAFT}' : ''}` : ''

      const personnelNames = values?.personnel?.map((id: string) => renderFullNameForUserId(id))

      const title = `${formTitle} ${personnelNames?.length > 0 ? `- ${personnelNames.join(', ')}` : ''}`

      let filesPosition
      let signaturePosition

      formElements.forEach(element => {
        switch (element.id) {
          case 'datetime':
            values.formData[`e${element.n}`] =
              typeof values.formData[`e${element.n}`] === 'number'
                ? values.formData[`e${element.n}`]
                : toMillis(values.formData[`e${element.n}`])
            break
          case 'files':
            values.formData[`e${element.n}`] = values.formData[`e${element.n}`]
            filesPosition = `e${element.n}`
            break
          case 'signature':
            if (values.formData[`e${element.n}`]) {
              values.formData[`e${element.n}`] = values.formData[`e${element.n}`]
              signaturePosition = `e${element.n}`
            } else {
              values.formData[`e${element.n}`] = undefined
              signaturePosition = undefined
            }
            break
          default:
            break
        }

        if (values.formData[`e${element.n}`] === undefined) {
          values.formData[`e${element.n}`] = ''
        }
      })

      const commonDto = {
        title,
        customFormId: customForm.id,
        vesselIds: values.vesselIds && values.vesselIds.length > 0 ? values.vesselIds : ['none'],
        personnelIds: values.personnel && values.personnel.length > 0 ? values.personnel : undefined,
        formData: values.formData,
        isDraft: values.isDraft,
        filesPosition,
        signaturePosition,
      }

      if (type === 'complete') {
        const dto: CompleteCustomFormDto = {
          ...commonDto,
          version: selectedVersion.version,
          versionId: selectedVersion.id,
          attachTo: undefined,
          attachToId: undefined,
          attachToTrainingTaskId: undefined,
          attachToVesselId: undefined,
        }

        const completeCustomForm = services.get(CompleteCustomFormUseCase)
        completeCustomForm
          .execute(dto, userId, licenseeId)
          .then(() => {
            setSavingStateManager({
              action: SaveStateAction.SAVED,
              onCloseTimeout: 1000,
            })
            onClose()
          })
          .catch(err => {
            setSavingStateManager({
              action: SaveStateAction.ERROR,
              onCloseTimeout: 1000,
            })
            console.error(`Error submitting complete form\n ${err.message}`)
          })
      } else if (type === 'edit' && selectedCompletedForm) {
        const dto: UpdateCompletedCustomFormDto = {
          ...commonDto,
          id: selectedCompletedForm.id,
        }

        const updateCompleteCustomForm = services.get(UpdateCompletedCustomFormUseCase)

        updateCompleteCustomForm
          .execute(dto, userId, licenseeId)
          .then(() => {
            setSavingStateManager({
              action: SaveStateAction.SAVED,
              onCloseTimeout: 1000,
            })
            onClose()
          })
          .catch(err => {
            setSavingStateManager({
              action: SaveStateAction.ERROR,
              onCloseTimeout: 1000,
            })
            console.error(`Error submitting complete form\n ${err.message}`)
          })
      }
    },
    [customForm, licenseeId, userId, selectedVersion, type, formElements]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
    validateOnBlur: true,
    validateOnChange: false,
    enableReinitialize: true,
  })

  const crewOptions = useMemo(() => {
    if (!users?.staff) return []

    return users.staff
      .filter(user => typeof user.id === 'string')
      .map(user => ({
        label: renderFullName(user),
        value: user.id ?? '',
      }))
  }, [users])

  const selectPersonnel = useCallback(
    (action: CheckBoxActions, changedValue: string) => {
      switch (action) {
        case CheckBoxActions.SELECT:
          formik.setFieldValue('personnel', [...(formik.values.personnel ?? []), changedValue])
          return
        case CheckBoxActions.DESELECT:
          formik.setFieldValue(
            'personnel',
            (formik.values.personnel ?? []).filter((id: string) => id !== changedValue)
          )
          return
        default:
          return
      }
    },
    [formik.values]
  )

  useEffect(() => {
    if (!customForm) return

    const selectedVersion =
      customFormVersions?.byFormIdAndVersion[customForm.id][selectedCompletedForm?.version ?? customForm.latestVersion]

    setSelectedVersion(selectedVersion)
    const formElements = makeFormElements(selectedVersion, selectedCompletedForm)
    setFormElements(formElements)
    setVesselIds(
      selectedCompletedForm?.vesselIds && !selectedCompletedForm.vesselIds.includes('none')
        ? selectedCompletedForm.vesselIds
        : []
    )

    setPersonnelIds(selectedCompletedForm?.personnelIds ?? [])
  }, [customForm, customFormVersions, setFormElements, selectedCompletedForm])

  useEffect(() => {
    formik.setFieldValue('vesselIds', vesselIds)
  }, [vesselIds])

  useEffect(() => {
    formik.setFieldValue('personnel', personnelIds)
  }, [personnelIds])

  return (
    <SeaDrawer
      title={customForm?.title ?? ''}
      titleAdditionalElements={() =>
        selectedCompletedForm?.isDraft ? (
          <SeaStatusPill primaryLabel="DRAFT" variant={SeaStatusType.Attention} />
        ) : (
          <></>
        )
      }
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        type !== 'view' ? (
          <SeaButton
            label={'Submit Form'}
            variant={SeaButtonVariant.Primary}
            key="Save Document"
            onPress={formik.handleSubmit}
          />
        ) : undefined
      }
      secondaryAction={
        type !== 'view' ? (
          <SeaButton
            label={'Save As Draft'}
            variant={SeaButtonVariant.Tertiary}
            key="Draft"
            onPress={async () => {
              await formik.setFieldValue('isDraft', true)
              formik.handleSubmit()
            }}
          />
        ) : undefined
      }
      headerActions={type === 'view' ? secondaryActionButton : undefined}>
      {/* TODO: Add this login when working on edit completed form */}
      {selectedCompletedForm &&
        customForm &&
        selectedVersion &&
        selectedVersion.version !== customForm.latestVersion && (
          <SeaTypography
            variant="body"
            color={theme.colors.status.critical}
            containerStyle={{ width: '100%', flex: 1, marginBottom: 20 }}>
            {`Note: This is an older version of the form dated ${formatDatetime(selectedVersion?.version)}.`}
          </SeaTypography>
        )}
      <View
        style={{
          width: '100%',
          flexWrap: 'wrap',
          flexDirection: 'row',
          gap: 10,
        }}>
        {customForm?.forVesselIds && customForm.forVesselIds[0] !== 'none' && (
          <View
            style={{
              width: calculateWidth(customForm?.vesselsElement?.width, isMobileWidth),
            }}>
            <VesselSelectInput
              label={customForm?.vesselsElement?.label ?? 'SELECT VESSELS / FACILITIES'}
              vesselIds={vesselIds}
              setVesselIds={setVesselIds}
              isMulti={customForm?.vesselsElement?.allowMultiple ?? false}
              vesselIdOptions={customForm.forVesselIds}
              errorText={formik?.errors?.vesselIds}
              hasError={!!formik?.errors?.vesselIds}
              forCustomForm
              disabled={type === 'view'}
            />
          </View>
        )}

        {customForm?.forCrew && (
          <View
            style={{
              width: calculateWidth(customForm?.crewElement?.width, isMobileWidth),
            }}>
            <SeaSelectInput
              label={customForm?.crewElement?.label ?? 'SELECT PERSONNEL'}
              isMulti={customForm?.crewElement?.allowMultiple ?? false}
              showSelectAllOption={false}
              data={crewOptions}
              selectedItemValues={formik.values.personnel}
              style={{ width: '100%' }}
              onItemSelect={selectPersonnel}
              errorText={formik.errors.personnel}
              hasError={!!formik.errors.personnel}
              forCustomForm
              disabled={type === 'view'}
            />
          </View>
        )}
        {formElements?.map(element => {
          return (
            <View
              key={element.n}
              style={{
                width: calculateWidth(element.width, isMobileWidth),
              }}>
              <SeaCustomFormElement
                element={{
                  ...element,
                  value: element.n !== undefined ? formik.values.formData[`e${element.n}`] : undefined,
                  error:
                    element.n !== undefined
                      ? Array.isArray(formik?.errors?.formData?.[`e${element.n}`])
                        ? (formik?.errors?.formData?.[`e${element.n}`] as string[])[0]
                        : typeof formik?.errors?.formData?.[`e${element.n}`] === 'string'
                          ? (formik?.errors?.formData?.[`e${element.n}`] as string)
                          : undefined
                      : undefined,
                }}
                setElement={(updatedElement: any) => {
                  formik.setFieldValue(`formData[e${element.n}]`, updatedElement.value)
                }}
                mode={type === 'view' ? 'view' : 'complete'}
                selectedElement={undefined}
                onSelectElement={element => {
                  // do nothing
                }}
              />
            </View>
          )
        })}
      </View>
    </SeaDrawer>
  )
}
