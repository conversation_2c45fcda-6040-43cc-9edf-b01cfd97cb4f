import { View, Text } from 'react-native'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { CustomForm } from '@src/shared-state/CompanyDocuments/CustomForms/customForms'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { FormikValues, useFormik } from 'formik'
import Yup from '@src/lib/yup'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { CustomFormTemplate } from '@src/shared-state/CompanyDocuments/CustomForms/customFormTemplates'
import {
  CreateCustomFormDto,
  CreateCustomFormUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/CreateCustomFormUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SaveStateAction, setSavingStateManager } from '@src/managers/SavingStateManager/SavingStateManager'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { formArrayToElements, formElementsToArray } from '@src/lib/customForms'
import { CustomFormElementType } from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { makeSeaFiles } from '@src/lib/files'

const validationSchema = Yup.object({
  title: Yup.string().max(500).required(),
  categoryId: Yup.string().max(500).required(),
  forVessels: Yup.boolean(),
  forCrew: Yup.boolean(),
})

export interface EditCustomFormDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  type: DrawerMode
  selectedForm?: CustomForm
  selectedTemplate?: Partial<CustomFormTemplate>
}

export function EditCustomFormDrawer({
  visible,
  onClose,
  style,
  type,
  selectedForm,
  selectedTemplate,
}: EditCustomFormDrawerProps) {
  const customFormCategories = sharedState.customFormCategories.use(visible)
  const superAdmin = sharedState.superAdmin.use(visible)
  const userId = sharedState.userId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const vesselIds = sharedState.vesselIds.use(visible)

  const [latestVersion, setLatestVersion] = useState<number | undefined>()
  const [formElement, setFormElement] = useState<CustomFormElementType[]>()
  const [historyElement, setHistoryElement] = useState<number>(0)

  const services = useServiceContainer()
  const router = useRouter()

  const categoriesOptions = useMemo(() => {
    if (!customFormCategories) return []

    const categories = customFormCategories?.ids.map(id => {
      const category = customFormCategories.byId[id]
      return {
        label: category.name,
        value: category.id,
      }
    })

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...(categories ?? []),
    ]
  }, [customFormCategories])

  const initialValues = useMemo(() => {
    return {
      title: selectedTemplate?.title ?? selectedForm?.title ?? '',
      categoryId: selectedTemplate?.categoryId ?? selectedForm?.categoryId ?? '',
      yesno: 0,
      isTemplate: selectedForm?.isTemplate ? true : false,
      forVessels: selectedForm?.forVesselIds.length !== 1 || selectedForm?.forVesselIds[0] !== 'none',
      forCrew: selectedForm?.forCrew ? true : false,
    }
  }, [selectedForm, selectedTemplate])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!licenseeId || !userId) {
        console.error('Licensee ID, or User ID is not available')
        return
      }

      let templateCategory = undefined as string | undefined
      if (superAdmin && values.isTemplate) {
        if (customFormCategories?.byId[values.categoryId]) {
          templateCategory = customFormCategories.byId[values.categoryId].name // Use name of existing category
        } else {
          templateCategory = values.categoryId // data.categoryId in this case contains the name of a new category
        }
      }

      // Handle form submission logic here
      const commonDto = {
        title: values.title,
        categoryId: values.categoryId,
        forVesselIds: values.forVessels ? vesselIds : ['none'],
        forCrew: values.forCrew,
        isTemplate: superAdmin ? (values.isTemplate ? true : false) : undefined,
        templateCategory,
      }

      if (type === 'create') {
        const dto: CreateCustomFormDto = {
          ...commonDto,
          form: formArrayToElements(formElement ?? []),
          historyElement,
          latestVersion,
        }

        const CustomForm = services.get(CreateCustomFormUseCase)
        CustomForm.execute(dto, userId, licenseeId)
          .then(data => {
            setSavingStateManager({
              action: SaveStateAction.SAVED,
              onCloseTimeout: 1000,
            })
            onClose()
            router.navigate({
              pathname: getRoutePath(Routes.CUSTOM_FORMS_BUILDER),
              params: {
                vesselId: vessel?.id,
                formId: data.ref.id,
              },
            })
          })
          .catch(err => {
            setSavingStateManager({
              action: SaveStateAction.ERROR,
              onCloseTimeout: 1000,
              message: `Error submitting form: ${err.message}`,
            })
            console.error(`Error submitting form\n ${err.message}`)
          })
      } else if (type === 'edit') {
        //
      }
    },
    [
      userId,
      licenseeId,
      type,
      superAdmin,
      customFormCategories,
      services,
      formElement,
      historyElement,
      latestVersion,
      vesselIds,
    ]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  useEffect(() => {
    if (selectedTemplate?.version) {
      setLatestVersion(selectedTemplate.version.version)
      const _formElements = formElementsToArray(selectedTemplate.version.form)
      setHistoryElement(selectedTemplate.version.historyElementN ?? 0)
      _formElements.forEach((element: any) => {
        if (element?.help?.files) {
          element.help.files = makeSeaFiles(element.help.files)
        }
      })
      setFormElement(_formElements)
    }
  }, [selectedTemplate])

  return (
    <SeaDrawer
      title={type === DrawerMode.Create ? 'Add New Form/Checklist' : 'Edit New Form/Checklist'}
      visible={visible}
      onClose={onClose}
      style={style}
      level={2}
      primaryAction={
        <SeaButton
          variant={SeaButtonVariant.Primary}
          onPress={formik.handleSubmit}
          label={type === DrawerMode.Create ? 'Save Form/Checklist' : 'Update Form/Checklist'}
        />
      }>
      <SeaStack direction={'column'} align="start" gap={20}>
        <SeaStack direction={'row'} gap={20} style={{ width: '100%' }}>
          <View style={{ flex: 1 }}>
            <SeaTextInput
              label={'Title'}
              value={formik.values.title}
              onChangeText={formik.handleChange('title')}
              hasError={Boolean(formik.errors.title && formik.errors.title)}
              errorText={formik.errors.title}
            />
          </View>

          <View style={{ flex: 1 }}>
            <SeaDropdown
              label={'Category'}
              items={categoriesOptions}
              value={formik.values.categoryId}
              onSelect={value => formik.setFieldValue('categoryId', value)}
              hasError={Boolean(formik.errors.categoryId && formik.touched.categoryId)}
              errorText={formik.errors.categoryId}
            />
          </View>
        </SeaStack>

        <SeaStack width={'100%'} direction="row" gap={20}>
          <SeaCheckbox
            heading="Is this for vessels / facilities?"
            label="Yes, link this form with vessels / facilities"
            value={formik.values.forVessels ? true : false}
            onChange={(changedValue: boolean) => {
              formik.setFieldValue('forVessels', changedValue)
            }}
          />
        </SeaStack>

        <SeaStack width={'100%'} direction="row" gap={20}>
          <SeaCheckbox
            heading="Is this for personnel?"
            label="Yes, link this form with personnel"
            value={formik.values.forCrew ? true : false}
            onChange={(changedValue: boolean) => {
              formik.setFieldValue('forCrew', changedValue)
            }}
          />
        </SeaStack>

        {superAdmin && (
          <SeaStack width={'100%'} direction="row" gap={20}>
            <SeaCheckbox
              heading="Template"
              label="Mark this as a template"
              value={formik.values.isTemplate ? true : false}
              onChange={(changedValue: boolean) => {
                formik.setFieldValue('isTemplate', changedValue)
              }}
            />
          </SeaStack>
        )}

        <SeaTypography variant="body" textStyle={{ fontSize: 12 }}>
          TODO: Add Last updated if edit form
        </SeaTypography>
      </SeaStack>

      {/* TODO LINKS */}
    </SeaDrawer>
  )
}
