import { View } from 'react-native'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import {
  CustomFormElementType,
  FormTextSize,
  SeaCustomFormElement,
} from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { CustomForm } from '@src/shared-state/CompanyDocuments/CustomForms/customForms'
import { sharedState } from '@src/shared-state/shared-state'
import { calculateWidth, formElementsToArray } from '@src/lib/customForms'
import { makeSeaFiles } from '@src/lib/files'
import { FieldSettings } from './FieldSettings'
import { ScrollView } from 'react-native-gesture-handler'
import { AddFormElements } from './AddFormElements'

const elementDefaults = {
  vessels: {
    name: 'Vessels',
    label: 'Select Vessels / Facilities',
    required: true,
  },
  input: {
    name: 'Text Field',
    label: 'TEXT FIELD',
    required: false,
  },
  textarea: {
    name: 'Text Area',
    label: 'TEXT AREA',
    lines: 3, // height = 37px + (lines * 15px)
    required: false,
  },
  checkbox: {
    name: 'Checkbox',
    label: 'Checked',
    required: false,
  },
  dropdown: {
    name: 'Dropdown',
    label: 'DROPDOWN',
    options: ['Option A', 'Option B'],
    required: false,
  },
  date: {
    name: 'Date',
    label: 'DATE',
    required: false,
  },
  datetime: {
    name: 'Date with Time',
    label: 'DATE WITH TIME',
    required: false,
  },
  yesno: {
    name: 'Yes | No',
    label: 'Question?',
  },
  checks: {
    name: 'Checklist',
    label: 'CHECKLIST',
    options: ['A check'],
  },
  files: {
    name: 'Images / Documents',
    label: 'IMAGES / DOCUMENTS',
    required: false,
  },
  signature: {
    name: 'Signature',
    label: 'SIGN OR INITIAL BELOW',
    required: true,
  },
  heading: {
    name: 'Heading',
    heading: 'A Heading',
    size: FormTextSize.MEDIUM,
  },
  text: {
    name: 'Text',
    content: 'Some text',
    size: FormTextSize.MEDIUM,
  },
  line: {},
  spacer: {
    height: 20,
  },
} as {
  [key: string]: Partial<CustomFormElementType>
}

interface FormBuilderProps {
  customForm: CustomForm
}
export function FormBuilder({ customForm }: FormBuilderProps) {
  const customFormVersions = sharedState.customFormVersions.use()

  const { styles } = useStyles(styleSheet)
  const [formElements, setFormElements] = useState<CustomFormElementType[]>([])
  const [selectedElement, setSelectedElement] = useState<CustomFormElementType>()
  const nRef = useRef(0)

  const onAddElement = useCallback(
    (id: string) => {
      nRef.current++
      const properties = {
        n: nRef.current,
        id: id,
        width: 100,
      }

      setFormElements([...formElements, Object.assign(properties, elementDefaults[id])])
    },
    [nRef, formElements, setFormElements]
  )

  useEffect(() => {
    const version = customFormVersions?.byFormIdAndVersion[customForm.id][customForm.latestVersion]
    let _formElements = [] as CustomFormElementType[]

    if (customForm.forVesselIds?.length > 0) {
      _formElements.push({
        isSpecial: true,
        id: 'vessels',
        n: -2,
        o: -2,
        label: customForm.vesselsElement?.label ?? 'SELECT VESSELS / FACILITIES',
        width: (customForm.vesselsElement?.width ?? 100).toString(),
        allowMultiple: customForm.vesselsElement?.allowMultiple ?? true,
        forVesselIds: [...customForm.forVesselIds],
        //value: forVesselIds,
      })
    }

    if (customForm.forCrew) {
      _formElements.push({
        isSpecial: true,
        id: 'crew',
        n: -1,
        o: -1,
        label: customForm.crewElement?.label ?? 'SELECT PERSONNEL',
        width: (customForm.crewElement?.width ?? 100).toString(),
        allowMultiple: customForm.crewElement?.allowMultiple ?? true,
      })
    }

    _formElements = version?.form ? [..._formElements, ...formElementsToArray(version.form)] : formElements
    // setHistoryElementN(version?.historyElementN ? version.historyElementN : 0)
    _formElements.forEach((element: CustomFormElementType) => {
      if (element?.help?.files) {
        element.help.files = makeSeaFiles(element.help.files)
      }

      if (element.n && element.n > nRef.current) {
        nRef.current = element.n
      }
    })

    setFormElements(_formElements)
  }, [customForm, customFormVersions])

  return (
    <View style={styles.formBuilder}>
      {/* Form Elements */}
      <View style={{ flex: 1, maxWidth: 280 }}>
        <AddFormElements onAddElement={onAddElement} />
      </View>

      {/* Form Body */}
      <View style={{ flex: 2, alignItems: 'center' }}>
        <ScrollView style={{ width: '100%' }} contentContainerStyle={{ alignItems: 'center' }}>
          <View
            style={[
              styles.container,
              {
                maxWidth: 800,
                boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
              },
            ]}>
            {formElements?.map(element => {
              return (
                <View
                  key={element.n}
                  style={{
                    width: calculateWidth(element.width, false, true),
                  }}>
                  <SeaCustomFormElement
                    element={element}
                    mode={'edit'}
                    selectedElement={selectedElement}
                    onSelectElement={element => setSelectedElement(element)}
                  />
                </View>
              )
            })}
          </View>
        </ScrollView>
      </View>

      {/* Field Settings */}
      <View style={{ flex: 1 }}>
        {selectedElement && (
          <FieldSettings
            selectedElement={selectedElement}
            updateElement={(updatedElement: CustomFormElementType) => {
              setFormElements(prev => prev.map(el => (el.n === updatedElement.n ? updatedElement : el)))
              setSelectedElement(updatedElement)
            }}
            removeElement={(element: CustomFormElementType) => {
              setFormElements(prev => prev.filter(el => el.n !== element.n))
              setSelectedElement(undefined)
            }}
            historyElementN={0}
            setHistoryElementN={n => {}}
          />
        )}
      </View>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  formBuilder: {
    flex: 1,
    height: '100%',
    flexDirection: 'row',
    gap: 10,
  },
  container: {
    width: '100%',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: 12,
    backgroundColor: theme.colors.white,
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
  },
  formElement: {
    width: '100%',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 4,
    backgroundColor: '#F1F2F5',
    gap: 10,
  },
}))
