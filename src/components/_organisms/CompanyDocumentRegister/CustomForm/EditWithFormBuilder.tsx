import React, { useMemo } from 'react'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { ScrollView } from 'react-native-gesture-handler'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { sharedState } from '@src/shared-state/shared-state'
import { useGlobalSearchParams } from 'expo-router'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { formatDatetime } from '@src/lib/datesAndTime'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { View } from 'react-native'
import { FormBuilder } from './FormBuilder'

export function EditWithFormBuilder() {
  const customForms = sharedState.customForms.use()
  const customFormCategories = sharedState.customFormCategories.use()

  const { formId } = useGlobalSearchParams()
  const { styles, theme } = useStyles(styleSheet)
  const { isDesktopWidth } = useDeviceWidth()

  const selectedItem = useMemo(() => {
    if (!customForms || !formId) return undefined

    return customForms.byId[Array.isArray(formId) ? formId[0] : formId]
  }, [formId, customForms])

  const selectedCategory = useMemo(() => {
    if (!selectedItem || !customFormCategories) return undefined
    return customFormCategories.byId[selectedItem.categoryId].name
  }, [selectedItem, customFormCategories])

  return (
    <RequirePermissions role="customForms" level={permissionLevels.CREATE} showDenial={true}>
      <View style={styles.container}>
        <SeaPageCard
          titleComponent={
            <SeaPageCardTitle
              customTitleRow={
                <SeaStack
                  direction="column"
                  gap={0}
                  align="start"
                  style={{
                    marginTop: 5,
                  }}>
                  {selectedCategory && (
                    <SeaTypography variant="caption" textStyle={{ margin: 0 }} containerStyle={{ margin: 0 }}>
                      {selectedCategory}
                    </SeaTypography>
                  )}
                  <SeaStack
                    direction={!isDesktopWidth ? 'column' : 'row'}
                    align={!isDesktopWidth ? 'start' : 'center'}
                    gap={10}>
                    <SeaTypography variant="title" textStyle={{ margin: 0 }} containerStyle={{ margin: 0 }}>
                      {selectedItem?.title}
                    </SeaTypography>

                    <SeaStack direction="row" align="center" gap={2}>
                      <SeaIcon icon="save" fill={false} color={theme.colors.grey} />
                      <SeaTypography
                        variant="body"
                        textStyle={{ margin: 0 }}
                        containerStyle={{ margin: 0 }}
                        color={theme.colors.grey}>
                        {selectedItem?.state === 'draft'
                          ? `Created on ${formatDatetime(selectedItem.whenAdded, ' ')}`
                          : `All changes saved on ${formatDatetime(selectedItem?.whenUpdated, ' ')}`}
                      </SeaTypography>
                    </SeaStack>
                  </SeaStack>
                </SeaStack>
              }
            />
          }
          primaryActionButton={
            isDesktopWidth ? (
              <SeaButton
                key={'Save'}
                label="Save"
                variant={SeaButtonVariant.Primary}
                // onPress={() => setIsVisibleEditDrawer(true)}
              />
            ) : undefined
          }
          secondaryActionButton={[
            <SeaEditButton
              key={'Edit'}
              // onPress={() => setIsVisibleEditDrawer(true)}
            />,
            <SeaButton key={'Preview'} label="Preview" variant={SeaButtonVariant.Tertiary} />,
          ]}
        />

        <View style={styles.formBuilderContainer}>
          {!isDesktopWidth ? (
            <SeaTypography variant="body" color={theme.colors.status.critical}>
              To edit forms/checklists you need be using the desktop app or a tablet with at least 1000 pixels of screen
              width.
            </SeaTypography>
          ) : selectedItem ? (
            <FormBuilder customForm={selectedItem} />
          ) : (
            <SeaTypography variant="body" color={theme.colors.status.critical}>
              No form selected. Please select a form to edit.
            </SeaTypography>
          )}
        </View>
      </View>

      {/* {isVisibleEditDrawer && (
            <EditCompanyDocumentsDrawer
              onClose={() => setIsVisibleEditDrawer(false)}
              selectedItem={selectedItem}
              visible={isVisibleEditDrawer}
              type={DrawerMode.Edit}
            />
          )} */}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
    height: '100%',
  },
  contentContainer: {
    flexGrow: 1,
    minHeight: '100%',
  },
  formBuilderContainer: {
    flex: 1,
    padding: 14,
  },
}))
