import { View } from 'react-native'
import React from 'react'

import { CustomFormTemplate } from '@src/shared-state/CompanyDocuments/CustomForms/customFormTemplates'
import { CustomFormPreview } from './CustomFormPreview'
import { CustomFormElementType } from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { SeaModal, SeaModalProps } from '@src/components/_atoms/SeaModal/v2/SeaModal'

interface ViewCustomFormTemplateModalProps extends Omit<SeaModalProps, 'children' | 'title'> {
  selectedTemplate?: CustomFormTemplate
  formElements: CustomFormElementType[]
}

export function ViewCustomFormTemplateModal({
  visible,
  onClose,
  maxHeight,
  maxWidth,
  style,
  selectedTemplate,
  actions,
  formElements,
}: ViewCustomFormTemplateModalProps) {
  return (
    <SeaModal
      title={selectedTemplate?.title ?? 'Custom Form Template'}
      visible={visible}
      onClose={onClose}
      maxHeight={maxHeight}
      maxWidth={maxWidth}
      style={style}
      actions={actions}>
      <View
        style={{
          width: '100%',
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        {selectedTemplate ? <CustomFormPreview customForm={selectedTemplate} formElements={formElements} /> : <></>}
      </View>
    </SeaModal>
  )
}
