import React, { useCallback, useMemo, useState } from 'react'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import {
  canAccessAllVessels,
  canAccessAnyVessels,
  canComplete,
  canEdit,
  permissionLevels,
} from '@src/shared-state/Core/userPermissions'
import { ScrollView } from 'react-native-gesture-handler'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { useGlobalSearchParams, useRouter } from 'expo-router'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { sharedState } from '@src/shared-state/shared-state'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import { formatDatetime } from '@src/lib/datesAndTime'
import { renderCategoryName } from '@src/lib/categories'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { CustomFormHistoryTable } from './CustomFormHistoryTable'
import { CompleteCustomFormDrawer } from './CompleteCustomFormDrawer'
import { CustomFormCompleted } from '@src/shared-state/CompanyDocuments/CustomForms/useCustomFormsCompleted'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

type TabValues = 'history'

const DESKTOP_ITEMS_WIDTH = '100%'

export function ViewCustomForm() {
  const vessel = sharedState.vessel.use()
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()
  const customForms = sharedState.customForms.use()
  const customFormCategories = sharedState.customFormCategories.use()
  const superAdmin = sharedState.superAdmin.use()

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)
  const [showCompletedModal, setShowCompletedModal] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [selectedHistoryForm, setSelectedHistoryForm] = useState<CustomFormCompleted | undefined>()
  const [activeTab, setActiveTab] = useState<TabValues>('history')

  const { formId } = useGlobalSearchParams()
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const services = useServiceContainer()
  const { styles } = useStyles(styleSheet)
  const router = useRouter()

  const selectedItem = useMemo(() => {
    return customForms?.byId[Array.isArray(formId) ? formId[0] : formId]
  }, [formId, customForms])

  const linkedTo = useMemo(() => {
    if (selectedItem?.forVesselIds && selectedItem.forVesselIds[0] !== 'none') {
      if (selectedItem?.forCrew) {
        return 'Vessels & Personnel'
      }
      return 'Vessels'
    } else if (selectedItem?.forCrew) {
      return 'Personnel'
    }
    return '-'
  }, [selectedItem])

  const canEditCustomForm = useMemo(() => {
    return (
      (canEdit('customForms') &&
        selectedItem?.forVesselIds &&
        (canAccessAllVessels(selectedItem.forVesselIds) || selectedItem.forVesselIds[0] === 'none')) ??
      superAdmin
    )
  }, [selectedItem?.forVesselIds, superAdmin])

  const canCompleteCustomForm = useMemo(() => {
    return (
      (canComplete('customForms') &&
        selectedItem?.forVesselIds &&
        (canAccessAnyVessels(selectedItem.forVesselIds) || selectedItem.forVesselIds[0] === 'none')) ??
      superAdmin
    )
  }, [selectedItem?.forVesselIds, superAdmin])

  const handleDelete = useCallback(() => {
    if (!selectedItem || !userId || !licenseeId) {
      console.error('Licensee ID, or User ID is not available')
      return
    }

    // const dto: DeleteCompanyDocumentDto = {
    //   id: selectedItem.id,
    //   title: selectedItem.title,
    // };

    // const deleteCompanyDocument = services.get(DeleteCompanyDocumentUseCase);

    // deleteCompanyDocument
    //   .execute(dto, userId, licenseeId)
    //   .then(() =>
    //     router.navigate({
    //       pathname: getRoutePath(Routes.COMPANY_DOCUMENTS),
    //       params: {
    //         vesselId,
    //       },
    //     }),
    //   )
    //   .catch((err) =>
    //     console.error(`Error deleting Company Document\n ${err.message}`),
    //   );
  }, [selectedItem, userId, licenseeId, services])

  const onDelete = useCallback(async () => {
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])
  return (
    <RequirePermissions role="customForms" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <SeaPageCard
          primaryActionButton={
            canCompleteCustomForm ? (
              <SeaButton
                key={'Complete'}
                label="Complete Task"
                iconOptions={{ icon: 'check' }}
                variant={SeaButtonVariant.Primary}
                onPress={() => setShowCompletedModal(true)} // TODO
              />
            ) : undefined
          }
          secondaryActionButton={[
            <>
              {canEditCustomForm && (
                <SeaEditButton
                  key={'Edit'}
                  onPress={() =>
                    router.navigate({
                      pathname: getRoutePath(Routes.CUSTOM_FORMS_BUILDER),
                      params: {
                        vesselId: vessel?.id,
                        formId,
                      },
                    })
                  }
                />
              )}
            </>,
            <RequirePermissions role="customForms" level={permissionLevels.FULL} key={'Delete'}>
              <SeaDeleteButton onPress={onDelete} />
            </RequirePermissions>,
          ]}
          subNav={[
            {
              title: 'History',
              onPress: () => setActiveTab('history'),
              isActive: activeTab === 'history',
            },
          ]}
          titleComponent={<SeaPageCardTitle title={selectedItem?.title} />}>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon={true}
                      label={'Last Updated'}
                      value={formatDatetime(selectedItem?.latestVersion)}
                    />

                    <SeaLabelValue
                      label={'Category'}
                      value={renderCategoryName(selectedItem?.categoryId, customFormCategories)}
                    />
                  </SeaStack>
                </SeaStack>
              </SeaStack>

              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                <SeaLabelValue label={'Linked To'} value={linkedTo} />

                <SeaLabelValue
                  label={'Template'}
                  value={selectedItem?.isTemplate ? 'This form is marked as a template!' : 'N/A'}
                />
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>

        {activeTab === 'history' && selectedItem && (
          <CustomFormHistoryTable
            selectedItem={selectedItem}
            setSelectedHistoryItem={item => {
              setSelectedHistoryForm(item)
              setShowCompletedModal(true)
            }}
          />
        )}
      </ScrollView>

      {showCompletedModal && (
        <CompleteCustomFormDrawer
          onClose={() => {
            setIsEditMode(false)
            setShowCompletedModal(false)
            setSelectedHistoryForm(undefined)
          }}
          customForm={selectedItem}
          visible={showCompletedModal}
          type={selectedHistoryForm && !isEditMode ? 'view' : selectedHistoryForm && isEditMode ? 'edit' : 'complete'}
          selectedCompletedForm={selectedHistoryForm}
          toggleEdit={() => setIsEditMode(!isEditMode)}
        />
      )}

      {/* {isVisibleEditDrawer && (
        <EditCompanyDocumentsDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleEditDrawer}
          type={DrawerMode.Edit}
        />
      )} */}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
    height: '100%',
  },
  content: {
    marginBottom: 20,
  },
  contentContainer: {
    flexGrow: 1,
    minHeight: '100%',
  },
  richTextContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
}))
