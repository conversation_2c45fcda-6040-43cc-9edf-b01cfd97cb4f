import { View, ScrollView } from 'react-native'
import React, { useCallback, useMemo, useState } from 'react'
import { SeaCard, SeaCardBody, SeaCardContent, SeaCardHeader } from '@src/components/_atoms/SeaCard/SeaCard'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { CustomFormElementType } from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaInputList } from '@src/components/_atoms/_inputs/SeaInputList/SeaInputList'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { VesselSelectInput } from '@src/components/_molecules/VesselSelectInput/VesselSelectInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaFile } from '@src/lib/fileImports'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { useStyles } from '@src/theme/styles'

interface FieldSettingsProps {
  selectedElement: CustomFormElementType // Replace with actual type if available
  updateElement: (element: CustomFormElementType) => void
  removeElement: (element: CustomFormElementType) => void
  // moveElement: (element: CustomFormElementType, movement: number) => void
  // canMoveElement: (element: CustomFormElementType, movement: number) => boolean
  historyElementN: number
  setHistoryElementN: (n: number) => void
}

export function FieldSettings({
  selectedElement,
  updateElement,
  removeElement,
  historyElementN,
  setHistoryElementN,
}: FieldSettingsProps) {
  const [files, setFiles] = useState<SeaFile[]>([])
  const { theme } = useStyles()

  const canShowInHistory = useMemo(() => {
    switch (selectedElement?.id) {
      case 'input':
      case 'textarea':
      case 'checkbox':
      case 'dropdown':
      case 'date':
      case 'datetime':
        return true

      default:
        return false
    }
  }, [selectedElement?.id])

  const canHaveHelp = useMemo(() => {
    if (selectedElement?.isSpecial) return false

    switch (selectedElement?.id) {
      case 'line':
      case 'spacer':
        return false

      default:
        return true
    }
  }, [selectedElement?.id])

  const contextualTextUploadedFiles = useMemo(() => {
    const uploadedFiles = selectedElement?.help?.files?.map((file: any) => file.uri ?? '')

    return uploadedFiles?.length ? uploadedFiles : []
  }, [selectedElement?.help?.files])

  const changeValue = useCallback(
    (id: keyof CustomFormElementType, value: any) => {
      const element: CustomFormElementType = { ...selectedElement }
      element[id] = value
      updateElement(element)
    },
    [selectedElement, updateElement]
  )

  console.debug(selectedElement)

  return (
    <SeaCard style={{ flex: 1 }}>
      <SeaCardHeader style={{ padding: 10 }}>
        <SeaTypography variant="body">Field Settings</SeaTypography>
      </SeaCardHeader>

      <SeaCardBody>
        <ScrollView style={{ flex: 1 }}>
          <SeaCardContent style={{ flex: 1 }}>
            <SeaStack direction="column" gap={20}>
              {selectedElement?.heading !== undefined && (
                <SeaTextInput
                  label="Heading"
                  value={selectedElement.heading}
                  onChangeText={value => changeValue('heading', value)}
                  noValidation
                />
              )}

              {selectedElement?.label !== undefined && (
                <SeaTextInput
                  label="Label"
                  value={selectedElement.label}
                  noValidation
                  onChangeText={value => changeValue('label', value)}
                />
              )}

              {selectedElement?.id === 'input' && (
                <SeaDropdown
                  label="Type"
                  value={selectedElement?.type}
                  items={[
                    {
                      label: 'Text',
                      value: '',
                    },
                    {
                      label: 'Number',
                      value: 'number',
                    },
                  ]}
                  onSelect={values => changeValue('type', values)}
                  style={{ width: '100%' }}
                  noValidation
                />
              )}

              {selectedElement?.lines !== undefined && (
                <SeaTextInput
                  label="Height (lines)"
                  value={selectedElement.lines.toString()}
                  noValidation
                  onChangeText={value => changeValue('lines', value)}
                />
              )}

              {selectedElement?.height !== undefined && (
                <SeaTextInput
                  label="Height (pixels)"
                  value={selectedElement.height.toString()}
                  noValidation
                  onChangeText={value => changeValue('height', value)}
                />
              )}

              {selectedElement?.description !== undefined && (
                <SeaTextInput
                  label="Description"
                  value={selectedElement.description}
                  noValidation
                  onChangeText={value => changeValue('description', value)}
                />
              )}

              {selectedElement?.text !== undefined && (
                <SeaTextInput
                  label="Text"
                  value={selectedElement.text}
                  noValidation
                  multiLine
                  onChangeText={value => changeValue('text', value)}
                />
              )}

              {selectedElement?.options !== undefined && (
                <SeaInputList
                  label={selectedElement.id === 'checks' ? 'Checks' : 'Dropdown Options'}
                  options={selectedElement.options}
                  onChange={options => changeValue('options', options)}
                />
              )}

              {selectedElement?.required !== undefined && (
                <SeaCheckbox
                  label="This is required"
                  onChange={value => changeValue('required', value ? true : false)}
                  value={selectedElement.required}
                />
              )}

              {canShowInHistory && (
                <SeaCheckbox
                  label="Show in history"
                  onChange={value => {
                    if (value) {
                      if (historyElementN && historyElementN !== selectedElement?.n) {
                        setHistoryElementN(selectedElement?.n ?? 0)
                      } else {
                        setHistoryElementN(selectedElement?.n ?? 0)
                      }
                    } else {
                      setHistoryElementN(0)
                    }
                  }}
                  value={selectedElement?.n === historyElementN}
                />
              )}

              {selectedElement?.size !== undefined && (
                <SeaDropdown
                  label="Size"
                  value={selectedElement?.size}
                  items={[
                    {
                      label: 'Large',
                      value: 'large',
                    },
                    {
                      label: 'Medium',
                      value: 'medium',
                    },
                    {
                      label: 'Small',
                      value: 'small',
                    },
                    { label: 'Tiny', value: 'tiny' },
                  ]}
                  onSelect={value => changeValue('size', value)}
                  style={{ width: '100%' }}
                  noValidation
                />
              )}

              <SeaDropdown
                label="Element Width"
                value={selectedElement.width}
                items={[
                  {
                    label: 'Full',
                    value: '100',
                  },
                  {
                    label: '1/2',
                    value: '50',
                  },
                  { label: '2/3', value: '66.66' },
                  { label: '3/4', value: '75' },
                  {
                    label: '1/3',
                    value: '33.33',
                  },
                  { label: '1/4', value: '25' },
                ]}
                onSelect={value => changeValue('width', value)}
                style={{ width: '100%' }}
                noValidation
              />

              {selectedElement?.forVesselIds !== undefined && (
                <VesselSelectInput
                  label="Vessels / facilities allowed"
                  vesselIds={selectedElement?.forVesselIds}
                  setVesselIds={vesselIds => changeValue('forVesselIds', vesselIds)}
                />
              )}

              {selectedElement?.allowMultiple && (
                <SeaCheckbox
                  label={`Allow multiple ${selectedElement.id === 'vessels' ? 'vessels / facilities' : 'personnel'}`}
                  onChange={value => changeValue('allowMultiple', value ? true : false)}
                  value={selectedElement?.allowMultiple ? true : false}
                />
              )}

              {canHaveHelp && (
                <>
                  <SeaCheckbox
                    label={'Contextual Text'}
                    onChange={value => changeValue('help', value ? { text: '' } : undefined)}
                    value={selectedElement?.help ? true : false}
                  />
                  {selectedElement?.help ? (
                    <>
                      <SeaTextInput
                        label="Helpful information"
                        value={selectedElement?.help?.text}
                        noValidation
                        onChangeText={value => {
                          const help = {
                            ...selectedElement?.help,
                            text: value,
                          }
                          changeValue('help', help)
                        }}
                      />
                      <SeaFileUploader
                        initialFiles={contextualTextUploadedFiles}
                        files={files}
                        setFiles={setFiles}
                        label="Helpful image(s)"
                      />
                    </>
                  ) : (
                    <></>
                  )}
                </>
              )}

              {selectedElement?.isSpecial ? (
                <SeaTypography
                  variant="body"
                  color={theme.colors.status.critical}
                  textStyle={{ margin: 0 }}
                  containerStyle={{ margin: 0 }}>
                  Note: This element is here because this form is associated with{' '}
                  {selectedElement.id === 'vessels' ? 'vessels' : 'personnel'}. It can't be removed and must remain at
                  the top of the page.
                </SeaTypography>
              ) : (
                <View
                  style={{
                    width: '100%',
                    flex: 1,
                    marginTop: 20,
                  }}>
                  <SeaButton
                    label="Remove Element"
                    variant={SeaButtonVariant.Secondary}
                    onPress={() => removeElement(selectedElement)}
                  />
                </View>
              )}
            </SeaStack>
          </SeaCardContent>
        </ScrollView>
      </SeaCardBody>
    </SeaCard>
  )
}
