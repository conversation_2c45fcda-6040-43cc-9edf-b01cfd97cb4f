import { ScrollView } from 'react-native'
import React, { useEffect, useMemo, useState } from 'react'
import { useGlobalSearchParams } from 'expo-router'
import { useCustomFormsCompleted } from '@src/shared-state/CompanyDocuments/CustomForms/useCustomFormsCompleted'
import { sharedState } from '@src/shared-state/shared-state'
import {
  SeaPageCard,
  SeaPageCardTitle,
  SecondaryActionButton,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'

import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import {
  canAccessAllVessels,
  canComplete,
  canDelete,
  canEdit,
  canView,
  permissionLevels,
} from '@src/shared-state/Core/userPermissions'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { CustomFormVersion } from '@src/shared-state/CompanyDocuments/CustomForms/customFormVersions'
import { makeFormElements } from '@src/lib/customForms'
import { CustomFormElementType } from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { formatDatetime } from '@src/lib/datesAndTime'
import { CompleteCustomFormDrawer } from './CompleteCustomFormDrawer'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'
import { CustomFormPreview } from './CustomFormPreview'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'

export function ViewCustomFormHistory() {
  const customForms = sharedState.customForms.use()
  const customFormVersions = sharedState.customFormVersions.use()
  const user = sharedState.user.use()

  const [selectedVersion, setSelectedVersion] = useState<CustomFormVersion | undefined>()
  const [formElements, setFormElements] = useState<CustomFormElementType[]>([])
  const [showCompletedModal, setShowCompletedModal] = useState(false)

  const { completedFormId, formId } = useGlobalSearchParams()
  const completedCustomFormsBeforeSearch = useCustomFormsCompleted(formId as string)

  const { styles, theme } = useStyles(styleSheet)

  const customForm = useMemo(() => {
    return customForms?.byId[Array.isArray(formId) ? formId[0] : formId]
  }, [formId, customForms])

  const selectedItem = useMemo(() => {
    return completedCustomFormsBeforeSearch?.byId[Array.isArray(completedFormId) ? completedFormId[0] : completedFormId]
  }, [completedFormId, completedCustomFormsBeforeSearch])

  useEffect(() => {
    if (!customForm) return

    const selectedVersion =
      customFormVersions?.byFormIdAndVersion[customForm.id][selectedItem?.version ?? customForm.latestVersion]

    setSelectedVersion(selectedVersion)
    const formElements = makeFormElements(selectedVersion, selectedItem)
    setFormElements(formElements)
  }, [customForm, selectedItem, customFormVersions, setFormElements])

  const userCanEdit = useMemo(() => {
    if (!selectedItem) return false
    const isCustomFormEditor =
      canEdit('customForms') && (selectedItem?.vesselIds[0] === 'none' || canAccessAllVessels(selectedItem?.vesselIds))

    const isCustomFormCompleter = canComplete('customForms') && selectedItem.isDraft

    const isVoyageEditor = selectedItem.attachTo === 'voyage' && canEdit('logbook')

    const isTrainingViewer = selectedItem.attachTo === 'trainingTaskReport' && canView('crewTraining')

    return isCustomFormEditor || isCustomFormCompleter || isVoyageEditor || isTrainingViewer
  }, [selectedItem, canEdit, canAccessAllVessels, canComplete, canView])

  const userCanDelete = useMemo(() => {
    if (!selectedItem) return false

    const isCustomFormDeleter = canDelete('customForms')

    const isCustomFormCompleterDeleter =
      canComplete('customForms') && selectedItem.isDraft && selectedItem.addedBy === user?.id

    const isVoyageEditor = selectedItem.attachTo === 'voyage' && canEdit('logbook')

    const isTrainingEditor = selectedItem.attachTo === 'trainingTaskReport' && canEdit('crewTraining')

    return isCustomFormDeleter || isCustomFormCompleterDeleter || isVoyageEditor || isTrainingEditor
  }, [selectedItem, user, canDelete, canComplete, canEdit])

  const secondaryActionButton = useMemo(() => {
    const actions = []

    if (userCanDelete) {
      actions.push(
        <SeaDeleteButton
        // onPress={onDelete}
        />
      )
    }

    return actions as SecondaryActionButton
  }, [userCanDelete])

  return (
    <RequirePermissions role="customForms" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container}>
        <SeaStack direction="column" gap={10} align="start">
          <SeaPageCard
            primaryActionButton={
              userCanEdit ? (
                <SeaEditButton
                  key="edit"
                  label={`Edit ${selectedItem?.isDraft ? 'Draft' : 'Completed Form'} `}
                  onPress={() => setShowCompletedModal(true)}
                  variant={SeaButtonVariant.Primary}
                />
              ) : undefined
            }
            secondaryActionButton={secondaryActionButton}
            titleComponent={
              <SeaPageCardTitle
                title={customForm?.title}
                additionalElements={
                  selectedItem?.isDraft ? (
                    <SeaStatusPill primaryLabel="DRAFT" variant={SeaStatusType.Attention} />
                  ) : undefined
                }
              />
            }>
            {selectedVersion?.version !== customForm?.latestVersion ? (
              <SeaStack direction="column" gap={0} align="start" width={'100%'}>
                <SeaTypography
                  variant="body"
                  color={theme.colors.status.critical}
                  containerStyle={{ width: '100%', flex: 1 }}>
                  {`Note: This is an older version of the form dated ${formatDatetime(selectedVersion?.version)}.`}
                </SeaTypography>
              </SeaStack>
            ) : (
              <></>
            )}
          </SeaPageCard>
          <SeaStack direction="column" gap={20} align="center" justify="center" width={'100%'}>
            {customForm ? (
              <CustomFormPreview
                customForm={customForm}
                vesselIds={selectedItem?.vesselIds}
                personnelIds={selectedItem?.personnelIds}
                formElements={formElements}
              />
            ) : (
              <SeaTypography variant="body" color={theme.colors.text.secondary}>
                No custom form found.
              </SeaTypography>
            )}
          </SeaStack>
        </SeaStack>
      </ScrollView>
      {showCompletedModal && (
        <CompleteCustomFormDrawer
          onClose={() => setShowCompletedModal(false)}
          customForm={customForm}
          selectedCompletedForm={selectedItem}
          visible={showCompletedModal}
          type={'edit'}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    width: '100%',
  },
}))
