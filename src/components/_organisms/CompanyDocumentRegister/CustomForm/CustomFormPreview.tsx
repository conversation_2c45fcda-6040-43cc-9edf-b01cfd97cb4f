import { View } from 'react-native'
import React from 'react'
import { CustomForm } from '@src/shared-state/CompanyDocuments/CustomForms/customForms'
import { calculateWidth } from '@src/lib/customForms'
import {
  CustomFormElementType,
  SeaCustomFormElement,
} from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { createStyleSheet, useStyles } from '@src/theme/styles'

interface CustomFormPreviewProps {
  customForm: CustomForm
  vesselIds?: string[]
  personnelIds?: string[]
  formElements?: CustomFormElementType[]
}
export function CustomFormPreview({ customForm, vesselIds, personnelIds, formElements }: CustomFormPreviewProps) {
  const { isMobileWidth, isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const { styles } = useStyles(styleSheet)

  return (
    <View
      style={[
        styles.container,
        {
          maxWidth: isDesktopWidth ? 800 : '100%',
          boxShadow: isLargeDesktopWidth ? '0px 4px 20px rgba(0, 0, 0, 0.1)' : 'none',
        },
      ]}>
      {formElements?.map(element => {
        return (
          <View
            key={element.n}
            style={{
              width: calculateWidth(element.width, isMobileWidth, true),
            }}>
            <SeaCustomFormElement
              element={element}
              mode="view"
              selectedElement={undefined}
              onSelectElement={element => {
                // do nothing
              }}
            />
          </View>
        )
      })}
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: 10,
    backgroundColor: theme.colors.white,
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
  },
}))
