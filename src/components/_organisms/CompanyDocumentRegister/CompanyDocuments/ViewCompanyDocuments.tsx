import { View } from 'react-native'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { ScrollView } from 'react-native-gesture-handler'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { sharedState } from '@src/shared-state/shared-state'
import { useGlobalSearchParams, useRouter } from 'expo-router'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import { getFileNameWithExtension, isPdf } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { formatValue } from '@src/lib/util'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { SeaPDFReader } from '@src/components/_atoms/SeaPDFReader/SeaPDFReader'
import SeaRichText from '@src/components/_dom/lexical/SeaRichText/SeaRichText'
import { initialRichTextState, loadSfdocNew, RichTextState } from '@src/lib/richText'
import { formatDate, formatEmailReminder, formatInterval, warnDays } from '@src/lib/datesAndTime'
import { EditCompanyDocumentsDrawer } from './EditCompanyDocumentsDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import {
  DeleteCompanyDocumentDto,
  DeleteCompanyDocumentUseCase,
} from '@src/domain/use-cases/companyDocumentRegister/DeleteCompanyDocumentUseCase'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'

const DESKTOP_ITEMS_WIDTH = '100%'

export function ViewCompanyDocuments() {
  const vesselId = sharedState.vesselId.use()
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()
  const companyDocuments = sharedState.companyDocuments.use()
  const companyDocumentCategories = sharedState.companyDocumentCategories.use()

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)
  const [richTextState, setRichTextState] = useState<RichTextState>(initialRichTextState)

  const scrollViewRef = useRef<ScrollView>(null)

  const { documentId } = useGlobalSearchParams()
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const services = useServiceContainer()
  const { styles } = useStyles(styleSheet)
  const router = useRouter()

  const selectedItem = useMemo(() => {
    return companyDocuments?.byId[Array.isArray(documentId) ? documentId[0] : documentId]
  }, [documentId, companyDocuments])

  const isPDFCheck = useCallback((file?: string) => {
    if (!file) return false

    return isPdf(file)
  }, [])

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return []

    return selectedItem?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('Coming soon!')} />],
    })) as MediaCardFile[]
  }, [selectedItem])

  const handleDelete = useCallback(() => {
    if (!selectedItem || !userId || !licenseeId) {
      console.error('Licensee ID, or User ID is not available')
      return
    }

    const dto: DeleteCompanyDocumentDto = {
      id: selectedItem.id,
      title: selectedItem.title,
    }

    const deleteCompanyDocument = services.get(DeleteCompanyDocumentUseCase)

    deleteCompanyDocument
      .execute(dto, userId, licenseeId)
      .then(() =>
        router.navigate({
          pathname: getRoutePath(Routes.COMPANY_DOCUMENTS),
          params: {
            vesselId,
          },
        })
      )
      .catch(err => console.error(`Error deleting Company Document\n ${err.message}`))
  }, [selectedItem, userId, licenseeId, services])

  const onDelete = useCallback(async () => {
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  useEffect(() => {
    if (selectedItem?.sfdoc) {
      const loadDocument = async () => {
        const loadedRichTextState = await loadSfdocNew(selectedItem.sfdoc)

        console.debug('Loaded Rich Text State:', loadedRichTextState)
        setRichTextState(loadedRichTextState as RichTextState)
      }

      loadDocument()
    }
  }, [selectedItem?.sfdoc])

  return (
    <RequirePermissions role="companyDocuments" level={permissionLevels.VIEW} showDenial={true}>
      <View style={styles.mainContainer}>
        <SeaPageCard
          secondaryActionButton={[
            <SeaEditButton key={'Edit'} onPress={() => setIsVisibleEditDrawer(true)} />,
            <RequirePermissions role="companyDocuments" level={permissionLevels.FULL} key={'Delete'}>
              <SeaDeleteButton onPress={onDelete} />
            </RequirePermissions>,
          ]}
          titleComponent={
            <SeaPageCardTitle
              title={selectedItem?.title}
              additionalElements={
                selectedItem?.dateExpires ? (
                  <WhenDueStatus
                    whenDue={selectedItem?.dateExpires}
                    warnDaysThreshold={warnDays.companyDocuments[0]}
                    hasFault={false}
                  />
                ) : undefined
              }
            />
          }>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Category'}
                      value={
                        selectedItem?.categoryId
                          ? formatValue(companyDocumentCategories?.byId[selectedItem.categoryId].name)
                          : ''
                      }
                    />

                    {selectedItem?.type === 'renewable' && (
                      <SeaLabelValue
                        iconOptions={{ icon: 'update' }}
                        showIcon
                        label={'Renewal Interval'}
                        value={formatValue(formatInterval(selectedItem?.interval))}
                      />
                    )}
                  </SeaStack>

                  {selectedItem?.type === 'renewable' && (
                    <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue
                        iconOptions={{ icon: 'calendar_month' }}
                        showIcon
                        label={'Expiry'}
                        value={formatDate(selectedItem?.dateExpires)}
                      />

                      <SeaLabelValue
                        label={'Notification'}
                        value={formatValue(formatEmailReminder(selectedItem?.emailReminder))}
                      />
                    </SeaStack>
                  )}
                </SeaStack>
              </SeaStack>

              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '30%' : '100%'}>
                <SeaMedia type="manuals" title="Files" files={uploadedFiles} />
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>
        {isPDFCheck(selectedItem?.files?.[0]) && <SeaPDFReader file={selectedItem?.files?.[0]} />}

        {selectedItem?.sfdoc && (
          <View style={styles.richTextContainer}>
            <SeaRichText
              forModal={false}
              visible={true}
              setOnScroll={() => console.log('TT-RT-SetOnScroll')}
              modalContentRef={scrollViewRef}
              richTextState={richTextState}
              onlineStatus={true}
            />
          </View>
        )}
      </View>

      {isVisibleEditDrawer && (
        <EditCompanyDocumentsDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleEditDrawer}
          type={DrawerMode.Edit}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  mainContainer: {
    flex: 1,
    height: '100%',
  },
  content: {
    marginBottom: 20,
  },
  contentContainer: {
    flexGrow: 1,
    minHeight: '100%',
  },
  richTextContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
}))
