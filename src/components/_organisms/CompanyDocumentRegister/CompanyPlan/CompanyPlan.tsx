import React, { useCallback, useEffect, useRef, useState } from 'react'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { ScrollView, StyleSheet, View } from 'react-native'
import { loadSfdocNew, RichTextState } from '@src/lib/richText'
import { getDefaultCompanyPlan } from '@src/components/_dom/default-to-move/defaultPlan'
import SeaRichText from '@src/components/_dom/lexical/SeaRichText/SeaRichText'
import { sharedState } from '@src/shared-state/shared-state'
import { regions } from '@src/lib/util'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { EditCompanyPlan } from '@src/components/_organisms/CompanyDocumentRegister/CompanyPlan/EditCompanyPlan'

interface CompanyPlanProps {
  // TODO
}

export const CompanyPlan = ({}: CompanyPlanProps) => {
  // Shared Data
  const companyPlan = sharedState.companyPlan.use()
  const licenseeSettings = sharedState.licenseeSettings.use()
  const companyPlanTitle = licenseeSettings ? regions[licenseeSettings?.region]?.companyPlan : ''
  const getDefaultContent = useCallback(() => {
    return getDefaultCompanyPlan(companyPlanTitle)
  }, [companyPlanTitle])

  // Create a ref for the ScrollView
  const scrollViewRef = useRef<ScrollView>(null)

  // Hooks
  const [richTextState, setRichTextState] = useState<RichTextState>({})
  const [editModalVisible, setEditModalVisible] = useState(false)

  // Handle loading
  useEffect(() => {
    if (companyPlan?.sfdoc) {
      const loadDocument = async () => {
        const loadedRichTextState = (await loadSfdocNew(
          companyPlan?.sfdoc ?? undefined,
          getDefaultContent,
          true
        )) as RichTextState
        setRichTextState(loadedRichTextState)
      }

      loadDocument()
    }
  }, [companyPlan?.sfdoc])

  return (
    <View style={styles.mainContainer}>
      <SeaPageCard
        titleComponent={<SeaPageCardTitle title={'Company Plan'} />}
        primaryActionButton={
          <RequirePermissions role="companyPlan" level={permissionLevels.EDIT}>
            <SeaEditButton
              key={'Edit'}
              label={'Edit'}
              variant={SeaButtonVariant.Secondary}
              onPress={() => setEditModalVisible(true)}
            />
          </RequirePermissions>
        }
        secondaryActionButton={[
          <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not implemented yet')} />,
          <SeaButton
            key={'print'}
            variant={SeaButtonVariant.Tertiary}
            iconOptions={{
              icon: 'print',
              fill: false,
            }}
            onPress={() => alert('This functionality is not implemented yet')}
          />,
          <SeaSettingsButton
            label={''}
            key={'settings'}
            onPress={() => alert('This functionality is not implemented yet')}
          />,
        ]}
      />

      <View style={styles.richTextContainer}>
        <SeaRichText
          forModal={false}
          visible={true}
          setOnScroll={() => console.log('TT-RT-SetOnScroll')}
          modalContentRef={scrollViewRef}
          richTextState={richTextState}
          onlineStatus={true}
        />
      </View>
      {editModalVisible && (
        <EditCompanyPlan
          visible={editModalVisible}
          onClose={() => {
            setEditModalVisible(false)
          }}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    height: '100%',
  },
  richTextContainer: {
    flex: 1,
    height: '100%',
    width: '100%',
  },
})
