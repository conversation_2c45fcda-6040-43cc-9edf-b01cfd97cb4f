import React, { useCallback, useEffect, useMemo } from 'react'
import { StyleSheet, useWindowDimensions, View } from 'react-native'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { SafetyCheckItem } from '@src/shared-state/VesselSafety/safetyCheckItems'
import { CategoriesData, renderCategoryName } from '@src/lib/categories'
import { formatInterval, formatValue } from '@src/lib/util'
import { formatDate, formatShortTimeDurationHrsMinsView, warnDays } from '@src/lib/datesAndTime'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { DateTime } from 'luxon'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { colors } from '@src/theme/colors'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { SeaStatusType } from '@src/types/Common'
import { SeaTableIcon } from '@src/components/_atoms/SeaTable/SeaTableIcon'
import { useLicenseeSettings } from '@src/hooks/useLicenseeSettings'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import {
  SeaTableIconCalendar,
  SeaTableIconFlag,
  SeaTableIconInterval,
  SeaTableIconLocation,
  SeaTableIconPerson,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

interface SafetyChecksTableProps {
  safetyCheckItems: SafetyCheckItem[]
  vesselSafetyItems: CategoriesData
  vesselLocations: CategoriesData
  vesselSafetyCheckCategories?: CategoriesData
  onSafetyCheckSelect: (selectedCheckId: string) => void
  showGrouped?: boolean
  style?: React.CSSProperties
}

export const SafetyChecksTable: React.FC<SafetyChecksTableProps> = ({
  safetyCheckItems,
  vesselSafetyItems,
  vesselLocations,
  vesselSafetyCheckCategories,
  style,
  onSafetyCheckSelect,
  showGrouped,
}) => {
  const checkIsCritical = useCallback((id: string) => !!vesselSafetyItems?.byId[id]?.isCritical, [vesselSafetyItems])

  const { hasTimeTrackingEnabled } = useLicenseeSettings()
  const { isTabletWidth } = useDeviceWidth()
  const { width } = useWindowDimensions()

  const seaTableColumns = useMemo(
    () =>
      buildColumns(vesselSafetyItems, vesselLocations, checkIsCritical, hasTimeTrackingEnabled, isTabletWidth, width),
    [vesselSafetyItems, vesselLocations, checkIsCritical, hasTimeTrackingEnabled, isTabletWidth, width]
  )

  const handlePress = (item: SafetyCheckItem) => {
    onSafetyCheckSelect(item.id)
  }

  const seaTableRows = useMemo(
    () => buildRows(safetyCheckItems, vesselSafetyCheckCategories, handlePress),
    [safetyCheckItems, vesselSafetyCheckCategories]
  )

  return (
    <View style={[styles.container, style]}>
      <SeaTable
        columns={seaTableColumns}
        rows={seaTableRows}
        showGroupedTable={showGrouped}
        sortFunction={(a, b) =>
          renderCategoryName(a.itemId, vesselSafetyItems).localeCompare(renderCategoryName(b.itemId, vesselSafetyItems))
        }
        style={{
          marginBottom: 50,
        }}
      />
    </View>
  )
}

const buildColumns = (
  vesselSafetyItems: CategoriesData,
  vesselLocations: CategoriesData,
  checkIsCritical: (id: string) => boolean,
  hasTimeTrackingEnabled: boolean,
  isTabletWidth: boolean,
  width: number
) => {
  return [
    {
      label: '',
      width: 60,
      render: x => <SeaTableImage files={x.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
    {
      label: 'Safety Item',
      value: x => renderCategoryName(x.itemId, vesselSafetyItems),
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Location',
      compactModeOptions: {
        hideRow: true,
      },
      isHidden: isTabletWidth,
      icon: () => <SeaTableIconLocation />,
      value: x => formatValue(renderCategoryName(x.locationId, vesselLocations)),
    },
    {
      label: 'Interval',
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
      icon: () => <SeaTableIconInterval />,
      value: x => formatInterval(x.interval),
    },
    {
      label: 'Next Check',
      compactModeOptions: {
        hideRow: true,
      },
      icon: () => <SeaTableIconCalendar />,
      value: x => formatDate(x.dateDue),
    },
    {
      label: 'Assigned To',
      isHidden: width < 1100,
      compactModeOptions: {
        hideRow: true,
      },
      icon: () => <SeaTableIconPerson />,
      value: x => formatValue(x.assignedTo?.map(id => renderFullNameForUserId(id))?.join(',')),
    },
    {
      label: 'Est Time',
      isHidden: !hasTimeTrackingEnabled || isTabletWidth,
      compactModeOptions: {
        hideRow: true,
      },
      value: x => formatValue(x.estimatedTime ? formatShortTimeDurationHrsMinsView(x.estimatedTime) : '-'),
    },
    {
      label: 'Status',
      compactModeOptions: {
        rowPosition: CompactRowPosition.TopRightCorner,
      },
      render: (x, isCompactView?: boolean) => (
        <WhenDueStatus
          whenDue={x.dateDue}
          warnDaysThreshold={warnDays.safetyEquipmentChecks[0]}
          hasFault={x.hasFault ?? false}
          compact={isCompactView}
        />
      ),
    },
    {
      label: 'Critical',
      compactModeOptions: {
        rowPosition: CompactRowPosition.BottomRightCorner,
      },
      render: x => <>{checkIsCritical(x.itemId) ? <SeaTableIconFlag /> : null}</>,
    },
  ] as SeaTableColumn<SafetyCheckItem>[]
}

const buildRows = (
  items: SafetyCheckItem[],
  vesselSafetyCheckCategories: CategoriesData | undefined,
  onPress: (item: SafetyCheckItem) => void
): SeaTableRow<SafetyCheckItem>[] => {
  return items.map(item => ({
    data: item,
    status: getStatusForItem(item),
    onPress: item => onPress(item),
    group: item => renderCategoryName(item.categoryId, vesselSafetyCheckCategories),
  }))
}

const getStatusForItem = (item: SafetyCheckItem) => {
  if (item.hasFault) return SeaStatusType.Critical

  const warnThresholdInDays = warnDays.safetyEquipmentChecks[0]
  const thresholdDate = DateTime.fromISO(item.dateDue)

  // TODO - Do we need to use 'today' from shared state here? Probably
  const now = DateTime.now().toLocal()

  const diff = thresholdDate.diff(now, 'days').toObject().days

  if (!diff) return SeaStatusType.Ok // TODO - How to handle?

  if (diff < 0) return SeaStatusType.Error
  if (diff >= 0 && diff <= 7) return SeaStatusType.Warning

  return SeaStatusType.Ok
}

const styles = StyleSheet.create({
  container: {},
})
