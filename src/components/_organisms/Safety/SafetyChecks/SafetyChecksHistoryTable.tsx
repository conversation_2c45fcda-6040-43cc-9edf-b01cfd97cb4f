import { SafetyCheckCompleted } from '@src/shared-state/VesselSafety/useCompletedSafetyCheckItems'
import React from 'react'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { formatDate, formatShortTimeDurationHrsMinsView } from '@src/lib/datesAndTime'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { formatValue } from '@src/lib/util'
import { SeaTableIcon } from '@src/components/_atoms/SeaTable/SeaTableIcon'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaStatusType } from '@src/types/Common'
import { SeaTableIconCalendar, SeaTableIconPerson } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

interface SafetyCheckHistoryTableProps {
  items: SafetyCheckCompleted[]
  hasTimeTrackingEnabled: boolean
  onRowPress?: (completedSafetyCheck: SafetyCheckCompleted) => void
}

export const SafetyCheckHistoryTable: React.FC<SafetyCheckHistoryTableProps> = ({
  items,
  hasTimeTrackingEnabled,
  onRowPress,
}) => {
  const columns: SeaTableColumn<SafetyCheckCompleted>[] = [
    {
      label: 'Date',
      icon: () => <SeaTableIconCalendar />,
      value: x => formatDate(x.whenCompleted),
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Completed By',
      icon: () => <SeaTableIconPerson />,
      value: x => renderFullNameForUserId(x.completedBy),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Actual Time',
      isHidden: !hasTimeTrackingEnabled,
      icon: x => (x.actualTime > 0 ? <SeaTableIcon icon={'timer'} /> : <></>),
      value: x => (x.actualTime > 0 ? formatValue(formatShortTimeDurationHrsMinsView(x.actualTime, true)) : '-'),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Notes',
      value: x =>
        x.shouldReportFault
          ? 'FAULT REPORTED'
          : formatValue(x.notes && x.notes?.length > 50 ? x.notes?.substring(0, 50) + '...' : x.notes),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: '',
      render: x => <SeaTableImage files={x.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
  ]

  const rows = items.map(item => ({
    data: item,
    onPress: onRowPress ? () => onRowPress(item) : undefined,
    status: getStatusForSafetyCheckItem(item),
  }))

  return <SeaTable columns={columns} rows={rows} />
}

export const getStatusForSafetyCheckItem = (item: SafetyCheckCompleted) => {
  if (item.shouldReportFault) return SeaStatusType.Critical

  return SeaStatusType.Ok
}
