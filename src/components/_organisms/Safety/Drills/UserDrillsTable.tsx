import React, { useMemo } from 'react'
import { Drill } from '@src/shared-state/VesselSafety/drills'
import { SeaTable, SeaTableColumn, SeaTableRow } from '@src/components/_atoms/SeaTable/SeaTable'
import { TrainingStatus, UserDrillsData } from '@src/hooks/useDrillsMatrix'
import { formatDateShort, formatInterval } from '@src/lib/datesAndTime'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'
import { TrainingStatusBadge } from '@src/components/_organisms/Safety/Drills/DrillStatusBadge'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { colors } from '@src/theme/colors'
import { View } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaTableIcon } from '@src/components/_atoms/SeaTable/SeaTableIcon'
import { SeaTableIconCalendar, SeaTableIconInterval } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

export interface UserDrillsTableProps {
  userDrillsData: UserDrillsData
  drills: Drill[]
}

export const UserDrillsTable = ({ userDrillsData, drills }: UserDrillsTableProps) => {
  const rows = useMemo(() => buildRows(userDrillsData, drills), [userDrillsData, drills])
  const columns = useMemo(() => buildColumns(drills, userDrillsData), [drills, userDrillsData])

  return <SeaTable rows={rows} columns={columns} />
}

const buildRows = (userDrillsData: UserDrillsData, drills: Drill[]): SeaTableRow<Drill>[] => {
  return drills.map(drill => {
    return {
      data: drill,
    }
  })
}

const buildColumns = (drills: Drill[], userDrillsData: UserDrillsData): SeaTableColumn<Drill>[] => {
  return [
    {
      label: 'Drill',
      compactModeOptions: {
        hideLabel: true,
      },
      render: x => (
        <View>
          <SeaTypography variant={'value'}>{x.name}</SeaTypography>
        </View>
      ),
      value: drill => drill.name,
      icon: x => <SeaTableIcon icon={'inventory'} />,
    },
    {
      label: 'Interval',
      compactModeOptions: { hideLabel: true },
      value: drill => formatInterval(drill.interval),
      icon: x => <SeaTableIconInterval />,
    },
    {
      label: 'Last Completed',
      compactModeOptions: { hideLabel: true },
      value: drill => {
        const data = userDrillsData.drillsData[drill.id]
        if (!data) return '-'
        return formatDateShort(data.dateLastCompleted)
      },
      icon: x => <SeaTableIconCalendar />,
    },
    {
      label: 'Next Due',
      compactModeOptions: { hideLabel: true },
      render: drill => {
        const data = userDrillsData.drillsData[drill.id]
        if (!data) return '-'
        return <TrainingStatusBadge status={data.status} daysRemaining={data.daysRemaining} />
      },
    },
  ]
}
