import { TrainingStatus } from '@src/hooks/useDrillsMatrix'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { StyleSheet } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import React from 'react'

export type TrainingCellProps = {
  numberOfDays?: number
  status?: TrainingStatus
}

/** Component used for showing training status in the matrix of either Drills or Crew Training */
export const TrainingCell = ({ numberOfDays, status }: TrainingCellProps) => {
  const cellStyle = getCellStyle(status)
  const cellContent = getCellContent(numberOfDays, status)

  return (
    <SeaStack
      direction={'column'}
      align={'center'}
      justify={'center'}
      style={StyleSheet.flatten([styles.cell, cellStyle])}>
      {cellContent}
    </SeaStack>
  )
}

const getCellContent = (numberOfDays?: number, status?: TrainingStatus) => {
  switch (status) {
    case TrainingStatus.Overdue:
      return (
        <>
          <SeaTypography
            variant={'value'}
            textStyle={StyleSheet.flatten([styles.cellNumber, { color: '#F8392C', fontWeight: 'bold' }])}>
            {Math.abs(numberOfDays)}
          </SeaTypography>
          <SeaTypography variant={'value'} textStyle={StyleSheet.flatten([styles.cellText, { color: '#F8392C' }])}>
            DAYS OD
          </SeaTypography>
        </>
      )
    case TrainingStatus.Upcoming:
    case TrainingStatus.Ok:
      return (
        <>
          <SeaTypography variant={'value'} textStyle={styles.cellNumber}>
            {numberOfDays}
          </SeaTypography>
          <SeaTypography variant={'value'} textStyle={styles.cellText}>
            DAYS
          </SeaTypography>
        </>
      )
    case TrainingStatus.Missing:
      return (
        <>
          <SeaTypography
            variant={'value'}
            textStyle={StyleSheet.flatten([styles.cellNumber, { color: '#8A8D93', fontSize: 12 }])}>
            MISSING
          </SeaTypography>
        </>
      )
    case TrainingStatus.Unassigned:
      return (
        <>
          <SeaTypography variant={'value'} textStyle={StyleSheet.flatten([styles.cellNumber, { color: '#8A8D93' }])}>
            NA
          </SeaTypography>
        </>
      )
  }
}

const getCellStyle = (status?: TrainingStatus) => {
  const cellStyles = StyleSheet.create({
    overdue: {
      backgroundColor: 'rgba(248,57,44,0.1)',
      borderColor: '#F8392C',
      borderWidth: 1,
    },
    upcoming: {
      backgroundColor: 'rgba(255,119,22,0.1)',
    },
    missing: {
      // backgroundColor: "rgba(163,29,220,0.1)",
      backgroundColor: 'rgba(163,29,220,0.07)',
    },
    okay: {
      backgroundColor: 'rgba(61,192,60,0.1)',
    },
    unassigned: {
      backgroundColor: '#F1F2F5',
    },
  })

  switch (status) {
    case TrainingStatus.Overdue:
      return cellStyles.overdue
    case TrainingStatus.Upcoming:
      return cellStyles.upcoming
    case TrainingStatus.Missing:
      return cellStyles.missing
    case TrainingStatus.Ok:
      return cellStyles.okay
    case TrainingStatus.Unassigned:
      return cellStyles.unassigned
  }
}

const styles = StyleSheet.create({
  cell: {
    height: 48,
    width: 100,
    borderRadius: 4,
    backgroundColor: 'rgba(61,192,60,0.1)',
  },
  cellOk: {
    backgroundColor: 'rgba(61,192,60,0.1)',
  },
  cellOverdue: {
    backgroundColor: 'rgba(255,193,7,0.1)',
  },
  cellUpcoming: {
    backgroundColor: 'rgba(255,59,48,0.1)',
  },
  cellUnassigned: {
    backgroundColor: 'rgba(255,59,48,0.1)',
  },
  cellMissing: {
    backgroundColor: 'rgba(255,59,48,0.1)',
  },
  cellNumber: {},
  cellText: { color: '#75787D' },
})
