import React from 'react'
import { DrillReport } from '@src/shared-state/VesselSafety/drillReports'
import { SeaDrawer } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { DrillHistoryTable } from '@src/components/_organisms/Safety/Drills/DrillHistoryTable'
import { Drill } from '@src/shared-state/VesselSafety/drills'

export interface DrillHistoryDrawerProps {
  visible: boolean
  onClose: () => void
  vesselDrills: Drill[]
  reports: DrillReport[]
  onReportPress: (report: DrillReport) => void
}

export const DrillHistoryDrawer = ({
  visible,
  onClose,
  vesselDrills,
  reports,
  onReportPress,
}: DrillHistoryDrawerProps) => {
  return (
    <SeaDrawer title={'Drill History'} visible={visible} onClose={onClose}>
      <DrillHistoryTable
        expanded
        vesselDrills={vesselDrills}
        drillReports={reports}
        onRowPress={() => {
          console.log('TODO')
        }}
      />
    </SeaDrawer>
  )
}
