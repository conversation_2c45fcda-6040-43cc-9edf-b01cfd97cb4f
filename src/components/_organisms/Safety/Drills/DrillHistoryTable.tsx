import React, { useMemo } from 'react'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { formatDateShort } from '@src/lib/datesAndTime'
import { DrillReport } from '@src/shared-state/VesselSafety/drillReports'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { Drill } from '@src/shared-state/VesselSafety/drills'
import { SeaTableIcon } from '@src/components/_atoms/SeaTable/SeaTableIcon'
import {
  SeaTableIconCalendar,
  SeaTableIconLocation,
  SeaTableIconPerson,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

export interface DrillHistoryTableProps {
  vesselDrills: Drill[]
  drillReports: DrillReport[]
  onRowPress: (report: DrillReport) => void
  showCompletedBy?: boolean
  showDrillsCompleted?: boolean
}

export const DrillHistoryTable = ({
  drillReports,
  vesselDrills,
  onRowPress,
  showCompletedBy,
  showDrillsCompleted,
}: DrillHistoryTableProps) => {
  const rows = useMemo(() => buildRows(drillReports, onRowPress), [drillReports, onRowPress])
  const columns = useMemo(
    () => buildColumns(vesselDrills, showCompletedBy, showDrillsCompleted),
    [vesselDrills, showCompletedBy, showDrillsCompleted]
  )

  return <SeaTable rows={rows} columns={columns} />
}

const buildRows = (
  drillReports: DrillReport[],
  onRowPress: (report: DrillReport) => void
): SeaTableRow<DrillReport>[] => {
  return drillReports.map(report => {
    return {
      data: report,
      onPress: () => onRowPress(report),
    }
  })
}

const buildColumns = (
  vesselDrills: Drill[],
  showCompletedBy?: boolean,
  showDrillsCompleted?: boolean
): SeaTableColumn<DrillReport>[] => {
  return [
    {
      label: 'Date',
      icon: () => <SeaTableIconCalendar />,
      value: report => formatDateShort(report.dateCompleted),
      width: 120,
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Location',
      icon: () => <SeaTableIconLocation />,
      value: (report: DrillReport) => report.location ?? '-',
      widthPercentage: 0.2,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    ...(showCompletedBy
      ? [
          {
            label: 'Completed By',
            icon: () => <SeaTableIconPerson />,
            value: (report: DrillReport) => {
              if (!report.crewInvolvedIds) return '-'

              return report.crewInvolvedIds.map(x => renderFullNameForUserId(x)).join(', ')
            },
            compactModeOptions: {
              label: {
                show: true,
                valueWidth: CompactValueWidth.Medium,
                name: 'Completed',
              },
            },
          },
        ]
      : []),
    ...(showDrillsCompleted
      ? [
          {
            label: 'Drills Completed',
            icon: () => <SeaTableIcon icon={'checklist'} />,
            value: (report: DrillReport) => {
              if (!report.drillIds) return '-'

              return report.drillIds
                .map(drillId => {
                  // TODO - Use a lookup instead of array find here
                  const drill = vesselDrills.find(d => d.id === drillId)
                  return drill?.name
                })
                .join(', ')
            },
            compactModeOptions: {
              label: {
                show: true,
                valueWidth: CompactValueWidth.Medium,
                name: 'Past Drills',
              },
            },
          },
        ]
      : []),
  ]
}
