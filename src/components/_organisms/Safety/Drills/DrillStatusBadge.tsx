import { TrainingStatus } from '@src/hooks/useDrillsMatrix'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'
import React from 'react'

export interface TrainingStatusBadgeProps {
  status: TrainingStatus
  daysRemaining?: number
}
export const TrainingStatusBadge = ({ status, daysRemaining }: TrainingStatusBadgeProps) => {
  const daysText = daysRemaining ? `${Math.abs(daysRemaining)}d` : ''
  switch (status) {
    case TrainingStatus.Overdue:
      return <SeaStatusPill variant={SeaStatusType.Error} primaryLabel={'OVERDUE'} secondaryLabel={daysText} />
    case TrainingStatus.Upcoming:
      return <SeaStatusPill variant={SeaStatusType.Warning} primaryLabel={'UPCOMING'} secondaryLabel={daysText} />
    case TrainingStatus.Missing:
      return <SeaStatusPill variant={SeaStatusType.Attention} primaryLabel={'MISSING'} secondaryLabel={`   ??`} />
    case TrainingStatus.Ok:
      return <SeaStatusPill variant={SeaStatusType.Ok} primaryLabel={'CURRENT'} secondaryLabel={daysText} />
    case TrainingStatus.Unassigned:
      return <SeaStatusPill variant={SeaStatusType.NotApplicable} primaryLabel={'NOT ASSIGNED'} />
  }
}
