import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { DrillReport } from '@src/shared-state/VesselSafety/drillReports'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { DateTime } from 'luxon'
import { useFormik } from 'formik'
import { sharedState } from '@src/shared-state/shared-state'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { CreateDrillReportDto, CreateDrillReportUseCase } from '@src/domain/use-cases/safety/CreateDrillReportUseCase'
import { UpdateDrillReportDto, UpdateDrillReportUseCase } from '@src/domain/use-cases/safety/UpdateDrillReportUseCase'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { CheckBoxActions, SimpleSelectionData } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import Yup from '@src/lib/yup'
import { SeaFile } from '@src/lib/fileImports'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

const validationSchema = Yup.object({
  drillIds: Yup.array().of(Yup.string()).min(1, 'At least one Drill is required').required(),
  crewInvolvedIds: Yup.array().of(Yup.string()).min(1, 'At least one Crew Member is required').required(),
  dateCompleted: Yup.date().required(),
})

export interface ModifyDrillReportDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: DrillReport
  selectedDrillId?: string
  selectedUserId?: string
  mode: DrawerMode
}

export const ModifyDrillReportDrawer = ({
  visible,
  onClose,
  mode,
  selectedItem,
  selectedDrillId,
  selectedUserId,
}: ModifyDrillReportDrawerProps) => {
  const vesselId = sharedState.vesselId.use()
  const vesselDrills = sharedState.vesselDrills.use(visible)
  const licenseeUsers = sharedState.users.use(visible)

  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const logger = useLogger('ModifyDrillReportDrawer')
  const serviceContainer = useServiceContainer()
  const createDrillReportUseCase = serviceContainer.get(CreateDrillReportUseCase)
  const updateDrillReportUseCase = serviceContainer.get(UpdateDrillReportUseCase)

  const drills = useMemo(() => {
    if (!vesselDrills) return []

    return vesselDrills?.all.filter(x => x.state === 'active')
  }, [vesselDrills])

  const drillOptions = useMemo(() => drills?.map(d => ({ label: d.name, value: d.id })), [drills])

  const users = useMemo(() => {
    if (!licenseeUsers || !vesselId) return []

    return licenseeUsers?.all.filter(x => x.state === 'active' && x.crewVesselIds?.includes(vesselId))
  }, [vesselId, licenseeUsers])

  const userOptions = useMemo(
    () =>
      users?.map(u => ({
        label: renderFullNameForUserId(u.id),
        value: u.id,
      })),
    [users]
  )

  const title = mode === DrawerMode.Create ? 'Complete Drill Report' : 'Edit Drill Report'

  const [files, setFiles] = useState<SeaFile[]>([])

  const initialValues = {
    dateCompleted: selectedItem?.dateCompleted ?? DateTime.now().toISODate(),
    location: selectedItem?.location ?? '',
    drillIds: selectedItem ? selectedItem.drillIds : selectedDrillId ? [selectedDrillId] : [],
    crewInvolvedIds: selectedItem ? selectedItem.crewInvolvedIds : selectedUserId ? [selectedUserId] : [],
    scenario: selectedItem?.scenario ?? '',
    equipment: selectedItem?.equipment ?? '',
    furtherTraining: selectedItem?.furtherTraining ?? '',
    modification: selectedItem?.modification ?? '',
  }

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
  })

  const executeCreate = (values: typeof initialValues, userId: string, vesselId: string, licenseeId: string) => {
    logger.info('Executing Create')

    const selectedDrills = drills?.filter(x => values.drillIds.includes(x.id))

    const dto: CreateDrillReportDto = {
      vesselId,
      drills: selectedDrills,
      crewInvolvedIds: values.crewInvolvedIds,
      dateCompleted: values.dateCompleted,
      location: values.location,
      scenario: values.scenario,
      equipment: values.equipment,
      furtherTraining: values.furtherTraining,
      modification: values.modification,
      files: [], // TODO
      signature: '', // TODO
    }

    createDrillReportUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Executed Create')
        resetForm()
        onClose()
      })
      .catch(err => {
        logger.error(err.message, err)
        resetForm()
        onClose()
      })
  }

  const executeUpdate = (userId: string, vesselId: string, licenseeId: string) => {
    logger.info('Executing Update')
    const dto: UpdateDrillReportDto = {}

    updateDrillReportUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Executed Update')
        resetForm()
        onClose()
      })
      .catch(err => {
        logger.error(err.message, err)
        resetForm()
        onClose()
      })
  }

  const doSubmit = useCallback(
    (values: typeof initialValues) => {
      logger.info('Form submitting with values:', { values })

      if (!userId || !vesselId || !licenseeId) {
        return logger.error('Cannot submit form due to missing User ID, Vessel ID, or Licensee ID')
      }

      return mode === DrawerMode.Create
        ? executeCreate(values, userId, vesselId, licenseeId)
        : executeUpdate(userId, vesselId, licenseeId)
    },
    [userId, vesselId, licenseeId]
  )

  return (
    <SeaDrawer
      title={title}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      primaryAction={
        <SeaButton
          variant={SeaButtonVariant.Primary}
          label={mode === DrawerMode.Create ? 'Complete Drill Report' : 'Save Drill Report'}
          iconOptions={{ icon: mode === DrawerMode.Create ? 'check' : 'save' }}
          onPress={handleSubmit}
        />
      }>
      <SeaStack direction={'column'} gap={20} width={'100%'} justify={'start'} align={'start'}>
        {/* Row 1 */}
        <SeaStack isCollapsible direction={'row'} gap={20} justify={'between'} width={'100%'}>
          <SeaDateTimeInput
            label={'Drill Date'}
            value={DateTime.fromISO(values.dateCompleted)}
            onChange={date => setFieldValue('dateCompleted', date.toISODate())}
            type={'datetime'}
            style={{ width: '100%', flex: 1 }}
          />
          <SeaTextInput
            label={'Location'}
            value={values.location}
            onChangeText={handleChange('location')}
            style={{ width: '100%', flex: 1 }}
          />
        </SeaStack>

        {/* Row 2 */}
        <SeaStack isCollapsible direction={'row'} gap={20} justify={'between'} width={'100%'}>
          <SeaSelectInput
            isMulti={true}
            showSelectAllOption={false}
            selectedItemValues={values.drillIds}
            onItemSelect={(action, changedValues) => {
              switch (action) {
                case CheckBoxActions.SELECT:
                  return setFieldValue('drillIds', [...values.drillIds, changedValues])
                case CheckBoxActions.DESELECT:
                  return setFieldValue(
                    'drillIds',
                    [...values.drillIds].filter(x => x !== changedValues)
                  )
              }
            }}
            label={'Drill Type'}
            data={drillOptions}
            hasError={Boolean(errors.drillIds)}
            errorText={errors.drillIds as string}
            style={{ width: '100%', flex: 1 }}
          />
        </SeaStack>

        {/* Row 3 */}
        <SeaStack isCollapsible direction={'row'} gap={20} width={'100%'} justify={'between'}>
          <SeaSelectInput
            isMulti={true}
            showSelectAllOption={false}
            selectedItemValues={values.crewInvolvedIds}
            onItemSelect={(action, changedValues) => {
              switch (action) {
                case CheckBoxActions.SELECT:
                  return setFieldValue('crewInvolvedIds', [...values.crewInvolvedIds, changedValues])
                case CheckBoxActions.DESELECT:
                  return setFieldValue(
                    'crewInvolvedIds',
                    [...values.crewInvolvedIds].filter(x => x !== changedValues)
                  )
              }
            }}
            label={'Personnel Present'}
            data={userOptions as SimpleSelectionData}
            hasError={Boolean(errors.crewInvolvedIds)}
            errorText={errors.crewInvolvedIds as string}
            style={{ width: '100%', flex: 1 }}
          />
        </SeaStack>

        {/* Row 4 */}
        <SeaStack isCollapsible direction={'row'} gap={20} width={'100%'} justify={'between'}>
          <SeaTextInput
            multiLine
            label={'Scenario'}
            value={values.scenario}
            onChangeText={handleChange('scenario')}
            style={{ width: '100%', flex: 1 }}
          />
          <SeaTextInput
            multiLine
            label={'Equipment Used'}
            value={values.equipment}
            onChangeText={handleChange('equipment')}
            style={{ width: '100%', flex: 1 }}
          />
        </SeaStack>

        {/* Row 5 */}
        <SeaStack isCollapsible direction={'row'} gap={20} justify={'between'} width={'100%'}>
          <SeaTextInput
            multiLine
            label={'Further Training Required'}
            value={values.furtherTraining}
            onChangeText={handleChange('furtherTraining')}
            style={{ width: '100%', flex: 1 }}
          />
          <SeaTextInput
            multiLine
            label={'Modification to Current Procedures'}
            value={values.modification}
            onChangeText={handleChange('modification')}
            style={{ width: '100%', flex: 1 }}
          />
        </SeaStack>

        {/* Row 6 */}
        <SeaStack direction="column" align="start" width={'100%'}>
          <SeaTypography variant="label">DOCUMENTS</SeaTypography>
          <SeaStack isCollapsible direction={'row'} gap={20} justify={'between'} width={'100%'}>
            <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
          </SeaStack>
        </SeaStack>
      </SeaStack>
    </SeaDrawer>
  )
}
