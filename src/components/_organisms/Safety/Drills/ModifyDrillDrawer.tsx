import React, { useMemo, useState } from 'react'
import { KeyboardAvoidingView, StyleSheet, ViewStyle } from 'react-native'
import { DrawerMode, SeaDrawer } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { sharedState } from '@src/shared-state/shared-state'
import { IntervalDropdown } from '@src/components/_molecules/IntervalDropdown/IntervalDropdown'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { useFormik } from 'formik'
import Yup from '@src/lib/yup'
import { CrewSelectInput } from '@src/components/_molecules/CrewSelectInput/CrewSelectInput'
import { UserType } from '@src/shared-state/Core/user'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { Drill } from '@src/shared-state/VesselSafety/drills'
import { UpdateDrillDto, UpdateDrillUseCase } from '@src/domain/use-cases/safety/UpdateDrillUseCase'
import { CreateDrillDto, CreateDrillUseCase } from '@src/domain/use-cases/safety/CreateDrillUseCase'

interface ModifyDrillDrawerProps {
  visible: boolean
  onClose: () => void
  mode: DrawerMode
  selectedDrill?: Drill
  style?: ViewStyle
}

const validationSchema = Yup.object({
  name: Yup.string().required('Drill name is required').min(1, 'Drill name cannot be empty'),
  interval: Yup.string().required('Interval is required'),
  notAssignedTo: Yup.array().of(Yup.string()),
})

export const useInitialDrillValues = (drill?: Drill) => {
  return useMemo(() => {
    return {
      name: drill?.name ?? '',
      interval: drill?.interval ?? '',
      notAssignedTo: drill?.notAssignedTo ?? [],
    }
  }, [drill])
}

export const ModifyDrillDrawer: React.FC<ModifyDrillDrawerProps> = ({
  visible,
  onClose,
  mode,
  selectedDrill,
  style,
}) => {
  const title = useMemo(() => (mode === DrawerMode.Edit ? 'Update Drill' : 'Add Drill'), [mode])

  const logger = useLogger(`ModifyDrillDrawer:${mode}`)

  const userId = sharedState.userId.use()
  const users = sharedState.users.use()
  const vesselId = sharedState.vesselId.use()
  const licenseeId = sharedState.licenseeId.use()

  const initialValues = useInitialDrillValues(selectedDrill)

  const crewSelectionData = useMemo(() => {
    if (!vesselId) return []

    const vesselUsers = users?.byVesselId[vesselId]

    if (!vesselUsers) return []

    return vesselUsers
      .filter((u: UserType) => u.state === 'active')
      .map(u => ({ label: renderFullNameForUserId(u.id), value: u.id }))
  }, [users, vesselId])

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
  })

  const serviceContainer = useServiceContainer()
  const updateDrillUseCase = serviceContainer.get(UpdateDrillUseCase)
  const createDrillUseCase = serviceContainer.get(CreateDrillUseCase)

  const executeUpdate = (values: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    if (!selectedDrill?.id) {
      throw new Error('Cannot update drill without ID')
    }

    const dto: UpdateDrillDto = {
      drillId: selectedDrill.id,
      vesselId,
      name: values.name,
      interval: values.interval,
      notAssignedTo: values.notAssignedTo,
      currentDrill: selectedDrill,
    }

    updateDrillUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Drill updated successfully')
      })
      .catch(err => logger.error(`Error updating drill\n ${err.message}`, err))
  }

  const executeCreate = (values: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    const dto: CreateDrillDto = {
      vesselId,
      name: values.name,
      interval: values.interval,
      notAssignedTo: values.notAssignedTo,
    }

    createDrillUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Drill created successfully')
      })
      .catch(err => logger.error(`Error creating drill\n ${err.message}`, err))
  }

  const doSubmit = (values: typeof initialValues) => {
    if (!userId || !licenseeId || !vesselId) {
      throw new Error('Missing Licensee or User')
    }

    if (mode === DrawerMode.Edit) {
      executeUpdate(values, vesselId, userId, licenseeId)
    } else if (mode === DrawerMode.Create) {
      executeCreate(values, vesselId, userId, licenseeId)
    }

    resetForm()
    onClose()
  }

  return (
    <SeaDrawer
      title={title}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      primaryAction={
        <SeaButton
          iconOptions={{ icon: mode === DrawerMode.Edit ? 'edit' : 'save' }}
          variant={SeaButtonVariant.Primary}
          onPress={handleSubmit}
          label={title}
        />
      }>
      <KeyboardAvoidingView>
        <SeaStack direction={'column'} align={'start'} gap={10} style={styles.column}>
          {/* Row 1 - Drill Name */}
          <SeaStack isCollapsible align={'start'} direction={'row'} gap={10} style={styles.row}>
            <SeaTextInput
              label={'Drill Name'}
              value={values.name}
              onChangeText={handleChange('name')}
              hasError={Boolean(errors.name && touched.name)}
              errorText={errors.name}
              style={{ flex: 1, width: '100%' }}
            />
          </SeaStack>

          {/* Row 2 - Interval */}
          <SeaStack isCollapsible align={'start'} direction={'row'} gap={10} style={styles.row}>
            <IntervalDropdown
              value={values.interval}
              onChange={handleChange('interval')}
              hasError={Boolean(errors.interval && touched.interval)}
              errorText={errors.interval}
              style={{ width: '100%', flex: 1 }}
            />
          </SeaStack>

          {/* Row 3 - Excluded Users */}
          <SeaStack width={'100%'}>
            <CrewSelectInput
              label={'Excluded Users'}
              selectedIds={values.notAssignedTo}
              data={crewSelectionData}
              onChange={excludedUsers => setFieldValue('notAssignedTo', excludedUsers)}
            />
          </SeaStack>

          <SeaSpacer height={50} />
        </SeaStack>
      </KeyboardAvoidingView>
    </SeaDrawer>
  )
}

const styles = StyleSheet.create({
  container: {},
  column: {},
  row: {
    width: '100%',
  },
})
