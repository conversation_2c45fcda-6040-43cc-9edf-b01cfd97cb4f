import React, { useMemo, useState } from 'react'
import { <PERSON>er<PERSON><PERSON>, SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { useFormik } from 'formik'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { sharedState } from '@src/shared-state/shared-state'
import { StyleSheet } from 'react-native'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { DateTime } from 'luxon'
import { SafetyEquipmentItem } from '@src/shared-state/VesselSafety/safetyEquipmentItems'
import {
  CompleteSafetyEquipmentExpiryDto,
  CompleteSafetyEquipmentExpiryUseCase,
} from '@src/domain/use-cases/safety/CompleteSafetyEquipmentExpiryUseCase'
import {
  UpdateCompletedSafetyEquipmentExpiryDto,
  UpdateCompletedSafetyEquipmentExpiryUseCase,
} from '@src/domain/use-cases/safety/UpdateCompletedSafetyEquipmentExpiryUseCase'
import { SafetyEquipmentTaskCompleted } from '@src/shared-state/VesselSafety/useCompletedSafetyEquipmentItems'
import Yup from '@src/lib/yup'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'

const validationSchema = Yup.object({
  whenCompleted: Yup.number().required(),
  newExpiryDate: Yup.number().required(),
})

export interface ModifyCompletedSafetyEquipmentExpiryDrawerProps
  extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  mode: DrawerMode
  selectedItem?: SafetyEquipmentItem
  existingCompletion?: SafetyEquipmentTaskCompleted
}

export const ModifyCompletedSafetyEquipmentExpiryDrawer = ({
  visible,
  onClose,
  mode,
  selectedItem,
  existingCompletion,
}: ModifyCompletedSafetyEquipmentExpiryDrawerProps) => {
  const [isDrawerReadOnly, setIsDrawerReadOnly] = useState(mode === DrawerMode.Edit)

  const drawerTitle = useMemo(() => {
    if (mode === DrawerMode.Create) return 'Complete Safety Task'

    return isDrawerReadOnly ? 'View Completed Safety Task' : 'Edit Completed Safety Task'
  }, [mode, isDrawerReadOnly])

  const initialValues = useMemo(() => {
    if (mode === DrawerMode.Edit && existingCompletion) {
      return {
        whenCompleted: existingCompletion.whenCompleted,
        newExpiryDate: existingCompletion.dateExpires
          ? DateTime.fromISO(existingCompletion.dateExpires).toUTC().toMillis()
          : DateTime.now().toUTC().toMillis(),
        notes: existingCompletion.notes ?? '',
        files: [], // TODO: Handle existing files
      }
    }
    return {
      whenCompleted: DateTime.now().toUTC().toMillis(),
      newExpiryDate: DateTime.now().toUTC().toMillis(),
      notes: '',
      files: [], // TODO
    }
  }, [mode, existingCompletion])

  const [files, setFiles] = useState<any[]>([])

  const logger = useLogger(`ModifyCompletedSafetyEquipmentExpiryDrawer:${mode}`)
  const serviceContainer = useServiceContainer()
  const completeSafetyEquipmentExpiryUseCase = serviceContainer.get(CompleteSafetyEquipmentExpiryUseCase)
  const updateCompletedSafetyEquipmentExpiryUseCase = serviceContainer.get(UpdateCompletedSafetyEquipmentExpiryUseCase)

  const licenseeId = sharedState.licenseeId.use()
  const vesselId = sharedState.vesselId.use()
  const userId = sharedState.userId.use()

  const doSubmit = (vals: typeof initialValues) => {
    if (!userId || !vesselId || !licenseeId || !selectedItem) {
      throw new Error('Unable to create/update Safety Equipment Expiry: Missing User ID, Vessel ID, or Licensee ID')
    }

    if (mode === DrawerMode.Create) {
      const dto: CompleteSafetyEquipmentExpiryDto = {
        vesselId,
        safetyEquipmentId: selectedItem.id,
        type: selectedItem.type,
        interval: selectedItem.interval,
        emailReminder: selectedItem.emailReminder,
        notes: vals.notes,
        whenCompleted: vals.whenCompleted,
        whenLastChecked: selectedItem.whenLastChecked,
        newExpiryDate: DateTime.fromMillis(vals.newExpiryDate).toISODate() ?? undefined,
        files: files,
      }
      completeSafetyEquipmentExpiryUseCase
        .execute(dto, userId, licenseeId)
        .then(() => {
          logger.info('Safety Equipment Expiry completed successfully')
          resetForm()
          onClose()
        })
        .catch(err => logger.error(`Error completing Safety Equipment Expiry\n ${err.message}`, err))
    } else if (mode === DrawerMode.Edit && existingCompletion) {
      const dto: UpdateCompletedSafetyEquipmentExpiryDto = {
        completedSafetyEquipmentExpiryId: existingCompletion.id,
        vesselId,
        safetyEquipmentId: selectedItem.id,
        whenCompleted: vals.whenCompleted,
        notes: vals.notes,
        files: files,
        newExpiryDate:
          selectedItem.type === 'expiring'
            ? (DateTime.fromMillis(vals.newExpiryDate).toISODate() ?? undefined)
            : undefined,
      }
      updateCompletedSafetyEquipmentExpiryUseCase
        .execute(dto, userId, licenseeId)
        .then(() => {
          logger.info('Completed Safety Equipment Expiry updated successfully')
          resetForm()
          onClose()
        })
        .catch(err => logger.error(`Error updating Completed Safety Equipment Expiry\n ${err.message}`, err))
    }
  }

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
    enableReinitialize: true,
  })

  return (
    <SeaDrawer
      title={drawerTitle}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      readonly={isDrawerReadOnly}
      headerActions={
        isDrawerReadOnly
          ? [
              <RequirePermissions key={'edit'} role={'safetyEquipmentList'} level={permissionLevels.EDIT}>
                <SeaEditButton key={'edit'} label={'Edit'} onPress={() => setIsDrawerReadOnly(false)} />
              </RequirePermissions>,
            ]
          : []
      }
      primaryAction={
        <RequirePermissions
          role={'safetyEquipmentList'}
          level={mode === DrawerMode.Create ? permissionLevels.COMPLETE : permissionLevels.EDIT}>
          <SeaButton
            variant={SeaButtonVariant.Primary}
            iconOptions={{ icon: 'check' }}
            label={mode === DrawerMode.Create ? 'Complete' : 'Update'}
            onPress={handleSubmit}
          />
        </RequirePermissions>
      }
      secondaryAction={
        <SeaButton
          variant={SeaButtonVariant.Tertiary}
          iconOptions={{ icon: 'close' }}
          label={'Cancel'}
          onPress={onClose}
        />
      }>
      <SeaStack direction={'column'} align={'start'} gap={20} style={styles.content}>
        <SeaStack isCollapsible direction={'row'} gap={20} justify={'start'} width={'100%'}>
          <SeaDateTimeInput
            value={DateTime.fromMillis(values.whenCompleted)}
            onChange={date => setFieldValue('whenCompleted', date.toUTC().toMillis())}
            type={'date'}
            label={'When Completed'}
            hasError={Boolean(errors.whenCompleted)}
            errorText={errors.whenCompleted}
            disabled={isDrawerReadOnly}
            style={{ width: '100%', flex: 1 }}
          />
        </SeaStack>
        {selectedItem?.type === 'expiring' && (
          <SeaStack isCollapsible direction={'row'} gap={20} justify={'start'} width={'100%'}>
            <SeaDateTimeInput
              value={DateTime.fromMillis(values.newExpiryDate)}
              onChange={date => setFieldValue('newExpiryDate', date.toUTC().toMillis())}
              type={'date'}
              label={'New Expiry Date'}
              hasError={selectedItem?.type === 'expiring' && Boolean(errors.newExpiryDate)}
              errorText={errors.newExpiryDate}
              disabled={isDrawerReadOnly}
              style={{ width: '100%', flex: 1 }}
            />
          </SeaStack>
        )}
        <SeaTextInput
          multiLine
          value={values.notes}
          onChangeText={handleChange('notes')}
          label={'Notes'}
          disabled={isDrawerReadOnly}
        />
        <SeaFileUploader
          label={'IMAGES / DOCUMENTS'}
          initialFiles={existingCompletion?.files ?? []}
          files={files}
          setFiles={setFiles}
          disabled={isDrawerReadOnly}
        />
      </SeaStack>
    </SeaDrawer>
  )
}

const styles = StyleSheet.create({
  content: {
    // backgroundColor: "magenta",
  },
})
