import React, { useEffect, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SafetyEquipmentItem } from '@src/shared-state/VesselSafety/safetyEquipmentItems'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { useFormik } from 'formik'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import {
  ADD_NEW_DROPDOWN_VALUE,
  AddNewDropdownItem,
  SeaDropdown,
  SeaDropdownItem,
} from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { sharedState } from '@src/shared-state/shared-state'
import { StyleSheet } from 'react-native'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { useServiceContainer, useLogger } from '@src/providers/ServiceProvider'
import {
  CreateSafetyEquipmentExpiryDto,
  CreateSafetyEquipmentExpiryUseCase,
} from '@src/domain/use-cases/safety/CreateSafetyEquipmentExpiryUseCase'
import { formatEmailReminder } from '@src/lib/datesAndTime'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { DateTime } from 'luxon'
import { SeaIntervalDropdown } from '@src/components/_atoms/SeaIntervalDropdown/SeaIntervalDropdown'
import {
  UpdateSafetyEquipmentExpiryDto,
  UpdateSafetyEquipmentExpiryUseCase,
} from '@src/domain/use-cases/safety/UpdateSafetyEquipmentExpiryUseCase'
import Yup from '@src/lib/yup'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'

import { SeaFile } from '@src/lib/fileImports'
import { useDeviceWidth } from '@src/hooks/useDevice'

const validationSchema = Yup.object({
  safetyItemId: Yup.string().required(),
  expiryDate: Yup.date().when('type', {
    is: 'expiring',
    then: schema => schema.required(),
    otherwise: schema => schema.optional(),
  }),
  type: Yup.string().required(),
  interval: Yup.string().when('type', {
    is: 'servicable',
    then: schema => schema.required(),
    otherwise: schema => schema.optional(),
  }),
  description: Yup.string().max(5000),
  assignedTo: Yup.string(),
  isCritical: Yup.boolean(),
  newSafetyItemName: Yup.string().when('safetyItemId', {
    is: ADD_NEW_DROPDOWN_VALUE,
    then: schema => schema.required('Please enter a value'),
    otherwise: schema => schema.optional(),
  }),
  newLocationName: Yup.string().when('locationId', {
    is: ADD_NEW_DROPDOWN_VALUE,
    then: schema => schema.required('Please enter a value'),
    otherwise: schema => schema.optional(),
  }),
})

export interface ModifySafetyEquipmentExpiryDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: SafetyEquipmentItem
  mode: DrawerMode
  isCritical?: boolean
}

export const ModifySafetyEquipmentExpiryDrawer = ({
  selectedItem,
  visible,
  onClose,
  mode,
  isCritical = false,
}: ModifySafetyEquipmentExpiryDrawerProps) => {
  const drawerTitle = mode === DrawerMode.Create ? 'Create Safety Equipment Expiry' : 'Edit Safety Equipment Expiry'

  const logger = useLogger(`ModifySafetyEquipmentExpiryDrawer:${mode}`)

  const initialValues = {
    safetyItemId: selectedItem?.itemId ?? '',
    locationId: selectedItem?.locationId ?? '',
    isCritical: isCritical,
    type: selectedItem?.type ?? '',
    quantity: selectedItem?.quantity ?? '',

    interval: selectedItem?.interval ?? '',
    lastCheck: selectedItem?.whenLastChecked ?? DateTime.now().toMillis(),

    expiryDate: selectedItem?.dateDue ?? DateTime.now().toISODate(),

    serviceTask: selectedItem?.description ?? '',
    emailReminder: selectedItem?.emailReminder ?? '',
    newSafetyItemName: '',
    newLocationName: '',
  }
  const [files, setFiles] = useState<SeaFile[]>([])

  const { isMobileWidth } = useDeviceWidth()

  const serviceContainer = useServiceContainer()
  const createSafetyEquipmentExpiryUseCase = serviceContainer.get(CreateSafetyEquipmentExpiryUseCase)
  const updateSafetyEquipmentExpiryUseCase = serviceContainer.get(UpdateSafetyEquipmentExpiryUseCase)

  const licenseeId = sharedState.licenseeId.use()
  const vesselId = sharedState.vesselId.use()
  const userId = sharedState.userId.use()

  const { values, handleChange, setFieldValue, handleSubmit, resetForm, errors, touched } = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => doSubmit(values),
  })

  // Safety Item Options
  const vesselSafetyItems = sharedState.vesselSafetyItems.use()
  const safetyItemOptions = useMemo(() => {
    if (!vesselSafetyItems) return []

    const result = Object.entries(vesselSafetyItems.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => {
        return {
          label: value.name,
          value: key,
        }
      })

    return [AddNewDropdownItem, ...result]
  }, [vesselSafetyItems])

  const isNewSafetyItem = useMemo(
    () => values.safetyItemId === ADD_NEW_DROPDOWN_VALUE,
    [values.safetyItemId, ADD_NEW_DROPDOWN_VALUE]
  )

  // Location Options
  const vesselLocations = sharedState.vesselLocations.use()
  const vesselLocationOptions = useMemo<SeaDropdownItem<string>[]>(() => {
    if (!vesselLocations) return []

    const result = Object.entries(vesselLocations.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => {
        return {
          label: value.name,
          value: key,
        }
      })

    return [AddNewDropdownItem, ...result]
  }, [vesselLocations])

  const isNewLocationItem = useMemo(
    () => values.locationId === ADD_NEW_DROPDOWN_VALUE,
    [values.locationId, ADD_NEW_DROPDOWN_VALUE]
  )

  const expiryTypeOptions = [
    { label: 'Servicable Equipment', value: 'servicable' },
    { label: 'Expiring Equipment', value: 'expiring' },
    { label: 'Non-Expiring Equipment', value: 'nonExpiring' },
  ]

  const emailReminderOptions = [
    { label: formatEmailReminder('0d'), value: '0d' },
    { label: formatEmailReminder('1d'), value: '1d' },
    { label: formatEmailReminder('2d'), value: '2d' },
    { label: formatEmailReminder('3d'), value: '3d' },
    { label: formatEmailReminder('7d'), value: '7d' },
    { label: formatEmailReminder('14d'), value: '14d' },
    { label: formatEmailReminder('1m'), value: '1m' },
  ]

  const executeCreate = (vals: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    const dto: CreateSafetyEquipmentExpiryDto = {
      vesselId: vesselId,
      safetyItemId: isNewSafetyItem ? undefined : vals.safetyItemId,
      isNewSafetyItem: isNewSafetyItem,
      newSafetyItemName: isNewSafetyItem ? vals.newSafetyItemName : undefined,
      type: vals.type,
      isCritical: vals.isCritical,
      locationId: isNewLocationItem ? undefined : vals.locationId,
      isNewLocation: isNewLocationItem,
      newLocationName: isNewLocationItem ? vals.newLocationName : undefined,
      quantity: vals.quantity,

      interval: vals.interval,
      lastCheck: vals.type === 'servicable' ? vals.lastCheck : undefined,
      description: vals.serviceTask,
      expiryDate: vals.type === 'expiring' ? vals.expiryDate : '',
      emailReminder: vals.emailReminder,

      files: files,
    }

    logger.debug('Creating Safety Equipment Expiry with dto', { dto })

    createSafetyEquipmentExpiryUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Safety Equipment Expiry created successfully')
        resetForm()
        onClose()
      })
      .catch(err => logger.error(`Error creating Safety Equipment Expiry\n ${err.message}`, err))
  }

  const executeUpdate = (vals: typeof initialValues, vesselId: string, userId: string, licenseeId: string) => {
    const docId = selectedItem?.id
    if (!docId) {
      throw new Error('Cannot execute Update without Doc ID')
    }

    const dto: UpdateSafetyEquipmentExpiryDto = {
      docId: docId,
      vesselId: vesselId,
      safetyItemId: isNewSafetyItem ? undefined : vals.safetyItemId,
      isNewSafetyItem: isNewSafetyItem,
      newSafetyItemName: isNewSafetyItem ? vals.newSafetyItemName : undefined,
      type: vals.type,
      isCritical: vals.isCritical,
      locationId: isNewLocationItem ? undefined : vals.locationId,
      isNewLocation: isNewLocationItem,
      newLocationName: isNewLocationItem ? vals.newLocationName : undefined,
      quantity: vals.quantity,

      interval: vals.interval,
      lastCheck: vals.type === 'servicable' ? vals.lastCheck : undefined,
      description: vals.serviceTask,
      expiryDate: vals.type === 'expiring' ? vals.expiryDate : '',
      emailReminder: vals.emailReminder,

      files: files,
    }

    logger.debug('Updating Safety Equipment Expiry with dto', { dto })

    updateSafetyEquipmentExpiryUseCase
      .execute(dto, userId, licenseeId)
      .then(() => {
        logger.info('Safety Equipment Expiry updated successfully')
        resetForm()
        onClose()
      })
      .catch(err => logger.error(`Error updating Safety Equipment Expiry\n ${err.message}`, err))
  }

  const doSubmit = (vals: typeof initialValues) => {
    console.log('Submitting')
    if (!userId || !vesselId || !licenseeId) {
      throw new Error('Unable to create Safety Equipment Expiry: Missing User ID, Vessel ID, or Licensee ID')
    }

    mode === DrawerMode.Create
      ? executeCreate(vals, vesselId, userId, licenseeId)
      : executeUpdate(vals, vesselId, userId, licenseeId)
  }

  return (
    <SeaDrawer
      title={drawerTitle}
      visible={visible}
      onClose={() => {
        resetForm()
        onClose()
      }}
      primaryAction={
        mode === DrawerMode.Create ? (
          <SeaButton
            variant={SeaButtonVariant.Primary}
            iconOptions={{ icon: 'save' }}
            label={'Create'}
            onPress={handleSubmit}
          />
        ) : (
          <SeaButton
            variant={SeaButtonVariant.Primary}
            iconOptions={{ icon: 'edit' }}
            label={'Edit'}
            onPress={handleSubmit}
          />
        )
      }>
      <SeaStack direction={'column'} gap={10} style={styles.content} width={'100%'}>
        <SeaStack isCollapsible align={'start'} direction={'row'} gap={10} width={'100%'}>
          <SeaStack isCollapsible direction={'column'} gap={5} style={{ flex: 1, width: '100%' }}>
            <SeaDropdown
              items={safetyItemOptions}
              value={values.safetyItemId}
              onSelect={handleChange('safetyItemId')}
              label={'Safety Item'}
              style={{ flex: 1, width: '100%' }}
              hasError={Boolean(errors.safetyItemId)}
              errorText={errors.safetyItemId}
            />
            {isNewSafetyItem && (
              <SeaTextInput
                value={values.newSafetyItemName}
                onChangeText={handleChange('newSafetyItemName')}
                placeholder="Enter new safety item name"
                hasError={Boolean(errors.newSafetyItemName) && touched.newSafetyItemName}
                errorText={errors.newSafetyItemName}
                style={{ flex: isMobileWidth ? 1 : 0.5, width: '100%' }}
              />
            )}
          </SeaStack>
          <SeaStack direction={'column'} gap={5} style={{ flex: 1, width: '100%' }}>
            <SeaDropdown
              items={vesselLocationOptions}
              value={values.locationId}
              onSelect={handleChange('locationId')}
              label={'Location'}
              style={{ flex: 1, width: '100%' }}
            />
            {isNewLocationItem && (
              <SeaTextInput
                value={values.newLocationName}
                onChangeText={handleChange('newLocationName')}
                placeholder="Enter new location"
                hasError={Boolean(errors.newLocationName) && touched.newLocationName}
                errorText={errors.newLocationName}
                style={{ flex: isMobileWidth ? 1 : 0.5, width: '100%' }}
              />
            )}
          </SeaStack>
        </SeaStack>

        <SeaStack direction={'row'} gap={5} width={'100%'} align={'start'}>
          <SeaCheckbox
            heading={'Critical Equipment'}
            label={'This equipment is critical'}
            value={values.isCritical}
            onChange={x => setFieldValue('isCritical', x)}
            style={{ width: '100%' }}
          />
        </SeaStack>

        <SeaStack isCollapsible direction={'row'} gap={10} justify={'between'} width={'100%'}>
          <SeaDropdown
            items={expiryTypeOptions}
            value={values.type}
            onSelect={handleChange('type')}
            label={'Type'}
            style={{ width: '100%', flex: 1 }}
            hasError={Boolean(errors.type)}
            errorText={errors.type}
          />
          <SeaTextInput
            label={'Quantity'}
            value={values.quantity}
            onChangeText={handleChange('quantity')}
            style={{ width: '100%', flex: 1 }}
          />
        </SeaStack>

        {(values.type === 'servicable' || values.type === 'expiring') && (
          <SeaStack isCollapsible direction={'column'} align={'start'} gap={10} style={styles.content} width={'100%'}>
            <SeaStack isCollapsible direction={'row'} gap={10} width={'100%'} justify={'between'}>
              {values.type === 'servicable' && (
                <>
                  <SeaIntervalDropdown
                    value={values.interval}
                    onSelect={handleChange('interval')}
                    label={'Interval'}
                    hasError={Boolean(errors.interval)}
                    errorText={errors.interval}
                    style={{ width: '100%', flex: 1 }}
                  />
                  <SeaDateTimeInput
                    label={'Last Check'}
                    type={'date'}
                    value={DateTime.fromMillis(values.lastCheck)}
                    onChange={date => setFieldValue('lastCheck', date.toMillis())}
                    style={{ width: '100%', flex: 1 }}
                  />
                </>
              )}
              {values.type === 'expiring' && (
                <>
                  <SeaDateTimeInput
                    value={DateTime.now()}
                    onChange={date => {
                      setFieldValue('expiryDate', date.toISODate())
                    }}
                    type={'date'}
                    label={'Expiry Date'}
                    style={{ width: '100%', flex: 1 }}
                  />
                </>
              )}
            </SeaStack>
            <SeaTextInput
              multiLine
              label={'Service Task'}
              value={values.serviceTask}
              onChangeText={handleChange('serviceTask')}
            />

            <SeaStack isCollapsible direction={'row'} align={'start'} width={'50%'} gap={10}>
              <SeaDropdown
                items={emailReminderOptions}
                value={values.emailReminder}
                onSelect={handleChange('emailReminder')}
                label={'Set Email Reminder'}
                style={{ width: '100%', flex: 1 }}
              />
            </SeaStack>
          </SeaStack>
        )}
        <SeaStack direction={'column'} align={'start'} width={'100%'}>
          <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
        </SeaStack>
      </SeaStack>
    </SeaDrawer>
  )
}

const styles = StyleSheet.create({
  content: {
    // backgroundColor: "magenta",
  },
})
