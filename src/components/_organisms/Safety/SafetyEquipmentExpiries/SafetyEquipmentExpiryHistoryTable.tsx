import React from 'react'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SafetyEquipmentTaskCompleted } from '@src/shared-state/VesselSafety/useCompletedSafetyEquipmentItems'
import { formatDateShort } from '@src/lib/datesAndTime'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { formatValue } from '@src/lib/util'
import { SeaTableIcon } from '@src/components/_atoms/SeaTable/SeaTableIcon'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconCalendar, SeaTableIconPerson } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

export type SafetyEquipmentExpiryHistoryTableProps = {
  items?: SafetyEquipmentTaskCompleted[]
  onPress: (item: SafetyEquipmentTaskCompleted) => void
}

export const SafetyEquipmentExpiryHistoryTable = ({ items, onPress }: SafetyEquipmentExpiryHistoryTableProps) => {
  if (!items) return <></>

  const rows = buildRows(items, onPress)
  const columns = buildColumns()

  return (
    <RequirePermissions role={'safetyEquipmentList'} level={permissionLevels.VIEW} showDenial={true}>
      <SeaTable
        rows={rows}
        columns={columns}
        style={{
          marginBottom: 50,
        }}
      />
    </RequirePermissions>
  )
}

const buildRows = (
  items: SafetyEquipmentTaskCompleted[],
  onPress: (item: SafetyEquipmentTaskCompleted) => void
): SeaTableRow<SafetyEquipmentTaskCompleted>[] => {
  return items.map(data => ({ data, onPress }))
}
const buildColumns = (): SeaTableColumn<SafetyEquipmentTaskCompleted>[] => {
  return [
    {
      label: 'Date',
      width: 130,
      value: x => formatDateShort(x.whenCompleted),
      icon: () => <SeaTableIconCalendar />,
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: 'Completed By',
      width: 150,
      value: x => renderFullNameForUserId(x.completedBy),
      icon: () => <SeaTableIconPerson />,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
          name: 'Completed',
        },
      },
    },
    {
      label: 'Notes',
      value: x => formatValue(x.notes),
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
    {
      label: '',
      render: x => <SeaTableImage files={x.files} />,
    },
  ]
}
