import React from 'react'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { Pressable } from 'react-native-gesture-handler'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { SeaSectionSubtitle } from '@src/components/_molecules/SeaSectionSubtitle/SeaSectionSubtitle'
import { formatValue } from '@src/lib/util'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { useRouter } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { renderCategoryName } from '@src/lib/categories'

interface ViewEquipmentSectionCompactProps {
  equipmentIds?: string[]
  contentWidth?: string
}

export const ViewEquipmentSectionCompact = ({ equipmentIds = [] }: ViewEquipmentSectionCompactProps) => {
  // Shared Data
  const vessel = sharedState.vessel.use()
  const vesselSystems = sharedState.vesselSystems.use()
  const equipments = sharedState.equipment.use()

  // Hooks
  const { isDesktopWidth } = useDeviceWidth()
  const router = useRouter()

  const renderItem = _e => {
    // TODO: Check when to show systemName vs equipmentName
    const systemName = renderCategoryName(_e?.systemId, vesselSystems)
    return (
      <>
        {_e?.systemId && (
          <SeaStack direction="column" gap={isDesktopWidth ? 20 : 0} align={'start'} width={'100%'}>
            <Pressable
              style={{
                width: '100%',
                display: 'flex',
                flexDirection: 'row',
              }}
              onPress={() => {
                return router.navigate({
                  pathname: getRoutePath(Routes.EQUIPMENT_LIST_VIEW),
                  params: {
                    vesselId: vessel?.id,
                    equipmentId: _e?.id,
                  },
                })
              }}
              disabled={!_e?.systemId}>
              <SeaSectionSubtitle showIcon={true}>{formatValue(_e.equipment)}</SeaSectionSubtitle>
            </Pressable>
          </SeaStack>
        )}
      </>
    )
  }
  return (
    <>
      {equipmentIds.map(id => {
        const equipment = equipments?.byId[id]
        return renderItem(equipment)
      })}
    </>
  )
}
