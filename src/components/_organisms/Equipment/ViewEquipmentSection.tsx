import React, { useMemo, useState } from 'react'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { Pressable } from 'react-native-gesture-handler'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { SeaSectionSubtitle } from '@src/components/_molecules/SeaSectionSubtitle/SeaSectionSubtitle'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatValue } from '@src/lib/util'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { useRouter } from 'expo-router'
import { sharedState } from '@src/shared-state/shared-state'
import { renderCategoryName } from '@src/lib/categories'
import { useLicenseeVesselLocations } from '@src/shared-state/VesselMaintenance/useLicenseeVesselLocations'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { ContactMediaCards } from '@src/components/_organisms/MediaCards/ContactMediaCards'
import { Equipment } from '@src/shared-state/VesselMaintenance/equipment'

interface ViewEquipmentSectionProps {
  equipment: Equipment
  vesselIds?: string[]
  contentWidth?: string
  nameType?: 'equipment' | 'system'
}

export const ViewEquipmentSection = ({
  equipment,
  // systemId,
  // locationId,
  vesselIds,
  // manuals,
  // contactsData,
  contentWidth = '100%',
  nameType = 'equipment',
}: ViewEquipmentSectionProps) => {
  // Shared Data
  const vessel = sharedState.vessel.use()
  const vesselSystems = sharedState.vesselSystems.use()
  const vesselLocationsState = sharedState.vesselLocations.use()
  const vesselLocationsHookData = useLicenseeVesselLocations(vesselIds)
  const vesselLocations = vesselIds ? vesselLocationsHookData : vesselLocationsState
  const equipmentManualDocuments = sharedState.equipmentManualDocuments.use()

  // Hooks
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const router = useRouter()

  const equipmentName = equipment?.equipment
  const systemName = renderCategoryName(equipment?.systemId, vesselSystems)
  const locationName = renderCategoryName(equipment?.locationId, vesselLocations)

  const manuals = useMemo(() => {
    if (vessel?.isShoreFacility || !equipment?.equipmentDocumentIds) return []

    return equipment?.equipmentDocumentIds.reduce<MediaCardFile[]>((acc, manualId) => {
      const manual = equipmentManualDocuments?.byId[manualId]

      if (!manual) return acc

      return [
        ...acc,
        {
          file: manual?.files,
          title: manual?.title,
          subTitle: 'Manual',
          actionButtons: [<SeaDownloadButton key={manualId} onPress={() => alert('Download')} />],
        },
      ]
    }, [])
  }, [vessel, equipment, equipmentManualDocuments])

  return (
    <>
      {/** Section Title - System Name */}
      {equipment?.systemId && (
        <SeaStack direction="column" gap={isDesktopWidth ? 20 : 0} align={'start'} width={'100%'}>
          <Pressable
            style={{
              width: '100%',
              display: 'flex',
              flexDirection: 'row',
            }}
            onPress={() => {
              return router.navigate({
                pathname: getRoutePath(Routes.EQUIPMENT_LIST_VIEW),
                params: {
                  vesselId: vessel?.id,
                  equipmentId: equipment?.id,
                },
              })
            }}
            disabled={!equipment?.systemId}>
            <SeaSectionSubtitle showIcon={true}>
              {nameType === 'equipment' ? equipmentName : systemName}
            </SeaSectionSubtitle>
          </Pressable>
        </SeaStack>
      )}

      <SeaStack direction={isLargeDesktopWidth ? 'row' : 'column'} gap={10} width={'100%'}>
        <SeaStack
          direction="column"
          gap={isDesktopWidth ? 20 : 10}
          align={'start'}
          justify={'start'}
          width={isLargeDesktopWidth ? '70%' : '100%'}
          style={{ height: isLargeDesktopWidth ? '100%' : 'auto' }}>
          <SeaStack
            direction="row"
            gap={isDesktopWidth ? 5 : 0}
            justify="start"
            align="start"
            width={'100%'}
            isCollapsible>
            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 5 : 0}
              style={{ flex: 1 }}
              isCollapsible={true}
              width={contentWidth}>
              <SeaLabelValue
                label={'System'}
                value={systemName ?? ''}
                iconOptions={{
                  icon: 'dns',
                  size: 18,
                }}
                showIcon
              />
              <SeaLabelValue
                label={'Location'}
                value={locationName ?? ''}
                iconOptions={{
                  icon: 'pin_drop',
                  size: 18,
                }}
                showIcon
              />
              <SeaLabelValue
                label={'Critical'}
                value={equipment?.isCritical ? 'Yes' : 'No'}
                iconOptions={{
                  icon: 'flag',
                  size: 18,
                }}
                showIcon
              />
            </SeaStack>
            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 5 : 0}
              style={{ flex: 1 }}
              isCollapsible={true}
              width={contentWidth}>
              <SeaLabelValue label={'Make'} value={equipment?.make ?? '-'} showIcon />
              <SeaLabelValue label={'Model'} value={equipment?.model ?? '-'} showIcon />
              <SeaLabelValue label={'Serial'} value={equipment?.serial ?? '-'} showIcon />
            </SeaStack>
          </SeaStack>
          {equipment?.notes && (
            <SeaStack isCollapsible={true} width={contentWidth} direction={'column'}>
              <SeaLabelValue label={'Equipment Notes'} value={formatValue(equipment.notes)} layout={'vertical'} />
            </SeaStack>
          )}
        </SeaStack>

        <SeaStack direction="column" align="start" gap={20} width={isLargeDesktopWidth ? '30%' : '100%'}>
          {manuals && <SeaMedia title="Manuals" files={manuals} type="manuals" />}
          {equipment?.contactIds && equipment?.contactIds?.length > 0 && (
            <ContactMediaCards title="Contacts" contactIds={equipment?.contactIds} />
          )}
        </SeaStack>
      </SeaStack>
    </>
  )
}
