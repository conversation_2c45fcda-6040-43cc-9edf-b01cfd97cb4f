import { View, Text, Dimensions } from 'react-native'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { useGlobalSearchParams, useRouter } from 'expo-router'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { getFileNameWithExtension, isPdf } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { ScrollView } from 'react-native-gesture-handler'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatValue } from '@src/lib/util'
import { formatEmailReminder, warnDays } from '@src/lib/datesAndTime'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { SeaPDFReader } from '@src/components/_atoms/SeaPDFReader/SeaPDFReader'
import { useItemLinks } from '@src/shared-state/General/useItemLinks'
import SeaRichText from '@src/components/_dom/lexical/SeaRichText/SeaRichText'
import { initialRichTextState, loadSfdocNew, RichTextState } from '@src/lib/richText'
import { EditVesselDocumentDrawer } from './EditVesselDocumentDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import {
  DeleteVesselDocumentDto,
  DeleteVesselDocumentUseCase,
} from '@src/domain/use-cases/vesselDocumentRegister/DeleteVesselDocumentUseCase'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'

const DESKTOP_ITEMS_WIDTH = '100%'

export function ViewVesselDocument() {
  const vesselId = sharedState.vesselId.use()
  const vesselDocuments = sharedState.vesselDocuments.use()
  const vesselDocumentCategories = sharedState.vesselDocumentCategories.use()
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const { documentId } = useGlobalSearchParams()
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const services = useServiceContainer()

  const { styles } = useStyles(styleSheet)

  const scrollViewRef = useRef<ScrollView>(null)

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)

  const [richTextState, setRichTextState] = useState<RichTextState>(initialRichTextState)

  const selectedItem = useMemo(() => {
    return vesselDocuments?.byId[Array.isArray(documentId) ? documentId[0] : documentId]
  }, [documentId, vesselDocuments])

  useEffect(() => {
    if (selectedItem?.sfdoc) {
      const loadDocument = async () => {
        const loadedRichTextState = await loadSfdocNew(selectedItem.sfdoc)

        console.debug('Loaded Rich Text State:', loadedRichTextState)
        setRichTextState(loadedRichTextState as RichTextState)
      }

      loadDocument()
    }
  }, [selectedItem?.sfdoc])

  //TODO: Implement links for the document
  const links = useItemLinks(selectedItem?.id ?? undefined)

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return []

    return selectedItem?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('Coming soon!')} />],
    })) as MediaCardFile[]
  }, [selectedItem])

  const isPDFCheck = useCallback((file?: string) => {
    if (!file) return false

    return isPdf(file)
  }, [])

  const handleDelete = useCallback(() => {
    if (!selectedItem || !vesselId || !userId || !licenseeId) {
      console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
      return
    }

    const dto: DeleteVesselDocumentDto = {
      id: selectedItem.id,
      title: selectedItem.title,
      vesselId,
    }
    const deleteVesselDocument = services.get(DeleteVesselDocumentUseCase)
    deleteVesselDocument
      .execute(dto, userId, licenseeId)
      .then(() => console.debug('Vessel Document deleted successfully'))
      .catch(err => console.error(`Error creating Vessel Document\n ${err.message}`))
  }, [vesselId, selectedItem, userId, licenseeId, services])

  const onDelete = useCallback(async () => {
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  return (
    <RequirePermissions role="vesselDocuments" level={permissionLevels.VIEW} showDenial={true}>
      <SeaPageCard
        secondaryActionButton={[
          <SeaEditButton key={'Edit'} onPress={() => setIsVisibleEditDrawer(true)} />,
          <RequirePermissions role="vesselDocuments" level={permissionLevels.FULL} key={'Delete'}>
            <SeaDeleteButton onPress={onDelete} />
          </RequirePermissions>,
        ]}
        titleComponent={
          <SeaPageCardTitle
            title={selectedItem?.title}
            additionalElements={
              selectedItem?.dateExpires ? (
                <WhenDueStatus
                  whenDue={selectedItem?.dateExpires}
                  warnDaysThreshold={warnDays.vesselCertificates[0]}
                  hasFault={false}
                />
              ) : (
                <></>
              )
            }
          />
        }>
        <SeaPageCardContentSection>
          <SeaStack
            direction={isLargeDesktopWidth ? 'row' : 'column'}
            gap={10}
            justify="start"
            align="start"
            width={'100%'}
            style={{ flex: 1 }}>
            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 20 : 10}
              align={'start'}
              width={isLargeDesktopWidth ? '70%' : '100%'}>
              <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                <SeaStack isCollapsible={true} width={'50%'} gap={isDesktopWidth ? 5 : 0}>
                  {selectedItem?.categoryId && vesselDocumentCategories?.byId ? (
                    <SeaLabelValue
                      label={'Category'}
                      value={formatValue(vesselDocumentCategories.byId[selectedItem.categoryId].name)}
                    />
                  ) : null}
                </SeaStack>

                {selectedItem?.type === 'renewable' && (
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon
                      label={'Expiry'}
                      value={selectedItem?.dateExpires}
                    />

                    <SeaLabelValue
                      label={'Notification'}
                      value={formatValue(formatEmailReminder(selectedItem?.emailReminder))}
                    />
                  </SeaStack>
                )}
              </SeaStack>
            </SeaStack>
            {uploadedFiles.length > 0 && (
              <View style={{ flex: 1 }}>
                <SeaMedia type="manuals" title="Files" files={uploadedFiles} />
              </View>
            )}
          </SeaStack>
        </SeaPageCardContentSection>
      </SeaPageCard>

      {isPDFCheck(selectedItem?.files?.[0]) && <SeaPDFReader file={selectedItem?.files?.[0]} />}

      {selectedItem?.sfdoc && (
        <View style={styles.richTextContainer}>
          <SeaRichText
            forModal={false}
            visible={true}
            setOnScroll={() => console.log('TT-RT-SetOnScroll')}
            modalContentRef={scrollViewRef}
            richTextState={richTextState}
            onlineStatus={true}
          />
        </View>
      )}

      {isVisibleEditDrawer && (
        <EditVesselDocumentDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleEditDrawer}
          type={DrawerMode.Edit}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
    height: '100%',
  },
  content: {
    marginBottom: 20,
  },
  contentContainer: {
    flexGrow: 1,
    minHeight: '100%',
  },
  richTextContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
}))
