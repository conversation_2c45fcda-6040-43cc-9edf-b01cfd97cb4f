import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { DrawerMode, SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'

import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { FormikValues, useFormik } from 'formik'
import { formatSeaDate } from '@src/lib/datesAndTime'
import { sharedState } from '@src/shared-state/shared-state'

import { makeDateTime } from '@src/lib/util'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { SeaFileUploader } from '@src/components/_atoms/_inputs/SeaFileUploader/SeaFileUploader'
import Yup, { notTooOld } from '@src/lib/yup'

import { SeaFile } from '@src/lib/fileImports'
import { Text, View } from 'react-native'
import { SOP } from '@src/shared-state/VesselDocuments/vesselSOPS'
import { DateTime } from 'luxon'
import { UpdateSOPDto, UpdateSOPUseCase } from '@src/domain/use-cases/vesselDocumentRegister/UpdateSOPUseCase'
import { CreateSOPDto, CreateSOPUseCase } from '@src/domain/use-cases/vesselDocumentRegister/CreateSOPUseCase'

enum DocumentationType {
  Controlled = 'sfdoc',
  ExternalFiles = 'files',
}

const validationSchema = Yup.object({
  title: Yup.string().max(500).required(),
  dateIssued: Yup.date()
    .required()
    .min(...notTooOld),
  categoryId: Yup.string().max(500).required(),
})

export interface EditSOPDrawerProps extends Pick<SeaDrawerProps, 'visible' | 'onClose' | 'style'> {
  selectedItem?: SOP
  type?: DrawerMode
}
export function EditSOPDrawer({ selectedItem, visible, onClose, type = DrawerMode.Edit, style }: EditSOPDrawerProps) {
  const userId = sharedState.userId.use(visible)
  const vessel = sharedState.vessel.use(visible)
  const vesselSopCategories = sharedState.vesselSOPCategories.use(visible)

  const vesselId = sharedState.vesselId.use(visible)
  const licenseeId = sharedState.licenseeId.use(visible)

  // Hooks
  const [files, setFiles] = useState<SeaFile[]>([])
  const services = useServiceContainer()

  const categoriesOptions = useMemo(() => {
    if (!vesselSopCategories) return []

    const categories = vesselSopCategories?.ids.map(id => {
      const category = vesselSopCategories.byId[id]
      return {
        label: category.name,
        value: category.id,
      }
    })

    return [
      {
        label: 'Not Set',
        value: '',
      },
      ...(categories ?? []),
    ]
  }, [vesselSopCategories])

  const initialValues = useMemo(() => {
    return {
      title: selectedItem?.title ?? '',
      dateIssued: selectedItem?.dateIssued ?? DateTime.now().toISODate(),
      categoryId: selectedItem?.categoryId ?? '',
      documentationType:
        selectedItem?.files && selectedItem.files.length > 0
          ? DocumentationType.ExternalFiles
          : DocumentationType.Controlled,
      files: selectedItem?.files ?? [],
      sfdoc: selectedItem?.sfdoc ?? {},
    }
  }, [selectedItem])

  const handleSubmit = useCallback(
    (values: FormikValues) => {
      if (!vesselId || !licenseeId || !userId || !vessel) {
        console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
        return
      }

      const commonDto = {
        vesselId,
        title: values.title,
        dateIssued: formatSeaDate(values.dateIssued) ?? undefined,
        categoryId: values.categoryId,
        files: files,
      }

      if (type === 'create') {
        const dto: CreateSOPDto = {
          ...commonDto,
        }
        const createSOPDocument = services.get(CreateSOPUseCase)
        createSOPDocument
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error creating SOP\n ${err.message}`))
      } else {
        const dto: UpdateSOPDto = {
          ...commonDto,
          id: selectedItem?.id ?? '',
        }

        const updateSOP = services.get(UpdateSOPUseCase)

        updateSOP
          .execute(dto, userId, licenseeId)
          .then(() => onClose())
          .catch(err => console.error(`Error updating SOP\n ${err.message}`))
      }
    },
    [vessel, files]
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: values => handleSubmit(values),
  })

  return (
    <SeaDrawer
      title={`${type === 'create' ? 'Add New Standard Operating Procedure' : 'Edit Standard Operating Procedure'}`}
      visible={visible}
      onClose={onClose}
      style={style}
      primaryAction={
        <SeaButton
          label={'Save Changes'}
          variant={SeaButtonVariant.Primary}
          key="Save Changes"
          onPress={formik.handleSubmit}
        />
      }>
      <SeaStack direction="column" gap={10} justify="start" align="start">
        <SeaTextInput
          label={'SOP Title'}
          value={formik.values.title}
          onChangeText={formik.handleChange('title')}
          errorText={formik.errors.title ?? undefined}
          hasError={!!formik.errors.title}
        />

        <SeaStack direction="row" gap={10} justify="start" align="start" width={'100%'}>
          <View style={{ flex: 1, width: '100%' }}>
            <SeaDropdown
              label={'Category'}
              items={categoriesOptions}
              style={{
                width: '100%',
              }}
              onSelect={value => formik.setFieldValue('categoryId', value)}
              value={formik.values.categoryId}
            />
          </View>

          <View style={{ flex: 1, width: '100%' }}>
            <SeaDateTimeInput
              value={makeDateTime(formik.values.dateIssued)}
              onChange={date => formik.setFieldValue('dateIssued', date)}
              type={'date'}
              label="Issue Date"
              style={{ flex: 1 }}
              errorText={formik.errors.dateIssued ?? undefined}
              hasError={!!formik.errors.dateIssued}
            />
          </View>
        </SeaStack>

        <SeaDropdown
          label={'Documentation'}
          items={[
            {
              label: 'Controlled',
              value: DocumentationType.Controlled,
            },
            {
              label: 'External File(s)',
              value: DocumentationType.ExternalFiles,
            },
          ]}
          style={{
            width: '100%',
          }}
          onSelect={value => formik.setFieldValue('documentationType', value)}
          value={formik.values.documentationType}
        />
        {formik.values.documentationType === DocumentationType.ExternalFiles ? (
          <SeaFileUploader initialFiles={selectedItem?.files} files={files} setFiles={setFiles} />
        ) : (
          <Text>TODO Rich text editor</Text>
        )}
      </SeaStack>

      {/* TODO: Add Links */}
    </SeaDrawer>
  )
}
