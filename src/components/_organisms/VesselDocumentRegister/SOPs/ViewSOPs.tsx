import { View, Text, ScrollView } from 'react-native'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { useGlobalSearchParams, useRouter } from 'expo-router'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { initialRichTextState, loadSfdocNew, RichTextState } from '@src/lib/richText'
import { useItemLinks } from '@src/shared-state/General/useItemLinks'
import { getFileNameWithExtension, isPdf } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatValue } from '@src/lib/util'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { SeaPDFReader } from '@src/components/_atoms/SeaPDFReader/SeaPDFReader.web'
import SeaRichText from '@src/components/_dom/lexical/SeaRichText/SeaRichText'
import { useFetchSingleItem } from '@src/shared-state/General/useFetchSingleItem'
import { renderCategoryName } from '@src/lib/categories'
import { formatDate } from '@src/lib/datesAndTime'
import { EditSOPDrawer } from './EditSOPDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { DeleteSOPDto, DeleteSOPUseCase } from '@src/domain/use-cases/vesselDocumentRegister/DeleteSOPUseCase'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

const DESKTOP_ITEMS_WIDTH = '100%'

export function ViewSOPs() {
  const vesselId = sharedState.vesselId.use()
  const vesselSOPCategories = sharedState.vesselSOPCategories.use()
  const SOPs = sharedState.vesselSOPs.use()

  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const { sopId } = useGlobalSearchParams()
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const services = useServiceContainer()
  const router = useRouter()

  const { styles } = useStyles(styleSheet)

  const scrollViewRef = useRef<ScrollView>(null)

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)

  const [richTextState, setRichTextState] = useState<RichTextState>(initialRichTextState)

  const selectedItem = useMemo(() => {
    return SOPs?.byId[Array.isArray(sopId) ? sopId[0] : sopId]
  }, [sopId, SOPs])

  const fetchedCategory = useFetchSingleItem(
    'vesselSopCategories',
    selectedItem?.categoryId ?? '',
    !!vesselSOPCategories?.byId[selectedItem?.categoryId ?? '']
  )
  useEffect(() => {
    if (selectedItem?.sfdoc) {
      const loadDocument = async () => {
        const loadedRichTextState = await loadSfdocNew(selectedItem.sfdoc)

        console.debug('Loaded Rich Text State:', loadedRichTextState)
        setRichTextState(loadedRichTextState as RichTextState)
      }

      loadDocument()
    }
  }, [selectedItem?.sfdoc])

  const categoryName = useMemo(() => {
    let category = ''
    // Implies that vesselSOPCategories doesn't exist, so there is likely no selected vessel
    if (fetchedCategory) {
      category = fetchedCategory.name
      // Implies that vesselSOPCategories exists, so there is likely a selected vessel
    } else if (vesselSOPCategories) {
      category = renderCategoryName(selectedItem?.categoryId, vesselSOPCategories)
    }
    return formatValue(category)
  }, [selectedItem?.categoryId, vesselSOPCategories, fetchedCategory])

  //TODO: Implement links for the document
  const links = useItemLinks(selectedItem?.id ?? undefined)

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return []

    return selectedItem?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('Coming soon!')} />],
    })) as MediaCardFile[]
  }, [selectedItem])

  const isPDFCheck = useCallback((file?: string) => {
    if (!file) return false

    return isPdf(file)
  }, [])

  const handleDelete = useCallback(() => {
    if (!selectedItem || !vesselId || !userId || !licenseeId) {
      console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
      return
    }

    const dto: DeleteSOPDto = {
      id: selectedItem.id,
      title: selectedItem.title,
      vesselId,
    }

    const deleteSopDocument = services.get(DeleteSOPUseCase)

    deleteSopDocument
      .execute(dto, userId, licenseeId)
      .then(() =>
        router.navigate({
          pathname: getRoutePath(Routes.STANDARD_OPERATING_PROCEDURES),
          params: {
            vesselId,
          },
        })
      )
      .catch(err => console.error(`Error creating SOP\n ${err.message}`))
  }, [vesselId, selectedItem, userId, licenseeId, services])

  const onDelete = useCallback(async () => {
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  return (
    <RequirePermissions role="standardOperatingProcedures" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <SeaPageCard
          secondaryActionButton={[
            <SeaEditButton key={'Edit'} onPress={() => setIsVisibleEditDrawer(true)} />,
            <RequirePermissions role="standardOperatingProcedures" level={permissionLevels.FULL} key={'Delete'}>
              <SeaDeleteButton onPress={onDelete} />
            </RequirePermissions>,
          ]}
          titleComponent={<SeaPageCardTitle title={selectedItem?.title} />}>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={'50%'} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Category'} value={categoryName} />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={'50%'} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon
                      label={'Issue Date'}
                      value={formatDate(selectedItem?.dateIssued)}
                    />
                  </SeaStack>
                </SeaStack>
              </SeaStack>
              {uploadedFiles.length > 0 && (
                <View style={{ flex: 1 }}>
                  <SeaMedia type="manuals" title="Files" files={uploadedFiles} />
                </View>
              )}
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>

        {isPDFCheck(selectedItem?.files?.[0]) && <SeaPDFReader file={selectedItem?.files?.[0]} />}

        {selectedItem?.sfdoc && (
          <View style={styles.richTextContainer}>
            <SeaRichText
              forModal={false}
              visible={true}
              setOnScroll={() => console.log('TT-RT-SetOnScroll')}
              modalContentRef={scrollViewRef}
              richTextState={richTextState}
              onlineStatus={true}
            />
          </View>
        )}
      </ScrollView>

      {isVisibleEditDrawer && (
        <EditSOPDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleEditDrawer}
          type={DrawerMode.Edit}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
    height: '100%',
  },
  content: {
    marginBottom: 20,
  },
  contentContainer: {
    flexGrow: 1,
    minHeight: '100%',
  },
  richTextContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
}))
