import { View, ScrollView } from 'react-native'
import React, { useCallback, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { useGlobalSearchParams, useRouter } from 'expo-router'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { getFileNameWithExtension, isPdf } from '@src/lib/files'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { formatDate } from '@src/lib/datesAndTime'
import { formatValue } from '@src/lib/util'
import { EditSurveyDocumentsDrawer } from './EditSurveyDocumentsDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaPDFReader } from '@src/components/_atoms/SeaPDFReader/SeaPDFReader'
import {
  DeleteSurveyDocumentDto,
  DeleteSurveyDocumentUseCase,
} from '@src/domain/use-cases/vesselDocumentRegister/DeleteSurveyDocumentUseCase'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

const DESKTOP_ITEMS_WIDTH = '100%'

export function ViewSurveyDocuments() {
  const vesselId = sharedState.vesselId.use()
  const reportItems = sharedState.vesselSurveyReports.use()
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)

  const { documentId } = useGlobalSearchParams()
  const { styles } = useStyles(styleSheet)
  const services = useServiceContainer()
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const router = useRouter()

  const selectedItem = useMemo(() => {
    return reportItems?.byId[Array.isArray(documentId) ? documentId[0] : documentId]
  }, [documentId, reportItems])

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return []

    return selectedItem?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('Coming soon!')} />],
    })) as MediaCardFile[]
  }, [selectedItem])

  const isPDFCheck = useCallback((file?: string) => {
    if (!file) return false

    return isPdf(file)
  }, [])

  const handleDelete = useCallback(() => {
    if (!selectedItem || !vesselId || !userId || !licenseeId) {
      console.error('Vessel ID, Licensee ID, Vessel, or User ID is not available')
      return
    }

    const dto: DeleteSurveyDocumentDto = {
      id: selectedItem.id,
      title: selectedItem.title,
      vesselId,
    }

    const deleteSurveyDocument = services.get(DeleteSurveyDocumentUseCase)

    deleteSurveyDocument
      .execute(dto, userId, licenseeId)
      .then(() =>
        router.navigate({
          pathname: getRoutePath(Routes.SURVEY_DOCUMENTS),
          params: {
            vesselId,
          },
        })
      )
      .catch(err => console.error(`Error deleting Survey Document\n ${err.message}`))
  }, [vesselId, selectedItem, userId, licenseeId, services])

  const onDelete = useCallback(async () => {
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  return (
    <RequirePermissions role="survey" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <SeaPageCard
          secondaryActionButton={[
            <RequirePermissions role="survey" level={permissionLevels.EDIT} key={'Edit'}>
              <SeaEditButton key={'Edit'} onPress={() => setIsVisibleEditDrawer(true)} />
            </RequirePermissions>,
            <RequirePermissions role="survey" level={permissionLevels.FULL} key={'Delete'}>
              <SeaDeleteButton onPress={onDelete} />,
            </RequirePermissions>,
          ]}
          titleComponent={<SeaPageCardTitle title={selectedItem?.title} />}>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon
                      label={'Survey Date'}
                      value={formatDate(selectedItem?.dateSurveyed)}
                    />
                    <SeaLabelValue
                      iconOptions={{ icon: 'location_on' }}
                      showIcon
                      label={'Location'}
                      value={formatValue(selectedItem?.location)}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'person' }}
                      showIcon
                      label={'Surveyor'}
                      value={formatValue(selectedItem?.surveyor)}
                    />

                    <SeaLabelValue
                      iconOptions={{ icon: 'person' }}
                      showIcon
                      label={'Personnel Present'}
                      value={formatValue(selectedItem?.personnelPresent)}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={'50%'} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'In or out of water'} value={formatValue(selectedItem?.inOrOutWater)} />
                  </SeaStack>
                </SeaStack>
              </SeaStack>
              {uploadedFiles.length > 0 && (
                <View style={{ flex: 1 }}>
                  <SeaMedia type="manuals" title="Files" files={uploadedFiles} />
                </View>
              )}
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>
        {isPDFCheck(selectedItem?.files?.[0]) && <SeaPDFReader file={selectedItem?.files?.[0]} />}
      </ScrollView>

      {isVisibleEditDrawer && (
        <EditSurveyDocumentsDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleEditDrawer}
          type={DrawerMode.Edit}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
    height: '100%',
  },
  content: {
    marginBottom: 20,
  },
  contentContainer: {
    flexGrow: 1,
    minHeight: '100%',
  },
  richTextContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
}))
