import React from 'react'
import { UserType } from '@src/shared-state/Core/user'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { formatValue } from '@src/lib/util'
import { renderCategoryNames } from '@src/lib/categories'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatDate } from '@src/lib/datesAndTime'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaMediaCard } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { SeaPageCardContentSection } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'

interface ViewCrewParticularsProfileProps {
  crewId: UserType['id']
}

const DESKTOP_ITEMS_WIDTH = '100%'
export const ViewCrewParticularsProfile = ({ crewId }: ViewCrewParticularsProfileProps) => {
  if (!crewId) return <></>

  // Hooks
  const { styles } = useStyles(styleSheet)
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  // Shared Data
  const users = sharedState.users.use()
  const crew = users?.byId[crewId as string]
  const vessels = sharedState.vessels.use()
  const userRoles = sharedState.userRoles.use()
  const userDetails = sharedState.userDetails.use()
  const crewUserDetails = userDetails?.byId[crew?.id ?? '']

  if (!crew) return <></>

  return (
    <>
      <SeaStack direction={'column'} gap={10} align={'start'} style={styles.container} width={'100%'}>
        <SeaPageCardContentSection contentStyle={{ width: '100%' }}>
          <SeaStack
            direction={isLargeDesktopWidth ? 'row' : 'column'}
            gap={10}
            justify="start"
            align="start"
            width={'100%'}
            style={{ flex: 1 }}>
            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 20 : 10}
              align={'start'}
              width={isLargeDesktopWidth ? '70%' : '100%'}>
              <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                {/** Card 1 - Row 1 */}
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    label={'Job Title / Position'}
                    value={formatValue(crew.position)}
                    style={styles.label}
                  />
                  <SeaLabelValue
                    label={'Roles / Departments'}
                    value={formatValue(renderCategoryNames(crew?.roleIds, userRoles))}
                    style={styles.label}
                  />
                </SeaStack>
                {crew.isStaff && (
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'cake' }}
                      showIcon={true}
                      label={'Date of Birth'}
                      value={crewUserDetails?.dateOfBirth ? formatDate(crewUserDetails?.dateOfBirth) : '-'}
                      style={styles.label}
                    />
                    <SeaLabelValue
                      iconOptions={{
                        icon: 'calendar_month',
                      }}
                      showIcon={true}
                      label={'Inducted Date'}
                      value={crew.dateInducted ? formatDate(crew.dateInducted) : '-'}
                      style={styles.label}
                    />
                  </SeaStack>
                )}

                {/** Card 1 - Row 2 */}
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    iconOptions={{ icon: 'mail' }}
                    showIcon={true}
                    label={'Email'}
                    value={formatValue(crewUserDetails?.email)}
                    style={styles.label}
                  />
                  <SeaLabelValue
                    iconOptions={{ icon: 'phone' }}
                    showIcon={true}
                    label={'Phone #'}
                    value={formatValue(crewUserDetails?.contactNumber)}
                    style={styles.label}
                  />
                </SeaStack>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    iconOptions={{ icon: 'location_on' }}
                    showIcon={true}
                    label={'Address'}
                    value={formatValue(crewUserDetails?.address)}
                    style={styles.label}
                  />
                  {crew.isStaff && (
                    <SeaLabelValue
                      label={'Passport #'}
                      value={formatValue(crewUserDetails?.passportNumber)}
                      style={styles.label}
                    />
                  )}
                </SeaStack>
              </SeaStack>
            </SeaStack>

            <SeaStack
              direction="column"
              gap={isDesktopWidth ? 20 : 10}
              align={'start'}
              width={isLargeDesktopWidth ? '30%' : '100%'}>
              <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                {/** Card 1 - Row 4 */}
                <SeaTypography variant={'subtitle'}>Next of kin Details</SeaTypography>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaMediaCard
                    title={`${formatValue(crewUserDetails?.nextOfKin?.name)} (${formatValue(crewUserDetails?.nextOfKin?.relationship)})`}
                    subTitle={formatValue(crewUserDetails?.nextOfKin?.phone)}
                    file={[]}
                    actionButtons={[
                      <SeaButton
                        iconOptions={{
                          icon: 'phone',
                          size: 18,
                          fill: false,
                        }}
                        variant={SeaButtonVariant.Tertiary}
                        key={`call-${crewUserDetails?.nextOfKin?.name}`}
                        onPress={() => {
                          alert(`Calling ${crewUserDetails?.nextOfKin?.phone}`)
                        }}
                      />,
                    ]}
                  />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaStack>
        </SeaPageCardContentSection>
        <SeaEmptyDivider />

        {crew.isStaff && (
          <>
            <SeaPageCardContentSection contentStyle={{ width: '100%' }}>
              <SeaStack
                direction={isLargeDesktopWidth ? 'row' : 'column'}
                gap={10}
                justify="start"
                align="start"
                width={'100%'}
                style={{ flex: 1 }}>
                <SeaStack
                  direction="column"
                  gap={isDesktopWidth ? 20 : 10}
                  align={'start'}
                  width={isLargeDesktopWidth ? '70%' : '100%'}>
                  <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue
                        label={'Medical Issues'}
                        value={formatValue(crewUserDetails?.currentMedicalIssues)}
                        style={styles.label}
                      />
                      <SeaLabelValue
                        label={'Current Medication'}
                        value={formatValue(crewUserDetails?.currentMedication)}
                        style={styles.label}
                      />
                    </SeaStack>

                    {/** Card 1 - Row 6 */}
                    <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue
                        label={'Previous injuries/surgeries'}
                        value={formatValue(crewUserDetails?.previousInjuryOrSurgery)}
                        style={styles.label}
                      />
                      <SeaLabelValue
                        label={'Allergies'}
                        value={formatValue(crewUserDetails?.allergies)}
                        style={styles.label}
                      />
                    </SeaStack>
                    <SeaStack isCollapsible={true} width={'50%'} gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue
                        label={'Blood Type'}
                        value={formatValue(crewUserDetails?.bloodType)}
                        style={styles.label}
                      />
                    </SeaStack>
                  </SeaStack>
                </SeaStack>
                <SeaStack
                  direction="column"
                  gap={isDesktopWidth ? 20 : 10}
                  align={'start'}
                  width={isLargeDesktopWidth ? '30%' : '100%'}>
                  <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    {/** Card 1 - Row 5 */}
                    <SeaTypography variant={'subtitle'}>Medical Details</SeaTypography>
                    <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                      <SeaMediaCard
                        title={formatValue(crewUserDetails?.medicalDoctor?.name)}
                        subTitle={formatValue(crewUserDetails?.medicalDoctor?.phone)}
                      />
                    </SeaStack>
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaPageCardContentSection>
            <SeaEmptyDivider />

            <SeaPageCardContentSection contentStyle={{ width: '100%' }}>
              <SeaStack
                direction={isLargeDesktopWidth ? 'row' : 'column'}
                gap={10}
                justify="start"
                align="start"
                width={'100%'}
                style={{ flex: 1 }}>
                <SeaStack
                  direction="column"
                  gap={isDesktopWidth ? 20 : 10}
                  align={'start'}
                  width={isLargeDesktopWidth ? '70%' : '100%'}>
                  <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    {/** Card 1 - Row 7 */}
                    <SeaTypography variant={'subtitle'}>Vessel Details</SeaTypography>
                  </SeaStack>
                  <SeaStack
                    direction="row"
                    isCollapsible={true}
                    align={'start'}
                    width={DESKTOP_ITEMS_WIDTH}
                    gap={isDesktopWidth ? 5 : 10}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'directions_boat_filled' }}
                      showIcon={true}
                      label={'Vessel / Asset Access'}
                      value={crew.vesselIds?.map(_vId => vessels?.byId[_vId].name).join(', ')}
                      style={styles.label}
                      layout={'vertical'}
                    />
                    <SeaLabelValue
                      iconOptions={{ icon: 'directions_boat_filled' }}
                      showIcon={true}
                      label={'Crewed Vessels'}
                      value={crew.crewVesselIds
                        ?.map(_vId => vessels?.byId[_vId].name ?? undefined)
                        .filter(v => !!v)
                        .join(', ')}
                      style={styles.label}
                      layout={'vertical'}
                    />
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaPageCardContentSection>
          </>
        )}
      </SeaStack>
    </>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    backgroundColor: theme.colors.white,
    // backgroundColor: 'red',
    borderRadius: 24,
    flex: 1,
  },
  row: {
    width: '100%',
  },
  label: {
    flex: 1,
  },
}))
