import { View, Text } from 'react-native'
import React, { useCallback, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { ScrollView } from 'react-native-gesture-handler'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { useGlobalSearchParams } from 'expo-router'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { formatValue } from '@src/lib/util'
import { formatEmailReminder } from '@src/lib/datesAndTime'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { getFileNameWithExtension, isPdf } from '@src/lib/files'
import { MediaCardFile } from '@src/components/_atoms/SeaMediaCard/SeaMediaCard'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { renderCategoryName } from '@src/lib/categories'
import { EditCrewCertificateDrawer } from './EditCrewCertificateDrawer'
import { RenewCrewCertificateDrawer } from './RenewCrewCertificateDrawer'
import { SeaPDFReader } from '@src/components/_atoms/SeaPDFReader/SeaPDFReader'

const DESKTOP_ITEMS_WIDTH = '100%'

export function ViewCrewCertificates() {
  const crewCertificates = sharedState.crewCertificates.use()
  const crewCertificateTitles = sharedState.crewCertificateTitles.use()

  const { certificateId } = useGlobalSearchParams()
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const { styles } = useStyles(styleSheet)

  const [isVisibleEditDrawer, setIsVisibleEditDrawer] = useState(false)
  const [isVisibleRenewDrawer, setIsVisibleRenewDrawer] = useState(false)

  const selectedItem = useMemo(() => {
    return crewCertificates?.byId[Array.isArray(certificateId) ? certificateId[0] : certificateId]
  }, [certificateId, crewCertificates])

  const uploadedFiles = useMemo(() => {
    if (!selectedItem?.files) return []

    return selectedItem?.files.map(file => ({
      title: getFileNameWithExtension(file),
      file: [file],
      actionButtons: [<SeaDownloadButton key={`download-${file}`} onPress={() => alert('Coming soon!')} />],
    })) as MediaCardFile[]
  }, [selectedItem])

  const isPDFCheck = useCallback((file?: string) => {
    if (!file) return false

    return isPdf(file)
  }, [])

  return (
    <RequirePermissions role="crewCertificates" level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView style={styles.container}>
        <SeaPageCard
          primaryActionButton={
            selectedItem?.type === 'renewable' ? (
              <SeaButton label="Renew Certificate" onPress={() => setIsVisibleRenewDrawer(true)} />
            ) : undefined
          }
          secondaryActionButton={[
            <RequirePermissions key={'Edit'} role="crewCertificates" level={permissionLevels.EDIT}>
              <SeaEditButton onPress={() => setIsVisibleEditDrawer(true)} />
            </RequirePermissions>,
            <RequirePermissions key={'Delete'} role="crewCertificates" level={permissionLevels.FULL}>
              <SeaDeleteButton onPress={() => alert('Coming soon!')} />
            </RequirePermissions>,
          ]}
          titleComponent={
            <SeaPageCardTitle
              title={`${renderFullNameForUserId(selectedItem?.heldBy)} - ${renderCategoryName(selectedItem?.titleId, crewCertificateTitles)}`}
            />
          }>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      iconOptions={{ icon: 'fact_check' }}
                      showIcon
                      label={'Issued By'}
                      value={selectedItem?.issuedBy}
                    />

                    <SeaLabelValue
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon
                      label={'Issued Date'}
                      value={selectedItem?.dateIssued}
                    />
                  </SeaStack>

                  {selectedItem?.type === 'renewable' && (
                    <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                      <SeaLabelValue
                        iconOptions={{ icon: 'calendar_month' }}
                        showIcon
                        label={'Expiry'}
                        value={selectedItem?.dateExpires}
                      />

                      <SeaLabelValue
                        label={'Notification'}
                        value={formatValue(formatEmailReminder(selectedItem?.emailReminder))}
                      />
                    </SeaStack>
                  )}
                </SeaStack>
              </SeaStack>
              {uploadedFiles.length > 0 && (
                <SeaStack
                  direction="column"
                  gap={isDesktopWidth ? 20 : 10}
                  align={'start'}
                  width={isLargeDesktopWidth ? '30%' : '100%'}>
                  <SeaMedia type="manuals" title="Files" files={uploadedFiles} />
                </SeaStack>
              )}
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>

        {isPDFCheck(selectedItem?.files?.[0]) ? <SeaPDFReader file={selectedItem?.files?.[0]} /> : <></>}
      </ScrollView>

      {isVisibleEditDrawer && (
        <EditCrewCertificateDrawer
          onClose={() => setIsVisibleEditDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleEditDrawer}
          type={DrawerMode.Edit}
        />
      )}

      {isVisibleRenewDrawer && (
        <RenewCrewCertificateDrawer
          onClose={() => setIsVisibleRenewDrawer(false)}
          selectedItem={selectedItem}
          visible={isVisibleRenewDrawer}
        />
      )}
    </RequirePermissions>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
  content: {
    marginBottom: 20,
  },
}))
