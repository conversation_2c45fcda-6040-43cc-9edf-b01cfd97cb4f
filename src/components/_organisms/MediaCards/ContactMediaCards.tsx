import React, { useMemo } from 'react'
import { SeaMedia } from '@src/components/_molecules/SeaMedia/SeaMedia'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { sharedState } from '@src/shared-state/shared-state'

interface ContactMediaCardsProps {
  contactIds?: string[]
  title?: string
}

export function ContactMediaCards({ contactIds = [], title }: ContactMediaCardsProps) {
  const contacts = sharedState.contacts.use()

  const contactCardData = useMemo(() => {
    if (contacts?.byId && contactIds && contactIds?.length > 0) {
      return contactIds.map((contactId: string) => {
        const contact = contacts.byId[contactId]
        return {
          title: `${contact.name}${contact.company ? ` (${contact.company})` : ''}`,
          subTitle: `${contact.number} ${contact.email}`,
          file: [contactId],
          actionButtons: [
            <SeaButton
              iconOptions={{
                icon: 'phone',
                size: 18,
                fill: false,
              }}
              variant={SeaButtonVariant.Tertiary}
              key={`call-${contactId}`}
              onPress={() => {
                alert(`Calling ${contact.number}`)
              }}
            />,
            <SeaButton
              iconOptions={{
                icon: 'mail',
                size: 18,
                fill: false,
              }}
              variant={SeaButtonVariant.Tertiary}
              key={`mail-${contactId}`}
              onPress={() => {
                alert(`Calling ${contact.email}`)
              }}
            />,
            <SeaButton
              iconOptions={{
                icon: 'pin_drop',
                size: 18,
                fill: false,
              }}
              variant={SeaButtonVariant.Tertiary}
              key={`location-${contactId}`}
              onPress={() => {
                alert(`Calling ${contact.address}`)
              }}
            />,
          ],
        }
      })
    }
    return []
  }, [contacts])

  return <SeaMedia title={title ?? 'Contacts'} files={contactCardData} type="contacts" layout={'vertical'} />
}
