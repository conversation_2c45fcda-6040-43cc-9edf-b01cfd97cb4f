import { View, Text } from 'react-native'
import React, { useMemo } from 'react'
import { SeaDropdown, SeaDropdownProps } from '../SeaDropdown/SeaDropdown'
import { formatEmailReminder } from '@src/lib/datesAndTime'

interface SeaSelectEmailReminderDropdownProps extends Omit<SeaDropdownProps<string>, 'items'> {
  safetyMeetingOptions?: boolean
}

export const SeaEmailReminderDropdown = ({ safetyMeetingOptions, ...props }: SeaSelectEmailReminderDropdownProps) => {
  const options = useMemo(() => {
    if (safetyMeetingOptions) {
      return [
        {
          value: '0d',
          label: formatEmailReminder('0d'),
        },
        {
          value: '1d',
          label: formatEmailReminder('1d'),
        },
        {
          value: '2d',
          label: formatEmailReminder('2d'),
        },
        {
          value: '3d',
          label: formatEmailReminder('3d'),
        },
        {
          value: '7d',
          label: formatEmailR<PERSON>inder('7d'),
        },
        {
          value: '14d',
          label: formatEmailReminder('14d'),
        },
        {
          value: '1m',
          label: formatEmailReminder('1m'),
        },
      ]
    } else {
      return [
        {
          value: '1d',
          label: formatEmailReminder('1d'),
        },
        {
          value: '7d',
          label: formatEmailReminder('7d'),
        },
        {
          value: '14d',
          label: formatEmailReminder('14d'),
        },
        {
          value: '1m',
          label: formatEmailReminder('1m'),
        },
        {
          value: '2m',
          label: formatEmailReminder('2m'),
        },
        {
          value: '3m',
          label: formatEmailReminder('3m'),
        },
        {
          value: '6m',
          label: formatEmailReminder('6m'),
        },
      ]
    }
  }, [safetyMeetingOptions])

  return <SeaDropdown items={options} {...props} />
}
