import type { Meta, StoryObj } from '@storybook/react'
import React, { useEffect, useState } from 'react'
import { View, Text } from 'react-native'
import { SeaDropdown, SeaDropdownItem } from '@src/components/_atoms/SeaDropdown/SeaDropdown'

const meta = {
  title: 'Components/Atoms/SeaDropdown',
  component: SeaDropdown,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
          <Text style={{ marginTop: 8 }}>Stuff underneath</Text>
          <Text style={{ marginTop: 8 }}>More stuff underneath</Text>
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaDropdown>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => {
    const [value, setValue] = useState<SeaDropdownItem<unknown>>()

    return (
      <SeaDropdown
        label={args.label}
        items={args.items}
        onSelect={val => {
          setValue(val)
        }}
      />
    )
  },
  args: {
    items: [
      { label: 'Item One', value: 'itemOne' },
      { label: 'Item Two', value: 'itemTwo' },
      { label: 'Item Three', value: 'itemThree' },
    ],
    onSelect: item => {},
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}

export const WithLabel: Story = {
  ...Default,
  args: {
    ...Default.args,
    label: 'Example Dropdown',
  },
}
