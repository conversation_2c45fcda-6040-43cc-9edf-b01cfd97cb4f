import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import { SeaFilePicker } from './SeaFilePicker'

const meta = {
  title: 'Components/Atoms/SeaFilePicker',
  component: SeaFilePicker,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaFilePicker>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => {
    return <SeaFilePicker />
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
