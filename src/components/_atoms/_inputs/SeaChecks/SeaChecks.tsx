import { View, Text, ViewStyle, Pressable, Animated } from 'react-native'
import React, { useEffect, useState } from 'react'
import { SeaStack } from '../../SeaStack/SeaStack'
import { SeaTypography } from '../../SeaTypography/SeaTypography'
import { SeaIcon } from '../../SeaIcon/SeaIcon'
import { useAnimatedStyle, useSharedValue, withSpring, withTiming } from 'react-native-reanimated'
import { createStyleSheet, useStyles } from '@src/theme/styles'

enum SeaChecksValues {
  PASS = 'pass',
  FAIL = 'fail',
  NA = 'na',
}

type SeaChecksData = {
  label: string
  value?: SeaChecksValues
}
interface SeaChecksProps {
  label?: string
  data: SeaChecksData[]
  setData?: (data: SeaChecksData[]) => void
  error?: string
  disabled?: boolean
}

export function SeaChecks({ label, data, setData, error, disabled }: SeaChecksProps) {
  const { styles, theme } = useStyles(styleSheet)

  const toggleValue = (index: number, value?: SeaChecksValues) => {
    if (data && setData) {
      const _data = [] as SeaChecksData[]
      for (let i = 0; i < data.length; i++) {
        if (i === index) {
          _data.push({
            label: data[i].label,
            value: data[i].value === value ? undefined : value,
          })
        } else {
          _data.push({
            label: data[i].label,
            value: data[i].value,
          })
        }
      }
      setData(_data)
    }
  }

  if (data === undefined || data.length === 0) {
    return null
  }

  return (
    <SeaStack direction="column" gap={10} align="start" width={'100%'}>
      {label && (
        <SeaTypography variant="label" containerStyle={{ marginBottom: 10 }} textStyle={{ marginBottom: 0 }}>
          {label.toUpperCase()}
        </SeaTypography>
      )}
      <SeaStack direction="row" gap={8} align="center" justify="start" width={'100%'}>
        <SeaIcon icon={'check'} size={16} fill color={theme.colors.black} style={{ width: 40 }} />
        <SeaTypography variant="label" containerStyle={{ width: 40 }}>
          Fault
        </SeaTypography>
        <SeaTypography variant="label" containerStyle={{ width: 40 }}>
          N/A
        </SeaTypography>
      </SeaStack>
      {data.map((item, index) => (
        <SeaChecksInput
          key={index}
          value={item.value}
          onChange={value => toggleValue(index, value)}
          label={item.label}
          style={styles.container}
          disabled={disabled}
        />
      ))}
    </SeaStack>
  )
}

interface SeaChecksInputProps {
  value?: SeaChecksValues
  onChange: (value: SeaChecksValues | undefined) => void
  label: string
  style?: ViewStyle
  disabled?: boolean
}

function SeaChecksInput({ value, onChange, label, style, disabled = false }: SeaChecksInputProps) {
  const scale = useSharedValue(1) // For scaling animation
  const iconOpacity = useSharedValue(value ? 1 : 0) // For icon fade-in
  const [selectedValue, setSelectedValue] = useState<SeaChecksValues | undefined>(undefined)
  const { styles, theme } = useStyles(styleSheet)

  // Animation values
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }))
  const iconStyle = useAnimatedStyle(() => ({ opacity: iconOpacity.value }))

  const handlePress = (check: SeaChecksValues | undefined) => {
    // Animations
    scale.value = withSpring(1.1, { damping: 5 }, () => {
      scale.value = withSpring(1) // Reset scale
    })
    iconOpacity.value = withTiming(check ? 0 : 1, { duration: 200 })

    onChange(check)
    setSelectedValue(check)
  }

  useEffect(() => {
    // Update the checkbox state when the value prop changes
    if (value !== SeaChecksValues.PASS && value !== SeaChecksValues.FAIL && value !== SeaChecksValues.NA) {
      setSelectedValue(undefined)
    } else {
      setSelectedValue(value)
    }
  }, [value])

  const isPass = selectedValue === SeaChecksValues.PASS
  const isFail = selectedValue === SeaChecksValues.FAIL
  const isNa = selectedValue === SeaChecksValues.NA

  return (
    <SeaStack
      direction="row"
      gap={10}
      align="center"
      style={{
        paddingVertical: 5,
      }}
      width={'100%'}>
      <Pressable
        style={[styles.container, style, { opacity: disabled ? 0.5 : 1 }]}
        onPress={() => handlePress(selectedValue === SeaChecksValues.PASS ? undefined : SeaChecksValues.PASS)}
        disabled={disabled}>
        <Animated.View
          style={[
            styles.checkboxContainer,
            animatedStyle,

            {
              backgroundColor: isPass
                ? theme.colors.status.okPrimary
                : disabled
                  ? theme.colors.input.disabledBackground
                  : theme.colors.white,
            },
            {
              height: 35,
              width: 35,
            },
          ]}>
          <Animated.View style={iconStyle}>
            {isPass && <SeaIcon icon={'check'} size={16} color={theme.colors.white} />}
          </Animated.View>
        </Animated.View>
      </Pressable>

      <Pressable
        style={[styles.container, style, { opacity: disabled ? 0.5 : 1 }]}
        onPress={() => handlePress(selectedValue !== undefined && isFail ? undefined : SeaChecksValues.FAIL)}
        disabled={disabled}>
        <Animated.View
          style={[
            styles.checkboxContainer,
            animatedStyle,
            {
              backgroundColor:
                selectedValue !== undefined && isFail
                  ? theme.colors.status.errorPrimary
                  : disabled
                    ? theme.colors.input.disabledBackground
                    : theme.colors.white,
            },
            {
              height: 35,
              width: 35,
            },
          ]}>
          <Animated.View style={iconStyle}>
            {selectedValue !== undefined && isFail && <SeaIcon icon={'close'} size={16} color={theme.colors.white} />}
          </Animated.View>
        </Animated.View>
      </Pressable>

      <Pressable
        style={[styles.container, style, { opacity: disabled ? 0.5 : 1 }]}
        onPress={() => handlePress(selectedValue !== undefined && isNa ? undefined : SeaChecksValues.NA)}
        disabled={disabled}>
        <Animated.View
          style={[
            styles.checkboxContainer,
            animatedStyle,
            {
              backgroundColor:
                selectedValue !== undefined && isNa
                  ? theme.colors.status.warnOutline
                  : disabled
                    ? theme.colors.input.disabledBackground
                    : theme.colors.white,
            },
            {
              height: 35,
              width: 35,
            },
          ]}>
          <Animated.View style={iconStyle}>
            {selectedValue !== undefined && isNa && <SeaIcon icon={'edit'} size={16} color={theme.colors.white} />}
          </Animated.View>
        </Animated.View>
      </Pressable>

      <SeaTypography
        variant={'body'}
        containerStyle={{
          flexShrink: 1,
          marginBottom: 0,
        }}
        textStyle={{ marginBottom: 0 }}>
        {label}
      </SeaTypography>
    </SeaStack>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
    // height: 40,
  },
  checkboxContainer: {
    height: 60,
    width: 40,
    backgroundColor: theme.colors.input.background,
    borderColor: theme.colors.borderColor,
    borderRadius: 8,
    borderWidth: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
}))
