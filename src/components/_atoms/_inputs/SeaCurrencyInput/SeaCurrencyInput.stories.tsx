import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import { SeaCurrencyInput } from '@src/components/_atoms/_inputs/SeaCurrencyInput/SeaCurrencyInput'

const meta = {
  title: 'Components/Atoms/SeaCurrencyInput',
  component: SeaCurrencyInput,
  argTypes: {
    currency: {
      control: 'select',
      options: ['NZD', 'AUD', 'GBP', 'USD'],
      description: 'The currency',
    },
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <View style={{ width: '30%' }}>
            <Story />
          </View>
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaCurrencyInput>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    label: 'Item Cost',
    currency: 'NZD',
    value: 0,
    onChange: val => {},
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
