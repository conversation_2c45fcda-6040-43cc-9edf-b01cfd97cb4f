import React from 'react'
import { View } from 'react-native'
import type { Meta, StoryObj } from '@storybook/react'
import { SeaFileUploader } from './SeaFileUploader'

const meta = {
  title: 'Components/Atoms/SeaFileUploader',
  component: SeaFileUploader,
  argTypes: {},
  args: {},
  decorators: [
    Story => {
      return (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaFileUploader>

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {
  args: {},
}
