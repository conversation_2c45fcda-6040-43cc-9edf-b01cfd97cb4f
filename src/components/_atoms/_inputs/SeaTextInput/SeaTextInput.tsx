import React, { ReactElement } from 'react'
import { Platform, TextInput, TextInputProps, TextStyle, View, ViewStyle } from 'react-native'
import SeaIcon from '@src/components/_legacy/SeaIcon/SeaIcon'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { ErrorText } from '@src/components/_atoms/_inputs/ErrorText/ErrorText'

const SINGLE_LINE_HEIGHT = 40
const MULTI_LINE_HEIGHT = 144

export interface SeaInputProps extends TextInputProps {
  value: string
  onChangeText?: (value: string) => void
  label?: string
  multiLine?: boolean
  placeholderText?: string
  prefixIcon?: ReactElement<typeof SeaIcon>
  suffixIcon?: ReactElement<typeof SeaIcon>
  style?: ViewStyle
  inputContainerStyle?: ViewStyle
  hasError?: boolean
  errorText?: string
  noValidation?: boolean
  forCustomForm?: boolean // Optional prop for custom form usage
  disabled?: boolean // Optional prop to disable the input
}

export const SeaTextInput = ({
  value,
  onChangeText,
  label,
  multiLine,
  placeholderText,
  prefixIcon,
  suffixIcon,
  style,
  inputContainerStyle,
  hasError = false,
  errorText,
  noValidation = false,
  forCustomForm = false, // Default to false if not provided
  disabled = false, // Default to false if not provided
  ...otherProps
}: SeaInputProps) => {
  const { styles, theme } = useStyles(styleSheet)
  const platformOverrides = Platform.select({
    web: { outline: 'none' } as TextStyle,
  })

  return (
    <View style={[styles.container, style]}>
      {label && <SeaTypography variant={'label'}>{forCustomForm ? label : label.toUpperCase()}</SeaTypography>}

      <View
        style={[
          styles.inputContainer,
          { height: multiLine ? MULTI_LINE_HEIGHT : SINGLE_LINE_HEIGHT },
          inputContainerStyle,
          hasError && styles.inputContainerError,
          disabled
            ? {
                opacity: 0.5,
                backgroundColor: theme.colors.input.disabledBackground,
              }
            : {},
        ]}>
        {prefixIcon && <View style={{ marginRight: 5 }}>{prefixIcon}</View>}
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholderText ?? label}
          multiline={multiLine}
          placeholderTextColor={theme.colors.text.placeholder}
          {...otherProps}
          style={[
            styles.textInputStyle,
            { height: multiLine ? MULTI_LINE_HEIGHT - 6 : SINGLE_LINE_HEIGHT },
            platformOverrides,
          ]}
          editable={!disabled}
        />
        {suffixIcon && <View style={{ marginLeft: 5 }}>{suffixIcon}</View>}
      </View>

      {!noValidation ? <ErrorText hasError={hasError} text={errorText} /> : <></>}
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
  },
  labelContainer: {},
  inputContainer: {
    borderRadius: 8,
    borderWidth: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    height: SINGLE_LINE_HEIGHT,
    width: '100%',
    backgroundColor: theme.colors.input.background,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    borderColor: theme.colors.borderColor,
  },
  inputContainerError: {
    borderColor: theme.colors.status.errorPrimary,
  },
  textInputStyle: {
    height: SINGLE_LINE_HEIGHT,
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 14,
    lineHeight: 24,
    fontWeight: 400,
    flex: 1,
    color: theme.colors.text.input,
    paddingVertical: 8,
  },
}))
