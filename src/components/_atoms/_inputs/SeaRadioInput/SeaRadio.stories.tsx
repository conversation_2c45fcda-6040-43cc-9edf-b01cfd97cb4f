import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import { SeaRadioInput } from '@src/components/_atoms/_inputs/SeaRadioInput/SeaRadioInput'

const meta = {
  title: 'Components/Atoms/SeaRadioInput',
  component: SeaRadioInput,
  argTypes: {},
  args: {},
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaRadioInput>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    items: [
      { value: 'itemOne', label: 'Item One' },
      { value: 'itemTwo', label: 'Item Two' },
      { value: 'itemThree', label: 'Item Three' },
    ],
  },
  parameters: {
    backgrounds: {
      default: 'Grey',
    },
  },
}
