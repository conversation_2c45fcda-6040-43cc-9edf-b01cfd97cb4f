import React from 'react'
import { Pressable, StyleSheet, Text, View, ViewStyle } from 'react-native'
import { colors } from '@src/theme/colors'
import Animated, { useAnimatedStyle, useSharedValue, withSpring, withTiming } from 'react-native-reanimated'

export interface SeaRadioItem {
  label: string
  value: string
}

interface SeaRadioProps {
  items: SeaRadioItem[]
  onSelect: (item: SeaRadioItem) => void
  style?: ViewStyle
}

export const SeaRadioInput: React.FC<SeaRadioProps> = ({ items, onSelect, style }) => {
  if (!items.length) return null
  const [selectedItem, setSelectedItem] = React.useState(items[0])

  return (
    <View style={[styles.container, style]}>
      {items.map(item => {
        const isSelected = selectedItem.value === item.value

        // Shared values for animations
        const scale = useSharedValue(1) // For scaling animation
        const innerCircleOpacity = useSharedValue(isSelected ? 1 : 0) // For fade-in animation

        // Scale animation
        const animatedOuterStyle = useAnimatedStyle(() => ({
          transform: [{ scale: scale.value }],
        }))

        // Inner circle fade-in animation
        const animatedInnerStyle = useAnimatedStyle(() => ({
          opacity: innerCircleOpacity.value,
        }))

        // Handle press event
        const handlePress = () => {
          if (selectedItem.value !== item.value) {
            // Trigger scale animation
            scale.value = withSpring(1.1, { damping: 5 }, () => {
              scale.value = withSpring(1)
            })

            // Trigger fade-in animation for inner circle
            innerCircleOpacity.value = withTiming(1, { duration: 200 })

            // Update state
            setSelectedItem(item)
            onSelect(item)
          }
        }

        return (
          <View key={item.value} style={styles.radioContainer}>
            <Pressable onPress={handlePress}>
              <Animated.View
                style={[
                  styles.radioButton,
                  animatedOuterStyle,
                  { backgroundColor: isSelected ? colors.primary : undefined },
                ]}>
                {isSelected && (
                  <Animated.View
                    style={[
                      styles.innerCircle,
                      animatedInnerStyle, // Apply fade-in animation
                    ]}
                  />
                )}
              </Animated.View>
            </Pressable>
            <Text>{item.label}</Text>
          </View>
        )
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    alignItems: 'flex-start',
    gap: 20,
  },
  radioContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 16,
  },
  radioButton: {
    backgroundColor: colors.white,
    height: 40,
    width: 40,
    borderRadius: 200,
    borderColor: '#E0E0E0',
    borderWidth: 2,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  innerCircle: {
    height: 16,
    width: 16,
    borderRadius: 200,
    backgroundColor: colors.white,
  },
})
