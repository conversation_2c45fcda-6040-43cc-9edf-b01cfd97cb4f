import { View, Text, Pressable, Animated, ViewStyle } from 'react-native'
import React, { useEffect, useState } from 'react'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '../../SeaTypography/SeaTypography'
import { useAnimatedStyle, useSharedValue, withSpring, withTiming } from 'react-native-reanimated'
import { SeaIcon } from '../../SeaIcon/SeaIcon'
import { SeaStack } from '../../SeaStack/SeaStack'

interface SeaYesNoInputProps {
  value: number
  onChange: (value: number) => void
  label: string
  compact?: boolean
  hasError?: boolean
  style?: ViewStyle
  disabled?: boolean
}

export function SeaYesNoInput({ value, onChange, label, style, disabled = false }: SeaYesNoInputProps) {
  const scale = useSharedValue(1) // For scaling animation
  const iconOpacity = useSharedValue(value ? 1 : 0) // For icon fade-in
  const [isChecked, setIsChecked] = useState<number>(0)
  const { styles, theme } = useStyles(styleSheet)

  // Animation values
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }))
  const iconStyle = useAnimatedStyle(() => ({ opacity: iconOpacity.value }))

  const handlePress = (check: number) => {
    // Animations
    scale.value = withSpring(1.1, { damping: 5 }, () => {
      scale.value = withSpring(1) // Reset scale
    })
    iconOpacity.value = withTiming(check ? 0 : 1, { duration: 200 })

    onChange(check)
    setIsChecked(check)
  }

  useEffect(() => {
    // Update the checkbox state when the value prop changes
    if (value !== 1 && value !== -1) {
      setIsChecked(0)
    } else {
      setIsChecked(value)
    }
  }, [value])

  return (
    <SeaStack
      direction="row"
      gap={10}
      align="end"
      style={{
        paddingVertical: 8,
        borderTopWidth: 1,
        borderTopColor: theme.colors.borderColor,
      }}>
      <Pressable
        style={[styles.container, style, { opacity: disabled ? 0.5 : 1 }]}
        onPress={() => handlePress(isChecked === 1 ? 0 : 1)}
        disabled={disabled}>
        <SeaTypography variant="label">Yes</SeaTypography>
        <Animated.View
          style={[
            styles.checkboxContainer,
            animatedStyle,
            {
              backgroundColor:
                isChecked === 1
                  ? theme.colors.status.okPrimary
                  : disabled
                    ? theme.colors.input.disabledBackground
                    : theme.colors.white,
            },
            {
              height: 35,
              width: 35,
            },
          ]}>
          <Animated.View style={iconStyle}>
            {isChecked === 1 && <SeaIcon icon={'check'} size={16} color={theme.colors.white} />}
          </Animated.View>
        </Animated.View>
      </Pressable>

      <Pressable
        style={[
          styles.container,
          style,
          disabled
            ? {
                opacity: 0.5,
              }
            : {},
        ]}
        onPress={() => handlePress(isChecked === -1 ? 0 : -1)}
        disabled={disabled}>
        <SeaTypography variant="label">No</SeaTypography>

        <Animated.View
          style={[
            styles.checkboxContainer,
            animatedStyle,
            {
              backgroundColor:
                isChecked === -1
                  ? theme.colors.status.errorPrimary
                  : disabled
                    ? theme.colors.input.disabledBackground
                    : theme.colors.white,
            },
            {
              height: 35,
              width: 35,
            },
          ]}>
          <Animated.View style={iconStyle}>
            {isChecked === -1 && <SeaIcon icon={'close'} size={16} color={theme.colors.white} />}
          </Animated.View>
        </Animated.View>
      </Pressable>

      <SeaTypography
        variant={'body'}
        containerStyle={{
          flexShrink: 1,
          marginBottom: 10,
        }}
        numberOfLines={2}
        textStyle={{ marginBottom: 0 }}>
        {label}
      </SeaTypography>
    </SeaStack>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
    // height: 40,
  },
  checkboxContainer: {
    height: 60,
    width: 40,
    backgroundColor: theme.colors.input.background,
    borderColor: theme.colors.borderColor,
    borderRadius: 8,
    borderWidth: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
}))
