import type { Meta, StoryObj } from '@storybook/react'
import React, { useMemo, useState } from 'react'
import { View, Text } from 'react-native'
import { SeaDurationInput } from './SeaDurationInput'
import { Duration } from 'luxon'

const meta = {
  title: 'Components/Atoms/SeaDurationInput',
  component: SeaDurationInput,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <View style={{ width: '30%' }}>
            <Story />
          </View>
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaDurationInput>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => {
    const [duration, setDuration] = useState<Duration>()
    const { hours, minutes } = useMemo(() => {
      if (!duration) return { hours: 0, minutes: 0 }
      const d = duration.shiftTo('hours', 'minutes')
      return { hours: d.hours, minutes: d.minutes }
    }, [duration])

    return (
      <View>
        <SeaDurationInput onChange={setDuration} label={args.label} />
        <Text style={{ marginTop: 8 }}>
          Duration entered: Hours: {hours}, Minutes: {minutes}
        </Text>
      </View>
    )
  },
  args: {
    onChange: val => {
      const d = val.shiftTo('hours', 'minutes')
      alert(`Entered duration - hours: ${d.hours}, minutes:", ${d.minutes}`)
    },
    label: 'Estimated time',
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
