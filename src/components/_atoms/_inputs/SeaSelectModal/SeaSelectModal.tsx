import React, { useEffect, useState } from 'react'
import { FlatList, Modal, Pressable, TouchableOpacity, View, ViewStyle } from 'react-native'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaCloseButton } from '@src/components/_molecules/IconButtons/SeaCloseButton'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaFilterSearch } from '@src/components/_atoms/SeaFilterSearch/SeaFilterSearch'
import { SafeAreaView } from 'react-native-safe-area-context'

export interface LabelAndValue {
  label?: string
  value: string
}

export type SimpleSelectionData = LabelAndValue[]
export type TabularSelectionData = Record<
  string,
  {
    title: string
    data: LabelAndValue[]
  }
>

export const enum CheckBoxActions {
  SELECT = 'SELECT',
  DESELECT = 'DESELECT',
}

interface SeaSelectProps {
  title: string
  data: SimpleSelectionData | TabularSelectionData[]
  visible: boolean
  onClose: () => void
  showSearch?: boolean
  style?: ViewStyle
  onItemSelect?: (action: CheckBoxActions, changedValue: string) => void
  onSetItems?: (action: CheckBoxActions, changedValues: string[]) => void
  selectedItemValues?: string[]
  showSelectAllOption?: boolean
  showPrimaryAction?: boolean
  primaryActionLabel?: string
  primaryActionOnPress?: () => void
}

export const SeaSelectModal = ({
  title,
  data: dataProps,
  visible,
  onClose,
  showSearch,
  onItemSelect,
  onSetItems,
  selectedItemValues = [],
  showSelectAllOption = true,
  showPrimaryAction = true,
  primaryActionLabel = 'Submit',
  primaryActionOnPress,
  style,
}: SeaSelectProps) => {
  const { styles } = useStyles(styleSheet)
  const [searchValue, setSearchValue] = useState('')
  const [data, setData] = useState(dataProps)

  useEffect(() => {
    if (searchValue) {
      const filteredData = (dataProps as SimpleSelectionData).filter(item =>
        item.label?.toLowerCase().includes(searchValue.toLowerCase())
      )
      setData(filteredData)
    } else {
      setData(dataProps)
    }
  }, [searchValue, dataProps])

  // TODO: Cater for Tabular data
  // const isTabularData = !Array.isArray(data); // Tabular data will be an object instead of an array

  return (
    <Modal visible={visible} transparent={true} style={{ zIndex: 1 }} onRequestClose={onClose} animationType={'none'}>
      <SafeAreaView style={{ flex: 1 }} edges={['top', 'left', 'right']}>
        <Pressable style={styles.scrim} onPress={onClose}>
          <View style={styles.container}>
            <Pressable
              style={{ flex: 1 }}
              onPress={() => {
                /* We need to wrap this in a pressable to prevent taps inside the drawer from causing a close event*/
              }}>
              {/** Header Section */}
              <SeaPageCard
                hideBreadcrumbs={true}
                style={{
                  borderBottomRightRadius: 0,
                  borderBottomLeftRadius: 0,
                  marginBottom: 0,
                }}
                subNav={undefined}
                titleComponent={<SeaPageCardTitle title={title} />}
                hidePath={true}
                primaryActionButton={<SeaCloseButton key={'close'} onPress={onClose} />}
              />

              {/** Content Section */}
              <SeaStack direction={'column'} justify={'start'} style={styles.content}>
                {showSearch && (
                  <View style={styles.searchInput}>
                    <SeaFilterSearch value={searchValue} onChangeText={text => setSearchValue(text)} width={'100%'} />
                  </View>
                )}

                {showSelectAllOption && (
                  <TouchableOpacity
                    onPress={() => {
                      if (onSetItems) {
                        const allItemValues = data.map(item => item.value)
                        onSetItems(CheckBoxActions.SELECT, allItemValues)
                        onClose()
                      }
                    }}>
                    <SeaTypography variant={'link'}>Select All</SeaTypography>
                  </TouchableOpacity>
                )}

                <FlatList
                  data={data as LabelAndValue[]}
                  style={{
                    width: '100%',
                    height: '100%',
                    paddingVertical: 5,
                  }}
                  ListEmptyComponent={() => <SeaTypography variant={'input'}>No results found</SeaTypography>}
                  renderItem={({ item, index }) => {
                    const isSelected = selectedItemValues?.includes(item?.value)
                    return (
                      <View
                        style={{
                          paddingVertical: 2,
                          paddingRight: 4,
                        }}>
                        <SeaCheckbox
                          key={item.label}
                          label={item?.label ?? item.value}
                          value={isSelected}
                          onChange={(changedValue: boolean) => {
                            onItemSelect &&
                              onItemSelect(changedValue ? CheckBoxActions.SELECT : CheckBoxActions.DESELECT, item.value)
                          }}
                          style={styles.selectionDataItem}
                        />
                      </View>
                    )
                  }}
                />
              </SeaStack>

              {/** Footer section */}
              <View style={[styles.footerRow]}>
                {showPrimaryAction && (
                  <SeaButton
                    variant={SeaButtonVariant.Primary}
                    onPress={() => {
                      primaryActionOnPress && primaryActionOnPress()
                      onSetItems && onSetItems(CheckBoxActions.SELECT, selectedItemValues)
                      onClose()
                    }}
                    label={primaryActionLabel}
                  />
                )}
              </View>
            </Pressable>
          </View>
        </Pressable>
      </SafeAreaView>
    </Modal>
  )
}

const styleSheet = createStyleSheet(theme => ({
  scrim: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: 26,
    height: '60%',
    width: '90%',
    maxWidth: 500,
    maxHeight: '90%',
    borderBottomRightRadius: 26,
    borderBottomLeftRadius: 26,
    justifyContent: 'center',
  },
  header: {
    marginBottom: 14,
    backgroundColor: theme.colors.white,
  },
  footerRow: {
    height: 80,
    paddingHorizontal: 20,
    paddingBottom: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.borderColor,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: 16,
    backgroundColor: theme.colors.white,
    borderBottomRightRadius: 26,
    borderBottomLeftRadius: 26,
  },
  content: {
    width: '100%',
    flex: 1,
    paddingHorizontal: 20,
  },
  searchInput: {
    width: '100%',
    paddingVertical: 10,
  },
  selectionContent: {
    width: '100%',
    height: '100%',
  },
  selectionData: {
    gap: 20,
  },
  selectionDataItem: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    backgroundColor: theme.colors.white,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
    borderRadius: 8,
  },
}))
