import type { Meta, StoryObj } from '@storybook/react'
import React, { useState } from 'react'
import { Text, View } from 'react-native'
import {
  SeaSelectModal,
  SimpleSelectionData,
  TabularSelectionData,
} from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDrawer } from '@src/components/_atoms/SeaDrawer/SeaDrawer'

const simpleData: SimpleSelectionData = [
  { label: '<PERSON> Ellis', value: '1' },
  { label: '<PERSON>', value: '2' },
  { label: '<PERSON>', value: '3' },
  { label: '<PERSON>', value: '4' },
  { label: '<PERSON>', value: '5' },
  { label: '<PERSON>', value: '6' },
  { label: '<PERSON><PERSON><PERSON>', value: '7' },
  { label: 'Sreeman Nalli', value: '8' },
]

const tabularData: TabularSelectionData = {
  sops: {
    title: 'SOPs',
    data: [],
  },
  companyDocuments: {
    title: 'Company Documents',
    data: [],
  },
  vesselDocuments: {
    title: 'Vessel Documents',
    data: [],
  },
}

const meta = {
  title: 'Components/Atoms/SeaSelectModal',
  component: SeaSelectModal,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaSelectModal>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => {
    const [visible, setVisible] = useState(false)

    return (
      <>
        <SeaButton onPress={() => setVisible(true)} label={'Open Select'} />
        <SeaSelectModal
          title={'Select Crew'}
          data={args.data}
          visible={visible}
          showSearch={args.showSearch}
          onClose={() => setVisible(false)}
        />
      </>
    )
  },
  args: {
    showSearch: false,
    data: simpleData,
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}

export const TabularSelect: Story = {
  ...Default,
  args: {
    data: tabularData,
  },
}
