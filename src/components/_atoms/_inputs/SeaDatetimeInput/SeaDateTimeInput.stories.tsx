import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import { SeaDateTimeInput } from '@src/components/_atoms/_inputs/SeaDatetimeInput/SeaDateTimeInput'
import { DateTime } from 'luxon'

const meta = {
  title: 'Components/Atoms/SeaDateTime',
  component: SeaDateTimeInput,
  argTypes: {
    type: {
      control: 'select',
      options: ['date', 'datetime'],
      description: 'Date or DateTime',
    },
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaDateTimeInput>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    value: DateTime.now().toLocal(),
    onChange: dateTime => {},
    type: 'date',
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
