import React, { useEffect, useState } from 'react'
import { TouchableOpacity, ViewStyle } from 'react-native'
import Animated, { useAnimatedStyle, useSharedValue, withSpring, withTiming } from 'react-native-reanimated'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaStack } from '../../SeaStack/SeaStack'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'

interface SeaCheckboxProps {
  value: boolean
  onChange: (value: boolean) => void
  heading?: string
  label: string
  compact?: boolean
  hasError?: boolean
  style?: ViewStyle
  disabled?: boolean
  forCustomForm?: boolean // Optional prop for custom form usage
}

export const SeaCheckbox = ({
  value,
  onChange,
  heading,
  label,
  style,
  disabled = false,
  forCustomForm = false, // Default to false if not provided
}: SeaCheckboxProps) => {
  const scale = useSharedValue(1) // For scaling animation
  const iconOpacity = useSharedValue(value ? 1 : 0) // For icon fade-in
  const [isChecked, setIsChecked] = useState<boolean>(value)
  const { styles, theme } = useStyles(styleSheet)

  // Animation values
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }))
  const iconStyle = useAnimatedStyle(() => ({ opacity: iconOpacity.value }))

  const handlePress = () => {
    // Animations
    scale.value = withSpring(1.1, { damping: 5 }, () => {
      scale.value = withSpring(1) // Reset scale
    })
    iconOpacity.value = withTiming(isChecked ? 0 : 1, { duration: 200 })

    onChange(!isChecked)
    setIsChecked(!isChecked)
  }

  useEffect(() => {
    // Update the checkbox state when the value prop changes
    setIsChecked(value)
  }, [value])

  return (
    <SeaStack direction="column" align="start" gap={10} style={{ width: '100%', flex: 1 }}>
      {heading && (
        <SeaTypography variant="label" textStyle={{ marginBottom: 0 }} containerStyle={{ marginBottom: 0 }}>
          {forCustomForm ? heading : heading.toUpperCase()}
        </SeaTypography>
      )}
      <TouchableOpacity
        style={[styles.container, style, { opacity: disabled ? 0.5 : 1 }]}
        onPress={handlePress}
        disabled={disabled}>
        <Animated.View
          style={[
            styles.checkboxContainer,
            animatedStyle,
            {
              backgroundColor: isChecked
                ? theme.colors.primary
                : disabled
                  ? theme.colors.input.disabledBackground
                  : theme.colors.white,
            },
            {
              height: 35,
              width: 35,
            },
          ]}>
          <Animated.View style={iconStyle}>
            {isChecked && <SeaIcon icon={'check'} size={16} color={theme.colors.white} />}
          </Animated.View>
        </Animated.View>
        <SeaTypography
          variant={'body'}
          containerStyle={{
            flexShrink: 1,
          }}
          numberOfLines={2}
          textStyle={{ marginBottom: 0 }}>
          {label}
        </SeaTypography>
      </TouchableOpacity>
      <SeaSpacer height={18} />
    </SeaStack>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    // height: 40,
  },
  checkboxContainer: {
    height: 60,
    width: 40,
    backgroundColor: theme.colors.input.background,
    borderColor: theme.colors.borderColor,
    borderRadius: 8,
    borderWidth: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
}))
