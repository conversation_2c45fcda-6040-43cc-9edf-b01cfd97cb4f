import type { Meta, StoryObj } from '@storybook/react'
import React, { useState } from 'react'
import { View } from 'react-native'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'

const meta = {
  title: 'Components/Atoms/SeaCheckbox',
  component: SeaCheckbox,
  argTypes: {},
  args: {},
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#E6E7EA',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaCheckbox>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => {
    const [value, setValue] = useState(args.value)
    return <SeaCheckbox value={value} onChange={value => setValue(value)} label={args.label} />
  },
  args: {
    label: 'Check the box!',
    value: false,
    onChange: () => {},
  },
}
