import React from 'react'
import { Pressable, StyleSheet, Text, View } from 'react-native'
import { colors } from '@src/theme/colors'
import { fontFamily } from '@src/theme/typography'

export interface SeaSelectorItem {
  label: string
  value: string
  onPress: (item: SeaSelectorItem) => void
}

interface SeaSelectorProps {
  items: SeaSelectorItem[]
  selectedValue?: string
}

export const SeaSelector: React.FC<SeaSelectorProps> = ({ items, selectedValue }) => {
  if (items.length === 0) return null

  return (
    <View style={[styles.container]}>
      {items.map((item, index) => {
        const isSelected = item.value === selectedValue

        return (
          <Pressable
            key={index}
            onPress={() => item.onPress(item)}
            style={[styles.itemContainer, isSelected ? styles.selectedItem : null]}>
            <Text style={[styles.itemText, isSelected ? styles.selectedItemText : null]} selectable={false}>
              {item.label}
            </Text>
          </Pressable>
        )
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFF',
    flexDirection: 'row',
    alignItems: 'center',
    //justifyContent: "space-evenly",
    padding: 6,
    borderRadius: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 6,
    gap: 10,
    flexWrap: 'wrap',
    rowGap: 6,
  },
  itemContainer: {
    paddingVertical: 8,
    paddingHorizontal: 11,
    borderRadius: 12,
  },
  itemText: {
    color: colors.text.primary,
    fontFamily: fontFamily.BODY_FONT,
    fontWeight: '700',
    fontSize: 14,
    lineHeight: 22,
    letterSpacing: 0.5,
  },
  selectedItem: {
    backgroundColor: '#E6E7EA',
  },
  selectedItemText: {
    color: colors.primary,
  },
})
