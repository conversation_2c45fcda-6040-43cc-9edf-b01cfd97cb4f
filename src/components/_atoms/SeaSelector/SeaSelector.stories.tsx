import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import { SeaSelector } from '@src/components/_atoms/SeaSelector/SeaSelector'

const meta = {
  title: 'Components/Atoms/SeaSelector',
  component: SeaSelector,
  argTypes: {},
  args: {},
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#E6E7EA',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaSelector>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    items: [
      { value: 'taskHistory', label: 'Task History' },
      { value: 'equipmentHistory', label: 'Equipment History' },
      { value: 'spareParts', label: 'Spare Parts' },
    ],
  },
}
