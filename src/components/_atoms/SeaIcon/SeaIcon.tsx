import React from 'react'
import { StyleProp, Text, ViewStyle } from 'react-native'

import { fontFamily } from '@src/theme/typography'
import { MaterialIconName } from '@src/types/MaterialIcons'
import { colors } from '@src/theme/globalStyle'

export type IconVariant = 'outlined' | 'rounded' | 'sharp'
export interface SeaIconProps {
  icon: MaterialIconName
  size?: number
  color?: string
  style?: StyleProp<ViewStyle>
  variant?: IconVariant
  fill?: boolean
}

export const SeaIcon = ({
  icon,
  size = 22,
  color = colors.primary,
  style,
  variant = 'outlined',
  fill = true,
}: SeaIconProps) => {
  if (!icon) return <></>

  return (
    <Text
      style={[
        style,
        {
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          fontSize: size,
          color,
          // @ts-expect-error: fontVariationSettings is valid on web
          fontVariationSettings: `${fill ? "'FILL' 1" : "'FILL' 0"}`,
          fontFamily:
            variant === 'sharp'
              ? fontFamily.MATERIAL_ICON_SHARP
              : variant === 'rounded'
                ? fontFamily.MATERIAL_ICON_ROUNDED
                : fontFamily.MATERIAL_ICON_OUTLINED,
        },
      ]}>
      {icon}
    </Text>
  )
}
