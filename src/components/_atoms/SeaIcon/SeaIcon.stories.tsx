import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import SeaFooter from '@src/components/_legacy/SeaFooter/SeaFooter'
import SeaIcon from '@src/components/_legacy/SeaIcon/SeaIcon'

const meta = {
  title: 'Components/Atoms/SeaIcon',
  component: SeaIcon,
  args: {
    size: 30,
    color: 'white',
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: 500,
          }}>
          <Story />
        </View>
      )
    },
  ],
  parameters: {
    backgrounds: {
      default: 'Grey',
    },
  },
} satisfies Meta<typeof SeaIcon>

export default meta

type Story = StoryObj<typeof meta>

export const Wind: Story = {
  args: {
    icon: 'wind',
  },
}

export const Menu: Story = {
  args: {
    icon: 'menu',
  },
}
export const Log: Story = {
  args: {
    icon: 'log',
  },
}
export const LogOut: Story = {
  args: {
    icon: 'log-out-outline',
  },
}
export const ChevronBack: Story = {
  args: {
    icon: 'chevron-back',
  },
}
export const ChevronForward: Story = {
  args: {
    icon: 'chevron-forward',
  },
}
export const Eye: Story = {
  args: {
    icon: 'eye',
  },
}
export const EyeOff: Story = {
  args: {
    icon: 'eye-off',
  },
}
export const Close: Story = {
  args: {
    icon: 'close',
  },
}
export const CloudOffline: Story = {
  args: {
    icon: 'cloud-offline-outline',
  },
}
