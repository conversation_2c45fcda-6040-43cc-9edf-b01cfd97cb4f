import { Text, View } from 'react-native'
import type { Meta, StoryObj } from '@storybook/react'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'

const meta = {
  title: 'Components/Atoms/SeaStatusPill',
  component: SeaStatusPill,
  argTypes: {
    variant: {
      control: 'select',
      options: ['ok', 'upcoming', 'dueToday', 'overdue', 'fault'],
      description: 'Select the pill variant',
    },
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#505050',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaStatusPill>

export default meta

type Story = StoryObj<typeof meta>

export const Ok: Story = {
  args: {
    variant: SeaStatusType.Ok,
    label: '00 DAYS',
  },
}
export const Warning: Story = {
  args: {
    variant: SeaStatusType.Warning,
    label: '00 DAYS',
  },
}
export const Attention: Story = {
  args: {
    variant: SeaStatusType.Attention,
    label: 'DUE TODAY',
  },
}
export const Error: Story = {
  args: {
    variant: SeaStatusType.Error,
    label: '00 DAYS OD',
  },
}
export const Critical: Story = {
  args: {
    variant: SeaStatusType.Critical,
    label: 'FAULT',
  },
}
