import React from 'react'
import { StyleProp, Text, TextStyle, View, ViewStyle } from 'react-native'
import { SeaStatusType } from '@src/types/Common'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaIcon, SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'

interface SeaStatusProps {
  variant?: SeaStatusType
  /** @deprecated: Use secondaryLabel instead.
   * TODO: Remove it in the future from all places
   */
  label?: string // Using label to keep it compatible with other use cases
  primaryLabel?: string
  secondaryLabel?: string
  containerStyle?: StyleProp<ViewStyle>
  textStyle?: StyleProp<TextStyle>
  compact?: boolean
}

export const SeaStatusPill = ({
  variant = SeaStatusType.Ok,
  primaryLabel: primaryLabelProp,
  /** @deprecated: TODO: Remove it in the future from all places */
  label,
  secondaryLabel = label ?? undefined,
  containerStyle,
  textStyle,
  compact = false,
}: SeaStatusProps) => {
  const { styles } = useStyles(styleSheet)

  const getVariantStyles = () => {
    switch (variant) {
      case SeaStatusType.Minor:
        return {
          primaryLabel: primaryLabelProp ?? 'DUE TODAY',
          primaryLabelStyle: styles.primaryLabelStyleMinor,
          primaryContainerStyle: styles.primaryContainerStyleMinor,
          secondaryLabelStyle: styles.secondaryLabelStyleMinor,
          secondaryContainerStyle: styles.secondaryContainerStyleMinor,
          iconOptions: {
            icon: 'schedule',
            color: styles.primaryLabelStyleMinor.color,
            fill: true,
            style: {
              fontWeight: 'bold',
            },
          } as SeaIconProps,
        }
      case SeaStatusType.Warning:
        return {
          primaryLabel: primaryLabelProp ?? 'UPCOMING',
          primaryLabelStyle: styles.primaryLabelStyleWarning,
          primaryContainerStyle: styles.primaryContainerStyleWarning,
          secondaryLabelStyle: styles.secondaryLabelStyleWarning,
          secondaryContainerStyle: styles.secondaryContainerStyleWarning,
          iconOptions: {
            icon: 'schedule',
            color: styles.primaryLabelStyleWarning.color,
            fill: true,
            style: {
              fontWeight: 'bold',
            },
          } as SeaIconProps,
        }
      case SeaStatusType.Attention:
        return {
          primaryLabel: primaryLabelProp ?? 'MISSING',
          primaryLabelStyle: styles.primaryLabelStyleAttention,
          primaryContainerStyle: styles.primaryContainerStyleAttention,
          secondaryLabelStyle: styles.secondaryLabelStyleAttention,
          secondaryContainerStyle: styles.secondaryContainerStyleAttention,
          iconOptions: {
            icon: 'cancel',
            color: styles.primaryLabelStyleAttention.color,
            fill: true,
            style: {
              fontWeight: 'bold',
            },
          } as SeaIconProps,
        }
      case SeaStatusType.Error:
        return {
          primaryLabel: primaryLabelProp ?? 'OVERDUE',
          primaryLabelStyle: styles.primaryLabelStyleError,
          primaryContainerStyle: styles.primaryContainerStyleError,
          secondaryLabelStyle: styles.secondaryLabelStyleError,
          secondaryContainerStyle: styles.secondaryContainerStyleError,
          iconOptions: {
            icon: 'error',
            color: styles.primaryLabelStyleError.color,
            fill: true,
            style: {
              fontWeight: 'bold',
            },
          } as SeaIconProps,
        }
      case SeaStatusType.Critical:
        return {
          primaryLabel: primaryLabelProp ?? 'FAULT',
          primaryLabelStyle: styles.primaryLabelStyleCritical,
          primaryContainerStyle: styles.primaryContainerStyleCritical,
          secondaryLabelStyle: styles.secondaryLabelStyleCritical,
          secondaryContainerStyle: styles.secondaryContainerStyleCritical,
          iconOptions: {
            icon: 'error',
            color: styles.primaryLabelStyleCritical.color,
            fill: true,
            style: {
              fontWeight: 'bold',
            },
          } as SeaIconProps,
        }
      case SeaStatusType.Ok:
        return {
          primaryLabel: primaryLabelProp ?? 'COMPLETE',
          primaryLabelStyle: styles.primaryLabelStyleOk,
          primaryContainerStyle: styles.primaryContainerStyleOk,
          secondaryLabelStyle: styles.secondaryLabelStyleOk,
          secondaryContainerStyle: styles.secondaryContainerStyleOk,
          iconOptions: {
            icon: 'check_circle',
            color: styles.primaryLabelStyleOk.color,
            fill: true,
            style: {
              fontWeight: 'bold',
            },
          } as SeaIconProps,
        }
      default:
        return {
          primaryLabel: primaryLabelProp ?? 'NA',
          primaryLabelStyle: styles.primaryLabelStyleNotApplicable,
          primaryContainerStyle: styles.primaryContainerStyleNotApplicable,
          secondaryLabelStyle: styles.secondaryLabelStyleNotApplicable,
          secondaryContainerStyle: styles.secondaryContainerStyleNotApplicable,
          iconOptions: {
            icon: 'do_not_disturb_on',
            color: styles.primaryLabelStyleNotApplicable.color,
            fill: true,
            style: {
              fontWeight: 'bold',
            },
          } as SeaIconProps,
        }
    }
  }

  const {
    primaryLabel,
    primaryLabelStyle,
    secondaryLabelStyle,
    primaryContainerStyle,
    secondaryContainerStyle,
    iconOptions,
  } = getVariantStyles()

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.innerContainer,
          {
            borderTopLeftRadius: 24,
            borderBottomLeftRadius: 24,
            borderTopRightRadius: secondaryLabel ? 0 : 24,
            borderBottomRightRadius: secondaryLabel ? 0 : 24,
          },
          primaryContainerStyle,
          containerStyle,
        ]}>
        <SeaIcon {...iconOptions} />
        {!compact && <Text style={[styles.primaryText, primaryLabelStyle, textStyle]}>{primaryLabel}</Text>}
      </View>
      {secondaryLabel && (
        <View
          style={[
            styles.innerContainer,
            {
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
              borderTopRightRadius: 24,
              borderBottomRightRadius: 24,
            },
            secondaryContainerStyle,
            containerStyle,
          ]}>
          <Text style={[styles.secondaryText, secondaryLabelStyle, textStyle]}>{secondaryLabel}</Text>
        </View>
      )}
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  // Base Styling
  container: {
    flexDirection: 'row',
  },
  innerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 2,
    paddingRight: 2,
  },
  primaryText: {
    fontSize: 12,
    fontWeight: 700,
    flexDirection: 'row',
    paddingHorizontal: 5,
  },
  secondaryText: {
    fontSize: 12,
    fontWeight: 500,
    paddingHorizontal: 5,
  },

  // OK style
  primaryLabelStyleOk: {
    color: theme.colors.status.okPrimary,
    backgroundColor: theme.colors.status.okOutlinePrimary,
  },
  primaryContainerStyleOk: {
    backgroundColor: theme.colors.status.okOutlinePrimary,
  },
  secondaryLabelStyleOk: {
    color: theme.colors.status.okPrimary,
    backgroundColor: theme.colors.status.okOutlineSecondary,
  },
  secondaryContainerStyleOk: {
    backgroundColor: theme.colors.status.okOutlineSecondary,
  },

  // NA style
  primaryLabelStyleNotApplicable: {
    color: theme.colors.status.notApplicablePrimary,
    backgroundColor: theme.colors.status.notApplicableOutlinePrimary,
  },
  primaryContainerStyleNotApplicable: {
    backgroundColor: theme.colors.status.notApplicableOutlinePrimary,
  },
  secondaryLabelStyleNotApplicable: {
    color: theme.colors.status.okPrimary,
    backgroundColor: theme.colors.status.notApplicableOutlineSecondary,
  },
  secondaryContainerStyleNotApplicable: {
    backgroundColor: theme.colors.status.notApplicableOutlineSecondary,
  },

  // Minor style
  primaryLabelStyleMinor: {
    color: theme.colors.status.minorPrimary,
    backgroundColor: theme.colors.status.minorOutlinePrimary,
  },
  primaryContainerStyleMinor: {
    backgroundColor: theme.colors.status.minorOutlinePrimary,
  },
  secondaryLabelStyleMinor: {
    color: theme.colors.status.minorPrimary,
    backgroundColor: theme.colors.status.minorOutlineSecondary,
  },
  secondaryContainerStyleMinor: {
    backgroundColor: theme.colors.status.minorOutlineSecondary,
  },

  // Warning Style
  primaryLabelStyleWarning: {
    color: theme.colors.status.warnPrimary,
    backgroundColor: theme.colors.status.warnOutlinePrimary,
  },
  primaryContainerStyleWarning: {
    backgroundColor: theme.colors.status.warnOutlinePrimary,
  },
  secondaryLabelStyleWarning: {
    color: theme.colors.status.warnPrimary,
    backgroundColor: theme.colors.status.warnOutlineSecondary,
  },
  secondaryContainerStyleWarning: {
    backgroundColor: theme.colors.status.warnOutlineSecondary,
  },

  // Due Today Style
  primaryLabelStyleAttention: {
    color: theme.colors.status.attentionPrimary,
    backgroundColor: theme.colors.status.attentionOutlinePrimary,
  },
  primaryContainerStyleAttention: {
    backgroundColor: theme.colors.status.attentionOutlinePrimary,
  },
  secondaryLabelStyleAttention: {
    color: theme.colors.status.attentionPrimary,
    backgroundColor: theme.colors.status.attentionOutlineSecondary,
  },
  secondaryContainerStyleAttention: {
    backgroundColor: theme.colors.status.attentionOutlineSecondary,
  },

  // Error Style
  primaryLabelStyleError: {
    color: theme.colors.status.errorPrimary,
    backgroundColor: theme.colors.status.errorOutlinePrimary,
  },
  primaryContainerStyleError: {
    backgroundColor: theme.colors.status.errorOutlinePrimary,
  },
  secondaryLabelStyleError: {
    color: theme.colors.status.errorPrimary,
    backgroundColor: theme.colors.status.errorOutlineSecondary,
  },
  secondaryContainerStyleError: {
    backgroundColor: theme.colors.status.errorOutlineSecondary,
  },

  // Critical Style
  primaryLabelStyleCritical: {
    color: theme.colors.status.criticalTextPrimary,
    backgroundColor: theme.colors.status.criticalOutlinePrimary,
  },
  primaryContainerStyleCritical: {
    backgroundColor: theme.colors.status.criticalOutlinePrimary,
  },
  secondaryLabelStyleCritical: {
    color: theme.colors.status.criticalTextSecondary,
    backgroundColor: theme.colors.status.criticalOutlineSecondary,
  },
  secondaryContainerStyleCritical: {
    borderColor: theme.colors.status.criticalOutlinePrimary,
    borderWidth: 2,
    backgroundColor: theme.colors.status.criticalOutlineSecondary,
  },
}))
