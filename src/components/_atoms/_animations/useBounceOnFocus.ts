import { useAnimatedStyle, useSharedValue, withSpring, withTiming } from 'react-native-reanimated'
import { useStyles } from '@src/theme/styles'

export const useBounceOnFocus = (borderColorParam?: string) => {
  const { theme } = useStyles()

  const borderColor = borderColorParam ?? theme.colors.borderColor

  const scale = useSharedValue(1)
  const borderCol = useSharedValue(borderColor)

  const triggerFocus = () => {
    scale.value = withSpring(1.1, { damping: 5, stiffness: 100 }, () => {
      scale.value = withSpring(1, { damping: 5, stiffness: 100 })
    })
    borderCol.value = withTiming(theme.colors.primary, { duration: 200 })
  }

  const triggerBlur = () => {
    borderCol.value = withTiming(theme.colors.borderColor, { duration: 200 })
  }

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    borderColor: borderCol.value,
  }))

  return { animatedStyle, triggerFocus, triggerBlur }
}
