import { useSharedValue, useAnimatedStyle, withTiming, useAnimatedReaction } from 'react-native-reanimated'

export const useFadeLabel = (showLabel: boolean) => {
  const labelOpacity = useSharedValue(0)

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: labelOpacity.value,
  }))

  useAnimatedReaction(
    () => showLabel,
    (cur, prev) => {
      labelOpacity.value = withTiming(cur ? 1 : 0, { duration: 300 })
    }
  )

  return { animatedStyle }
}
