import { View, Text, Pressable, StyleSheet } from 'react-native'
import React from 'react'

import { SeaCard, SeaCardBody, SeaCardContent } from '../SeaCard/SeaCard'
import { SeaStack } from '../SeaStack/SeaStack'
import { SeaTypography } from '../SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaIcon, SeaIconProps } from '../SeaIcon/SeaIcon'
import { useDeviceWidth } from '@src/hooks/useDevice'

interface SeaMetricCardProps {
  onPress?: () => void
  title: string
  iconOptions?: SeaIconProps
  metricValue: string
  additionalInfo?: string
  additionalInfoColor?: string
}
export function SeaMetricCard({
  metricValue,
  title,
  additionalInfo,
  additionalInfoColor,
  iconOptions,
  onPress,
}: SeaMetricCardProps) {
  const { styles, theme } = useStyles(styleSheet)
  const { isMobileWidth } = useDeviceWidth()
  const { color: iconColor, icon, ...restIconOptions } = { ...iconOptions }

  return (
    <Pressable onPress={onPress} disabled={!onPress} style={styles.card}>
      <SeaCard>
        <SeaCardBody style={isMobileWidth ? { padding: 12 } : {}}>
          <SeaCardContent style={{ flexDirection: 'column', gap: 10 }}>
            <SeaStack justify="between" align="center" gap={10}>
              <SeaTypography variant="body">{title}</SeaTypography>
              {onPress && <SeaIcon icon="north_east" size={20} color={theme.colors.primary} />}
            </SeaStack>
            <SeaStack direction="row" gap={10} align="center" justify="start">
              {icon && <SeaIcon icon={icon} color={iconColor ?? theme.colors.text.body} {...restIconOptions} />}
              <SeaTypography
                variant="cardTitle"
                textStyle={StyleSheet.flatten([styles.title, isMobileWidth ? styles.mobileTitle : {}])}
                containerStyle={{
                  margin: 0,
                }}>
                {metricValue}
              </SeaTypography>
              {additionalInfo && (
                <SeaTypography
                  variant="body"
                  textStyle={StyleSheet.flatten([
                    styles.additionalDetailsText,
                    isMobileWidth ? styles.mobileAdditionalDetailsText : {},
                  ])}
                  containerStyle={styles.additionalDetailsContainer}
                  color={additionalInfoColor ?? undefined}>
                  {additionalInfo}
                </SeaTypography>
              )}
            </SeaStack>
          </SeaCardContent>
        </SeaCardBody>
      </SeaCard>
    </Pressable>
  )
}

const styleSheet = createStyleSheet(theme => ({
  card: {
    flex: 1,
    width: '100%',
    minWidth: 300,
  },
  mobileTitle: {
    fontSize: 20,
    margin: 0,
  },
  title: {
    margin: 0,
    fontSize: 24,
  },
  mobileAdditionalDetailsText: {
    fontSize: 12,
  },
  additionalDetailsText: {
    marginBottom: 6,
  },
  additionalDetailsContainer: {
    margin: 0,
    alignSelf: 'flex-end',
  },
}))
