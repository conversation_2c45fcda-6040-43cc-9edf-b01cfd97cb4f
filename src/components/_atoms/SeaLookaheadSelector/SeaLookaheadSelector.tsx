import React from 'react'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { createStyleSheet, useStyles } from '@src/theme/styles'

export interface SeaLookaheadSelectorProps {
  value?: string
  onChange: (value: string) => void
}

export const SeaLookaheadSelector = ({ value, onChange }: SeaLookaheadSelectorProps) => {
  const { styles } = useStyles(styleSheet)

  const handleSelect = (value: string) => {
    onChange(value)
  }

  const isActive = value !== undefined

  return (
    <SeaDropdown
      rounded
      label={' '}
      value={value}
      placeholder={'Due Within'}
      items={[
        { label: 'Next 7 days', value: '7d' },
        { label: 'Next 14 days', value: '14d' },
        { label: 'Next 30 days', value: '30d' },
        { label: 'Next 60 days', value: '60d' },
        { label: 'Next 90 days', value: '90d' },
        { label: 'Next 180 days', value: '180d' },
      ]}
      onSelect={value => handleSelect(value)}
      containerStyle={isActive ? styles.active : styles.inactive}
      textStyle={isActive ? styles.activeText : styles.inactiveText}
    />
  )
}

const styleSheet = createStyleSheet(theme => ({
  active: {
    backgroundColor: '#c4deff',
    borderColor: 'transparent',
  },
  activeText: {
    color: '#2883f8',
    fontWeight: 'bold',
    fontSize: 12,
  },
  inactive: {
    backgroundColor: theme.colors.white,
  },
  inactiveText: {
    fontSize: 12,
    color: theme.colors.text.placeholder,
  },
}))
