import { View, ViewStyle, Modal, Pressable, ScrollView, DimensionValue, StyleSheet } from 'react-native'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
import { SeaCloseButton } from '@src/components/_molecules/IconButtons/SeaCloseButton'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '../../SeaTypography/SeaTypography'
import { SeaStack } from '../../SeaStack/SeaStack'

export interface SeaModalProps {
  title: string
  visible: boolean
  onClose: () => void
  actions?: React.ReactNode
  children: React.ReactNode
  maxHeight?: DimensionValue
  maxWidth?: DimensionValue
  style?: ViewStyle
}

export function SeaModal({
  children,
  title,
  visible,
  onClose,
  actions,
  maxHeight = '80%',
  maxWidth = '90%',
}: SeaModalProps) {
  const { styles } = useStyles(styleSheet)
  return (
    <Modal visible={visible} transparent onRequestClose={onClose}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.overlay}>
          <Pressable style={styles.scrim} onPress={onClose} />

          <View
            style={[
              styles.modalContainer,
              {
                maxHeight: maxHeight as DimensionValue,
                maxWidth: maxWidth as DimensionValue,
              },
            ]}>
            <View style={styles.header}>
              <SeaStack gap={10} direction="row" align="center" justify="between">
                <View style={{ flex: 1 }}>
                  <SeaTypography variant="cardTitle" textStyle={{ margin: 0 }} containerStyle={{ margin: 0 }}>
                    {title}
                  </SeaTypography>
                </View>
                <View style={{ width: 40 }}>
                  <SeaCloseButton key={'close'} onPress={onClose} />
                </View>
              </SeaStack>
            </View>
            <ScrollView style={styles.scroll} contentContainerStyle={styles.scrollContent}>
              {children}
            </ScrollView>

            {actions && <View style={styles.footer}>{actions}</View>}
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  )
}

const styleSheet = createStyleSheet(theme => ({
  safeArea: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrim: {
    ...StyleSheet.absoluteFillObject,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    width: '90%',
    maxHeight: '80%',
    overflow: 'hidden',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderColor: theme.colors.borderColor,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  scroll: {
    flexGrow: 0,
  },
  scrollContent: {
    padding: 16,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderColor: theme.colors.borderColor,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
}))
