import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import { SeaStack } from './SeaStack'

const meta = {
  title: 'Components/Atoms/SeaStack',
  component: SeaStack,
  argTypes: {},
  args: {},
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaStack>

export default meta

type Story = StoryObj<typeof meta>

export const Row: Story = {
  args: {
    gap: 10,
    padding: 16,
    justify: 'start',
    children: (
      <>
        <View
          style={{
            width: 50,
            height: 50,
            borderRadius: 16,
            backgroundColor: 'red',
          }}
        />
        <View
          style={{
            width: 50,
            height: 60,
            borderRadius: 16,
            backgroundColor: 'green',
          }}
        />
        <View
          style={{
            width: 50,
            height: 70,
            borderRadius: 16,
            backgroundColor: 'blue',
          }}
        />
      </>
    ),
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}

export const Column: Story = {
  ...Row,
  args: {
    ...Row.args,
    direction: 'column',
    children: Row.args?.children,
  },
}
