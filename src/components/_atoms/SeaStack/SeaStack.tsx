import React, { useMemo } from 'react'
import { StyleSheet, View, ViewStyle } from 'react-native'
import { useDeviceWidth } from '@src/hooks/useDevice'

export interface SeaStackProps {
  children: React.ReactNode
  padding?: number
  gap?: number
  direction?: 'row' | 'column'
  justify?: 'start' | 'end' | 'center' | 'between' | 'around'
  align?: 'start' | 'center' | 'end'
  style?: ViewStyle
  width?: ViewStyle['width']
  /** @var isCollapsible - If true, the stack will be collapsible on mobile and take up the full width */
  isCollapsible?: boolean
}

export const SeaStack = ({
  direction = 'row',
  children,
  padding,
  gap,
  justify,
  align,
  style,
  width,
  isCollapsible = false,
}: SeaStackProps) => {
  const { isDesktopWidth } = useDeviceWidth()

  const collapsibleStyles = useMemo((): ViewStyle => {
    // If collapsible, then we want to take up the full width on mobile and collapse to column
    if (isCollapsible && !isDesktopWidth) {
      return {
        flexDirection: 'column',
        width: '100%',
      }
    }
    return {
      flexDirection: direction,
      width: width,
    }
  }, [direction, width, isCollapsible, isDesktopWidth])

  const dynamicStyles: ViewStyle = {
    ...collapsibleStyles,
    padding: padding,
    gap: gap,
    justifyContent: getJustifyValue(justify),
    alignItems: getAlignValue(align),
  }
  return <View style={[styles.container, dynamicStyles, style]}>{children}</View>
}

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    alignItems: 'center',
  },
})

const getAlignValue = (input?: string) => {
  switch (input) {
    case 'start':
      return 'flex-start'
    case 'end':
      return 'flex-end'
    case 'center':
      return 'center'
    default:
      return 'center'
  }
}

const getJustifyValue = (input?: string) => {
  switch (input) {
    case 'start':
      return 'flex-start'
    case 'end':
      return 'flex-end'
    case 'center':
      return 'center'
    case 'between':
      return 'space-between'
    case 'around':
      return 'space-around'
    default:
      return undefined
  }
}
