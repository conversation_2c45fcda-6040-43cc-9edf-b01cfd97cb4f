import React, { useState, useRef, useEffect, useMemo } from 'react'
import { View, StyleSheet, Platform, Pressable } from 'react-native'
import { PanGestureHandler, State, GestureHandlerRootView } from 'react-native-gesture-handler'
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  runOnJS,
  withSpring,
  withTiming,
} from 'react-native-reanimated'

interface SeaDraggableProps {
  type?: 'tiles' | 'customForms' | 'list'
  items: any[]
  onReorder?: (newOrder: any[]) => void
  onDrop?: (initialIndex: number, newIndex: number) => void
  setIsInternalDrag?: (isInternalDrag: boolean) => void
  selectedIndex?: number | null
  setSelectedIndex?: (index: number | null) => void
  flexDirection?: 'row' | 'column'
  nonDraggableStartComponent?: React.ReactNode
  nonDraggableEndComponent?: React.ReactNode
  children: React.ReactNode[]
}

export const SeaDraggable: React.FC<SeaDraggableProps> = ({
  type = 'tiles',
  items,
  onReorder,
  onDrop,
  setIsInternalDrag,
  selectedIndex,
  setSelectedIndex,
  flexDirection = 'row',
  nonDraggableStartComponent,
  nonDraggableEndComponent,
  children,
}) => {
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null)
  const [highlightedIndex, setHighlightedIndex] = useState<number | null>(null)
  const [draggedOverIndex, setDraggedOverIndex] = useState<number | null>(null)
  const [itemPositions, setItemPositions] = useState<Array<{ x: number; y: number; width: number; height: number }>>([])
  const [containerLayout, setContainerLayout] = useState<{ x: number; y: number; width: number; height: number }>({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  })

  const dragItem = useRef<number | null>(null)
  const translateX = useSharedValue(0)
  const translateY = useSharedValue(0)
  const scale = useSharedValue(1)
  const opacity = useSharedValue(1)

  const notEnoughDraggableItems = useMemo(() => {
    if (!Array.isArray(children) || children.length <= 1) {
      return true
    }
    return children.length <= 1
  }, [children])

  const handlePress = (index: number) => {
    setHighlightedIndex(index)
    setSelectedIndex?.(index)
  }

  const findDropIndex = (absoluteX: number, absoluteY: number) => {
    // Convert absolute coordinates to relative coordinates within the container
    const relativeX = absoluteX - containerLayout.x
    const relativeY = absoluteY - containerLayout.y

    let closestIndex = 0
    let closestDistance = Infinity

    for (let i = 0; i < itemPositions.length; i++) {
      const pos = itemPositions[i]
      if (!pos) continue

      const centerX = pos.x + pos.width / 2
      const centerY = pos.y + pos.height / 2
      const distance = Math.sqrt(Math.pow(relativeX - centerX, 2) + Math.pow(relativeY - centerY, 2))

      if (distance < closestDistance) {
        closestDistance = distance
        closestIndex = i
      }
    }

    return closestIndex
  }

  const handleItemLayout = (index: number, event: any) => {
    const { x, y, width, height } = event.nativeEvent.layout
    setItemPositions(prev => {
      const newPositions = [...prev]
      newPositions[index] = { x, y, width, height }
      return newPositions
    })
  }

  const handleContainerLayout = (event: any) => {
    const { x, y, width, height } = event.nativeEvent.layout
    setContainerLayout({ x, y, width, height })
  }

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (event, context) => {
      if (dragItem.current !== null) {
        context.startX = translateX.value
        context.startY = translateY.value
        scale.value = withSpring(1.05)
        opacity.value = withTiming(0.9)

        if (setIsInternalDrag) {
          runOnJS(setIsInternalDrag)(true)
        }
        runOnJS(setDraggingIndex)(dragItem.current)
      }
    },
    onActive: (event, context) => {
      if (dragItem.current !== null) {
        translateX.value = context.startX + event.translationX
        translateY.value = context.startY + event.translationY

        // Find which item we're dragging over
        const dropIndex = runOnJS(findDropIndex)(event.absoluteX, event.absoluteY)

        if (dropIndex !== dragItem.current) {
          runOnJS(setDraggedOverIndex)(dropIndex)
        }
      }
    },
    onEnd: event => {
      const finalDropIndex = draggedOverIndex

      if (dragItem.current !== null && finalDropIndex !== null && finalDropIndex !== dragItem.current) {
        // Perform reorder
        const newItems = [...items]
        const [draggedItem] = newItems.splice(dragItem.current, 1)
        newItems.splice(finalDropIndex, 0, draggedItem)

        if (onReorder) {
          runOnJS(onReorder)(newItems)
        }
        if (onDrop) {
          runOnJS(onDrop)(dragItem.current, finalDropIndex)
        }

        runOnJS(setSelectedIndex)?.(finalDropIndex)
        runOnJS(setHighlightedIndex)(finalDropIndex)
      }

      // Reset animations
      translateX.value = withSpring(0)
      translateY.value = withSpring(0)
      scale.value = withSpring(1)
      opacity.value = withTiming(1)

      // Reset state
      runOnJS(setDraggingIndex)(null)
      runOnJS(setDraggedOverIndex)(null)
      dragItem.current = null

      if (setIsInternalDrag) {
        runOnJS(setIsInternalDrag)(false)
      }
    },
  })

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: translateX.value }, { translateY: translateY.value }, { scale: scale.value }],
      opacity: opacity.value,
      zIndex: draggingIndex !== null ? 1000 : 1,
    }
  })

  useEffect(() => {
    if (selectedIndex !== undefined) {
      setHighlightedIndex(selectedIndex)
    }
  }, [selectedIndex, items])

  // If there aren't enough draggable items, return simple view
  if (notEnoughDraggableItems) {
    return (
      <View style={[styles.container, flexDirection === 'column' ? styles.column : styles.row]}>
        {nonDraggableStartComponent && <View style={styles.nonDraggableItem}>{nonDraggableStartComponent}</View>}
        {children}
        {nonDraggableEndComponent && <View style={styles.nonDraggableItem}>{nonDraggableEndComponent}</View>}
      </View>
    )
  }

  const renderDraggableItem = (child: React.ReactNode, index: number) => {
    const childElement = child as React.ReactElement
    const { className = '' } = childElement.props || {}

    // Determine if the element is non-draggable
    if (className && className.includes('non-draggable')) {
      return (
        <View key={index} onLayout={event => handleItemLayout(index, event)}>
          {childElement}
        </View>
      )
    }

    const isDragging = draggingIndex === index
    const isDraggedOver = draggedOverIndex === index

    return (
      <PanGestureHandler
        key={index}
        onGestureEvent={gestureHandler}
        onHandlerStateChange={event => {
          if (event.nativeEvent.state === State.BEGAN) {
            dragItem.current = index
          }
        }}
        enabled={!isDragging} // Prevent multiple simultaneous drags
      >
        <Animated.View
          style={[
            styles.draggableItem,
            styles[`draggableItem${type}`],
            isDragging && styles.dragging,
            isDraggedOver && styles.draggingOver,
            type !== 'customForms' && styles.hasOutline,
            isDragging ? animatedStyle : {},
          ]}
          onLayout={event => handleItemLayout(index, event)}>
          <Pressable style={styles.childContainer} onPress={() => handlePress(index)}>
            {childElement}
          </Pressable>
        </Animated.View>
      </PanGestureHandler>
    )
  }

  return (
    <GestureHandlerRootView style={styles.gestureContainer}>
      <View
        style={[styles.container, styles[`container${type}`], flexDirection === 'column' ? styles.column : styles.row]}
        onLayout={handleContainerLayout}>
        {nonDraggableStartComponent && <View style={styles.nonDraggableItem}>{nonDraggableStartComponent}</View>}
        {children?.map((child, index) => renderDraggableItem(child, index))}
        {nonDraggableEndComponent && <View style={styles.nonDraggableItem}>{nonDraggableEndComponent}</View>}
      </View>
    </GestureHandlerRootView>
  )
}

const styles = StyleSheet.create({
  gestureContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  column: {
    flexDirection: 'column',
  },
  draggableItem: {
    position: 'relative',
    zIndex: 1,
  },
  draggableItemtiles: {
    margin: 4,
  },
  draggableItemlist: {
    marginVertical: 2,
  },
  draggableItemcustomForms: {
    margin: 2,
  },
  dragging: {
    zIndex: 1000,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  draggingOver: {
    backgroundColor: 'rgba(0, 123, 255, 0.1)',
    borderColor: '#007bff',
    borderWidth: 2,
    borderRadius: 4,
  },
  hasOutline: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
  },
  nonDraggableItem: {
    // Add specific styling for non-draggable items
  },
  childContainer: {
    flex: 1,
  },
  containertiles: {
    gap: 8,
  },
  containerlist: {
    gap: 4,
  },
  containercustomForms: {
    gap: 12,
  },
})
