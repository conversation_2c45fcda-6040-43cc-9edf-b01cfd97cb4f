import React, { useCallback } from 'react'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { FlatList, StyleSheet, TouchableOpacity } from 'react-native'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { useBottomSheet } from '@src/providers/BottomSheetProvider'
import { SeaCard, SeaCardBody } from '@src/components/_atoms/SeaCard/SeaCard'

type Section = {
  title: string
  key: string
  tag: string
}

interface SeaTableOfContentsProps {
  sections: Section[]
  onPress: (_s: Section) => void
}

export const SeaTableOfContents = ({ sections, onPress = () => {} }: SeaTableOfContentsProps) => {
  // Styling
  const { styles } = useStyles(styleSheet)
  const getStyleVariants = useCallback((sectionTag: Section['tag']) => {
    switch (sectionTag) {
      case 'h1':
        return {
          container: styles.sectionHeaderContainer,
          text: styles.sectionHeaderText,
        }
      case 'h2':
        return {
          container: styles.subSectionHeaderContainer,
          text: styles.subSectionHeaderText,
        }
      default:
        return {
          container: styles.sectionHeaderContainer,
          text: styles.sectionHeaderText,
        }
    }
  }, [])

  // Hooks
  const { show, hide } = useBottomSheet()
  const { isLargeDesktopWidth } = useDeviceWidth()

  const renderItem = useCallback(({ item: section, index }: { item: Section; index: number }) => {
    const { container: containerVariantStyle, text: textVariantStyle } = getStyleVariants(section.tag)
    return (
      <TouchableOpacity
        key={section.key}
        onPress={e => {
          onPress(section)
          hide()
        }}
        style={[styles.baseSectionContainer]}>
        <SeaTypography
          key={section.key}
          variant={'label'}
          containerStyle={StyleSheet.flatten([styles.baseSectionContainer, containerVariantStyle])}
          textStyle={textVariantStyle}>
          {section.title}
        </SeaTypography>
      </TouchableOpacity>
    )
  }, [])

  const renderTableOfContents = useCallback(() => {
    return (
      <SeaStack
        direction={'column'}
        align={'start'}
        width={'100%'}
        style={{
          flex: 1,
          height: '100%',
        }}>
        {/* Header Title */}
        <SeaTypography variant={'title'} containerStyle={{ marginBottom: 10 }}>
          Table of Contents
        </SeaTypography>
        <FlatList
          data={sections}
          renderItem={renderItem}
          contentContainerStyle={styles.flatListContainer}
          style={styles.flatListStyle}
          scrollEnabled={true}
          showsVerticalScrollIndicator={true}
          nestedScrollEnabled={true}
        />
      </SeaStack>
    )
  }, [sections])

  const handleOnClick = useCallback(() => show(renderTableOfContents(), ['50%', '80%']), [renderTableOfContents, show])

  return (
    <>
      {/** Desktop & Tablet view */}
      {isLargeDesktopWidth && (
        <SeaStack direction={'column'} align={'start'} width={'100%'} style={styles.desktopContainer}>
          {renderTableOfContents()}
        </SeaStack>
      )}

      {/** Mobile view */}
      {!isLargeDesktopWidth && (
        <>
          <SeaStack direction={'column'} align={'start'} width={'100%'} style={styles.mobileContainer}>
            {/* Header Title */}
            <SeaCard style={{ width: '100%' }}>
              <TouchableOpacity onPress={handleOnClick}>
                <SeaCardBody style={{ width: '100%', padding: 5 }}>
                  <SeaStack direction={'column'} width={'100%'}>
                    <SeaTypography
                      variant={'title'}
                      textStyle={{ marginBottom: 0 }}
                      containerStyle={{ marginBottom: 0 }}>
                      Table of Contents
                    </SeaTypography>
                    <SeaTypography variant={'label'}>{'Click to view'}</SeaTypography>
                  </SeaStack>
                </SeaCardBody>
              </TouchableOpacity>
            </SeaCard>
          </SeaStack>
        </>
      )}
    </>
  )
}

const styleSheet = createStyleSheet(theme => ({
  desktopContainer: {
    width: '100%',
    flex: 1,
    paddingVertical: 10,
    paddingLeft: 10,
    paddingRight: 30,
    height: '100%',
  },
  mobileContainer: {
    flex: 1,
    width: '100%',
    paddingBottom: 10,
  },
  baseSectionContainer: {
    width: '100%',
  },
  flatListContainer: {
    flex: 1,
    width: '100%',
    paddingBottom: 20,
  },
  flatListStyle: {
    width: '100%',
    flex: 1,
  },
  sectionHeaderContainer: {
    backgroundColor: theme.colors.lightGrey,
    paddingHorizontal: 15,
    borderRadius: 20,
    paddingVertical: 8,
  },
  sectionHeaderText: {
    fontWeight: 500,
  },
  subSectionHeaderContainer: {
    paddingLeft: 10,
    paddingRight: 15,
    paddingVertical: 8,
    marginLeft: 20,
    borderLeftWidth: 2,
    borderColor: theme.colors.darkBorderColor,
    marginBottom: 0,
  },
  subSectionHeaderText: {
    fontWeight: 400,
  },
}))
