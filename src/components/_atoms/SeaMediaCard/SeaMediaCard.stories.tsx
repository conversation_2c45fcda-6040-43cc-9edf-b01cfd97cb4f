import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import { SeaMediaCard } from './SeaMediaCard'
import { SeaButton, SeaButtonVariant } from '../SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'

const meta = {
  title: 'Components/Atoms/SeaMediaCard',
  component: SeaMediaCard,
  argTypes: {},
  args: {
    title: 'Media Card Title',
    subTitle: 'Media Card Subtitle',
    file: ['https://example.com/file1.jpg'],
    actionButtons: [<SeaDownloadButton key={'download'} onPress={() => alert('Download button clicked')} />],
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#505050',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaMediaCard>

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {
  args: {
    title: 'Media Card Title',
    subTitle: 'Media Card Subtitle',
    file: ['https://example.com/file1.jpg'],
    actionButtons: [<SeaDownloadButton key={'download'} onPress={() => alert('Download button clicked')} />],
  },
}
