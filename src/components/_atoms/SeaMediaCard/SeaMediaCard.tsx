import { View, Text, ViewStyle, StyleSheet } from 'react-native'
import React from 'react'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '../SeaTypography/SeaTypography'
import SeaFileImage from '../SeaFileImage/SeaFileImage'
import { SeaStack } from '../SeaStack/SeaStack'
import { SeaButton, SeaButtonVariant } from '../SeaButton/SeaButton'
import { theme } from '@src/theme'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { truncateText } from '@src/lib/util'

export type MediaCardFile = {
  title: string
  subTitle?: string
  file: string[]
  actionButtons?:
    | [] // 0 buttons
    | [React.ReactElement<typeof SeaButton>] // 1 button
    | [React.ReactElement<typeof SeaButton>, React.ReactElement<typeof SeaButton>] // 2 buttons
    | [React.ReactElement<typeof SeaButton>, React.ReactElement<typeof SeaButton>, React.ReactElement<typeof SeaButton>] // 3 buttons

  style?: ViewStyle
}
interface SeaMediaCardProps extends MediaCardFile {}

export function SeaMediaCard({ title, file, subTitle, actionButtons, style }: SeaMediaCardProps) {
  const { styles } = useStyles(styleSheet)
  const { isMobileWidth } = useDeviceWidth()
  return (
    <SeaStack direction="row" gap={8} style={StyleSheet.flatten([styles.filesContainer, style])} justify="between">
      <SeaStack direction="row" gap={8} align="center" justify="start" style={{ flex: 1 }}>
        <View>
          <SeaFileImage files={file} size="tiny" showOthers />
        </View>
        <SeaStack direction="column" justify="center" align="start" gap={4} style={{ flex: 1 }}>
          <SeaTypography
            variant="body"
            textStyle={styles.title}
            fontWeight="regular"
            containerStyle={{ flexShrink: 1 }}>
            {title}
          </SeaTypography>
          {subTitle && (
            <SeaTypography
              variant="body"
              textStyle={styles.subTitle}
              fontWeight="regular"
              containerStyle={{ flexShrink: 1 }}>
              {subTitle}
            </SeaTypography>
          )}
        </SeaStack>
      </SeaStack>
      {actionButtons && (
        <SeaStack direction="row" gap={10}>
          {actionButtons}
        </SeaStack>
      )}
    </SeaStack>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
    flex: 1,
  },
  title: {
    color: theme.colors.black,
  },
  subTitle: {
    fontSize: 12,
    color: '#616367',
  },
  filesContainer: {
    width: '100%',
    backgroundColor: theme.colors.borderColor,
    padding: 8,
    borderRadius: 8,
  },
}))
