import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

const meta = {
  title: 'Components/Atoms/SeaTypography',
  component: SeaTypography,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaTypography>

export default meta

type Story = StoryObj<typeof meta>

export const Title: Story = {
  args: {
    variant: 'title',
    children: 'Example Title Text',
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}

export const Label: Story = {
  args: {
    variant: 'label',
    children: 'Example Label Text',
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}

export const Overline: Story = {
  args: {
    variant: 'overline',
    children: 'Example Overline Text',
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}

export const Subtitle: Story = {
  args: {
    variant: 'subtitle',
    children: 'Example Subtitle Text',
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
