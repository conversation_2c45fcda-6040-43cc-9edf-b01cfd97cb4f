import React from 'react'
import { ColorValue, StyleSheet, Text, TextProps, TextStyle, View, ViewStyle } from 'react-native'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { isNative } from '@src/lib/device'
import { fontFamily } from '@src/theme/typography'

type TypographyVariant =
  | 'title'
  | 'subtitle'
  | 'heading'
  | 'cardTitle'
  | 'body'
  | 'label'
  | 'caption'
  | 'overline'
  | 'input'
  | 'link'
  | 'value'

interface SeaTypographyProps extends Omit<TextProps, 'style'> {
  variant: TypographyVariant
  children: React.ReactNode
  color?: ColorValue
  containerStyle?: ViewStyle
  textStyle?: TextStyle
  fontWeight?: 'regular' | 'semiBold' | 'bold'
}

export const SeaTypography = ({
  variant,
  children,
  color,
  containerStyle,
  textStyle,
  fontWeight,
  ...rest
}: SeaTypographyProps) => {
  // Styling
  const { styles } = useStyles(styleSheet)

  // Variant Styling
  const { container: containerVariantStyle, text: textVariantStyle } = getStyleVariants(variant, styles)

  const combinedTextStyle = [
    styles.textBase,
    textVariantStyle,
    textStyle,
    styles[
      variant === 'title' ? (`title${fontWeight ?? 'regular'}` as keyof typeof styles) : (fontWeight ?? 'regular')
    ],
    color ? { color: color } : {},
  ]

  const flattenedTextStyle = StyleSheet.flatten<TextStyle>(combinedTextStyle)

  /**
   * This is necessary to handle bold for Native Mobile.
   * Only for the current `body fontFamily`, switch it to use the `bold body fontFamily` if necessary
   */
  const isBold = isNative && isBodyFontFamily(flattenedTextStyle) && isFontBold(flattenedTextStyle)

  return (
    <View style={[styles.containerBase, containerVariantStyle, containerStyle]}>
      <Text {...rest} style={[combinedTextStyle, isBold ? getFontWeightStyle(flattenedTextStyle) : undefined]}>
        {children}
      </Text>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  containerBase: {},
  textBase: {
    fontSize: 14,
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    color: theme.colors.text.label,
  },
  containerTitle: { marginBottom: 8 },
  textTitle: {
    color: theme.colors.text.default,
    fontFamily: theme.typography.fontFamily.TITLE_SEMI_BOLD_FONT,
    fontSize: 24,
    letterSpacing: 0.5,
    marginBottom: 16,
  },
  cardTitle: {
    color: theme.colors.text.default,
    fontFamily: theme.typography.fontFamily.TITLE_SEMI_BOLD_FONT,
    fontSize: 20,
    letterSpacing: 0.5,
    marginBottom: 8,
  },
  containerSubtitle: {},
  textSubtitle: {
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 16,
    fontWeight: 500,
  },
  containerLabel: {
    marginBottom: 4,
  },
  textLabel: {
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 12,
    fontWeight: 'bold',
  },
  containerValue: {
    marginBottom: 4,
  },
  textValue: {
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 14,
    fontWeight: 400,
    color: theme.colors.text.labelValue,
  },
  containerOverline: {
    marginBottom: 4,
  },
  textOverline: {
    textTransform: 'uppercase',
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 14,
    fontWeight: 600,
    letterSpacing: 0.8,
  },
  containerInput: {},
  textInput: {
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 14,
    fontWeight: '400',
    width: '100%',
    color: theme.colors.text.primary,
  },
  containerLink: {
    marginBottom: 4,
  },
  textLink: {
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    marginRight: 8,
    letterSpacing: 0.15,
    fontWeight: '400',
    width: '100%',
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },

  regular: {},
  semiBold: {
    fontWeight: 500,
  },
  bold: {
    fontWeight: 700,
  },
  titleregular: {},
  titlebold: {
    fontFamily: theme.typography.fontFamily.TITLE_BOLD_FONT,
  },
  titllesemiBold: {
    fontFamily: theme.typography.fontFamily.TITLE_SEMI_BOLD_FONT,
  },
}))

function getStyleVariants(variant: TypographyVariant, styles: any) {
  switch (variant) {
    case 'title':
      return { container: styles.containerTitle, text: styles.textTitle }
    case 'subtitle':
      return { container: styles.containerSubtitle, text: styles.textSubtitle }
    case 'heading':
      return { container: styles.containerBase, text: styles.textBase }
    case 'cardTitle':
      return { container: styles.containerBase, text: styles.cardTitle }
    case 'label':
      return { container: styles.containerLabel, text: styles.textLabel }
    case 'value':
      return { container: styles.containerValue, text: styles.textValue }
    case 'body':
      return { container: styles.containerBase, text: styles.textBase }
    case 'caption':
      return { container: styles.containerBase, text: styles.textBase }
    case 'overline':
      return { container: styles.containerOverline, text: styles.textOverline }
    case 'link':
      return { container: styles.containerLink, text: styles.textLink }
    case 'input':
      return {
        container: styles.containerInput,
        text: styles.textInput,
      }
    default:
      return { container: styles.containerBase, text: styles.textBase }
  }
}

const getFontWeightStyle = <T extends TextStyle>(textStyle: T): TextStyle | undefined => {
  const getFontWeight = (styleFontWeight: T['fontWeight']): number | undefined => {
    if (styleFontWeight === 'bold') return 700
    if (styleFontWeight === 'regular') return 400
    if (styleFontWeight === 'semibold') return 600
    if (styleFontWeight && typeof styleFontWeight === 'number') return textStyle.fontWeight
    if (styleFontWeight && typeof styleFontWeight === 'string') return parseInt(textStyle.fontWeight)
    return undefined
  }

  if (textStyle?.fontWeight) {
    switch (getFontWeight(textStyle.fontWeight)) {
      case 100:
        return { fontFamily: fontFamily.BODY_100_FONT }
      case 200:
        return { fontFamily: fontFamily.BODY_200_FONT }
      case 300:
        return { fontFamily: fontFamily.BODY_300_FONT }
      case 400:
        return { fontFamily: fontFamily.BODY_400_FONT }
      case 500:
        return { fontFamily: fontFamily.BODY_500_FONT }
      case 600:
        return { fontFamily: fontFamily.BODY_600_FONT }
      case 700:
        return { fontFamily: fontFamily.BODY_700_FONT }
      case 800:
        return { fontFamily: fontFamily.BODY_800_FONT }
      case 900:
        return { fontFamily: fontFamily.BODY_900_FONT }
      default:
        return undefined
    }
  }
  return undefined
}

const isFontBold = <T extends TextStyle>(textStyle: T): boolean => {
  if (
    textStyle?.fontWeight === 'bold' ||
    (typeof textStyle?.fontWeight === 'number' && textStyle.fontWeight > 400) ||
    (typeof textStyle?.fontWeight === 'string' && parseInt(textStyle.fontWeight) > 400)
  ) {
    return true
  }
  return false
}

const isBodyFontFamily = <T extends TextStyle>(textStyle: T): boolean => {
  return textStyle?.fontFamily === fontFamily.BODY_FONT
}
