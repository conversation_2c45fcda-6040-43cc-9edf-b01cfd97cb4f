import type { Meta, StoryObj } from '@storybook/react'
import React, { useState } from 'react'
import { View, Text } from 'react-native'
import { SeaDrawer } from './SeaDrawer'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'

const meta = {
  title: 'Components/Atoms/SeaDrawer',
  component: SeaDrawer,
  argTypes: {},
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#FFF',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaDrawer>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => {
    const [visible, setVisible] = useState(false)
    return (
      <>
        <SeaButton onPress={() => setVisible(true)} label={'Open Drawer'} />
        <SeaDrawer
          title={args.title}
          visible={visible}
          onClose={() => {
            args.onClose()
            setVisible(false)
          }}
          primaryAction={() => {
            args.primaryAction()
            setVisible(false)
          }}
          primaryActionLabel={args.primaryActionLabel}
          primaryActionIcon={args.primaryActionIcon}
          secondaryAction={() => {
            args.secondaryAction?.()
            setVisible(false)
          }}
          secondaryActionLabel={args.secondaryActionLabel}
          secondaryActionIcon={args.secondaryActionIcon}>
          <Text>Hello from SeaDrawer!</Text>
        </SeaDrawer>
      </>
    )
  },
  args: {
    title: 'Safety Check Item',
    visible: false,
    onClose: () => alert('Close called'),
    primaryAction: () => {},
    primaryActionLabel: 'Done',
    primaryActionIcon: 'check',
    secondaryAction: () => {},
    secondaryActionLabel: 'Cancel',
    secondaryActionIcon: 'close',
    children: <Text>Hello from the drawer</Text>,
  },
}
