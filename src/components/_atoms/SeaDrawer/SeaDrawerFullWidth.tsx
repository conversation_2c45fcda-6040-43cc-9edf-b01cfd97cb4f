import React, { ReactElement, useEffect, useState } from 'react'
import { Modal, Pressable, SafeAreaView, ScrollView, useWindowDimensions, View, ViewStyle } from 'react-native'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import Animated, { Easing, runOnJS, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { theme } from '@src/theme'
import { SeaDrawer, SeaDrawerProps } from '@src/components/_atoms/SeaDrawer/SeaDrawer'

interface SeaDrawerFullWidthProps extends SeaDrawerProps {}

export const SeaDrawerFullWidth = (props: SeaDrawerFullWidthProps) => {
  return <SeaDrawer {...props} isFullWidth={true} isScrollable={false} />
}
