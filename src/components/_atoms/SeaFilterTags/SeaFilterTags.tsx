import React from 'react'
import { StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { colors } from '@src/theme/colors'
import { fontFamily } from '@src/theme/typography'
import { theme } from '@src/theme'

type SeaFilterTagsProperty = {
  isActive: boolean
  count?: number
}

export type SeaFilterTagsValue = {
  all: SeaFilterTagsProperty
  overdue: SeaFilterTagsProperty
  upcoming: SeaFilterTagsProperty
  critical: SeaFilterTagsProperty
}

interface SeaFilterTagsProps {
  value: Partial<SeaFilterTagsValue>
  onChange: (value: Partial<SeaFilterTagsValue>) => void
  style?: ViewStyle
}

export const SeaFilterTags: React.FC<SeaFilterTagsProps> = ({ value, onChange, style }) => {
  const handleToggle = (filter: keyof SeaFilterTagsValue) => {
    if (filter === 'all') {
      if (value.all?.isActive) return // Prevent un-toggling "All"

      onChange({
        all: { ...value.all, isActive: true },
        ...('overdue' in value && {
          overdue: { ...value.overdue, isActive: false },
        }),
        ...('upcoming' in value && {
          upcoming: { ...value.upcoming, isActive: false },
        }),
        ...('critical' in value && {
          critical: { ...value.critical, isActive: false },
        }),
      })
      return
    }

    const newValue = {
      ...value,
      [filter]: { ...value[filter], isActive: !value[filter]?.isActive }, // Toggle the selected filter
      all: { ...value.all, isActive: false }, // Deselect "All" when toggling individual filters
    }

    // If all other filters are off, enable "All"
    if (!newValue.overdue?.isActive && !newValue.upcoming?.isActive && !newValue.critical?.isActive) {
      newValue.all.isActive = true
    }

    onChange(newValue)
  }

  return (
    <SeaStack direction={'row'} justify={'start'} gap={8} style={style}>
      {'all' in value && (
        <SeaFilterTag
          onToggle={() => handleToggle('all')}
          label={'All'}
          enabled={value.all?.isActive ?? false}
          count={value.all?.count}
        />
      )}

      {'overdue' in value && (
        <SeaFilterTag
          onToggle={() => handleToggle('overdue')}
          label={'Overdue'}
          enabled={value.overdue?.isActive ?? false}
          count={value.overdue?.count}
        />
      )}

      {'upcoming' in value && (
        <SeaFilterTag
          onToggle={() => handleToggle('upcoming')}
          label={'Upcoming'}
          enabled={value.upcoming?.isActive ?? false}
          count={value.upcoming?.count}
        />
      )}

      {'critical' in value && (
        <SeaFilterTag
          onToggle={() => handleToggle('critical')}
          label={'Critical'}
          enabled={value.critical?.isActive ?? false}
          count={value.critical?.count}
        />
      )}
    </SeaStack>
  )
}

interface SeaFilterTagProps {
  enabled: boolean
  label: string
  onToggle: () => void
  count?: number
  showCount?: boolean
}

export const SeaFilterTag: React.FC<SeaFilterTagProps> = ({ enabled, label, onToggle, count, showCount = true }) => {
  return (
    <TouchableOpacity onPress={onToggle} style={[tagStyles.container, enabled && tagStyles.containerEnabled]}>
      <SeaStack direction={'row'} justify={'between'} align="center" gap={5}>
        <Text style={[tagStyles.labelText, enabled && tagStyles.labelTextEnabled]}>{label}</Text>

        {showCount && <Text style={[tagStyles.count, enabled && tagStyles.countEnabled]}>{count ?? '0'}</Text>}
      </SeaStack>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {},
})

const tagStyles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.white,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    minWidth: 60,
    paddingHorizontal: 18,
    paddingVertical: 10,
    borderRadius: 24,
    height: 40,
  },
  containerEnabled: {
    backgroundColor: '#c4deff',
    borderColor: 'transparent',
  },
  labelText: {
    fontFamily: fontFamily.BODY_FONT,
    fontSize: 12,
    color: colors.text.primary,
  },
  labelTextEnabled: {
    color: '#2883f8',
    fontWeight: '600',
  },
  count: {
    fontFamily: fontFamily.BODY_FONT,
    fontWeight: '300',
    color: theme.colors.text.placeholder,
    fontSize: 12,
  },
  countEnabled: {
    fontWeight: '400',
  },
})
