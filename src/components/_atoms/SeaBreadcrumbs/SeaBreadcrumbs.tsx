import { View, Text, Pressable, StyleSheet } from 'react-native'
import React, { useMemo } from 'react'
import { useRouter, useSegments } from 'expo-router'
import { theme } from '@src/theme'
import { Routes, ROUTES_CONFIG } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaStack } from '../SeaStack/SeaStack'
import { fontFamily } from '@src/theme/typography'
import { SeaIcon } from '../SeaIcon/SeaIcon'
import { useDeviceWidth } from '@src/hooks/useDevice'

type SegmentData = {
  title?: string
  path?: string
}
const segmentData: Record<string, SegmentData> = Object.values(Routes).reduce(
  (acc: Record<string, SegmentData>, key) => {
    const routeConfig = ROUTES_CONFIG[key as keyof typeof ROUTES_CONFIG]
    return {
      ...acc,
      [key]: { title: routeConfig?.title ?? '', path: routeConfig.path },
    }
  },
  {}
)

export function SeaBreadcrumbs({ hidePath = false, onBackPress }: { hidePath?: boolean; onBackPress?: () => void }) {
  const { isDesktopWidth, isTabletWidth } = useDeviceWidth()

  const segments = useSegments()
  const router = useRouter()
  const vessel = sharedState.vessel.use()

  const links = useMemo(() => {
    // For mobile and desktop devices, don't show the breadcrumbs
    if ((!isDesktopWidth && !isTabletWidth) || hidePath) {
      return []
    }

    const urlSegments = segments
      .filter(name => !/\(.*?\)/.test(name))
      .map(segment => {
        const isVesselSegment = segment === Routes.VESSEL

        return {
          ...segmentData[segment],
          isVesselName: isVesselSegment,
        }
      })

    if (urlSegments.length <= 2 || isDesktopWidth) return urlSegments

    return [
      urlSegments[0],
      {
        title: '...',
      },
      ...urlSegments.slice(-1),
    ]
  }, [vessel, segments, isDesktopWidth])

  return (
    <SeaStack direction={'row'} justify="start" align="center" gap={5}>
      {/* TODO: [SF-307] Make sure the back button is not visible if there is nothing to go back to */}
      {router.canGoBack() && (
        <Pressable onPress={() => (onBackPress ? onBackPress() : router.back())} style={{ marginRight: 10 }}>
          <View style={styles.backButton}>
            <SeaIcon icon="arrow_back" size={20} color={theme.colors.black} />
          </View>
        </Pressable>
      )}
      {links.map((link, index) => {
        const isLastLink = index === links.length - 1
        const isVesselName = 'isVesselName' in link && link.isVesselName

        return (
          <SeaStack key={link.title} direction="row" align="center" justify="center" gap={5}>
            <BreadCrumbLink
              title={isVesselName ? vessel?.name : link.title}
              path={'path' in link ? link.path : undefined}
              isVesselName={isVesselName}
              isActive={isLastLink}
            />

            {!isLastLink && (
              <SeaIcon icon="chevron_right" color={theme.colors.text.placeholder} size={16} style={{ marginTop: 2 }} />
            )}
          </SeaStack>
        )
      })}
    </SeaStack>
  )
}

const BreadCrumbLink = ({
  title,
  path,
  isVesselName,
  isActive,
}: SegmentData & { isVesselName?: boolean; isActive?: boolean }) => {
  const router = useRouter()
  const vessel = sharedState.vessel.use()

  const handlePress = () => {
    if (isVesselName) {
      router.navigate({
        pathname: `/vessel/${Routes.VESSEL_DASHBOARD}`,
        params: {
          vesselId: vessel?.id,
        },
      })
    } else if (path) {
      router.navigate({
        pathname: path,
        params: {
          vesselId: vessel?.id,
        },
      })
    }
  }

  return (
    <Pressable onPress={handlePress}>
      <Text style={[styles.link, isActive ? styles.active : null]}>{title}</Text>
    </Pressable>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 40,
    backgroundColor: theme.colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  link: {
    color: theme.colors.text.placeholder,
    fontSize: 14,
    fontFamily: fontFamily.BODY_FONT,
  },
  active: {
    color: theme.colors.black,
    fontWeight: 500,
  },
})
