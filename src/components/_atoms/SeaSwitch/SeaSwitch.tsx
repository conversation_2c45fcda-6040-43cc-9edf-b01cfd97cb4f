import React from 'react'
import { Pressable, StyleSheet } from 'react-native'
import { colors } from '@src/theme/colors'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import Animated, { useAnimatedStyle, useSharedValue, withTiming, runOnJS } from 'react-native-reanimated'

interface SeaSwitchProps {
  enabled: boolean
  onToggle: () => void
}

export const SeaSwitch: React.FC<SeaSwitchProps> = ({ enabled, onToggle }) => {
  const buttonTranslateX = useSharedValue(enabled ? 24 : 0) // Start position based on value
  const iconOpacity = useSharedValue(enabled ? 1 : 0) // For icon fade-in/fade-out
  const containerColor = useSharedValue(enabled ? colors.primary : colors.white) // For container background color
  const borderColor = useSharedValue(enabled ? colors.primary : colors.white) // For container border color

  const handlePress = () => {
    if (enabled) {
      // Fade out the icon before moving the button
      iconOpacity.value = withTiming(0, { duration: 100 }, () => {
        runOnJS(onToggle)() // Trigger toggle after fade-out completes
      })
    } else {
      // Directly trigger the toggle when enabling
      onToggle()
    }
  }

  // Update the animated values when `enabled` changes
  React.useEffect(() => {
    if (enabled) {
      // Move button and fade-in icon when enabling
      buttonTranslateX.value = withTiming(24, { duration: 200 })
      iconOpacity.value = withTiming(1, { duration: 200 })

      // Update container background and border colors
      containerColor.value = withTiming(colors.primary, { duration: 200 })
      borderColor.value = withTiming(colors.primary, { duration: 200 })
    } else {
      // Move button to "off" position when disabling
      buttonTranslateX.value = withTiming(0, { duration: 200 })

      // Update container background and border colors
      containerColor.value = withTiming(colors.white, { duration: 200 })
      borderColor.value = withTiming(colors.white, { duration: 200 })
    }
  }, [enabled])

  // Animated style for the container
  const animatedContainerStyle = useAnimatedStyle(() => ({
    backgroundColor: containerColor.value,
    borderColor: borderColor.value,
  }))

  // Animated style for the button
  const animatedButtonStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: buttonTranslateX.value }],
    backgroundColor: enabled ? colors.white : '#CACBCE', // Dynamic background color
  }))

  // Animated style for the icon
  const animatedIconStyle = useAnimatedStyle(() => ({
    opacity: iconOpacity.value,
  }))

  return (
    <Animated.View style={[styles.container, animatedContainerStyle]}>
      <Pressable onPress={handlePress}>
        <Animated.View style={[styles.button, animatedButtonStyle]}>
          <Animated.View style={animatedIconStyle}>
            <SeaIcon icon={'check'} color={colors.primary} />
          </Animated.View>
        </Animated.View>
      </Pressable>
    </Animated.View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: 64,
    height: 40,
    backgroundColor: colors.white,
    borderRadius: 40,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: 2,
    borderWidth: 2,
    borderColor: colors.white,
  },
  button: {
    height: 32,
    width: 32,
    borderRadius: 200,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
})
