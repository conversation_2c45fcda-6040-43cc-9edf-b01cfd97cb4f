import type { Meta, StoryObj } from '@storybook/react'
import React, { useEffect, useState } from 'react'
import { View } from 'react-native'
import { SeaSwitch } from '@src/components/_atoms/SeaSwitch/SeaSwitch'

const meta = {
  title: 'Components/Atoms/SeaSwitch',
  component: SeaSwitch,
  argTypes: {},
  args: {},
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaSwitch>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => {
    const [value, setValue] = useState(true)

    useEffect(() => {
      setValue(args.enabled)
    }, [args.enabled])

    return <SeaSwitch enabled={value} onToggle={() => setValue(!value)} />
  },
  args: {
    enabled: true,
    onToggle: () => {},
  },
  parameters: {
    backgrounds: {
      default: 'Grey',
    },
  },
}
