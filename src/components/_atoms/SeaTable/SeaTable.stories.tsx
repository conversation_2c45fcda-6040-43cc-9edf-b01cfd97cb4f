import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import React from 'react'
import { Dimensions, View } from 'react-native'
import { SeaTable, SeaTableColumn, SeaTableRow } from './SeaTable'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { SeaStatusType } from '@src/types/Common'

type ExampleItem = {
  title: string
  system: string
  task: string
  tags: string[]
  interval: string
  nextDue: string
  status: SeaStatusType
  statusText: string
  critical: boolean
}

const exampleData: ExampleItem[] = [
  {
    title: 'Life Raft',
    task: 'Annual Check',
    system: 'Survival',
    tags: [],
    interval: 'Monthly',
    nextDue: '1st Jan 25',
    statusText: 'FAULT',
    status: SeaStatusType.Critical,
    critical: true,
  },
  {
    title: 'Port Engine',
    task: 'Monthly Check',
    system: 'Propulsion',
    tags: [],
    interval: 'Monthly',
    nextDue: '12th Jan 25',
    statusText: '00 DAYS OD',
    status: SeaStatusType.Error,
    critical: true,
  },
  {
    title: 'Starboard Engine',
    task: 'Monthly Check',
    system: 'Propulsion',
    tags: [],
    interval: 'Monthly',
    nextDue: '12th Jan 25',
    statusText: '00 DAYS OD',
    status: SeaStatusType.Error,
    critical: true,
  },
  {
    title: 'Port Generator',
    task: 'Monthly Check',
    system: 'Power',
    tags: [],
    interval: 'Monthly',
    nextDue: '12th Mar 25',
    statusText: '00 DAYS',
    status: SeaStatusType.Warning,
    critical: true,
  },
  {
    title: 'Starboard Generator',
    task: 'Monthly Check',
    system: 'Power',
    tags: [],
    interval: 'Monthly',
    nextDue: '12th Mar 25',
    statusText: '00 DAYS',
    status: SeaStatusType.Warning,
    critical: true,
  },
  {
    title: 'Life Rings',
    task: 'Monthly Check',
    system: 'Survival',
    tags: [],
    interval: 'Monthly',
    nextDue: '10th Jan 25',
    statusText: 'DUE TODAY',
    status: SeaStatusType.Attention,
    critical: true,
  },
  {
    title: 'Life Jackets',
    task: 'Monthly Check',
    system: 'Survival',
    tags: [],
    interval: 'Monthly',
    nextDue: '10th Jan 25',
    statusText: 'DUE TODAY',
    status: SeaStatusType.Attention,
    critical: true,
  },
  {
    title: 'Sea-Flux updates',
    task: 'Weekly Check',
    system: 'Compliance',
    tags: [],
    interval: 'Weekly',
    nextDue: '14th Jan 25',
    statusText: '00 DAYS',
    status: SeaStatusType.Ok,
    critical: true,
  },
]

const { width } = Dimensions.get('window')

const seaTableRows = exampleData.map(row => ({
  data: row,
  onPress: x => alert(`Pressed ${x.task}`),
  status: row.status,
})) as SeaTableRow<ExampleItem>[]
const seaTableColumns = [
  {
    label: '',
    // widthPercentage: 60,
    value: x => '',
    style: { fontWeight: 'bold' },
    isHidden: width < 550,
  },
  {
    label: 'Title',
    // widthPercentage: 170,
    value: x => x.title,
    style: { fontWeight: 'bold' },
  },
  {
    label: 'Task',
    // widthPercentage: 170,
    value: x => x.task,
  },
  { label: 'Tags', value: x => x.tags, isHidden: width < 550 },
  { label: 'Interval', value: x => x.interval },
  { label: 'Next Due', value: x => x.nextDue },
  {
    label: 'Status',
    value: x => x.status,
    render: x => <SeaStatusPill variant={x.status} label={x.statusText} />,
  },
  { label: 'Critical', value: x => x.critical },
] as SeaTableColumn<ExampleItem>[]
const meta = {
  title: 'Components/Atoms/SeaTable',
  component: SeaTable<ExampleItem>,
  argTypes: {},
  args: {
    columns: seaTableColumns,
    rows: seaTableRows,
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#FFF',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaTable<ExampleItem>>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {}
export const WithGrouping: Story = {
  args: {
    categoryKey: 'system',
  },
}
export const Compact: Story = {
  args: {
    compactViewBreakpoint: 2000,
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#FFF',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: 549,
          }}>
          <Story />
        </View>
      )
    },
  ],
}
export const CompactWithGrouping: Story = {
  args: {
    compactViewBreakpoint: 2000,
    categoryKey: 'system',
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#FFF',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: 549,
          }}>
          <Story />
        </View>
      )
    },
  ],
}
