import { View } from 'react-native'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import React from 'react'
import { useDeviceWidth } from '@src/hooks/useDevice'

export interface SeaTableImageProps {
  files?: string[]
}

export const SeaTableImage = ({ files }: SeaTableImageProps) => {
  const { isMobileWidth } = useDeviceWidth()

  const dimension = isMobileWidth ? 48 : 48
  return (
    <View style={{ borderRadius: 12, height: dimension, width: dimension }}>
      <SeaFileImage files={files} style={{ height: dimension, width: dimension }} mustGetDefaultImage={false} />
    </View>
  )
}
