import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react'
import SignatureCanvas from 'react-signature-canvas'
import { colors } from '@src/theme/colors'
import { SignatureRef } from '@src/components/_atoms/SeaSignatureInput/SeaSignatureInput'

interface SeaSignatureWebProps {
  signature: string
  onChange: (signature: string) => void
  width: number
  height: number
}

export const SeaSignatureWeb = forwardRef<SignatureRef, SeaSignatureWebProps>(
  ({ signature, onChange, width, height }, ref) => {
    const canvasRef = useRef<SignatureCanvas | null>(null)
    const signatureData = useRef('')

    useImperativeHandle(ref, () => ({
      clear: () => {
        canvasRef.current?.clear()
        signatureData.current = ''
      },
    }))

    useEffect(() => {
      console.log('Remount')
      if (!canvasRef.current) {
        return
      }

      canvasRef.current.fromDataURL(signature, {
        ratio: 1,
        width: 500,
        height: 200,
      })
    }, [signature])

    const handleChange = (event: MouseEvent) => {
      console.log('handle change called!')
      if (!canvasRef.current) return

      const signature = canvasRef.current.toDataURL('image/png')
      signatureData.current = signature

      console.log({ sig: signatureData.current })

      onChange(signature)
    }

    return (
      <SignatureCanvas
        ref={canvasRef}
        penColor={colors.primary} // TODO - Do we like this as the color?
        // penColor={colors.text.primary}
        // backgroundColor={"magenta"}
        onEnd={handleChange}
        canvasProps={{
          width: 500,
          height: 200,
          className: 'sea-sig-canvas',
        }}
      />
    )
  }
)
