import type { Meta, StoryObj } from '@storybook/react'
import React, { useState } from 'react'
import { View, Text } from 'react-native'
import { SeaSignatureInput } from './SeaSignatureInput'

const meta = {
  title: 'Components/Atoms/SeaSignatureInput',
  component: SeaSignatureInput,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <View style={{ width: 339 }}>
            <Story />
          </View>
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaSignatureInput>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => {
    const [signature, setSignature] = useState('')

    const onChange = (sig: string) => {
      setSignature(sig)
    }

    return (
      <View>
        <SeaSignatureInput width={500} onChange={onChange} label={args.label} />
        <View
          style={{
            marginTop: 80,
            borderWidth: 2,
            borderRadius: 4,
            borderColor: 'magenta',
            padding: 16,
          }}>
          <Text style={{ color: 'magenta' }}>DEBUG</Text>
          <Text>Signature:</Text>
          <Text>{signature.slice(0, 200)}...</Text>
        </View>
      </View>
    )
  },
  args: {
    onChange: img => {
      console.log({ img })
    },
    label: 'Sign Here',
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
