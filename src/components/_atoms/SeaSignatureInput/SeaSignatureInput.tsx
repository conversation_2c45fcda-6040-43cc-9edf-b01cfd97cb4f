import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import { Platform, Pressable, StyleSheet, View, ViewStyle } from 'react-native'
import Signature from 'react-native-signature-canvas'
import SignatureCanvas from 'react-signature-canvas'
import { useBounceOnFocus } from '@src/components/_atoms/_animations/useBounceOnFocus'
import Animated from 'react-native-reanimated'
import { colors } from '@src/theme/colors'
import { useFadeLabel } from '@src/components/_atoms/_animations/useFadeLabel'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import createAnimatedComponent = Animated.createAnimatedComponent
import { SeaSignatureWeb } from '@src/components/_atoms/SeaSignatureInput/SeaSignatureWeb'

const HEIGHT = 144
const WIDTH = 330

interface SeaSignatureInputProps {
  /* When the signature input is changed, a base64 PNG version of the signature is returned*/
  onChange: (base64Img: string) => void
  label?: string
  style?: ViewStyle
  width?: number
}

export const SeaSignatureInput: React.FC<SeaSignatureInputProps> = ({ onChange, label, width = WIDTH, style }) => {
  const [signature, setSignature] = useState('')
  const [hasValue, setHasValue] = useState(false)
  const webSigRef = useRef<SignatureRef | null>(null)

  const { animatedStyle: borderStyle, triggerFocus, triggerBlur } = useBounceOnFocus()
  const { animatedStyle: labelStyle } = useFadeLabel(hasValue)

  const onFocus = () => {
    triggerFocus()
  }
  const onBlur = () => {
    triggerBlur()
  }

  const handleChange = (signature: string) => {
    setSignature(signature)
    setHasValue(true)

    onChange(signature)
  }

  const clear = () => {
    if (webSigRef.current) webSigRef.current.clear()

    setHasValue(false)
    setSignature('')
  }

  const APressable = createAnimatedComponent(Pressable)

  const SignatureInput = Platform.select({
    web: (
      <SeaSignatureWeb
        ref={webSigRef}
        signature={signature}
        onChange={signature => handleChange(signature)}
        width={width}
        height={HEIGHT}
      />
    ),
    native: <SeaSignatureMobile width={width} />,
  })

  const showPlaceholder = !!label && !hasValue

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.labelContainer, labelStyle]}>
        <SeaTypography variant={'label'}>{label}</SeaTypography>
      </Animated.View>

      {showPlaceholder && (
        <View style={styles.placeholder}>
          <SeaTypography variant={'input'} color={colors.text.placeholder}>
            {label}
          </SeaTypography>
        </View>
      )}

      {hasValue && (
        <Pressable onPress={clear} style={[styles.clearButton, labelStyle]}>
          <SeaTypography variant={'label'}>Clear</SeaTypography>
        </Pressable>
      )}
      <APressable
        style={[styles.inputContainer, { width: WIDTH }, borderStyle, style]}
        onFocus={onFocus}
        onBlur={onBlur}>
        {SignatureInput}
      </APressable>
    </View>
  )
}

export interface SignatureRef {
  clear: () => void
}

// TODO - This will need work once Storybook is working on a mobile device
const SeaSignatureMobile = ({ width }: { width: number }) => {
  return <Signature />
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    // height: HEIGHT,
    width: WIDTH,
    // backgroundColor: "magenta",
  },
  labelContainer: {},
  label: {},
  clearButton: {
    position: 'absolute',
    top: 24,
    right: 8,
    zIndex: 1,
  },
  placeholder: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    zIndex: 1,
  },
  inputContainer: {
    height: HEIGHT,
    borderRadius: 12,
    borderWidth: 2,
    paddingVertical: 12,
    // paddingHorizontal: 16,
    width: '100%',
    borderColor: '#E0E0E0',
    backgroundColor: colors.white,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
})
