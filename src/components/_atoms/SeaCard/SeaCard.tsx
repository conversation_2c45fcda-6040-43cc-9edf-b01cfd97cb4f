import React, { ReactElement } from 'react'
import { View, ViewStyle } from 'react-native'
import SeaFileImage from '../SeaFileImage/SeaFileImage'
import { createStyleSheet, useStyles } from '@src/theme/styles'

type SeaCardThumbnailProps = {
  files: string[]
  children?: React.ReactNode
  onPress?: () => void
  style?: ViewStyle
}

export const SeaCardThumbnail: React.FC<SeaCardThumbnailProps> = ({ files, children, onPress, style }) => {
  const { styles } = useStyles(styleSheet)

  return (
    <View style={[styles.thumbnail, style]}>
      <SeaFileImage files={files} size="full" onPress={onPress} />
      {children}
    </View>
  )
}

type SeaCardBodyProps = {
  children:
    | ReactElement<typeof SeaCardHeader>
    | ReactElement<typeof SeaCardContent>
    | [ReactElement<typeof SeaCardHeader>, ReactElement<typeof SeaCardContent>]
  style?: ViewStyle
}

export const SeaCardBody: React.FC<SeaCardBodyProps> = ({ children, style }) => {
  const { styles } = useStyles(styleSheet)

  return <View style={[styles.body, style]}>{children}</View>
}

type SeaCardHeaderProps = {
  children: React.ReactNode
  style?: ViewStyle
}

export const SeaCardHeader: React.FC<SeaCardHeaderProps> = ({ children, style }) => {
  const { styles } = useStyles(styleSheet)

  return <View style={[styles.header, style]}>{children}</View>
}

type SeaCardContentProps = {
  children: React.ReactNode
  style?: ViewStyle
}
export const SeaCardContent: React.FC<SeaCardContentProps> = ({ children, style }) => {
  return <View style={[style]}>{children}</View>
}

interface SeaCardProps {
  children:
    | ReactElement<typeof SeaCardThumbnail>
    | ReactElement<typeof SeaCardBody>
    | [ReactElement<typeof SeaCardThumbnail>, ReactElement<typeof SeaCardBody>]
  style?: ViewStyle
}

export const SeaCard: React.FC<SeaCardProps> = ({ children, style }) => {
  const { styles } = useStyles(styleSheet)

  return <View style={[styles.container, style]}>{children}</View>
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    borderRadius: 12,
    width: '100%',
    overflow: 'hidden',
    backgroundColor: '#e9eaed',
  },
  thumbnail: {
    width: '100%',
    aspectRatio: 16 / 9,
    maxHeight: 300,
    position: 'relative',
  },
  body: {
    backgroundColor: theme.colors.white,
    padding: 20,
    flex: 1,
  },
  header: {
    paddingVertical: 5,
    borderBottomColor: theme.colors.darkBorderColor,
  },
}))
