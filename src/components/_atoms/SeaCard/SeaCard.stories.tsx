import * as React from 'react'
import { Text, View } from 'react-native'
import type { Meta, StoryObj } from '@storybook/react'
import { SeaCard, SeaCardBody, SeaCardContent, SeaCardHeader, SeaCardThumbnail } from './SeaCard'

const meta = {
  title: 'Components/Atoms/SeaCard',
  component: SeaCard,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#505050',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <View style={{ width: 400 }}>
            <Story />
          </View>
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaCard>

export default meta

type Story = StoryObj<typeof meta>

export const Card: Story = {
  args: {
    children: [
      <SeaCardThumbnail key="thumbnail" files={['23mxpgwuPHmcUg8gC6JuS_B1D2E21A-39E6-42A2-B80C-5382DE8B1554.jpeg']} />,
      <SeaCardBody key="body">
        <SeaCardHeader>
          <Text>Header</Text>
        </SeaCardHeader>
        <SeaCardContent>
          <Text>Content</Text>
        </SeaCardContent>
      </SeaCardBody>,
    ],
  },
}
