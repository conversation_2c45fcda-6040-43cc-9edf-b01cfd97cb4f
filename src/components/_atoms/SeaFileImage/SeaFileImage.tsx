import React, { useEffect, useState } from 'react'
import { Image, ImageBackground, StyleProp, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native'
import { SFDoc } from '@src/shared-state/CompanyDocuments/companyDocuments'
import { getImgSrcFromExt, getImgSrcPlaceholder, renderSrc } from '@src/lib/files'
import { getCachedImgSrcs } from '@src/shared-state/FileSyncSystem/filesToLoad'
import { revealImage } from '@src/lib/util'
import { SeaIcon } from '../SeaIcon/SeaIcon'
import { theme } from '@src/theme'

interface SeaFileImageProps {
  files?: string[]
  sfdoc?: SFDoc
  size?: 'tiny' | 'medium' | 'full'
  mode?: 'vessel' | 'customForms'
  showOthers?: boolean
  onPress?: () => void
  style?: StyleProp<ViewStyle>
  mustGetDefaultImage?: boolean
}

/**
 * SeaFileImage component
 *
 * TODO: This is an AI generated code added to make this work with react-native.
 * TODO: Need to review it at some point and make sure it is working as expected.
 *
 * @param files
 * @param sfdoc
 * @param size
 * @param mode
 * @param showOthers
 * @param onPress
 * @param style
 * @param mustGetDefaultImage
 * @constructor
 */
const SeaFileImage = ({
  files,
  sfdoc,
  size = 'tiny',
  mode,
  showOthers,
  onPress,
  style,
  mustGetDefaultImage = true,
}: SeaFileImageProps) => {
  const [srcs, setSrcs] = useState<string[]>()

  useEffect(() => {
    if (!files || files.length === 0) {
      setSrcs([getImgSrcPlaceholder(size)])
      return
    }

    let isActive = true
    const fileType = size === 'tiny' && mode !== 'customForms' ? 'T' : 'F'

    const loadImages = () => {
      getCachedImgSrcs(files, fileType, cachedSrcs => {
        const loadSrcs = async () => {
          const _srcs: string[] = await Promise.all(
            cachedSrcs.map((src, index) => renderSrc(files[index], src, getRenderSize(index), false))
          )

          if (isActive) setSrcs(_srcs)
        }
        loadSrcs()
      })
    }

    const getRenderSize = (index: number) => {
      if (size === 'tiny' && showOthers) return mode === 'customForms' ? 'medium' : size
      if (size === 'medium' && index > 0 && showOthers && mode !== 'vessel') return 'tiny'
      return size
    }

    loadImages()

    return () => {
      isActive = false
    }
  }, [files, size, mode, showOthers])

  if (!srcs) return null

  if ((files === undefined || files.length === 0) && sfdoc && Object.keys(sfdoc).length > 0) {
    return (
      <Image
        style={[styles.image, styles[size], mode ? styles[mode] : null, style]}
        source={{ uri: getImgSrcFromExt('sfdoc', size) }}
        onLoad={revealImage}
      />
    )
  }

  if ((files === undefined || files.length === 0) && size !== 'tiny') return null

  const onClick = (e: any, index = 0) => {
    if (onPress) return onPress()

    if (!files || files.length === 0 || mode === 'vessel') return
    e.stopPropagation()
    e.preventDefault()
  }

  const renderImages = () => {
    if (size === 'tiny' && showOthers && files && files.length > 1) {
      return (
        <View style={styles.imageContainer}>
          {files.map((file, index) => (
            <TouchableOpacity key={file} onPress={e => onClick(e, index)}>
              <Image
                style={[styles.image, styles.tiny, styles.other]}
                source={{ uri: srcs[index] }}
                onLoad={revealImage}
              />
            </TouchableOpacity>
          ))}
        </View>
      )
    }

    return (
      <View style={{ flex: 1 }}>
        <TouchableOpacity onPress={e => onClick(e, 0)} style={{ flex: 1 }}>
          <View
            style={{
              flex: 1,
              justifyContent: 'center', // Centers vertically
              alignItems: 'center', // Centers horizontally
            }}>
            {srcs[0].startsWith('/assets/file') ? (
              <>
                {mustGetDefaultImage ? (
                  <View style={styles.file}>
                    <SeaIcon icon="description" size={20} color={theme.colors.text.placeholder} fill={false} />
                  </View>
                ) : (
                  <>
                    <View
                      style={[
                        styles.filePlaceholder,
                        {
                          width: style?.width ?? 32,
                          height: style?.height ?? 32,
                        },
                      ]}
                    />
                  </>
                )}
              </>
            ) : (
              <ImageBackground
                style={[styles.image, styles[size], mode ? styles[mode] : null, style]}
                source={{ uri: srcs[0] }}
                onLoad={revealImage}
                resizeMode={'cover'}
                resizeMethod={'resize'}
              />
            )}
          </View>
        </TouchableOpacity>
        {size === 'medium' && files && files.length > 1 && showOthers && mode !== 'vessel' && (
          <View style={styles.additionalImagesContainer}>
            {files.slice(1).map((file, index) => (
              <TouchableOpacity key={file} onPress={e => onClick(e, index + 1)}>
                <Image
                  style={[styles.image, styles.tiny, styles.other]}
                  source={{ uri: srcs[index + 1] }}
                  onLoad={revealImage}
                />
              </TouchableOpacity>
            ))}
            {!showOthers && <Text>({files.length} images)</Text>}
          </View>
        )}
      </View>
    )
  }

  return renderImages()
}

const styles = StyleSheet.create({
  image: {
    // common styles for images
  },
  tiny: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
  },
  medium: {
    width: 80,
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
  },
  full: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  vessel: {
    // specific styles for vessel mode
  },
  customForms: {
    // specific styles for customForms mode
  },
  file: {
    // specific styles for file type
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 4,
  },
  filePlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
  },
  other: {
    margin: 6,
  },
  imageContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  additionalImagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    textAlign: 'right',
    padding: 4,
  },
})

export default SeaFileImage
