import React from 'react'
import { StyleSheet, Text, ViewStyle } from 'react-native'
import { TouchableOpacity } from 'react-native-gesture-handler'
import { SeaIcon, SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'

export interface SeaActionButtonProps {
  label?: string
  onPress?: () => void
  viewStyle?: any
  textStyle?: any
  iconOptions: SeaIconProps
}

export const SeaActionButton = ({ label, onPress, viewStyle, textStyle, iconOptions }: SeaActionButtonProps) => {
  return (
    <>
      {iconOptions?.icon && (
        <TouchableOpacity onPress={onPress} style={[styles.container, viewStyle]}>
          <SeaIcon color="#282828" style={[styles.label as ViewStyle]} {...iconOptions} />
          {label && <Text style={[styles.label, textStyle]}>{label}</Text>}
        </TouchableOpacity>
      )}
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white', // DEBUG
    height: 48,
    minWidth: 120,
    borderRadius: 12,
    alignItems: 'center',
    paddingHorizontal: 20,
    display: 'flex',
    flexDirection: 'row',
    gap: 8,
    justifyContent: 'flex-start',
    shadowOffset: { width: 0, height: 1 },
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
  },
  label: {
    fontFamily: 'sea-bold',
    fontSize: 14,
    lineHeight: 22,
    letterSpacing: 0.5,
    fontWeight: '700',
  },
  baseLabel: {
    color: '#282828',
  },
})
