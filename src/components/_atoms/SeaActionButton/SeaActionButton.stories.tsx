import { Text, View } from 'react-native'
import type { Meta, StoryObj } from '@storybook/react'
import { SeaActionButton } from './SeaActionButton'

const meta = {
  title: 'Components/Atoms/SeaActionButton',
  component: SeaActionButton,
  argTypes: {},
  args: {
    label: 'Press Me',
    onPress: () => alert('Button pressed!'),
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#505050',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaActionButton>

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {
  args: {
    iconOptions: {
      icon: 'directions_boat_filled',
      size: 24,
    },
  },
}
