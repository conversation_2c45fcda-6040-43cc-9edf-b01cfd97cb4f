import React from 'react'
import { colors } from '@src/theme/colors'
import { View, ViewProps } from 'react-native'
import { createStyleSheet, useStyles } from '@src/theme/styles'

interface SeaEmptyDividerProps extends ViewProps {}

export const SeaEmptyDivider = ({ style, ...props }: SeaEmptyDividerProps) => {
  const { styles } = useStyles(styleSheet)
  return <View style={[styles.container, style]} {...props} />
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
    height: 10,
    backgroundColor: colors.background.primary,
  },
}))
