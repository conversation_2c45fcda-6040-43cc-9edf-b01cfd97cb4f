import React from 'react'
import { View, ViewProps } from 'react-native'
import { createStyleSheet, useStyles } from '@src/theme/styles'

interface SeaLineBreakProps extends ViewProps {}

export const SeaLineBreak = ({ style, ...props }: SeaLineBreakProps) => {
  const { styles } = useStyles(styleSheet)
  return <View style={[styles.container, style]} {...props} />
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    marginHorizontal: '5%',
    borderBottomWidth: 1,
    borderColor: theme.colors.borderColor,
  },
}))
