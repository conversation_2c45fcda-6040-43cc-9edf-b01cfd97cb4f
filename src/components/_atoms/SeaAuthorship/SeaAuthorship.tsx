import React from 'react'
import { CreateableDocument, UpdateableDocument } from '@src/shared-state/shared-state'
import { StyleSheet, View } from 'react-native'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { formatDateSimplify, formatTime } from '@src/lib/datesAndTime'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

export enum AuthorAction {
  CREATED = 'Created',
  REVIEW_CREATED = 'Review Created',
  COMPLETED = 'Completed',
  LAST_UPDATED = 'Last Updated',
}

interface SeaAuthorshipProps {
  item: CreateableDocument & UpdateableDocument
  includeTime?: boolean
  createdAction?: AuthorAction
}

export const SeaAuthorship = ({
  item,
  includeTime = true,
  createdAction = AuthorAction.CREATED,
}: SeaAuthorshipProps) => {
  if (!item) {
    return <></>
  }

  const renderAuthorDetails = (action: AuthorAction, by?: string, when?: number) => {
    if (!when && !by) {
      return <></>
    }

    const actionDate = formatDateSimplify(when)
    const actionTime = includeTime ? `at ${formatTime(when)}` : ''
    return (
      <SeaTypography variant={'label'}>
        {`${action} by ${renderFullNameForUserId(by)}, ${actionDate} ${actionTime}`}
      </SeaTypography>
    )
  }

  return (
    <View style={styles.container}>
      {/** Custom create action name because it can be referred to as `Review Created` from other places */}
      {renderAuthorDetails(createdAction, item.addedBy, item.whenAdded)}
      {renderAuthorDetails(AuthorAction.COMPLETED, item?.completedBy, item?.whenCompleted)}
      {renderAuthorDetails(AuthorAction.LAST_UPDATED, item.updatedBy, item.whenUpdated)}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    fontStyle: 'italic',
  },
})
