import { Image, View, ViewStyle } from 'react-native'
import React from 'react'

interface SeaHeaderImageProps {
  showIcon?: boolean
  style?: ViewStyle
}

export const SeaHeaderImage = ({ showIcon = true }: SeaHeaderImageProps) => {
  return (
    <View style={{ alignItems: 'center' }}>
      {showIcon ? (
        <Image
          source={require('@assets/sea-flux-logo-full-blue.svg')}
          resizeMode={'cover'}
          style={{
            height: 24,
            width: 144,
          }}
        />
      ) : (
        <Image
          source={require('@assets/sea-flux-logo-icon.svg')}
          resizeMode={'contain'}
          style={{
            height: 24,
            width: 24,
          }}
        />
      )}
    </View>
  )
}
