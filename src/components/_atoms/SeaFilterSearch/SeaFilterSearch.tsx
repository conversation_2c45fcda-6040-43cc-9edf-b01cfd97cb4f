import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaInputProps, SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import React from 'react'
import { StyleSheet, ViewStyle } from 'react-native'

interface SeaFilterSearchProps extends Omit<SeaInputProps, 'icon'> {
  width?: ViewStyle['width']
}

export const SeaFilterSearch = ({ width = 160, inputContainerStyle, ...rest }: SeaFilterSearchProps) => {
  return (
    <SeaTextInput
      prefixIcon={<SeaIcon icon="search" fill={false} size={20} />}
      style={{
        width: width,
      }}
      inputContainerStyle={StyleSheet.flatten([
        {
          borderRadius: 50,
        },
        inputContainerStyle,
      ])}
      {...rest}
    />
  )
}
