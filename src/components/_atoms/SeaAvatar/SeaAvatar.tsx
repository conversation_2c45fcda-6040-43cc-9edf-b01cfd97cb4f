import { View, Text, ViewStyle } from 'react-native'
import React from 'react'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaIcon } from '../SeaIcon/SeaIcon'
import SeaFileImage from '../SeaFileImage/SeaFileImage'
import { SeaTypography } from '../SeaTypography/SeaTypography'
import { SeaStatusType } from '@src/types/Common'

interface SeaAvatarProps {
  label?: string
  image?: string
  size?: number
  style?: ViewStyle
  status?: SeaStatusType
  backgroundColor?: string // Optional background color prop
}
export function SeaAvatar({ label, image, size = 32, style, status, backgroundColor }: SeaAvatarProps) {
  const { styles, theme } = useStyles(styleSheet)

  return (
    <View
      style={[
        styles.avatar,
        {
          width: size,
          height: size,
          ...(backgroundColor ? { backgroundColor } : {}),
        },
        style,
      ]}>
      {image ? (
        <SeaFileImage files={[image]} size="medium" />
      ) : label ? (
        <SeaTypography variant="body" fontWeight="semiBold" color={theme.colors.white}>
          {label}
        </SeaTypography>
      ) : (
        <SeaIcon icon={'person'} color={theme.colors.white} size={40} style={{ marginTop: -3 }} />
      )}
      {status && <View style={[styles.statusWrapper, styles[status]]}></View>}
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  avatar: {
    backgroundColor: theme.colors.lightGrey,
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },

  statusWrapper: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  ok: {
    backgroundColor: theme.colors.status.okPrimary,
  },
  warning: {},
  attention: {
    backgroundColor: theme.colors.status.attentionPrimary,
  },
  minor: {},
  error: {
    backgroundColor: theme.colors.status.errorPrimary,
  },
  critical: {},
}))
