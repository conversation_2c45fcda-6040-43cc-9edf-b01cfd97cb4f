import React, { useMemo, useState } from 'react'
import { Pressable, StyleSheet, View, ViewStyle } from 'react-native'
import { UserType } from '@src/shared-state/Core/user'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { sharedState } from '@src/shared-state/shared-state'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaModal } from '@src/components/_atoms/SeaModal/SeaModal'
import { SeaSelector } from '@src/components/_atoms/SeaSelector/SeaSelector'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { ScrollView } from 'react-native-gesture-handler'

interface SeaSelectCrewOrContactProps {
  label?: string
  value?: {
    userId: string
    contactId?: string
    name: string
  }
  setValue?: (value?: { userId?: string; contactId?: string; name?: string }) => void
  crew?: UserType[]
  required?: boolean
  requiredError?: string
  isSubmitting?: boolean
  style?: ViewStyle
}

export const SeaSelectCrewOrContact = ({
  label,
  value,
  setValue,
  crew,
  required,
  requiredError,
  isSubmitting,
  style,
}: SeaSelectCrewOrContactProps) => {
  const contacts = sharedState.contacts.use()

  const [showModal, setShowModal] = useState(false)
  const [activeTab, setActiveTab] = useState<'crew' | 'contacts'>('crew')

  const userName = useMemo(() => {
    if (value) {
      if (value.userId) {
        return renderFullNameForUserId(value.userId)
      } else if (value.contactId && contacts?.byId?.[value.contactId]) {
        return contacts.byId[value.contactId].name
      }
    }
    return ''
  }, [value, contacts?.byId])

  const crewList = useMemo(() => {
    if (!crew) return []

    return crew.map(user => ({
      label: `${user.firstName} ${user.lastName}`,
      value: user.id ?? '',
      onPress: () => {
        setValue?.({
          userId: user.id,
          name: `${user.firstName} ${user.lastName}`,
        })
        setShowModal(false)
      },
    }))
  }, [crew])

  const contactsSorted = useMemo(() => {
    if (contacts?.all?.length) {
      const array = [...contacts.all]
      array.sort((a, b) => {
        return a.name.localeCompare(b.name)
      })
      return array
    }
    return undefined
  }, [contacts])

  return (
    <View style={[style]}>
      {label && <SeaTypography variant={'label'}>{label}</SeaTypography>}
      <Pressable
        onPress={() => setShowModal(true)}
        style={{
          width: '100%',
        }}>
        <SeaTextInput
          value={userName}
          readOnly
          style={{
            width: '100%',
          }}
        />
      </Pressable>
      <SeaModal visible={showModal} onClose={() => setShowModal(false)} maxWidth={500} title={'Assigned to'}>
        <SeaStack direction={'column'}>
          <SeaSelector
            items={[
              {
                value: 'crew',
                label: 'Crew',
                onPress: () => setActiveTab('crew'),
              },
              {
                value: 'contacts',
                label: 'Contacts',
                onPress: () => setActiveTab('contacts'),
              },
            ]}
            selectedValue={activeTab}
          />
        </SeaStack>

        {activeTab === 'crew' && (
          <ScrollView style={{ paddingVertical: 10 }}>
            {crewList.map(item => (
              <Pressable key={item.value} onPress={item.onPress}>
                <SeaTypography variant="input">{item.label}</SeaTypography>
              </Pressable>
            ))}
          </ScrollView>
        )}

        {activeTab === 'contacts' && (
          <ScrollView style={{ paddingVertical: 10 }}>
            {contactsSorted?.map(item => (
              <Pressable
                key={item.id}
                onPress={() => {
                  setValue?.({
                    userId: undefined,
                    contactId: item.id,
                    name: item.name,
                  })
                  setShowModal(false)
                }}>
                <SeaTypography variant="input">{item.name}</SeaTypography>
              </Pressable>
            ))}
          </ScrollView>
        )}
      </SeaModal>
    </View>
  )
}

const styles = StyleSheet.create({})
