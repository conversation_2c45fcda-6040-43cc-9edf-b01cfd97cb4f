import { View } from 'react-native'
import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { SeaSelectCrewOrContact } from '@src/components/_atoms/SeaSelectCrewOrContact/SeaSelectCrewOrContact'

const meta = {
  title: 'Components/Atoms/SeaSelectCrewOrContact',
  component: SeaSelectCrewOrContact,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaSelectCrewOrContact>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: args => {
    return <SeaSelectCrewOrContact label={'Select Crew'} value={args.value} setValue={args.setValue} crew={args.crew} />
  },
  args: {},
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
