import React from 'react'
import { Modal, TouchableOpacity, View } from 'react-native'
import { SeaModal } from '@src/components/_atoms/SeaModal/SeaModal'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { createStyleSheet, useStyles } from '@src/theme/styles'

interface SeaPasscodeModalProps {
  visible: boolean
  onClose: () => void
  passcode: string
  onPasscodeChange: (passcode: string) => void
  onSubmit: () => void
  error?: string
  loading?: boolean
}

export const SeaPasscodeModal: React.FC<SeaPasscodeModalProps> = ({
  visible,
  onClose,
  passcode,
  onPasscodeChange,
  onSubmit,
  error,
  loading = false,
}) => {
  const { styles } = useStyles(styleSheet)

  const handleSubmit = () => {
    if (passcode.length === 6 && !loading) {
      onSubmit()
    }
  }

  return (
    <SeaModal visible={visible} onClose={onClose}>
      <View style={styles.modalContainer}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <SeaIcon icon="close" size={24} />
        </TouchableOpacity>

        <SeaStack direction="column" gap={20} align="center">
          <SeaTypography variant="title" textStyle={styles.modalTitle}>
            Authentication Code Required
          </SeaTypography>

          <SeaTypography variant="body" textStyle={styles.modalText}>
            Please enter the authentication code we sent to you via email.
          </SeaTypography>

          <SeaTextInput
            value={passcode}
            onChangeText={onPasscodeChange}
            placeholder="------"
            keyboardType="number-pad"
            maxLength={6}
            hasError={!!error}
            errorText={error}
            style={styles.passcodeInput}
          />

          <SeaButton
            label={loading ? 'Submitting...' : 'Submit'}
            onPress={handleSubmit}
            viewStyle={[styles.submitButton, (passcode.length !== 6 || loading) && styles.disabledButton]}
          />
        </SeaStack>
      </View>
    </SeaModal>
  )
}

const styleSheet = createStyleSheet(theme => ({
  modalContainer: {
    backgroundColor: theme.colors.white,
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    minHeight: 300,
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 1,
  },
  modalTitle: {
    textAlign: 'center',
    marginBottom: 8,
  },
  modalText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  passcodeInput: {
    width: 200,
  },
  submitButton: {
    width: 160,
  },
  disabledButton: {
    opacity: 0.5,
  },
}))
