import { Text, View } from 'react-native'
import type { Meta, StoryObj } from '@storybook/react'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'

const meta = {
  title: 'Components/Atoms/SeaButton',
  component: SeaButton,
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'tertiary'],
      description: 'Choose the button variant',
    },
  },
  args: {
    label: 'Press Me',
    onPress: () => alert('Button pressed!'),
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#505050',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaButton>

export default meta

type Story = StoryObj<typeof meta>

export const Primary: Story = {}
export const PrimaryWithIcon: Story = {
  args: {
    iconOptions: {
      icon: 'directions_boat_filled',
    },
  },
}
export const Secondary: Story = {
  args: {
    variant: SeaButtonVariant.Secondary,
  },
}
export const SecondaryWithIcon: Story = {
  args: {
    variant: SeaButtonVariant.Secondary,
    iconOptions: {
      icon: 'directions_boat_filled',
    },
  },
}
export const Tertiary: Story = {
  args: {
    variant: SeaButtonVariant.Tertiary,
  },
}
export const TertiaryWithIcon: Story = {
  args: {
    variant: SeaButtonVariant.Tertiary,
    iconOptions: {
      icon: 'directions_boat_filled',
    },
  },
}
