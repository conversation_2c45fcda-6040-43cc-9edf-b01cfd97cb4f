import React from 'react'
import { StyleProp, Text, TextStyle, TouchableOpacity, ViewStyle } from 'react-native'
import { SeaIcon, SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { createStyleSheet, useStyles } from '@src/theme/styles'

export enum SeaButtonVariant {
  Primary = 'primary',
  Secondary = 'secondary',
  Tertiary = 'tertiary',
  Link = 'link',
}

export interface SeaButtonProps {
  variant?: SeaButtonVariant
  label?: string
  iconOptions?: SeaIconProps
  onPress?: () => void
  viewStyle?: StyleProp<ViewStyle>
  textStyle?: TextStyle
}

export const SeaButton = ({
  variant = SeaButtonVariant.Primary,
  label,
  iconOptions,
  onPress,
  viewStyle,
  textStyle,
}: SeaButtonProps) => {
  // Styling
  const { styles } = useStyles(styleSheet)

  const getVariantStyles = () => {
    switch (variant) {
      case SeaButtonVariant.Primary:
        return {
          containerStyle: styles.primaryContainer,
          labelStyle: styles.primaryLabel,
        }
      case SeaButtonVariant.Secondary:
        return {
          containerStyle: styles.secondaryContainer,
          labelStyle: styles.secondaryLabel,
        }
      case SeaButtonVariant.Tertiary:
        return {
          containerStyle: styles.tertiaryContainer,
          labelStyle: styles.tertiaryLabel,
        }
      case SeaButtonVariant.Link:
        return {
          containerStyle: styles.linkContainer,
          labelStyle: styles.linkLabel,
        }
      default:
        return {
          containerStyle: styles.primaryContainer,
          labelStyle: styles.primaryLabel,
        }
    }
  }

  const { containerStyle, labelStyle } = getVariantStyles()
  const { color: iconColor, ...restIconOptions } = { ...iconOptions }

  return (
    <>
      <TouchableOpacity
        onPress={onPress}
        style={[styles.container, containerStyle, !label && styles.iconOnlyContainer, viewStyle]}>
        <SeaIcon
          color={iconColor ?? labelStyle.color}
          {...restIconOptions}
          style={[labelStyle, styles.icon as ViewStyle]}
        />
        {label && <Text style={[styles.label, labelStyle, textStyle]}>{label}</Text>}
      </TouchableOpacity>
    </>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    borderRadius: 50,
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'center',
    shadowOffset: { width: 0, height: 3 },
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 5,
  },
  iconOnlyContainer: {
    borderRadius: 50, // Makes it a perfect circle
    width: 40,
    height: 40,
    padding: 0,
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  label: {
    fontFamily: theme.typography.fontFamily.BODY_FONT,
    fontSize: 14,
    lineHeight: 22,
    letterSpacing: 0.5,
  },
  icon: {
    textDecorationLine: 'none',
  },
  primaryContainer: {
    backgroundColor: theme.colors.primary,
  },
  primaryLabel: {
    color: theme.colors.white,
  },
  secondaryContainer: {
    backgroundColor: theme.colors.black,
  },
  secondaryLabel: {
    color: theme.colors.white,
  },
  tertiaryContainer: {
    backgroundColor: theme.colors.background.primary,
  },
  tertiaryLabel: {
    color: theme.colors.black,
  },
  linkContainer: {
    paddingHorizontal: 5,
    shadowOffset: { width: 0, height: 0 },
    shadowColor: '#000',
    shadowOpacity: 0,
    shadowRadius: 0,
  },
  linkLabel: {
    color: theme.colors.primary,
    textDecorationLine: 'underline',
  },
}))
