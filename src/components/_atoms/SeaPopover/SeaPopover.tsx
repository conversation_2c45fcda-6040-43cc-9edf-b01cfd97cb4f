import React, { useEffect, useState, useRef, useCallback } from 'react'
import { Dimensions, Modal, StyleSheet, View, ViewStyle, Text, TouchableOpacity } from 'react-native'

export type PopoverPlacement = 'top' | 'bottom' | 'left' | 'right'

interface PopoverProps {
  children: React.ReactNode
  isVisible: boolean
  onClose: () => void
  placement?: PopoverPlacement
  anchorRef: React.RefObject<View>
}

/**
 * Custom hook to calculate the position of the anchor relative to the screen.
 */
const useAnchorPosition = (anchorRef: React.RefObject<View>, isVisible: boolean) => {
  const [anchorPosition, setAnchorPosition] = useState<{
    x: number
    y: number
    width: number
    height: number
  } | null>(null)

  useEffect(() => {
    if (anchorRef.current && isVisible) {
      anchorRef.current.measure((x, y, width, height, pageX, pageY) => {
        setAnchorPosition({ x: pageX, y: pageY, width, height })
      })
    }
  }, [isVisible, anchorRef])

  return anchorPosition
}

/**
 * Calculates the position of the popover based on the anchor and placement.
 */
const usePopoverPosition = (
  anchorPosition: {
    x: number
    y: number
    width: number
    height: number
  } | null,
  placement: PopoverPlacement
) => {
  const [popoverPosition, setPopoverPosition] = useState<{
    top: number
    left: number
  } | null>(null)
  const window = Dimensions.get('window')

  useEffect(() => {
    if (anchorPosition) {
      const { x, y, width, height } = anchorPosition
      let top = y
      let left = x

      switch (placement) {
        case 'top':
          top = y - height - 10
          break
        case 'bottom':
          top = y + height + 10
          break
        case 'left':
          left = x - width - 10
          break
        case 'right':
          left = x + width + 10
          break
      }

      // Prevent popover from going out of screen
      top = Math.max(10, Math.min(top, window.height - 50))
      left = Math.max(10, Math.min(left, window.width - 50))

      setPopoverPosition({ top, left })
    }
  }, [anchorPosition, placement, window])

  return popoverPosition
}

export const SeaPopover: React.FC<PopoverProps> = ({
  children,
  isVisible,
  onClose,
  placement = 'bottom',
  anchorRef,
}) => {
  const popoverRef = useRef<View>(null)
  const [caretPosition, setCaretPosition] = useState<ViewStyle | null>(null)
  const anchorPosition = useAnchorPosition(anchorRef, isVisible)
  const popoverPosition = usePopoverPosition(anchorPosition, placement)

  /**
   * Calculate caret position after the popover layout is rendered.
   */
  const handlePopoverLayout = useCallback(() => {
    if (popoverRef.current) {
      popoverRef.current.measure((x, y, width, height, pageX, pageY) => {
        const caretStyles: ViewStyle = {}

        switch (placement) {
          case 'top':
            caretStyles.bottom = -5
            caretStyles.left = 10
            break
          case 'bottom':
            caretStyles.top = -5
            caretStyles.left = 10
            break
          case 'left':
            caretStyles.right = -5
            caretStyles.top = height / 2 - 5
            break
          case 'right':
            caretStyles.left = -5
            caretStyles.top = height / 2 - 5
            break
        }

        setCaretPosition(caretStyles)
      })
    }
  }, [placement])

  if (!popoverPosition) return null

  return (
    <Modal transparent animationType="fade" visible={isVisible}>
      <TouchableOpacity style={styles.overlay} onPress={onClose}>
        <View
          style={[
            styles.popover,
            {
              top: popoverPosition.top,
              left: popoverPosition.left,
              maxWidth: Dimensions.get('window').width * 0.8,
            },
          ]}
          ref={popoverRef}
          onLayout={handlePopoverLayout} // Trigger caret calculation after layout
          accessibilityRole="menu">
          {caretPosition && <View style={[styles.caret, caretPosition]} />}
          {children}
        </View>
      </TouchableOpacity>
    </Modal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
  },
  popover: {
    position: 'absolute',
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 8,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  caret: {
    position: 'absolute',
    width: 10,
    height: 10,
    backgroundColor: 'white',
    transform: [{ rotate: '45deg' }],
  },
})
