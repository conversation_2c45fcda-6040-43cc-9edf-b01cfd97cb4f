import React, { useRef, useState } from 'react'
import { View, Text, Button, StyleSheet } from 'react-native'
import type { Meta, StoryObj } from '@storybook/react'
import { SeaPopover } from './SeaPopover' // Adjust the import path as needed

const meta: Meta<typeof SeaPopover> = {
  title: 'Components/Atoms/SeaPopover',
  component: SeaPopover,
}

export default meta

type Story = StoryObj<typeof SeaPopover>

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  anchor: {
    width: 100,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
})

export const Default: Story = {
  render: args => {
    const [isVisible, setIsVisible] = useState(false)
    const anchorRef = useRef<View>(null)

    return (
      <View style={styles.container}>
        <View style={styles.anchor} ref={anchorRef}>
          <Button title="Toggle Popover" onPress={() => setIsVisible(!isVisible)} />
          <SeaPopover {...args} isVisible={isVisible} onClose={() => setIsVisible(false)} anchorRef={anchorRef}>
            <Text>This is a popover!</Text>
          </SeaPopover>
        </View>
      </View>
    )
  },
  args: {
    placement: 'bottom',
  },
}

export const TopPlacement: Story = {
  ...Default,
  args: {
    placement: 'top',
  },
}

export const LeftPlacement: Story = {
  ...Default,
  args: {
    placement: 'left',
  },
}

export const RightPlacement: Story = {
  ...Default,
  args: {
    placement: 'right',
  },
}
