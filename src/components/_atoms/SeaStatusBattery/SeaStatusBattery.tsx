import React from 'react'
import { SeaStatusType } from '@src/types/Common'
import { ReactElement, useMemo, useState } from 'react'
import { Text, TouchableOpacity, View, ViewStyle, StyleSheet } from 'react-native'
import { SeaStatusBatteryPopover } from './SeaStatusBatteryPopover'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '../SeaTypography/SeaTypography'
import { useDeviceWidth } from '@src/hooks/useDevice'

const MIN_PILL_WIDTH = 34

const statusOrder = [
  SeaStatusType.Critical,
  SeaStatusType.Error,
  SeaStatusType.Warning,
  SeaStatusType.Attention,
  SeaStatusType.Minor,
  SeaStatusType.Ok,
]

export enum LinkType {
  Popover = 'popover',
  NotPopover = 'not-popover',
}

type CommonTypes = {
  status: SeaStatusType
  count: number
}

type HavePopover = CommonTypes & {
  type: LinkType.Popover
  onShowPopover: () => void
  popover: ReactElement<typeof SeaStatusBatteryPopover>
}

type HaveLink = CommonTypes & {
  type?: LinkType.NotPopover
  onPress?: () => void
}

export type BatteryStatus = HavePopover | HaveLink

interface SeaStatusBatteryProps {
  label?: string
  statuses: BatteryStatus[]
  batteryStyle?: ViewStyle
}

export const SeaStatusBattery = ({ label, statuses, batteryStyle }: SeaStatusBatteryProps) => {
  const { styles } = useStyles(styleSheet)

  const { isMobileWidth } = useDeviceWidth()

  const [wrapperWidth, setWrapperWidth] = useState(0)

  const sortedStatuses = useMemo(
    () => [...statuses].sort((a, b) => statusOrder.indexOf(a.status) - statusOrder.indexOf(b.status)),
    [statuses]
  )

  const totalCount = useMemo(() => statuses.reduce((sum, status) => sum + status.count, 0), [statuses])

  const adjustedWidths = useMemo(() => {
    if (!wrapperWidth) return null

    const initialWidths = sortedStatuses.map(status => (status.count / totalCount) * wrapperWidth)
    const adjustedWidths = initialWidths.map(width => Math.max(width, MIN_PILL_WIDTH))
    const scaleFactor = wrapperWidth / adjustedWidths.reduce((sum, w) => sum + w, 0)

    return adjustedWidths.map(width => width * scaleFactor)
  }, [wrapperWidth, sortedStatuses, totalCount])

  return (
    <View
      style={[
        styles.container,
        isMobileWidth
          ? { flexDirection: 'column' }
          : {
              flexDirection: 'row',
            },
      ]}>
      {label && (
        <SeaTypography
          variant="subtitle"
          textStyle={{
            fontSize: 14,
          }}
          containerStyle={StyleSheet.flatten([styles.label, isMobileWidth ? { width: '100%' } : { width: '40%' }])}>
          {label}
        </SeaTypography>
      )}
      <View style={[styles.battery, isMobileWidth ? { width: '100%' } : { width: '60%' }]}>
        <View
          style={[styles.pillWrapper, batteryStyle]}
          onLayout={event => setWrapperWidth(event.nativeEvent.layout.width)}>
          {sortedStatuses.map((status, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                if (status.type === LinkType.Popover) {
                  status.onShowPopover()
                } else if (status.onPress) {
                  status.onPress()
                }
              }}
              style={[styles.pill, styles[status.status], { width: adjustedWidths?.[index] ?? 0 }]}>
              <Text style={[styles.pillText, status.status === 'critical' && styles.criticalPillText]}>
                {status.count}
              </Text>
              {status.type === LinkType.Popover && status.popover}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    gap: 8,
    minWidth: 120,
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
    width: '100%',
  },
  label: {
    flexShrink: 1,
  },
  battery: {
    flex: 1,
    borderRadius: 4,
    flexShrink: 0,
  },
  pillWrapper: {
    flexDirection: 'row',
    borderRadius: 4,
    overflow: 'hidden',
    width: '100%',
    height: 24,
    alignItems: 'center',
  },
  pill: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    height: '100%',
    justifyContent: 'center',
    minWidth: MIN_PILL_WIDTH,
    overflow: 'hidden',
  },
  pillText: {
    fontSize: 12,
    color: theme.colors.white,
  },
  criticalPillText: {
    color: theme.colors.status.errorPrimary,
  },
  ok: { backgroundColor: theme.colors.status.okPrimary },
  minor: { backgroundColor: theme.colors.status.minorPrimary },
  warning: { backgroundColor: theme.colors.status.warnPrimary },
  attention: { backgroundColor: theme.colors.status.attentionPrimary },
  error: { backgroundColor: theme.colors.status.errorPrimary },
  critical: {
    backgroundColor: theme.colors.white,
    borderColor: theme.colors.status.errorPrimary,
    borderWidth: 2,
    color: theme.colors.status.errorPrimary,
  },
}))
