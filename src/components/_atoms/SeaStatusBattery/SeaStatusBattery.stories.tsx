import { Text, View } from 'react-native'
import type { Meta, StoryObj } from '@storybook/react'
import { SeaStatusBattery } from './SeaStatusBattery'
import { SeaStatusType } from '@src/types/Common'

const meta = {
  title: 'Components/Atoms/SeaStatusBattery',
  component: SeaStatusBattery,
  argTypes: {},
  args: {
    label: 'Press each pill on me',
  },
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            backgroundColor: '#E6E7EA',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaStatusBattery>

export default meta

type Story = StoryObj<typeof meta>

export const Base: Story = {
  args: {
    statuses: [
      { status: SeaStatusType.Error, count: 20, onPress: () => alert('Error') },
      {
        status: SeaStatusType.Warning,
        count: 14,
        onPress: () => alert('Warning'),
      },
      {
        status: SeaStatusType.Attention,
        count: 12,
        onPress: () => alert('Attention'),
      },
      { status: SeaStatusType.Ok, count: 10, onPress: () => alert('Ok') },
      {
        status: SeaStatusType.Critical,
        count: 3,
        onPress: () => alert('Critical'),
      },
    ],
  },
}
