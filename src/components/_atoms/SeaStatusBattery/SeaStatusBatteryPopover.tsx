import React from 'react'
import { View } from 'react-native'
import { PopoverPlacement, SeaPopover } from '../SeaPopover/SeaPopover'

interface SeaStatusBatteryPopoverProps {
  isVisible: boolean
  onClose: () => void
  anchorRef: React.RefObject<View>
  children: React.ReactNode
  placement?: PopoverPlacement
}
export const SeaStatusBatteryPopover: React.FC<SeaStatusBatteryPopoverProps> = ({
  isVisible,
  onClose,
  anchorRef,
  children,
  placement = 'bottom',
}) => {
  return (
    <View ref={anchorRef}>
      <SeaPopover isVisible={isVisible} onClose={onClose} anchorRef={anchorRef} placement={placement}>
        {children}
      </SeaPopover>
    </View>
  )
}
