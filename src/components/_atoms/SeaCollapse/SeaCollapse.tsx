import React from 'react'
import { Pressable, StyleSheet, Text, View, ViewStyle } from 'react-native'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { colors } from '@src/theme/colors'
import { fontFamily } from '@src/theme/typography'

interface SeaCollapseProps {
  title: string
  children: React.ReactNode
  style?: ViewStyle
}

export const SeaCollapse: React.FC<SeaCollapseProps> = ({ title, children, style }) => {
  const [isCollapsed, setIsCollapsed] = React.useState(true)
  return (
    <View style={[styles.container]}>
      <Pressable onPress={() => setIsCollapsed(!isCollapsed)}>
        <SeaStack direction={'row'} align={'center'} justify={'start'} gap={10} padding={0}>
          <Text style={styles.titleText}>{title}</Text>
          <SeaIcon icon={isCollapsed ? 'arrow_drop_down' : 'arrow_drop_up'} color={colors.primary} size={24} />
        </SeaStack>
      </Pressable>
      {!isCollapsed && <View style={style}>{children}</View>}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  titleText: {
    color: colors.primary,
    fontFamily: fontFamily.BODY_FONT,
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 18,
    letterSpacing: 0.15,
  },
})
