import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View, Text } from 'react-native'
import { SeaCollapse } from '@src/components/_atoms/SeaCollapse/SeaCollapse'

const meta = {
  title: 'Components/Atoms/SeaCollapse',
  component: SeaCollapse,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <View style={{ width: '50%' }}>
            <Story />
          </View>
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaCollapse>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    title: 'Click to open the collapse',
    children: (
      <View>
        <Text>Hello from the collapse!</Text>
      </View>
    ),
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
