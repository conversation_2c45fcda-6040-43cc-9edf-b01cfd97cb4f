import React from 'react'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { StyleSheet } from 'react-native'

import { FileReference } from '@src/lib/fileImports'

interface SeaSignatureImageProps {
  visible?: boolean
  file?: FileReference
}
/** TODO: Build out the signature image from the `SeaImage` component */
export const SeaSignatureImage = ({ visible = true, file }: SeaSignatureImageProps) => {
  // const [imgSrc, setImgSrc] = useState<string>();

  // useEffect(() => {
  //   if (file) {
  //     getCachedFileSrc(file, 'S')
  //       .then(src => {
  //         setImgSrc(src);
  //       })
  //       .catch(e => {
  //         if (file[0] === '0') {
  //           setImgSrc(getFileTrappedSrc('sig'));
  //         } else {
  //           // setImgSrc(getImgSrcFromString(file, 'sig'));
  //           getImgSrcFromString(file, 'sig').then((_imgSrc: string) => {
  //             setImgSrc(_imgSrc);
  //           });
  //         }
  //       });
  //   }
  // }, [file]);
  //
  // if (file === undefined) {
  //   return <></>;
  // }
  //
  // const revealImage = (e: any) => {
  //   e.target.style.opacity = 1;
  // };
  //
  // return (
  //   <img
  //     className={`sea-file-image fade signature`}
  //     src={imgSrc}
  //     onLoad={revealImage}
  //     alt="Signature"
  //   />
  // );

  return <SeaLabelValue label={'Signature'} value={'Signature - Todo'} style={styles.label} />
}

const styles = StyleSheet.create({
  label: {
    flex: 1,
  },
})
