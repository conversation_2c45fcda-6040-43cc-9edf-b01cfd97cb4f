import React from 'react'
import { SeaIcon, SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { MaterialIconName } from '@src/types/MaterialIcons'
import { useStyles } from '@src/theme/styles'

interface SeaLabelIconProps extends Omit<SeaIconProps, 'icon'> {
  icon?: MaterialIconName
}

export const SeaLabelIcon = (props: SeaLabelIconProps) => {
  const { theme } = useStyles()
  return <SeaIcon icon={'label'} color={theme.colors.text.labelIcon} size={20} fill={false} {...props} />
}
