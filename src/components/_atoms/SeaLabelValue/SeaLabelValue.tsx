import React from 'react'
import { StyleSheet, View, ViewStyle } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaLabelIcon } from '@src/components/_atoms/SeaLabelValue/SeaLabelIcon'
import { SeaLineBreak } from '@src/components/_atoms/SeaDividers/SeaLineBreak'
import { useDeviceWidth } from '@src/hooks/useDevice'

export interface SeaLabelValueProps {
  label: string
  value?: string
  style?: ViewStyle
  iconOptions?: SeaIconProps
  showIcon?: boolean
  showLineBreak?: boolean
  leftBorderColor?: string
  showLeftBorder?: boolean
  layout?: 'horizontal' | 'vertical'
}

export const SeaLabelValue = ({
  label,
  value,
  style,
  iconOptions,
  showIcon = false,
  showLineBreak = true,
  leftBorderColor,
  showLeftBorder = false,
  layout = 'horizontal',
}: SeaLabelValueProps) => {
  const { styles } = useStyles(styleSheet)
  const { isDesktopWidth } = useDeviceWidth()

  const isVerticalLayout = layout === 'vertical'

  return (
    <View
      style={[
        styles.container,
        { flexDirection: 'column' },
        isVerticalLayout ? styles.verticalContainer : styles.horizontalContainer,
        style,
      ]}>
      <View
        style={[
          styles.container,
          isVerticalLayout ? styles.verticalInnerContainer : styles.horizontalInnerContainer,
          style,
        ]}>
        <View style={[styles.labelContainer]}>
          {(!isVerticalLayout || showIcon) && <SeaLabelIcon {...iconOptions} />}
          <SeaTypography
            variant={'label'}
            containerStyle={StyleSheet.flatten([
              styles.labelTextContainer,
              isVerticalLayout || showLeftBorder || (!showIcon && showLeftBorder) ? styles.leftBorderStyle : {},
              leftBorderColor ? { borderLeftColor: leftBorderColor } : {},
            ])}
            textStyle={StyleSheet.flatten([styles.labelText, isVerticalLayout ? styles.labelTextVertical : undefined])}>
            {label}
          </SeaTypography>
        </View>

        <SeaTypography
          variant={'value'}
          containerStyle={StyleSheet.flatten([
            styles.valueContainer,
            isVerticalLayout ? { width: '100%' } : { flex: 2, width: '50%' },
          ])}
          textStyle={styles.valueText}
          fontWeight={'regular'}>
          {value}
        </SeaTypography>
      </View>
      {showLineBreak && !isDesktopWidth && <SeaLineBreak style={styles.lineBreak} />}
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    width: '100%',
    gap: 8,
    justifyContent: 'center',
  },
  verticalContainer: {},
  horizontalContainer: {
    paddingTop: 8,
  },
  verticalInnerContainer: {
    display: 'flex',
    flex: 1,
    flexDirection: 'column',
    gap: 8,
  },
  horizontalInnerContainer: {
    flexDirection: 'row',
  },
  leftBorderStyle: {
    paddingLeft: 8,
    borderLeftWidth: 4,
    paddingVertical: 3,
    borderLeftColor: theme.colors.primary,
  },
  desktopContainer: {},
  labelContainer: {
    display: 'flex',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  labelTextContainer: {
    flex: 1,
    marginBottom: 0,
    flexShrink: 1,
  },
  labelText: {
    textAlignVertical: 'center',
    fontWeight: '600',
  },
  labelTextVertical: {
    textTransform: 'uppercase',
    letterSpacing: 0.8,
    fontWeight: '600',
  },
  valueContainer: {
    justifyContent: 'center',
    marginBottom: 0,
    flexShrink: 1,
  },
  valueText: {},
  lineBreak: {
    marginHorizontal: 0,
    marginVertical: 4,
  },
}))
