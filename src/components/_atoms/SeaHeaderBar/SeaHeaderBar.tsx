import React from 'react'
import { TouchableOpacity, View, ViewStyle } from 'react-native'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaIcon, SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { DrawerActions, useNavigation } from '@react-navigation/native'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

interface SeaHeaderBarProps {
  style?: ViewStyle
  title?: string
  iconName?: SeaIconProps['icon']
}

export const SeaHeaderBar = ({ style, title, iconName }: SeaHeaderBarProps) => {
  const navigation = useNavigation()
  const { styles, theme } = useStyles(styleSheet)

  const router = useRouter()

  const handleNavigation = (routePath: string, params?: Record<string, any>) => {
    router.navigate({
      pathname: routePath,
      params: {
        ...params,
      },
    })
  }

  return (
    <View style={[styles.container, style]}>
      {/* Left drawer icon */}
      <TouchableOpacity style={styles.drawerButton} onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}>
        <SeaIcon icon={'menu'} size={24} color={theme.colors.text.body} />
      </TouchableOpacity>

      {/* Centered title */}
      <>
        {title && (
          <SeaStack style={styles.titleContainer}>
            <TouchableOpacity
              onPress={() => handleNavigation(getRoutePath(Routes.VESSEL_DASHBOARD))}
              style={[
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 10,
                  flexShrink: 1,
                },
              ]}>
              {iconName && (
                // TODO: Replace with Sea-Flux Logo
                <SeaIcon icon={iconName} size={22} color={theme.colors.primary} />
              )}
              <SeaTypography
                variant={'title'}
                textStyle={styles.titleText}
                color={theme.colors.primary}
                numberOfLines={1}
                containerStyle={{ flexShrink: 1 }}>
                {title}
              </SeaTypography>
            </TouchableOpacity>
          </SeaStack>
        )}
      </>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
    height: 40,
    backgroundColor: theme.colors.background.primary,
    paddingHorizontal: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  drawerButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    left: 60,
    right: 60,
    top: 0,
    bottom: 0,
  },
  titleText: {
    marginBottom: 0,
    textAlign: 'center',
    fontSize: 18,
    color: theme.colors.primary,
  },
}))
