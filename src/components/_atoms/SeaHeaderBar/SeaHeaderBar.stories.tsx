import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View, Text } from 'react-native'
import { SeaHeaderBar } from './SeaHeaderBar'

const meta = {
  title: 'Components/Atoms/SeaHeaderBar',
  component: SeaHeaderBar,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
          <Text>Foo</Text>
          <Text>Bar</Text>
          <Text>Baz</Text>
        </View>
      )
    },
  ],
} satisfies Meta<typeof SeaHeaderBar>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
