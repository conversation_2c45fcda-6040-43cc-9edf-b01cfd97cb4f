import React, { ReactNode } from 'react';
import { sharedState } from '../../../shared-state/shared-state';
import PleaseWait from '@src/pages/AccountManagement/PleaseWait/PleaseWait';
import { NavigationProp, useNavigation, useRoute } from '@react-navigation/native';
import AppRoutes, { RootStackParamList } from '@src/navigation/AppRoutes';

interface RequireSuperAdminProps {
    children: ReactNode
}

const RequireSuperAdmin: React.FC<RequireSuperAdminProps> = ({ children }) => {
    const superAdmin = sharedState.superAdmin.use();
    const user = sharedState.user.use();
    const appReadyState = sharedState.appReadyState.use()!;
    const navigation = useNavigation<NavigationProp<RootStackParamList>>();
    const route = useRoute();
    // replace location.pathname and location.search with:
    const currentPath = route.name;
    // console.log(`currentPath=${currentPath}`);
    const currentParams = route.params ? `?${new URLSearchParams(route.params as Record<string, string>)}` : '';


    if (!appReadyState.isReady) {
        return <PleaseWait message={appReadyState.notReadyMessage} />
    }

    if (!superAdmin) {
        setTimeout(() => {
            if (user) {
                navigation.navigate(AppRoutes.HOME.name as never);
            } else {
                // navigation.navigate(`/login?d=${encodeURIComponent(location.pathname + (location.search ? location.search : ''))}`);
                navigation.navigate('login', {
                    d: encodeURIComponent(currentPath + currentParams)
                } as never);
            }
        });
        return <></>;
    }

    return <>{children}</>;
};

export default RequireSuperAdmin;
