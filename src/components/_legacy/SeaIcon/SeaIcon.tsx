import React from 'react';
import { StyleProp, TouchableOpacity, ViewStyle } from 'react-native';
import * as ExpoIcons from '@expo/vector-icons';
import { colors } from '@src/theme/globalStyle';

export type IconFamily = keyof typeof ExpoIcons;

export type IconProps<T extends IconFamily> = React.ComponentProps<typeof ExpoIcons[T]>;

interface SeaIconProps<T extends IconFamily = 'Ionicons'> {
    family?: T;
    icon: IconProps<T>['name'];
    size?: number;
    color?: string;
    style?: StyleProp<ViewStyle>;
    onPress?: () => void;
}

const SeaIcon = <T extends IconFamily = 'Ionicons'>({ 
    family = 'Ionicons' as T, 
    icon, 
    size = 20, 
    color = colors.icon_grey, 
    style,
    onPress
}: SeaIconProps<T>) => {
    const IconComponent = ExpoIcons[family] as any;
    if (onPress) {
        return <TouchableOpacity onPress={onPress}><IconComponent name={icon} size={size} color={color} style={style} /></TouchableOpacity>;
    }
    return <IconComponent name={icon} size={size} color={color} style={style} />;
};

export default SeaIcon;