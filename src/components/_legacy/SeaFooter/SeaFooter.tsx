import React, { useState } from "react";
import { View, Text, StyleSheet, Linking, Platform } from "react-native";
import { firebaseSettings } from "@src/lib/firebase/config/firebase";
import packageJson from "../../../../package.json";
import SeaLinkButton from "../SeaLinkButton/SeaLinkButton";
import { colors, fonts } from "@src/theme/globalStyle";

const SeaFooter: React.FC = () => {
  const [showContactModal, setShowContactModal] = useState(false);

  const onOpenContactModal = () => {
    setShowContactModal(true);
  };

  const openURL = (url: string) => {
    Linking.openURL(url).catch((err) =>
      console.error("Error opening URL:", err),
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.footer}>
        <View style={styles.leftSection}>
          <Text style={styles.text}>
            © {new Date().getFullYear()} SEA FLUX.
          </Text>
        </View>

        {/* <View style={styles.centerSection}>

                </View> */}

        <View style={styles.rightSection}>
          <Text style={styles.text}>
            Version {packageJson.version}, Build {packageJson.build}
            {/* {firebaseSettings.projectId === 'sea-flux-3c853'
                            ? ''
                            : ` (${firebaseSettings.projectId?.substring(firebaseSettings.projectId.lastIndexOf('-') + 1)})`
                        } */}
          </Text>
          <SeaLinkButton mode="small-link" onPress={onOpenContactModal}>
            Contact
          </SeaLinkButton>

          <SeaLinkButton
            mode="small-link"
            onPress={() => openURL("https://sea-flux.com/privacy-policy/")}
          >
            Privacy
          </SeaLinkButton>

          <SeaLinkButton
            mode="small-link"
            onPress={() =>
              openURL("https://sea-flux.com/terms-and-conditions/")
            }
          >
            Terms of Use
          </SeaLinkButton>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  footer: {
    height: 50, // var(--footer-height)
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingBottom: 12,
  },
  text: {
    color: colors.text_on_grey,
    fontSize: 10,
    fontFamily: fonts.sea,
  },
  leftSection: {
    flex: 1,
    alignItems: "flex-start",
  },
  centerSection: {
    flex: 2,
    alignItems: "center",
  },
  rightSection: {
    flex: 2,
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "center",
  },
});

export default SeaFooter;
