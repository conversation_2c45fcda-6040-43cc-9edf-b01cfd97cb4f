import React, {
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import {
  StyleSheet,
  Text,
  TextInput,
  View,
} from "react-native";
import { SeaHelp } from "../SeaContextualHelp/SeaContextualHelp";
import SeaLabel from "../SeaLabel/SeaLabel";
import SeaInputError from "../SeaInputError/SeaInputError";
import {
  colors,
  theme,
} from "@src/theme/globalStyle";

interface SeaInputProps {
  zone?: "blue" | "white" | "grey";
  inputMode?: "none" | "text" | "tel" | "url" | "email" | "numeric" | "decimal" | "search";
  type?: "default" | "email-address" | "numeric" | "phone-pad" | "number-pad" | "decimal-pad";
  name?: string;
  label?: string;
  help?: SeaHelp;
  value?: string | number;
  placeholder?: string;
  error?: string;
  warn?: boolean;
  style?: any;
  disabled?: boolean;
  maxLength?: number;
  readonly?: boolean;
  required?: boolean;
  prefix?: string;
  suffix?: React.ReactNode;
  autoCapitalize?: "none" | "sentences" | "words" | "characters";
  autoComplete?: "off" | "username" | "password" | "email" | "name" | "tel" | "street-address" | "postal-code" | "cc-number" | "cc-csc" | "cc-exp" | "cc-exp-month" | "cc-exp-year";
  secureTextEntry?: boolean;
  onChangeText?: (text: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  setFocus?: () => Promise<void>; // Call to focus the input
}

const SeaInput = React.forwardRef<TextInput, SeaInputProps>(({
  zone = "white",
  inputMode,
  type = "default",
  name,
  label,
  help,
  value,
  placeholder,
  error,
  warn,
  style,
  disabled = false,
  maxLength,
  readonly = false,
  required,
  prefix,
  suffix,
  autoCapitalize = "sentences",
  autoComplete,
  secureTextEntry,
  onChangeText,
  onBlur,
  onFocus,
}, forwardedRef: any) => {
  const isMounted = useRef(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<TextInput>(null);
  
  const focus = useCallback(() => {
    setTimeout(() => {
      if (!isMounted.current) return;
      if (inputRef?.current) {
        inputRef.current.focus();
        setIsFocused(true);
      }
    }, 600);
  }, []);
  
  // Provide callable method focus() to parent
  useImperativeHandle(forwardedRef, () => ({
    focus,
    inputRef: inputRef,
  }));
  
  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };
  
  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };
  
  const inputColor = zone === "blue" ? colors.light_blue : zone === "grey" ? colors.input_background_color : colors.text_on_white;
  
  return (
    <View style={[styles.container, style]}>
      {label && <SeaLabel help={help}>{label}</SeaLabel>}
      <View style={[
        styles.inputContainer,
        isFocused ? styles.focusedContainer : undefined,
        error ? styles.errorContainer : undefined,
      ]}
      >
        {prefix && <Text style={[styles.prefix, { color: inputColor }]}>{prefix}</Text>}
        <TextInput
          ref={inputRef}
          style={[
            styles.input,
            { color: inputColor },
            disabled && styles.disabledInput,
          ]}
          value={value?.toString()}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.medium.main}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          editable={!disabled && !readonly}
          keyboardType={type}
          maxLength={maxLength}
          autoCapitalize={autoCapitalize}
          autoComplete={autoComplete}
          secureTextEntry={secureTextEntry}
        />
        {suffix && (
          <View style={styles.suffixContainer}>
            {suffix}
          </View>
        )}
      </View>
      <SeaInputError>{error}</SeaInputError>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.light,
    borderWidth: 1,
    borderColor: colors.light,
    borderRadius: 5,
    height: 40,
    paddingHorizontal: 10,
  },
  focusedContainer: {
    borderColor: colors.primary,
  },
  input: {
    flex: 1,
    fontFamily: "System",
    fontSize: 12,
    fontWeight: "500",
    // @ts-ignore
    outlineStyle: "none",
  },
  disabledInput: {
    opacity: 0.4,
  },
  errorContainer: {
    borderColor: colors.danger,
    backgroundColor: "#fff5f5",
  },
  prefix: {
    marginRight: 5,
    opacity: 0.8,
  },
  suffixContainer: {
    marginLeft: 5,
  },
});

export default SeaInput;