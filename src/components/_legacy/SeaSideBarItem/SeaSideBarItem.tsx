import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  useWindowDimensions,
} from "react-native";
import SeaIcon, { IconFamily } from "../SeaIcon/SeaIcon";
import { breakpoints, colors } from "@src/theme/globalStyle";
import { fontFamily } from "@src/theme/typography";

export interface SubItem {
  name: string;
  title: string;
  isSubSelected?: boolean;
  onPress: () => void;
}

interface SeaSideBarItemProps {
  name: string;
  title: string;
  icon: string;
  family?: IconFamily;
  isSectionSelected: boolean;
  isSubSectionSelected?: boolean;
  hasNewTag?: boolean;
  subItems?: SubItem[];
  onPress: () => void;
  isThinDrawerOpen: boolean;
}

const SeaSideBarItem: React.FC<SeaSideBarItemProps> = ({
  name,
  title,
  icon,
  family = "Ionicons",
  isSectionSelected = false,
  isSubSectionSelected = false,
  hasNewTag,
  subItems,
  onPress,
  isThinDrawerOpen = true,
}) => {
  const isWeb = Platform.OS === "web";
  const { width } = useWindowDimensions();
  const isThinSidebar = width >= breakpoints.md && width < breakpoints.xl;
  const [isThinDrawerExpanded, setIsThinDrawerExpanded] = useState(true);

  const iconColor = () => {
    if (isSectionSelected) {
      return subItems ? colors.primary : colors.white;
    }
    return colors.text_on_white;
  };

  const iconThinDrawerColor = () => {
    if (isSectionSelected) {
      return colors.primary;
    }
    return colors.text_on_white;
  };

  const sectionSelectedTextStyle = () => {
    if (isSectionSelected) {
      return subItems || isSubSectionSelected
        ? styles.selectedSectionName
        : styles.selectedName;
    }
    return styles.selectedNameRegular;
  };

  if (isThinSidebar && !isThinDrawerOpen) {
    return (
      <>
        <TouchableOpacity style={[styles.drawerItem]} onPress={onPress}>
          <View style={styles.iconContainer}>
            <SeaIcon
              icon={icon}
              family={family}
              size={24}
              color={iconThinDrawerColor()}
            />
          </View>
        </TouchableOpacity>
      </>
    );
  } else {
    return (
      <>
        <TouchableOpacity
          style={[
            styles.drawerItem,
            isSectionSelected && !subItems && styles.selectedItem,
          ]}
          onPress={onPress}
        >
          <View style={styles.iconContainer}>
            <SeaIcon
              icon={icon}
              family={family}
              size={24}
              color={iconColor()}
            />
          </View>
          <View style={styles.itemContainer}>
            <Text style={[styles.itemName, sectionSelectedTextStyle()]}>
              {title || name}
            </Text>
            {hasNewTag && (
              <View style={styles.newTag}>
                <Text style={styles.newTagText}>NEW</Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
        {subItems && (
          <View style={styles.subItemsContainer}>
            {subItems.map((subItem, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.subItem,
                  subItem.isSubSelected && styles.selectedSubItem,
                ]}
                onPress={subItem.onPress}
              >
                <Text
                  numberOfLines={1}
                  style={[
                    styles.subItemLabel,
                    subItem.isSubSelected && styles.selectedSubItemLabel,
                  ]}
                >
                  {subItem.title || subItem.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </>
    );
  }
};

const styles = StyleSheet.create({
  drawerItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    width: "100%",
  },
  selectedItem: {
    backgroundColor: colors.primary,
  },
  selectedSubItem: {
    backgroundColor: colors.primary,
  },
  iconContainer: {
    marginRight: 8,
  },
  itemContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  itemName: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.text_on_white,
    fontFamily: fontFamily.BODY_FONT,
  },
  selectedName: {
    color: colors.white,
  },
  selectedNameRegular: {
    color: colors.text_on_white,
  },
  selectedSectionName: {
    color: colors.primary,
  },
  newTag: {
    backgroundColor: "#ff6014",
    paddingHorizontal: 5,
    paddingVertical: 2,
    borderRadius: 4,
  },
  newTagText: {
    color: "white",
    fontSize: 10,
    fontWeight: "500",
  },
  subItemsContainer: {
    width: "100%",
  },
  subItem: {
    paddingVertical: 8,
    paddingHorizontal: 8,
    paddingLeft: 40, // 16 (parent padding) + 24 (icon width) + 16 (icon margin)
    width: "100%",
  },
  subItemLabel: {
    fontSize: 14,
    fontWeight: "400",
    color: colors.text_on_white,
    fontFamily: fontFamily.BODY_FONT,
  },
  selectedSubItemLabel: {
    color: colors.white,
    fontWeight: "600",
  },
});

export default SeaSideBarItem;
