import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
    sideBarBox: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
      width: 240,
      height: 44,
    },
    tabBox: {
      height: 26,
    },
    selectedBox: {
      backgroundColor: '#3880ff', // assuming this is your primary color
    },
    iconContainer: {
      width: 42,
      justifyContent: 'center',
      alignItems: 'center',
      paddingLeft: 5,
    },
    sideBarName: {
      color: '#bfc4ca',
      fontSize: 14,
      fontWeight: '600',
      paddingLeft: 2,
    },
    tabName: {
      fontWeight: '400',
      paddingRight: 8,
      width: 216,
    },
    selectedName: {
      color: 'white',
    },
    selectedSubMenuName: {
      color: '#3880ff',
    },
    newTag: {
      backgroundColor: '#ff6014',
      borderRadius: 12,
      paddingHorizontal: 5,
      paddingVertical: 3,
      position: 'absolute',
      right: 8,
    },
    newTagText: {
      color: 'white',
      fontSize: 10,
      fontWeight: '500',
    },
  });

export { styles };