import { colors, fonts } from '@src/theme/globalStyle';
import React, { ReactNode } from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
/*
    This is really a button that doesn't look like a button (i.e. could look like a link)
    It is to get around the fact we're using a link that acts as a button so shouldn't use an <a/> tag
    see: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/master/docs/rules/anchor-is-valid.md
*/

interface SeaLinkButtonProps {
    children: ReactNode;
    onPress?: () => void;
    style?: any;
    mode?: 'small-link' | 'standard-link' | 'simple-link';
}

const SeaLinkButton: React.FC<SeaLinkButtonProps> = ({
    children,
    onPress,
    style,
    mode
}) => {
    return (
        <TouchableOpacity
            onPress={onPress}
            style={[styles.button, style]}
        >
            <Text style={[
                styles.text,
                mode === 'small-link' && styles.smallLink,
                mode === 'standard-link' && styles.standardLink,
                mode === 'simple-link' && styles.simpleLink,
            ]}>
                {children}
            </Text>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    button: {
        backgroundColor: 'transparent',
        margin: 0,
        padding: 8,
    },
    text: {
        textTransform: 'uppercase',
        fontWeight: '700',
        color: colors.primary,
        letterSpacing: 1,
        fontSize: 12,
        fontFamily: fonts.sea
    },
    smallLink: {
        color: colors.text_on_grey,
        textDecorationLine: 'underline',
        textTransform: 'none',
        fontSize: 10,
        fontWeight: '400',
        margin: 0,
        padding: 0,
    },
    standardLink: {
        textDecorationLine: 'underline',
        fontWeight: '500',
    },
    simpleLink: {
        textDecorationLine: 'underline',
        fontWeight: '500',
        textTransform: 'none',
        margin: 0,
        letterSpacing: 0,
    },
});

export default SeaLinkButton;