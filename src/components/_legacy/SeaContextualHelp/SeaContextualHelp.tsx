// SeaContextualHelp.tsx
import React from 'react';
import { TouchableOpacity, View, StyleSheet } from 'react-native';
import { openContextualHelp } from '../../../managers/ContextualHelpManager/ContextualHelpManager';
import SeaIcon from '../SeaIcon/SeaIcon';

export interface SeaHelp {
  text?: string;
  files?: string[];
}

interface SeaContextualHelpProps {
  help: SeaHelp;
  mode?: 'label' | 'checkbox' | 'text';
}

const SeaContextualHelp: React.FC<SeaContextualHelpProps> = ({ help, mode }) => {
  if (!help || (!help.text && (!help.files || help.files.length === 0))) {
    return null;
  }

  const handlePress = () => {
    openContextualHelp(help);
  };

  return (
    <TouchableOpacity
      style={[styles.container, mode === 'text' && styles.textMode, mode === 'checkbox' && styles.checkboxMode]}
      onPress={handlePress}
    >
      <SeaIcon icon="help"/>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 8,
  },
  textMode: {
    paddingVertical: 0,
    paddingHorizontal: 10,
  },
  checkboxMode: {
    padding: 8,
  },
});

export default SeaContextualHelp;