// SeaFormError.tsx
import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { alertMessage } from '../../../managers/AlertManager/AlertManager';

interface SeaFormErrorProps {
    message?: string;
}

const SeaFormError: React.FC<SeaFormErrorProps> = ({ message }) => {
    useEffect(() => {
        if (message) {
            alertMessage(message);
        }
    }, [message]);

    return null;
};

export default SeaFormError;

const styles = StyleSheet.create({
    formError: {
        color: 'red', // Replace with your danger color
        fontSize: 12,
        padding: 20,
    },
    desktopFormError: {
        paddingTop: 0,
    },
});