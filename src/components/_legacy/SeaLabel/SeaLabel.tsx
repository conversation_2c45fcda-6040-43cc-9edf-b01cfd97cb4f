import React, { ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { SeaHelp } from '@src/components/_legacy/SeaContextualHelp/SeaContextualHelp';
import SeaIcon from '@src/components/_legacy/SeaIcon/SeaIcon';
import { openContextualHelp } from '@src/managers/ContextualHelpManager/ContextualHelpManager';
import { colors } from '@src/theme/globalStyle';

interface SeaLabelProps {
  children: ReactNode;
  zone?: 'blue' | 'white' | 'grey';
  onPress?: () => void;
  help?: SeaHelp;
}

const SeaLabel: React.FC<SeaLabelProps> = ({ children, zone, onPress, help }) => {
  const handlePress = () => {
    if (help) {
      openContextualHelp(help);
    }
    if (onPress) {
      onPress();
    }
  };

  const content = (
    <View style={[styles.container, zone && styles[zone]]}>
      <Text style={[styles.label, zone && styles[`${zone}Text` as keyof typeof styles]]}>
        {children}
      </Text>
      {help && (
        <View style={styles.helpIcon}>
          <SeaIcon icon="help" />
        </View>
      )}
    </View>
  );

  if (onPress || help) {
    return (
      <TouchableOpacity onPress={handlePress}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 21,
    overflow: 'visible',
  },
  label: {
    textTransform: 'uppercase',
    fontWeight: '500',
    color: colors.label_text,
    fontSize: 11,
    lineHeight: 13, // Approximation of 0.95em
    textAlign: 'left',
  },
  grey: {
    marginBottom: -4,
  },
  white: {
    backgroundColor: 'transparent',
  },
  blue: {
    height: undefined,
    marginBottom: -12,
  },
  blueText: {
    color: colors.primary,
  },
  greyText: {
    color: colors.label_text,
  },
  whiteText: {
    color: colors.label_text,
  },
  helpIcon: {
    marginLeft: 4,
  },
});

export default SeaLabel;