import React, { ReactNode, useEffect } from 'react';
import { Modal, View, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import SeaScrollableArea from '../SeaScrollableArea/SeaScrollableArea';

interface SeaAlertProps {
    children?: ReactNode;
    showAlert: boolean;
    setShowAlert: (showAlert: boolean) => void;
    onOpened?: () => void;
    onClosed?: () => void;
    type: 'pdf';
}

const SeaAlert: React.FC<SeaAlertProps> = ({ 
    children, 
    showAlert, 
    setShowAlert, 
    onOpened, 
    onClosed, 
    type 
}) => {
    useEffect(() => {
        if (showAlert && onOpened) {
            onOpened();
        } else if (!showAlert && onClosed) {
            onClosed();
        }
    }, [showAlert]);

    return (
        <Modal
            visible={showAlert}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setShowAlert(false)}
        >
            <View style={styles.modalOverlay}>
                <View style={[styles.modalContent, styles[type]]}>
                    <TouchableOpacity 
                        style={styles.closeButton}
                        onPress={() => setShowAlert(false)}
                    >
                        <Ionicons name="close" size={26} color="#007AFF" />
                    </TouchableOpacity>
                    <SeaScrollableArea>
                        <View style={styles.alertContainer}>
                            <View style={styles.contentContainer}>
                                {children}
                            </View>
                        </View>
                    </SeaScrollableArea>
                </View>
            </View>
        </Modal>
    );
};

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 10,
        width: '90%',
        maxWidth: 460,
        maxHeight: '80%',
        position: 'relative',
    },
    pdf: {
        width: Math.min(460, width - 40),
        height: Math.min(260, height - 80),
    },
    closeButton: {
        position: 'absolute',
        top: 0,
        right: 0,
        width: 44,
        height: 44,
        zIndex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    alertContainer: {
        flex: 1,
        width: '100%',
        height: '100%',
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
});

export default SeaAlert;