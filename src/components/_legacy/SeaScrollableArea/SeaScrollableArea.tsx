import React, { ReactNode, useImperativeHandle, useRef } from 'react';
import { ScrollView, StyleSheet, ViewStyle, ScrollViewProps } from 'react-native';

interface SeaScrollableAreaProps extends Omit<ScrollViewProps, 'horizontal'> {
    children: ReactNode;
    style?: ViewStyle;
    scrollX?: boolean;
    scrollEvents?: boolean;
    onScroll?: (event: any) => void;
    onScrollEnd?: () => void;
    onPress?: () => void;
}

export interface SeaScrollable {
    x: () => number;
    y: () => number;
    scrollToTop: (animated: boolean) => void;
    scrollByPoint: (x: number, y: number, animated: boolean) => void;
}

const SeaScrollableArea = React.forwardRef<SeaScrollable, SeaScrollableAreaProps>(({
    children,
    style,
    scrollX,
    scrollEvents,
    onScroll,
    onScrollEnd,
    onPress,
    ...scrollViewProps
}, forwardedRef) => {
    const scrollViewRef = useRef<ScrollView>(null);
    const scrollOffsetX = useRef(0);
    const scrollOffsetY = useRef(0);

    useImperativeHandle(forwardedRef, () => ({
        x: () => scrollOffsetX.current,
        y: () => scrollOffsetY.current,
        scrollToTop: (animated: boolean) => {
            scrollViewRef.current?.scrollTo({
                x: 0,
                y: 0,
                animated,
            });
        },
        scrollByPoint: (x: number, y: number, animated: boolean) => {
            scrollViewRef.current?.scrollTo({
                x: scrollOffsetX.current + x,
                y: scrollOffsetY.current + y,
                animated,
            });
        },
    }), []);

    const handleScroll = (event: any) => {
        const { x, y } = event.nativeEvent.contentOffset;
        scrollOffsetX.current = x;
        scrollOffsetY.current = y;
        onScroll?.(event);
    };

    return (
        <ScrollView
            ref={scrollViewRef}
            style={[styles.container]}
            contentContainerStyle={style}
            horizontal={scrollX}
            scrollEventThrottle={scrollEvents ? 16 : 0}
            onScroll={handleScroll}
            onScrollEndDrag={onScrollEnd}
            onMomentumScrollEnd={onScrollEnd}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={scrollX}
            {...scrollViewProps}
        >
            {children}
        </ScrollView>
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
        width: '100%',
    },
});

export default SeaScrollableArea;