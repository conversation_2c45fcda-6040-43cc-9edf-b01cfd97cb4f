import React, { ReactNode, useEffect } from "react";
// import { useNavigate, useLocation } from 'react-router-dom';
import { sharedState } from "@src/shared-state/shared-state";
import PleaseWait from "../../../pages/AccountManagement/PleaseWait/PleaseWait";
import { useRoute } from "@react-navigation/native";
import { NavigationProp } from "@react-navigation/native";
import { useNavigation } from "@react-navigation/native";
import { DrawerParamList } from "@src/navigation/RootRouter";
import { APP_ROUTES } from "@src/navigation/routes";

interface RequireUserProps {
  children: ReactNode;
}

const RequireUser: React.FC<RequireUserProps> = ({ children }) => {
  const user = sharedState.user.use();
  const superAdmin = sharedState.superAdmin.use();
  const appReadyState = sharedState.appReadyState.use()!;
  const navigation = useNavigation<NavigationProp<DrawerParamList>>();
  const route = useRoute();
  // replace location.pathname and location.search with:
  const currentPath = route.name;
  // console.log(`currentPath=${currentPath}`);
  const currentParams = route.params
    ? `?${new URLSearchParams(route.params as Record<string, string>)}`
    : "";
  // console.log(`currentParams=${currentParams}`);

  useEffect(() => {
    // console.log(`appReadyState.isReady=${appReadyState.isReady}`);
  }, [appReadyState.isReady]);

  if (!appReadyState.isReady) {
    return <PleaseWait message={appReadyState.notReadyMessage} />;
  }

  let isAllowed = false;
  if (user) {
    if (user.isLoginDisabled || user.isDeactivated || user.state !== "active") {
      isAllowed = superAdmin ? true : false;
    } else {
      isAllowed = true;
    }
  }

  if (!isAllowed) {
    setTimeout(() => {
      if (superAdmin) {
        navigation.navigate(APP_ROUTES.MAIN_NAVIGATION.name as never);
      } else {
        // navigation.navigate(`/login?d=${encodeURIComponent(location.pathname + (location.search ? location.search : ''))}`);
        navigation.navigate("login", {
          d: encodeURIComponent(currentPath + currentParams),
        } as never);
      }
    });
    return <></>;
  }

  return <>{children}</>;
};

export default RequireUser;
