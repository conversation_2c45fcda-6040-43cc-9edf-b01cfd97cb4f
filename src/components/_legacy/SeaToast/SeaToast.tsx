import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, Text } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {ToastType} from "@src/managers/ToastManager/ToastManager";

interface SeaToastProps {
    message: string;
    type?: ToastType;
    setMessage: (message: string) => void;
    duration?: number;
}

const SeaToast: React.FC<SeaToastProps> = ({ message, type, setMessage, duration = 3000 }) => {
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const translateY = useRef(new Animated.Value(-20)).current;
    const insets = useSafeAreaInsets();

    useEffect(() => {
        if (message && message.length > 0) {
            // Show animation
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 200,
                    useNativeDriver: false,
                }),
                Animated.timing(translateY, {
                    toValue: 20,
                    duration: 200,
                    useNativeDriver: false,
                })
            ]).start();

            // Auto hide
            const timer = setTimeout(() => {
                Animated.parallel([
                    Animated.timing(fadeAnim, {
                        toValue: 0,
                        duration: 200,
                        useNativeDriver: false,
                    }),
                    Animated.timing(translateY, {
                        toValue: -20,
                        duration: 200,
                        useNativeDriver: false,
                    })
                ]).start(() => setMessage(''));
            }, duration);

            return () => clearTimeout(timer);
        }
    }, [message]);

    if (!message || message.length === 0) return null;

    return (
        <Animated.View
            style={[
                styles.toast,
                {
                    opacity: fadeAnim,
                    transform: [{ translateY }],
                    top: insets.top,
                },
                getStyleVariants(type),
            ]}
        >
            <Text style={styles.text}>{message}</Text>
        </Animated.View>
    );
};

const getStyleVariants = (type?: ToastType) => {
    switch (type) {
        case ToastType.ERROR:
            return { backgroundColor: "red" };
        case ToastType.SUCCESS:
            return { backgroundColor: "green" };
        case ToastType.WARNING:
            return { backgroundColor: "yellow" };
        case ToastType.INFO:
            return { backgroundColor: "blue" };
        default:
            return { backgroundColor: "blue" };
    }
};

const styles = StyleSheet.create({
    toast: {
        position: 'absolute',
        left: 16,
        right: 16,
        backgroundColor: '#fff',
        padding: 12,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        zIndex: 1000,
        alignItems: 'center',
    },
    text: {
        color: '#000',
        textAlign: 'center',
        fontSize: 16,
    }
});

export default SeaToast;