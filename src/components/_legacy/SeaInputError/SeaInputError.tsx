import { colors } from '@src/theme/globalStyle';
import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  runOnJS,
  withSpring
} from 'react-native-reanimated';

interface SeaInputErrorProps {
  children: React.ReactNode;
  zone?: 'blue' | 'white' | 'grey';
  alignLeft?: boolean;
}

const SeaInputError: React.FC<SeaInputErrorProps> = ({ children, alignLeft, zone }) => {
  const opacity = useSharedValue(0);
  const height = useSharedValue(0);

  useEffect(() => {
    if (children) {
      opacity.value = withTiming(1, { duration: 300 });
      height.value = withSpring(20); // Adjust this value based on your needs
    } else {
      opacity.value = withTiming(0, { duration: 300 });
      height.value = withSpring(0);
    }
  }, [children]);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      height: height.value,
      overflow: 'hidden',
    };
  });

  return (
    <Animated.View style={[styles.container, alignLeft ? styles.left : styles.right, animatedStyles]}>
      <Text style={styles.errorText}>{children}</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 2,
    paddingRight: 4,
  },
  errorText: {
    fontWeight: '500',
    color: colors.danger,
    fontSize: 12,
  },
  left: {
    alignItems: 'flex-start',
  },
  right: {
    alignItems: 'flex-end',
  },
});

export default SeaInputError;