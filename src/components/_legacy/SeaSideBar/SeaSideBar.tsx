import React, { useState } from 'react';
import { View, Image, StyleSheet, useWindowDimensions, ImageStyle, TouchableOpacity } from 'react-native';
import { DrawerContentComponentProps, DrawerContentScrollView, DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import SeaSideBarItem, { SubItem } from '../SeaSideBarItem/SeaSideBarItem';
import { breakpoints, colors } from '@src/theme/globalStyle';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { styles } from './SeaSideBarStyles';
import SeaIcon from '../SeaIcon/SeaIcon';
import AppRoutes, { DrawerParamList } from '@src/navigation/AppRoutes';
import SidebarConfig from '@src/navigation/SidebarConfig';


type SeaSideBarNavigationProp = DrawerNavigationProp<DrawerParamList>;

interface SideBarProps extends DrawerContentComponentProps {
  setIsThinDrawerOpen: (value: boolean) => void;
  isThinDrawerOpen: boolean;
}

const SeaSideBar: React.FC<SideBarProps> = (props) => {
  const { setIsThinDrawerOpen, isThinDrawerOpen, ...drawerProps } = props;
  const navigation = useNavigation<SeaSideBarNavigationProp>();
  const insets = useSafeAreaInsets();
  const { width } = useWindowDimensions();
  const isMediumScreen = width >= breakpoints.md;
  const showToggleButton = width >= breakpoints.md && width < breakpoints.xl;
  const [isSectionSelected, setIsSectionSelected] = useState<string | null>(AppRoutes.HOME.name);
  const [isSubSectionSelected, setIsSubSectionSelected] = useState<string | null>(null);

  const handleSectionPress = (item: any) => {
    setIsSectionSelected(item.name);
    setIsSubSectionSelected(null);
    navigation.navigate(item.name);
  };

  const handleSubSectionPress = (mainItem: any, subItem: any) => {
    setIsSectionSelected(mainItem.name);
    setIsSubSectionSelected(subItem.name);
    navigation.navigate(mainItem.name, { tab: subItem.name });
  };

  return (
    <DrawerContentScrollView
      {...props}
      style={[styles.container]}
      contentContainerStyle={{
        paddingTop: insets.top,
      }}
    >
      {!isMediumScreen && (
        <View style={[styles.header]}>
          <Image
            source={require('@assets/images/seaflux.png')}
            style={styles.logo as ImageStyle}
          />
        </View>
      )}

      {showToggleButton && (
        <View
          style={{ backgroundColor: colors.icon_grey, alignItems: 'flex-end', padding: 10 }}
        >
          <SeaIcon
            icon={isThinDrawerOpen ? 'chevron-back' : 'chevron-forward'}
            size={24}
            color={colors.black}
            onPress={() => { setIsThinDrawerOpen(!isThinDrawerOpen); }}
          />
        </View>
      )}

      {SidebarConfig.map((item) => (
        <SeaSideBarItem
          key={item.name}
          name={item.name}
          title={item.title}
          icon={item.icon}
          isThinDrawerOpen={isThinDrawerOpen}
          isSectionSelected={isSectionSelected === item.name}
          onPress={() => handleSectionPress(item)}
          subItems={'subItems' in item ? item.subItems?.map((subItem: any) => ({
            name: subItem.name,
            title: subItem.title,
            isSubSelected: isSubSectionSelected === subItem.name,
            onPress: () => handleSubSectionPress(item, subItem),
          })) : []}
        />
      ))}
    </DrawerContentScrollView>
  );
};

export default SeaSideBar;
