import { colors } from '@src/theme/globalStyle';
import React from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';

interface BlankScreenProps {
    show: boolean | undefined;
}

const BlankScreen: React.FC<BlankScreenProps> = ({ show }) => {
    if (!show) return null;

    return (
        <View style={styles.blankScreen}>
            <ActivityIndicator
                size="large"
                color="#bfc4ca"  // Using the spinner color from your CSS
                style={styles.spinner}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    blankScreen: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: colors.header,
        zIndex: 1000,
        justifyContent: 'center',
        alignItems: 'center',
    },
    spinner: {
        position: 'absolute',
        top: '50%',
        marginTop: -20, // Half of the default large ActivityIndicator size
    },
});

export default BlankScreen;