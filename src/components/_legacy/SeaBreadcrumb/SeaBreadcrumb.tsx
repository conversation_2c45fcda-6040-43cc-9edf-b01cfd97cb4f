import React, { ReactNode } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import AnchorIcon from '../../../assets/svg/Anchor'; // Ensure this is compatible with React Native

interface SeaBreadcrumbProps {
    children?: ReactNode;
}

const SeaBreadcrumb: React.FC<SeaBreadcrumbProps> = ({ children }) => {
    return (
        <View style={styles.breadcrumbContainer}>
            <AnchorIcon style={styles.icon} />
            {children && React.Children.map(children, (child, index) => (
                <View style={styles.breadcrumbItem} key={index}>
                    {index > 0 && <Text style={styles.slash}>/</Text>}
                    {child}
                </View>
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    breadcrumbContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    icon: {
        marginBottom: -1,
        marginRight: 3,
    },
    breadcrumbItem: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    slash: {
        paddingHorizontal: 6,
    },
});

export default SeaBreadcrumb;