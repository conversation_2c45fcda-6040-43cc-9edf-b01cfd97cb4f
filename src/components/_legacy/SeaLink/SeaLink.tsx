import React, { ReactNode } from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { colors } from '@src/theme/globalStyle';

interface SeaLinkProps {
    children?: ReactNode;
    to: string;
}

const SeaLink: React.FC<SeaLinkProps> = ({ children, to }) => {
    const navigation = useNavigation();

    const onClick = () => {
        navigation.navigate(to as never);
    };

    return (
        <TouchableOpacity onPress={onClick} style={styles.link}>
            <Text style={styles.linkText}>{children}</Text>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    link: {
        // Add any styles for the link container here
    },
    linkText: {
        color: colors.text_on_grey, // Example color, adjust as needed
        textDecorationLine: 'underline',
    },
});

export default SeaLink;