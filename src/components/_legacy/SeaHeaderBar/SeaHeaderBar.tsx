import React from "react";
import { View, Text, TouchableOpacity, useWindowDimensions, Image, ImageStyle } from "react-native";
import SeaIcon from "@src/components/_legacy/SeaIcon/SeaIcon";
import { colors, breakpoints } from "@src/theme/globalStyle";
import { seaHeaderBarStyle as styles } from "./SeaHeaderBarStyle";

interface SeaHeaderBarProps {
    toggleDrawer: () => void;
    onLogout: () => void;
}

const SeaHeaderBar: React.FC<SeaHeaderBarProps> = ({ toggleDrawer, onLogout }) => {
    const { width } = useWindowDimensions();
    const isMediumScreen = width >= breakpoints.md;

    return (
        <View className={styles.container}>
            {!isMediumScreen && (
                <View style={{ marginRight: 10 }}>
                    <SeaIcon icon='menu' size={30} color={colors.icon_grey} onPress={toggleDrawer} />
                </View>
            )}
            {isMediumScreen && (
                <View style={{ marginRight: 10 }}>
                    <Image
                        source={require('@assets/images/seaflux.png')}
                        style={styles.logo as ImageStyle}
                    />
                </View>
            )}
            <View className={styles.iconContainer}>
                <SeaIcon icon='wind' size={30} family='Feather' />
                <SeaIcon icon='log' size={20} family='Octicons' />
                <SeaIcon icon='log-out-outline' size={30} onPress={() => {
                    onLogout();
                }} />
            </View>
        </View>
    )
}

export default SeaHeaderBar;