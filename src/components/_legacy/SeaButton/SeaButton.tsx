import React, { ReactNode } from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { onUserAction } from '@src/shared-state/General/appActivity';
import { jsxToText } from '@src/lib/util';
import { fonts, theme } from '@src/theme/globalStyle';

interface SeaButtonProps {
  children: ReactNode;
  zone?: 'blue' | 'white' | 'grey' | 'gold';
  shape?: 'circle' | 'square' | 'square-outline';
  disabled?: boolean;
  onPress?: () => void;
  size?: 'small' | 'standard' | 'wide';
  mini?: boolean;
  enhanced?: boolean;
  transparent?: boolean;
  to?: string;
  faded?: boolean;
}

const SeaButton: React.FC<SeaButtonProps> = ({
  children,
  zone = 'white',
  shape,
  disabled,
  size,
  mini,
  onPress,
  enhanced,
  transparent,
  to,
  faded
}) => {
  const navigation = useNavigation();

  const handlePress = () => {
    if (to) {
      navigation.navigate(to as never);
    } else if (onPress) {
      onPress();
    }
    onUserAction(`Clicked "${jsxToText(children, 'Unknown')}"`);
  };

  const getButtonStyle = (): ViewStyle => {
    const style: ViewStyle = { ...styles.button };

    if (zone === 'blue') {
      style.backgroundColor = 'transparent';
    } else if (zone === 'white' || zone === 'grey') {
      style.backgroundColor = enhanced ? theme.colors.secondary.main : theme.colors.primary.main;
    }

    if (zone === 'grey') {
      style.borderColor = 'white';
      style.borderWidth = 2;
      style.backgroundColor = 'transparent';
    }

    if (zone === 'white') {
      style.shadowColor = theme.colors.primary.main;
      style.shadowOpacity = 0.35;
    }

    if (shape === 'circle' || shape === 'square' || shape === 'square-outline') {
      style.width = 40;
      style.paddingHorizontal = 0;
    }

    if (shape === 'square' || shape === 'square-outline') {
      style.borderRadius = 6;
      style.shadowOpacity = 0;
    }

    if (shape === 'square-outline') {
      style.backgroundColor = 'white';
      style.borderWidth = 2;
      style.borderColor = theme.colors.primary.main;
    }

    if (size === 'small') {
      style.minWidth = 100;
    } else if (size === 'standard') {
      style.minWidth = 150;
    } else if (size === 'wide') {
      style.minWidth = 180;
    }

    if (mini) {
      style.transform = [{ scale: 0.85 }];
    }

    if (transparent) {
      style.backgroundColor = 'transparent';
      style.borderWidth = 0;
      style.shadowOpacity = 0;
    }

    if (faded) {
      style.opacity = 0.5;
    }

    return style;
  };

  const getTextStyle = (): TextStyle => {
    const style: TextStyle = { ...styles.text };

    if (zone === 'blue' || shape === 'square-outline') {
      style.color = theme.colors.primary.main;
    } else {
      style.color = theme.colors.light.main;
    }

    return style;
  };

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <Text style={getTextStyle()}>{children}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    height: 40,
    paddingHorizontal: 22,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.24,
        shadowRadius: 10,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  text: {
    fontFamily: fonts.sea_bold, // Make sure you have this font in your project
    fontWeight: '600',
    fontSize: 13,
    color: theme.colors.light.main,
  },
});

export default SeaButton;