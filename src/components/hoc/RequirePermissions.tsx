import React, { ReactNode, useMemo } from 'react'
import { View } from 'react-native'
import { permissionLevels, PermissionRole } from '@src/shared-state/Core/userPermissions'
import { sharedState } from '@src/shared-state/shared-state'

interface RequirePermissionsProps {
  children: ReactNode
  role?: PermissionRole
  level?: number
  showDenial?: boolean
  licenseePermission?:
    | 'hasCorrectiveActions'
    | 'hasIncidents'
    | 'hasReporting'
    | 'hasSafetyCheckTaskTime'
    | 'hasMaintenanceTaskTime'
}

export const RequirePermissions = ({
  children,
  role,
  level = permissionLevels.VIEW,
  showDenial,
  licenseePermission,
}: RequirePermissionsProps) => {
  const userPermissions = sharedState.userPermissions.use()
  const licenseeSettings = sharedState.licenseeSettings.use()

  const isLicenseeDenied = useMemo(() => {
    if (licenseePermission) {
      return !licenseeSettings?.[licenseePermission]
    }

    return false
  }, [licenseePermission, licenseeSettings])

  const isDenied = useMemo(() => {
    if (isLicenseeDenied) {
      return true
    }
    if (role) {
      if (userPermissions && userPermissions[role] >= level) {
        return false
      }
    } else {
      return false
    }
    return true
  }, [isLicenseeDenied, role, userPermissions, level])

  if (isDenied) {
    if (showDenial) {
      return (
        <View
          style={{
            flex: 1,
          }}>
          <View>{`${isLicenseeDenied ? 'Your licensee does' : 'You do'} not have permission to view this content`}</View>
        </View>
      )
    }
    return <></>
  }
  return <>{children}</>
}
