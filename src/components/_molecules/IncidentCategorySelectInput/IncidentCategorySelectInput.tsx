import React, { useCallback, useMemo } from 'react'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { sharedState } from '@src/shared-state/shared-state'
import { renderCategoryName } from '@src/lib/categories'
import { IncidentState } from '@src/shared-state/HealthSafety/incidents'
import {
  CheckBoxActions,
  SimpleSelectionData,
  TabularSelectionData,
} from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'

export interface IncidentCategorySelectInputProps {
  visible?: boolean
  categoryId?: string
  setCategoryId?: React.Dispatch<React.SetStateAction<string>>
}

export const IncidentCategorySelectInput = ({
  visible = true,
  categoryId,
  setCategoryId,
}: IncidentCategorySelectInputProps) => {
  // Shared Data
  const incidentCategories = sharedState.incidentCategories.use(visible)

  const categoryOptionsData = useMemo(() => {
    return incidentCategories?.ids
      .map(id => {
        // Check category state
        if (incidentCategories?.byId[id].state !== IncidentState.active) {
          return undefined
        }

        return {
          label: renderCategoryName(id, incidentCategories),
          value: id,
        }
      })
      .filter(item => item !== undefined)
  }, [incidentCategories])

  const onSetItems = useCallback((action: CheckBoxActions, itemIds: string[]) => {
    if (!setCategoryId || itemIds.length <= 0) return

    switch (action) {
      case CheckBoxActions.SELECT:
        setCategoryId(itemIds[0])
        return
      case CheckBoxActions.DESELECT:
      default:
        return
    }
  }, [])

  return (
    <>
      <SeaSelectInput
        label={'Event Category'}
        onSetItems={onSetItems}
        data={categoryOptionsData as SimpleSelectionData | TabularSelectionData[]}
        selectedItemValues={categoryId ? [categoryId] : undefined}
        isMulti={false}
        style={{ width: '100%' }}
      />
    </>
  )
}
