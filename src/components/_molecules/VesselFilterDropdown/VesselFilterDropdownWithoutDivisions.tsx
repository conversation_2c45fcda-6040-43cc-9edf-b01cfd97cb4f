import React, { useCallback, useMemo } from 'react'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { sharedState } from '@src/shared-state/shared-state'
import { VesselFilterDropdownProps } from './VesselFilterDropdown'

export function VesselFilterDropdownWithoutDivisions({
  vesselIds = [],
  setVesselIds,
  isMulti = true,
  style,
  noValidation = false,
}: VesselFilterDropdownProps) {
  // Shared Data
  const user = sharedState.user.use()
  const vessels = sharedState.vessels.use()

  // Vessel data for the Selector
  const vesselOptionsData = useMemo(() => {
    return (
      user?.vesselIds?.map((vesselId: string) => ({
        label: vessels?.byId[vesselId]?.name,
        value: vesselId,
      })) ?? []
    )
  }, [vessels, user])

  const onItemSelect = useCallback(
    (action: CheckBoxActions, changedValue: string) => {
      if (!setVesselIds) return

      switch (action) {
        case CheckBoxActions.SELECT:
          setVesselIds(prevState => {
            const newVesselIds = [...prevState]
            newVesselIds.push(changedValue)
            return newVesselIds
          })
          return
        case CheckBoxActions.DESELECT:
          setVesselIds(prevState => {
            return prevState.filter(vesselId => vesselId !== changedValue)
          })
          return
        default:
          return
      }
    },
    [setVesselIds]
  )

  const onSetItems = (action: CheckBoxActions, itemIds: string[]) => {
    if (!setVesselIds) return

    switch (action) {
      case CheckBoxActions.SELECT:
        setVesselIds(itemIds)
        return
      case CheckBoxActions.DESELECT:
      default:
        return
    }
  }

  const inputTextValue = useMemo(() => {
    if (vesselIds.length === vesselOptionsData.length) {
      return 'All Vessels'
    }
    return undefined
  }, [vesselIds, vessels])

  return (
    <>
      <SeaSelectInput
        modalTitle={'Vessels / Facilities'}
        data={vesselOptionsData}
        inputTextProp={inputTextValue}
        textInputStyle={{
          borderRadius: 20,
          width: '0%',
          minWidth: 150,
        }}
        onItemSelect={onItemSelect}
        onSetItems={onSetItems}
        selectedItemValues={vesselIds}
        primaryActionOnPress={() => {
          console.log('DO Nothing for now')
        }}
        isMulti={isMulti}
        style={style}
        showSelectAllOption={false}
        showSearch={true}
        noValidation={noValidation}
      />
    </>
  )
}
