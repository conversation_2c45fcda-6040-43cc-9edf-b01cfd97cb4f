import { FlatList, Modal, Pressable, StyleSheet, View } from 'react-native'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { VesselFilterDropdownProps } from './VesselFilterDropdown'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SafeAreaView } from 'react-native-safe-area-context'
import { SeaPageCard, SeaPageCardTitle } from '../SeaPageCard/SeaPageCard'
import { SeaCloseButton } from '../IconButtons/SeaCloseButton'
import { SeaCheckbox } from '@src/components/_atoms/_inputs/SeaCheckbox/SeaCheckbox'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { Division, renderVesselsListUsingDivisions } from '@src/shared-state/Core/divisions'
import { sharedState } from '@src/shared-state/shared-state'
import { Vessel } from '@src/shared-state/Core/vessel'
import { useDeviceWidth } from '@src/hooks/useDevice'

const ROW_HEIGHT = 40
const INDENT = 30
const MOBILE_INDENT = 10
type DivisionsData = {
  id: string
  label: string
  children?: DivisionsData[]
  selected?: boolean
  toggleItem: (item: DivisionsData) => void
  level: number
  parent?: DivisionsData
  disabled?: boolean
}

const TreeItem = (item: DivisionsData) => {
  const { label, children, selected, toggleItem, level, disabled } = item

  const { styles } = useStyles(styleSheet)
  const { isMobileWidth } = useDeviceWidth()
  const [isExpanded, setIsExpanded] = useState(true)
  const hasChildren = !!children?.length

  const handleChevronPress = () => {
    setIsExpanded(prev => !prev)
  }

  return (
    <View
      style={{
        paddingVertical: 2,
        paddingLeft: level !== 0 ? level + (isMobileWidth ? MOBILE_INDENT : INDENT) : 0,
        width: '100%',
      }}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: 2,
          width: '100%',
          flex: 1,
        }}>
        {(hasChildren || disabled) && level !== 0 ? (
          <Pressable onPress={handleChevronPress}>
            <SeaIcon
              icon={!disabled && isExpanded ? 'keyboard_arrow_down' : 'keyboard_arrow_right'}
              style={{
                opacity: disabled ? 0.5 : 1,
              }}
            />
          </Pressable>
        ) : level !== 0 && level !== 1 ? (
          <View
            style={{
              width: 22,
            }}></View>
        ) : (
          <></>
        )}
        <SeaCheckbox
          label={label}
          value={selected ? true : false}
          onChange={(changedValue: boolean) => {
            toggleItem({
              ...item,
              selected: changedValue,
            })
          }}
          style={StyleSheet.flatten([styles.selectionDataItem, hasChildren ? styles.selectionDivisions : {}])}
          disabled={disabled ?? false}
        />
      </View>

      {hasChildren && isExpanded && children.map(child => <TreeItem key={child.id} {...child} level={level + 1} />)}
    </View>
  )
}

export function VesselFilterDropdownWithDivisions({
  vesselIds = [],
  setVesselIds,
  style,
}: Omit<VesselFilterDropdownProps, 'isMulti' | 'hideSpacer'>) {
  const divisions = sharedState.divisions.use()

  const { styles } = useStyles(styleSheet)
  const [viewModal, setViewModal] = useState(false)
  const [treeData, setTreeData] = useState<DivisionsData | null>(null)

  const onClose = useCallback(() => {
    setViewModal(false)
  }, [])

  const selectDivision = useCallback(
    (division: Division, checked: boolean) => {
      // Apply checked to all vessel descendants
      if (!setVesselIds || division.numVesselsAccess === 0) return
      setVesselIds(current => {
        const result = [...(current ?? [])]
        const doVessel = checked
          ? (vessel: Vessel) => {
              if (vessel.canAccess && !result.includes(vessel.id)) {
                result.push(vessel.id)
              }
            }
          : (vessel: Vessel) => {
              const index = result.indexOf(vessel.id)
              if (index !== -1) {
                result.splice(index, 1)
              }
            }
        const doDivision = (division: Division) => {
          division.vessels.forEach(vessel => {
            doVessel(vessel)
          })
          division.children?.forEach(child => {
            doDivision(child)
          })
        }
        doDivision(division)

        return result
      })
    },
    [setVesselIds]
  )

  const isDivisionSelected = useMemo(() => {
    const isSelected = {} as Record<string, boolean>
    if (divisions?.root && vesselIds) {
      const isDivisionSelected = (division: Division): boolean => {
        if (division.numVesselsAccess === 0) {
          isSelected[division.id] = false
          return true // Should be counted as selected for parent
        }
        let selected = true
        if (division.children) {
          division.children.forEach(child => {
            if (!isDivisionSelected(child)) {
              selected = false
            }
          })
        }
        if (division.vessels && selected) {
          for (const vessel of division.vessels) {
            if (vessel.canAccess && !vesselIds.includes(vessel.id)) {
              selected = false
              break
            }
          }
        }
        isSelected[division.id] = selected
        return selected
      }
      isDivisionSelected(divisions.root)
    }
    return isSelected
  }, [divisions?.root, vesselIds])

  const buildDivisionsArray = useCallback(() => {
    if (!divisions?.root) return null

    const createDivisionsTree = (division: Division, level = 0, parent?: DivisionsData): DivisionsData => {
      const divisionData: DivisionsData = {
        id: division.id,
        label:
          division.id === 'root'
            ? `All Vessels (${division.numVesselsAccess})`
            : `${division.name} (${division.numVesselsAccess})`,
        level,
        toggleItem: (item: DivisionsData) => selectDivision(division, item.selected ?? false),
        children: [],
        selected: isDivisionSelected[division.id] ?? false,
        parent,
        disabled: division.numVesselsAccess === 0,
      }

      const divisionChildren: DivisionsData[] = []
      const vesselChildren: DivisionsData[] = []

      if (division.vessels) {
        const vesselNodes = division.vessels.map((vessel: Vessel) => ({
          id: vessel.id,
          label: vessel.name,
          selected: vesselIds.includes(vessel.id),
          toggleItem: (item: DivisionsData) => {
            if (!setVesselIds) return

            if (item.selected) {
              setVesselIds(prevState => [...prevState, item.id])
            } else {
              setVesselIds(prevState => prevState.filter(id => id !== item.id))
            }
          },
          level: level + 1,
          children: [],
          parent: divisionData,
        }))

        vesselChildren.push(...vesselNodes)
      }

      if (division.children) {
        const childDivision = division.children.map(childDivision =>
          createDivisionsTree(childDivision, level + 1, divisionData)
        )

        divisionChildren.push(...childDivision)
      }

      divisionData.children = [...divisionChildren, ...vesselChildren]

      return divisionData
    }

    return createDivisionsTree(divisions.root, 0)
  }, [divisions, vesselIds, setVesselIds, selectDivision])

  useEffect(() => {
    if (!divisions?.root) {
      setTreeData(null)
      return
    }

    const treeData = buildDivisionsArray()
    setTreeData(treeData)
  }, [buildDivisionsArray, divisions])

  const summaryText = useMemo(() => {
    return renderVesselsListUsingDivisions(vesselIds, undefined, divisions, 'None', 'All Vessels', true, 36)
  }, [divisions, vesselIds])

  return (
    <View style={[styles.container, style]}>
      <View style={[styles.dropdownContainer]}>
        <Pressable style={{ height: ROW_HEIGHT, overflow: 'hidden' }} onPress={() => setViewModal(!viewModal)}>
          <SeaStack justify={'between'} style={styles.stack}>
            <SeaTypography variant={'input'}>{summaryText}</SeaTypography>
            <SeaIcon icon={viewModal ? 'arrow_drop_up' : 'arrow_drop_down'} />
          </SeaStack>
        </Pressable>

        {/* Multi select - Modal Popup */}
        <Modal
          visible={viewModal}
          transparent={true}
          style={{ zIndex: 1 }}
          onRequestClose={onClose}
          animationType={'none'}>
          <SafeAreaView style={{ flex: 1 }} edges={['top', 'left', 'right']}>
            <Pressable style={styles.scrim} onPress={onClose}>
              <View style={styles.modalContainer}>
                <Pressable
                  style={{ flex: 1 }}
                  onPress={() => {
                    /* We need to wrap this in a pressable to prevent taps inside the drawer from causing a close event*/
                  }}>
                  {/** Header Section */}
                  <SeaPageCard
                    hideBreadcrumbs={true}
                    style={{
                      borderBottomRightRadius: 0,
                      borderBottomLeftRadius: 0,
                      marginBottom: 0,
                    }}
                    subNav={undefined}
                    titleComponent={<SeaPageCardTitle title={'Select Division/Vessels'} />}
                    hidePath={true}
                    primaryActionButton={<SeaCloseButton key={'close'} onPress={onClose} />}
                  />

                  {/** Content Section */}
                  <SeaStack
                    direction={'column'}
                    justify={'start'}
                    align="start"
                    style={StyleSheet.flatten([styles.content])}>
                    <FlatList
                      data={Array.isArray(treeData) ? treeData : [treeData]}
                      renderItem={({ item }) => <TreeItem {...item} level={0} />}
                      keyExtractor={item => item.id}
                      style={{
                        width: '100%',
                        flex: 1,
                      }}
                    />
                  </SeaStack>

                  {/** Footer section */}
                  <View style={[styles.footerRow]}>
                    <SeaButton
                      variant={SeaButtonVariant.Primary}
                      onPress={() => {
                        onClose()
                      }}
                      label={'Ok'}
                    />
                  </View>
                </Pressable>
              </View>
            </Pressable>
          </SafeAreaView>
        </Modal>
      </View>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: { flex: 1 },
  dropdownContainer: {},
  stack: {
    height: ROW_HEIGHT,
    borderWidth: 1,
    width: '100%',
    borderColor: theme.colors.borderColor,
    backgroundColor: theme.colors.input.background,
    paddingHorizontal: 16,
    borderRadius: 20,
    minWidth: 150,
  },
  stackError: {
    borderColor: theme.colors.status.errorPrimary,
  },

  scrim: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },

  modalContainer: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: 26,
    height: '60%',
    width: '90%',
    maxWidth: 500,
    maxHeight: '90%',
    borderBottomRightRadius: 26,
    borderBottomLeftRadius: 26,
    justifyContent: 'center',
  },
  header: {
    marginBottom: 14,
    backgroundColor: theme.colors.white,
  },
  footerRow: {
    height: 80,
    paddingHorizontal: 20,
    paddingBottom: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.borderColor,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: 16,
    backgroundColor: theme.colors.white,
    borderBottomRightRadius: 26,
    borderBottomLeftRadius: 26,
  },
  content: {
    width: '100%',
    flex: 1,
    padding: 12,
  },
  searchInput: {
    width: '100%',
    paddingVertical: 10,
  },
  selectionContent: {
    width: '100%',
    height: '100%',
  },
  selectionData: {
    gap: 20,
  },
  selectionDataItem: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    backgroundColor: theme.colors.white,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
    borderRadius: 8,
    flex: 1,
    width: '100%',
  },
  selectionDivisions: {
    backgroundColor: theme.colors.lightGrey,
  },
}))
