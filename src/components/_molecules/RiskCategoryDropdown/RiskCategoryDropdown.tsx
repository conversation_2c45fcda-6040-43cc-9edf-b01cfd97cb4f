import React from 'react'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { formatValue } from '@src/lib/util'
import { renderCategoryName } from '@src/lib/categories'
import { sharedState } from '@src/shared-state/shared-state'
import { ViewStyle } from 'react-native'

interface RiskCategoryDropdownProps {
  visible?: boolean
  categoryId?: string
  onSelect?: (value: string) => void
  style?: ViewStyle
}

export const RiskCategoryDropdown = ({
  visible = false,
  categoryId,
  onSelect = () => {},
  style,
}: RiskCategoryDropdownProps) => {
  // Shared Data
  const riskCategories = sharedState.riskCategories.use(visible)

  const items = riskCategories?.ids.map(id => {
    return {
      label: renderCategoryName(id, riskCategories),
      value: id,
    }
  })

  return (
    <>
      <SeaDropdown
        label={'Category'}
        items={items}
        value={renderCategoryName(categoryId, riskCategories)}
        onSelect={value => onSelect(value)}
        style={style}
      />
    </>
  )
}
