import React, { useMemo } from 'react'
import { StyleSheet, ViewStyle } from 'react-native'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { sharedState } from '@src/shared-state/shared-state'

interface VesselLocationsDropdownProps {
  selectedItemId: string
  onSelectItem: (itemId: string) => void
  style?: ViewStyle
}

export const VesselLocationsDropdown: React.FC<VesselLocationsDropdownProps> = ({
  selectedItemId,
  onSelectItem,
  style,
}) => {
  const vesselLocations = sharedState.vesselLocations.use()
  const options = useMemo(() => {
    if (!vesselLocations) return []

    return Object.entries(vesselLocations.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => {
        return {
          label: value.name,
          value: key,
        }
      })
  }, [vesselLocations])

  const initialValue = useMemo(() => {
    return options.find(x => x.value === selectedItemId)
  }, [options])

  return (
    <SeaDropdown
      items={options}
      initialValue={initialValue}
      onSelect={x => onSelectItem(x.value)}
      label={'Location'}
      style={[styles.base, style]}
    />
  )
}

const styles = StyleSheet.create({
  base: {
    width: '100%',
  },
})
