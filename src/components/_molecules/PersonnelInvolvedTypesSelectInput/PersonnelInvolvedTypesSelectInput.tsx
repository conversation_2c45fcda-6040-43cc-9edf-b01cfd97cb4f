import React, { useCallback, useMemo } from 'react'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { IncidentWhoInvolvedTypes } from '@src/shared-state/HealthSafety/incidents'

interface PersonnelInvolvedTypesSelectInputProps {
  isMulti?: boolean
  items?: string[]
  setItems?: React.Dispatch<React.SetStateAction<string[]>>
}

export const PersonnelInvolvedTypesSelectInput = ({ items, setItems }: PersonnelInvolvedTypesSelectInputProps) => {
  const dataItems = useMemo(() => {
    return Object.entries(IncidentWhoInvolvedTypes).map(([key, value]) => ({
      label: value,
      value: key,
    }))
  }, [])

  return (
    <>
      <SeaSelectInput
        label={'Types of personnel involved'}
        isMulti={true}
        showSelectAllOption={false}
        data={dataItems}
        selectedItemValues={items}
        style={{ width: '100%' }}
        onItemSelect={useCallback((action: CheckBoxActions, changedValue: string) => {
          if (!setItems) return

          switch (action) {
            case CheckBoxActions.SELECT:
              setItems(prevState => {
                const newIds = [...prevState]
                newIds.push(changedValue)
                return newIds
              })
              return
            case CheckBoxActions.DESELECT:
              setItems(prevState => {
                return prevState.filter(id => id !== changedValue)
              })
              return
            default:
              return
          }
        }, [])}
      />
    </>
  )
}
