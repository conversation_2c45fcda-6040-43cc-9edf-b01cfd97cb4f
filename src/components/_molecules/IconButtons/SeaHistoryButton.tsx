import { SeaButton, SeaButtonProps, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import React from 'react'

export const SeaHistoryButton = (props: SeaButtonProps) => {
  const { variant = SeaButtonVariant.Tertiary, iconOptions, ...rest } = props

  return (
    <SeaButton
      key={'history'}
      variant={variant}
      iconOptions={{
        icon: 'history',
      }}
      {...rest}
    />
  )
}
