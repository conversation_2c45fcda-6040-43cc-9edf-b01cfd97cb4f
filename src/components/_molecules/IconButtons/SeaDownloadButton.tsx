import React from 'react'
import { SeaButton, SeaButtonProps, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'

export const SeaDownloadButton = (props: SeaButtonProps) => {
  const { variant = SeaButtonVariant.Tertiary, iconOptions, ...rest } = props

  return (
    <SeaButton
      key={'download'}
      variant={variant}
      iconOptions={{
        icon: 'download',
        size: 20,
      }}
      {...rest}
    />
  )
}
