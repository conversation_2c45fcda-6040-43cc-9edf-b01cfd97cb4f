import React from 'react'
import { SeaButton, SeaButtonProps, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { useDeviceWidth } from '@src/hooks/useDevice'

export const SeaEditButton = (props: SeaButtonProps) => {
  const { variant = SeaButtonVariant.Tertiary, iconOptions, ...rest } = props

  return (
    <SeaButton
      key={'edit'}
      variant={variant}
      iconOptions={{
        icon: 'edit_square',
        fill: false,
      }}
      {...rest}
    />
  )
}
