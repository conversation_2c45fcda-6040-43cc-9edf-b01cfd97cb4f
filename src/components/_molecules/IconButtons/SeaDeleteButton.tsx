import React from 'react'
import { SeaButton, SeaButtonProps, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'

export const SeaDeleteButton = (props: SeaButtonProps) => {
  const { variant = SeaButtonVariant.Tertiary, iconOptions, ...rest } = props

  return (
    <SeaButton
      key={'delete'}
      variant={variant}
      iconOptions={{
        icon: 'delete',
        fill: false,
      }}
      {...rest}
    />
  )
}
