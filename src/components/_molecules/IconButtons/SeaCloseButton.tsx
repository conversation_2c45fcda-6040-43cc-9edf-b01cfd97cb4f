import React from 'react'
import { SeaButton, SeaButtonProps, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'

export const SeaCloseButton = (props: SeaButtonProps) => {
  const { variant = SeaButtonVariant.Tertiary, iconOptions, ...rest } = props

  return (
    <SeaButton
      key={'close'}
      variant={variant}
      iconOptions={{
        icon: 'close',
      }}
      {...rest}
    />
  )
}
