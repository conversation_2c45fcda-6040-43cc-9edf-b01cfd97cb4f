import React from 'react'
import { SeaButton, SeaButtonProps, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'

export const SeaSettingsButton = (props: SeaButtonProps) => {
  const { variant = SeaButtonVariant.Tertiary, iconOptions, ...rest } = props

  return (
    <SeaButton
      key={'settings'}
      variant={variant}
      iconOptions={{
        icon: 'settings',
      }}
      {...rest}
    />
  )
}
