import React from 'react'
import { SeaButton, SeaButtonProps, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'

export const SeaCheckButton = (props: SeaButtonProps) => {
  const { variant = SeaButtonVariant.Tertiary, iconOptions, ...rest } = props

  return (
    <SeaButton
      key={'check'}
      variant={variant}
      iconOptions={{
        icon: 'check',
      }}
      {...rest}
    />
  )
}
