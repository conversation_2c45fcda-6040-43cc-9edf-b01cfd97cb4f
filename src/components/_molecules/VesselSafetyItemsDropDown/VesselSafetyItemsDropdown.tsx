import React, { useMemo } from 'react'
import { StyleSheet, ViewStyle } from 'react-native'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { sharedState } from '@src/shared-state/shared-state'

interface VesselSafetyItemsDropdownProps {
  selectedItemId: string
  onSelectItem: (itemId: string) => void
  style?: ViewStyle
}

export const VesselSafetyItemsDropdown: React.FC<VesselSafetyItemsDropdownProps> = ({
  selectedItemId,
  onSelectItem,
  style,
}) => {
  const vesselSafetyItems = sharedState.vesselSafetyItems.use()
  const vesselSafetyItemOptions = useMemo(() => {
    if (!vesselSafetyItems) return []

    return Object.entries(vesselSafetyItems.byId)
      .filter(([key, value]) => value.state === 'active')
      .map(([key, value]) => {
        return {
          label: value.name,
          value: key,
        }
      })
  }, [vesselSafetyItems])

  const initialValue = useMemo(() => {
    return vesselSafetyItemOptions.find(x => x.value === selectedItemId)
  }, [vesselSafetyItemOptions])

  return (
    <SeaDropdown
      items={vesselSafetyItemOptions}
      initialValue={initialValue}
      onSelect={x => onSelectItem(x.value)}
      label={'Item'}
      style={[styles.base, style]}
    />
  )
}

const styles = StyleSheet.create({
  base: {
    width: '100%',
  },
})
