import React from 'react'
import { StyleProp, StyleSheet, Text, ViewStyle } from 'react-native'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { formatInterval } from '@src/lib/datesAndTime'

// TODO - Extract to a constants file
export const AVAILABLE_INTERVALS = [
  '1d',
  '7d',
  '14d',
  '1m',
  '5w',
  '2m',
  '3m',
  '4m',
  '5m',
  '6m',
  '9m',
  '12m',
  '18m',
  '24m',
  '30m',
  '36m',
  '48m',
  '60m',
  '72m',
  '96m',
  '120m',
  '180m',
]

interface IntervalDropdownProps {
  value: string
  onChange: (val: string) => void
  style?: StyleProp<ViewStyle>
  label?: string
  hasError?: boolean
  errorText?: string
}

export const IntervalDropdown: React.FC<IntervalDropdownProps> = ({
  value,
  onChange,
  style,
  label = 'Interval',
  hasError = false,
  errorText,
}) => {
  const options = AVAILABLE_INTERVALS.map(interval => ({
    label: formatInterval(interval),
    value: interval,
  }))

  return (
    <SeaDropdown
      value={value}
      onSelect={val => onChange(val)}
      label={label}
      items={options}
      style={[styles.base, style]}
      hasError={hasError}
      errorText={errorText}
    />
  )
}

const styles = StyleSheet.create({
  base: { width: '100%' },
})
