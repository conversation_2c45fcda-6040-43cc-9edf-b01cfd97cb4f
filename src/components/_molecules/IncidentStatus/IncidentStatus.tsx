import React, { useMemo } from 'react'
import { IncidentState } from '@src/shared-state/HealthSafety/incidents'
import { SeaStatusType } from '@src/types/Common'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { renderCamelCase } from '@src/lib/util'

interface IncidentStatusProps {
  status: IncidentState
  compact?: boolean
}

export const IncidentStatus = ({ status, compact: compactProp = false }: IncidentStatusProps) => {
  const { primaryLabel, compact } = useMemo(() => {
    if (compactProp) {
      if (status === IncidentState.forReview) {
        return {
          primaryLabel: 'FR',
          compact: false,
        }
      }
      if (status === IncidentState.inReview) {
        return {
          primaryLabel: 'IR',
          compact: false,
        }
      }
      return {
        primaryLabel: renderCamelCase(status),
        compact: true,
      }
    }

    return {
      primaryLabel: renderCamelCase(status),
      compact: false,
    }
  }, [status, compactProp])

  return <SeaStatusPill primaryLabel={primaryLabel} variant={getStatusForIncidentState(status)} compact={compact} />
}

/**
 * Get the SeaStatusType based on the Incident's/Event's reported status
 *
 * @param statusLabel - status of the Incident
 * @return SeaStatusType - type
 */
export const getStatusForIncidentState = (statusLabel: IncidentState): SeaStatusType => {
  switch (statusLabel) {
    case IncidentState.deleted:
      return SeaStatusType.Minor
    case IncidentState.draft:
      return SeaStatusType.Draft
    case IncidentState.forReview:
      return SeaStatusType.Warning
    case IncidentState.inReview:
      return SeaStatusType.Attention
    case IncidentState.completed:
    default:
      return SeaStatusType.Ok
  }
}
