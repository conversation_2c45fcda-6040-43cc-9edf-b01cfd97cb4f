import { SeaActionButton, SeaActionButtonProps } from '@src/components/_atoms/SeaActionButton/SeaActionButton'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { colors } from '@src/theme/colors'
import React, { useEffect, useState } from 'react'
import { Dimensions, StyleSheet, useWindowDimensions, View, ViewStyle } from 'react-native'

export interface QuickAction extends SeaActionButtonProps {}

interface VesselQuickActionsProps {
  style?: ViewStyle
  quickActions: QuickAction[]
  vesselImages?: string[]
}

const getColumnNumber = () => {
  const screenWidth = Dimensions.get('window').width

  if (screenWidth > 1200) return 4 // For desktops or large screens
  if (screenWidth > 768) return 2 // For tablets or medium screens
  return 1 // Default for mobile
}

export const VesselQuickActions: React.FC<VesselQuickActionsProps> = ({ style, quickActions, vesselImages }) => {
  const [containerWidth, setContainerWidth] = useState(0)
  const [columnsNumber, setColumnsNumber] = useState(getColumnNumber())
  const { width } = useWindowDimensions()

  useEffect(() => {
    const updateColumns = () => setColumnsNumber(getColumnNumber())
    const subscription = Dimensions.addEventListener('change', updateColumns)

    return () => subscription?.remove()
  }, [])

  return (
    <View style={[styles.container, style]}>
      <View style={styles.row} onLayout={event => setContainerWidth(event.nativeEvent.layout.width)}>
        {quickActions.map(action => (
          <SeaActionButton
            key={action.label}
            label={action.label}
            onPress={action.onPress}
            iconOptions={{
              ...action.iconOptions,
              size: 30,
            }}
            viewStyle={{
              width: containerWidth / columnsNumber - 15,
              minWidth: 120,
              maxWidth: 500,
            }}
          />
        ))}
      </View>
      {vesselImages && width > 1200 && (
        <View style={styles.vesselImagesDiv}>
          <SeaFileImage files={vesselImages} size="medium" mode="vessel" />
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background.primary,
    borderRadius: 20,
    padding: 20,
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 20,
  },
  vesselImagesDiv: {
    width: 220,
    height: 120,
    borderRadius: 0,
    backgroundColor: '#e9eaed',
  },
  row: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    gap: 16,
    flexWrap: 'wrap',
  },
})
