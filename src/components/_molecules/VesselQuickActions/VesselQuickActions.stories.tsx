import { View } from 'react-native'

import type { Meta, StoryObj } from '@storybook/react'
import { VesselQuickActions } from './VesselQuickActions'

const meta = {
  title: 'Components/Molecules/VesselQuickActions',
  component: VesselQuickActions,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof VesselQuickActions>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    quickActions: [
      {
        label: '34 Actions in 7 days',
        onPress: () => alert('34 Actions in 7 days'),
        iconOptions: {
          icon: 'assignment_turned_in',
        },
      },
      {
        label: 'Start New Voyage',
        onPress: () => alert('Start New Voyage'),
        iconOptions: {
          icon: 'support',
        },
      },
      {
        label: 'Log Incident',
        onPress: () => alert('Log Incident'),
        iconOptions: {
          icon: 'health_and_safety',
        },
      },
      {
        label: 'Survey',
        onPress: () => alert('Survey'),
        iconOptions: {
          icon: 'markdown_paste',
        },
      },
      {
        label: 'Update Engine Hours',
        onPress: () => alert('Update Engine Hours'),
        iconOptions: {
          icon: 'schedule',
        },
      },
      {
        label: 'Complete Form',
        onPress: () => alert('Complete Form'),
        iconOptions: {
          icon: 'description',
        },
      },
      {
        label: 'Log Risk Assessment',
        onPress: () => alert('Log Risk Assessment'),
        iconOptions: {
          icon: 'health_and_safety',
        },
      },
      {
        label: 'Vessel Details',
        onPress: () => alert('Vessel Details'),
        iconOptions: {
          icon: 'directions_boat_filled',
        },
      },
    ],
    vesselImages: ['23mxpgwuPHmcUg8gC6JuS_B1D2E21A-39E6-42A2-B80C-5382DE8B1554.jpeg'],
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
