import React from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

interface RiskRatingStatusProps {
  likelihoodId: number | string
  consequenceId: number | string
}

export const RiskRatingStatus = ({ likelihoodId, consequenceId }: RiskRatingStatusProps) => {
  // Get matrix from licenseeSettings
  const matrix = sharedState?.licenseeSettings.use(1)?.riskRegister.matrix
  if (!matrix) {
    return <></>
  }

  const { styles } = useStyles(styleSheet)

  const raw = matrix[`${likelihoodId}-${consequenceId}`] || ''
  const colour = raw.substring(0, 6)
  const ratingStatus = raw.substring(7).toUpperCase()

  return (
    <SeaTypography
      variant={'value'}
      textStyle={styles.label}
      numberOfLines={1}
      containerStyle={[
        styles.container,
        {
          backgroundColor: `#${colour}`,
          borderColor: `#${colour}`,
        },
      ]}>
      {ratingStatus}
    </SeaTypography>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: 5,
    paddingVertical: 4,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 0,
  },
  label: {
    fontSize: 12,
    fontWeight: 700,
    flexDirection: 'row',
    paddingHorizontal: 4,
  },
}))
