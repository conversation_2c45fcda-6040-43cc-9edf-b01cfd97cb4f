import React, { useMemo } from 'react'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { sharedState } from '@src/shared-state/shared-state'
import { renderFullName } from '@src/shared-state/Core/users'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { StyleProp, ViewStyle } from 'react-native'

interface PersonnelPresentSelectInputProps {
  visible?: boolean
  vesselIds?: string[]
  personnelPresentIds?: string[]
  setPersonnelPresentIds?: React.Dispatch<React.SetStateAction<string[]>>
  style?: StyleProp<ViewStyle>
}

export const PersonnelPresentSelectInput = ({
  visible = false,
  vesselIds,
  personnelPresentIds,
  setPersonnelPresentIds,
  style,
}: PersonnelPresentSelectInputProps) => {
  // Shared Data
  const users = sharedState.users.use(visible)

  const personnelPresentOptions = useMemo(() => {
    if (vesselIds && vesselIds.length > 0) {
      if (vesselIds.length === 1) {
        return users?.byVesselId[vesselIds[0]]?.map(u => {
          return {
            label: renderFullName(u),
            value: u.id!,
          }
        })
      } else {
        return users?.staff
          .filter(u => u.vesselIds?.some(r => vesselIds.includes(r)))
          .map(u => {
            return {
              label: renderFullName(u),
              value: u.id!,
            }
          })
      }
    }
    return undefined
  }, [users, vesselIds])

  const onItemSelect = (action: CheckBoxActions, changedValue: string) => {
    if (!setPersonnelPresentIds) return

    switch (action) {
      case CheckBoxActions.SELECT:
        setPersonnelPresentIds(prevState => {
          const newVesselIds = [...prevState]
          newVesselIds.push(changedValue)
          return newVesselIds
        })
        return
      case CheckBoxActions.DESELECT:
        setPersonnelPresentIds(prevState => {
          return prevState.filter(vesselId => vesselId !== changedValue)
        })
        return
      default:
        return
    }
  }

  return (
    <>
      <SeaSelectInput
        label={'Personnel Present'}
        data={personnelPresentOptions}
        onItemSelect={onItemSelect}
        selectedItemValues={personnelPresentIds}
        primaryActionOnPress={() => {
          console.log('DO Nothing for now')
        }}
        visible={true}
        style={style}
      />
    </>
  )
}
