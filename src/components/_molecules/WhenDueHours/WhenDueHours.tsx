import React from 'react'
import { StyleSheet } from 'react-native'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { DateTime } from 'luxon'
import { SeaStatusType } from '@src/types/Common'

interface WhenDueHoursProps {
  engineHoursLeft: number
  warnHoursThreshold?: number
  compact?: boolean
}

export const WhenDueHours: React.FC<WhenDueHoursProps> = ({
  engineHoursLeft,
  warnHoursThreshold = 50,
  compact = false,
}) => {
  const { label, variant } = getLabelAndVariant(engineHoursLeft, warnHoursThreshold)

  return <SeaStatusPill label={label} variant={variant} compact={compact} />
}

const getLabelAndVariant = (
  engineHoursLeft: number,
  warnHoursThreshold: number
): { label: string; variant: SeaStatusType } => {
  if (engineHoursLeft === 0) {
    return { label: 'Due now', variant: SeaStatusType.Error }
  }

  if (engineHoursLeft < 0) {
    // Overdue
    return {
      label: `${Math.abs(engineHoursLeft)}hrs`,
      variant: SeaStatusType.Error,
    }
  }

  if (engineHoursLeft < warnHoursThreshold) {
    // Warning
    return {
      label: `${Math.abs(engineHoursLeft)}hrs`,
      variant: SeaStatusType.Warning,
    }
  }

  return {
    label: `${Math.abs(engineHoursLeft)}hrs`,
    variant: SeaStatusType.Ok,
  }
}
