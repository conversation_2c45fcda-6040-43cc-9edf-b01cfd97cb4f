import React from 'react'
import { StyleSheet, Text, View, ViewStyle } from 'react-native'
import { SeaHeaderBar } from '@src/components/_atoms/SeaHeaderBar/SeaHeaderBar'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { colors } from '@src/theme/colors'

interface SeaLayoutProps {
  children: React.ReactNode
  style?: ViewStyle
}

export const SeaLayout: React.FC<SeaLayoutProps> = ({ children, style }) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = React.useState(false)
  return (
    <View style={[styles.container, style]}>
      <SeaHeaderBar />
      <SeaStack direction={'row'} style={styles.stack}>
        <View style={styles.content}>{children}</View>
      </SeaStack>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
  },
  stack: {
    height: '100%',
  },
  content: {
    height: '100%',
    width: '100%',
    paddingTop: 30,
    paddingHorizontal: 20,
    backgroundColor: colors.white,
  },
})
