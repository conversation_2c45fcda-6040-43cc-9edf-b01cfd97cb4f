import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { Text, View } from 'react-native'
import { SeaLayout } from './SeaLayout'

const meta = {
  title: 'Components/Molecules/SeaLayout',
  component: SeaLayout,
  decorators: [
    Story => {
      return <Story />
    },
  ],
} satisfies Meta<typeof SeaLayout>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    children: (
      <View>
        <Text>Example Content</Text>
      </View>
    ),
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
