import { Pressable } from 'react-native'
import React from 'react'
import { ActionLogEntry } from '@src/shared-state/General/actionLog'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { formatDatetimeLonger, formatTime } from '@src/lib/datesAndTime'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { LicenseeSettings } from '@src/shared-state/Core/licenseeSettings'
import { regions } from '@src/lib/util'
import { SeaAvatar } from '@src/components/_atoms/SeaAvatar/SeaAvatar'
import { sharedState } from '@src/shared-state/shared-state'

export const formatAction = (action: ActionLogEntry, licenseeSettings: LicenseeSettings | undefined) => {
  const getDefaultVerb = (action: ActionLogEntry): string => {
    switch (action.action) {
      case 'A':
        return 'Added'
      case 'U':
        return 'Updated'
      case 'N':
        return 'Renewed'
      case 'V':
        return 'Archived'
      case 'R':
        return 'Unarchived'
      case 'D':
        return 'Deleted'
    }
    return 'Updated'
  }
  let verb = getDefaultVerb(action)
  const getNoun = (action: ActionLogEntry): string => {
    switch (action.collection) {
      case 'engines':
        return 'Engine'
      case 'vessels':
        if (action.type) {
          if (action.type === 'logbookSettings') return 'Logbook Settings'
          if (action.type === 'safetyMeetingSettings') return 'Safety Meeting Settings'
        }
        return 'Vessel Settings'
      case 'voyages':
        if (action.type) {
          if (action.type === 'progress') return 'Voyage Progress'
          if (action.type === 'complete') {
            verb = 'Completed'
            return 'Voyage'
          }
        }
        return 'Voyage'
      case 'safetyCheckItems':
        return 'Safety Check'
      case 'safetyCheckCompleted':
        if (action.action === 'A') {
          verb = 'Completed'
          return 'Safety Check'
        }
        return 'Completed Safety Check'
      case 'safetyEquipmentItems':
        return 'Safety Equipment'
      case 'safetyEquipmentTaskCompleted':
        if (action.action === 'A') {
          verb = 'Completed'
          return 'Safety Check'
        }
        return 'Completed Safety Check'
      case 'hazards':
        return 'Hazard'
      case 'risks':
        return 'Risk Assessment'
      case 'risksReviewed':
        if (action.action === 'A') {
          verb = 'Reviewed'
          return 'Risk Assessment'
        }
        return 'Risk Assessment Review'
      //case 'medicalReports': return 'Incident/Accident/Medical Report';
      case 'incidents':
        return 'Incident / Event Report'
      case 'incidentReviews':
        return 'Incident / Event Review'
      case 'drills':
        return 'Drill'
      case 'drillReports':
        return 'Drill Report'
      case 'scheduledMaintenanceTasks':
        return 'Scheduled Maintenance Task'
      case 'maintenanceTasksCompleted':
        if (action.type) {
          if (action.type === 'scheduled') {
            if (action.action === 'A') {
              verb = 'Completed'
              return 'Scheduled Maintenance Task'
            }
            return 'Completed Scheduled Maintenance Task'
          } else if (action.type === 'job') {
            verb = 'Completed'
            return 'Job'
          }
        }
        return 'Completed Maintenance Task'
      case 'jobs':
        return 'Job'
      case 'spareParts':
        return 'Spare Part'
      case 'equipment':
        return 'Equipment'
      case 'vesselCertificates':
        return 'Vessel Certificate'
      case 'vesselDocuments':
        return 'Vessel Document'
      case 'surveyReports':
        return 'Survey Document'
      case 'SOPs':
        return 'SOP'
      case 'SOPsCompleted':
        if (action.action === 'A') {
          verb = 'Completed'
          return 'SOP'
        }
        return 'Completed SOP'
      case 'safetyMeetingReports':
        return 'Safety Meeting Report'
      case 'dangerousGoods':
        return 'Dangerous Good'
      case 'equipmentManualDocuments':
        return 'Equipment Manual Document'
      case 'customForms':
        return 'Form/Checklist'
      case 'customFormsCompleted':
        if (action.action === 'A') {
          verb = 'Completed'
          return 'Form/Checklist'
        }
        return 'Completed Form/Checklist'
      case 'users':
        return 'User'
      case 'userPermissions':
        return 'User Permissions'
      case 'userDocuments':
        return 'User Document(s)'
      case 'voyageDocuments':
        return 'Voyage Document(s)'
      case 'crewCertificates':
        return 'Crew Certificate'
      case 'trainingTasks':
        return 'Crew Training Task'
      case 'trainingTaskReports':
        if (action.action === 'A') {
          verb = 'Completed'
          return 'Crew Training'
        }
        // else deleted
        verb = ''
        return 'Crew Removed from Completed Training'
      case 'trainingTaskReportDocuments':
        if (action.action === 'A') {
          verb = 'Added'
          return 'Crew Training Document(s)'
        }
        return 'Crew Training Document(s)'
      case 'contacts':
        return 'Contact/Supplier'
      case 'companyPlans':
        if (!licenseeSettings) {
          return ''
        }
        return regions[licenseeSettings?.region]?.companyPlanShort
      case 'companyDocuments':
        return 'Company Document'
      case 'userPermissionDefaults':
        return 'User Permission Default'
      case 'correctiveActions':
        if (action.action === 'A') {
          verb = 'Completed'
          return 'Corrective Action'
        }
        return 'Corrective Action'
    }
    return 'Something'
  }
  const noun = getNoun(action)
  return `${noun} ${verb}`
}

interface ActionLogProps {
  log: ActionLogEntry
}
export function ActionLog({ log }: ActionLogProps) {
  const licenseeSettings = sharedState.licenseeSettings.use()

  const { styles, theme } = useStyles(styleSheet)

  return (
    <Pressable disabled style={{ flex: 1 }}>
      <SeaStack direction="row" gap={12} style={styles.log}>
        <SeaAvatar />
        <SeaStack direction="column" gap={4} align="start" justify="start" style={{ flex: 1, width: '100%' }}>
          <SeaStack direction="row" gap={8} style={{ flex: 1, width: '100%' }} align="center" justify="start">
            <SeaTypography variant="subtitle" textStyle={{ fontSize: 14 }}>
              {renderFullNameForUserId(log.userId)}
            </SeaTypography>
            <SeaIcon icon="fiber_manual_record" size={6} color={theme.colors.grey} />
            <SeaTypography
              variant="body"
              containerStyle={{ flex: 1 }}
              textStyle={{
                color: theme.colors.grey,
              }}>
              {formatDatetimeLonger(log.when, ' ')}
            </SeaTypography>
          </SeaStack>
          <SeaTypography
            variant="body"
            ellipsizeMode="tail"
            numberOfLines={2}
            textStyle={{
              color: '#616367',
            }}>
            {formatAction(log, licenseeSettings)} - {log.detail}
          </SeaTypography>
        </SeaStack>
      </SeaStack>
    </Pressable>
  )
}

const styleSheet = createStyleSheet(theme => ({
  log: {
    flex: 1,
    width: '100%',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderColor,
  },
}))
