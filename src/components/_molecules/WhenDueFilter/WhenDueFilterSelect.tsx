import React, { Dispatch, SetStateAction } from 'react'
import { StyleSheet, Text, ViewStyle } from 'react-native'
import { LabelAndValue, SeaSelectModal } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { warnDays } from '@src/lib/datesAndTime'

export interface WhenDueFilter {
  all: boolean
  overdue: boolean
  critical?: boolean
  dueWithin: { days: number; selected: true }[]
}

interface WhenDueFilterSelectProps {
  visible: boolean
  section: keyof typeof warnDays
  showCritical: boolean
  value: WhenDueFilter
  onChange: (filter: WhenDueFilter) => void
  onClose: () => void
}

export const WhenDueFilterSelect: React.FC<WhenDueFilterSelectProps> = ({
  section,
  showCritical,
  visible,
  onClose,
}) => {
  const data = buildDataForSection(section, showCritical)

  return <SeaSelectModal title={'Select When Due'} data={data} visible={visible} onClose={onClose} />
}

const buildDataForSection = (section: keyof typeof warnDays, showCritical: boolean) => {
  const result: LabelAndValue[] = [
    { label: 'All', value: 'all' },
    { label: 'Overdue', value: 'overdue' },
  ]

  if (showCritical) {
    result.push({ label: 'Critical', value: 'critical' })
  }

  const warn = warnDays[section]

  const warnOptions: LabelAndValue[] = warn.map(x => ({
    label: `Due within ${x} days`,
    value: x.toString(), // TODO - support numbers
  }))

  // TODO - Might need to return a stronger type for the selection values?
  return [...result, ...warnOptions]
}

const styles = StyleSheet.create({
  container: {},
})
