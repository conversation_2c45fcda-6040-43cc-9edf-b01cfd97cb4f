import React from 'react'
import { SeaTableIcon } from '@src/components/_atoms/SeaTable/SeaTableIcon'
import { SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { colors } from '@src/theme/colors'

export interface SeaTableIconProps extends Omit<SeaIconProps, 'icon'> {}

export const SeaTableIconPerson = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'person'} {...props} />
}

export const SeaTableIconCalendar = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'calendar_month'} {...props} />
}

export const SeaTableIconInterval = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'update'} {...props} />
}

export const SeaTableIconVessel = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'directions_boat_filled'} {...props} />
}

export const SeaTableIconLocation = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'location_on'} {...props} />
}

export const SeaTableIconFlag = (props: SeaTableIconProps) => {
  return (
    <SeaTableIcon
      icon={'flag'}
      size={22}
      {...props}
      // TODO: Ask Matt about color consistency
      // TODO: Ask Matt about color consistency
      // TODO: Ask Matt about color consistency
      // color={colors.grey}
      // color={colors.text.placeholder}
    />
  )
}

export const SeaTableIconMail = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'mail'} size={22} {...props} />
  // TODO: Ask Matt about color consistency
  // TODO: Ask Matt about color consistency
  // TODO: Ask Matt about color consistency
  // color={colors.grey}
  // color={colors.text.placeholder}
}

export const SeaTableIconIssuedBy = (props: SeaTableIconProps) => {
  return <SeaTableIcon icon={'fact_check'} {...props} />
}
