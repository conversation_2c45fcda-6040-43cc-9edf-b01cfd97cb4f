import React from 'react'
import { SeaStatusPill } from '@src/components/_atoms/SeaStatusPill/SeaStatusPill'
import { DateTime } from 'luxon'
import { SeaStatusType } from '@src/types/Common'

interface WhenDueStatusProps {
  whenDue: string
  warnDaysThreshold: number
  hasFault: boolean
  compact?: boolean
}

export const WhenDueStatus: React.FC<WhenDueStatusProps> = ({
  whenDue,
  warnDaysThreshold,
  hasFault,
  compact = false,
}) => {
  const { label, variant } = getLabelAndVariant(whenDue, warnDaysThreshold, hasFault)

  return label && variant ? <SeaStatusPill label={label} variant={variant} compact={compact} /> : <></>
}

const getLabelAndVariant = (
  whenDue: string,
  warnDaysThreshold: number,
  hasFault: boolean
): { label: string; variant: SeaStatusType } => {
  if (hasFault) {
    return { label: 'FAULT', variant: SeaStatusType.Critical }
  }

  const whenDueDatetime = DateTime.fromISO(whenDue).toLocal()
  const now = DateTime.now().toLocal()
  const diff = whenDueDatetime.diff(now, 'days').toObject().days

  if (!diff) {
    // TODO - Handle this better
    throw new Error('Unable to calculate whenDue status')
  }

  const daysDiff = Math.floor(diff)

  if (diff < 0) {
    // Overdue
    const days = Math.abs(daysDiff)
    return { label: `${days}d`, variant: SeaStatusType.Error }
  }

  if (diff > 0 && diff < warnDaysThreshold) {
    // Warning
    return { label: `${daysDiff}d`, variant: SeaStatusType.Warning }
  }

  return { label: `${daysDiff}d`, variant: SeaStatusType.Ok }
}
