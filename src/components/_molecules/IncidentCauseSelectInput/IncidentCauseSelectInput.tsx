import React, { useCallback, useMemo } from 'react'
import { SeaSelectInput } from '@src/components/_atoms/_inputs/SeaSelectInput/SeaSelectInput'
import { sharedState } from '@src/shared-state/shared-state'
import { IncidentState } from '@src/shared-state/HealthSafety/incidents'
import { CheckBoxActions } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'

export interface IncidentCauseSelectInputProps {
  visible?: boolean
  causeIds?: string[]
  setCauseIds?: React.Dispatch<React.SetStateAction<string[]>>
}

export const IncidentCauseSelectInput = ({
  visible = true,
  causeIds = [],
  setCauseIds,
}: IncidentCauseSelectInputProps) => {
  // Shared Data
  const incidentCauses = sharedState.incidentCauses.use(visible)

  const causeOptionsData = useMemo(() => {
    return incidentCauses?.ids
      .map(id => {
        // Check state
        const causeItem = incidentCauses?.byId[id]
        if (causeItem.state !== IncidentState.active) {
          return undefined
        }

        return {
          label: causeItem.name,
          value: id,
        }
      })
      .filter(item => item !== undefined)
  }, [incidentCauses])

  const onItemSelect = useCallback(
    (action: CheckBoxActions, changedValue: string) => {
      if (!setCauseIds) return

      switch (action) {
        case CheckBoxActions.SELECT:
          setCauseIds(prevState => {
            const newIds = [...prevState]
            newIds.push(changedValue)
            return newIds
          })
          return
        case CheckBoxActions.DESELECT:
          setCauseIds(prevState => {
            return prevState.filter(id => id !== changedValue)
          })
          return
        default:
          return
      }
    },
    [setCauseIds]
  )

  const onSetItems = useCallback((action: CheckBoxActions, itemIds: string[]) => {
    if (!setCauseIds || itemIds.length <= 0) return

    switch (action) {
      case CheckBoxActions.SELECT:
        setCauseIds(itemIds)
        return
      case CheckBoxActions.DESELECT:
      default:
        return
    }
  }, [])

  return (
    <>
      <SeaSelectInput
        label={'Contributing Factors of this Incident/Event'}
        selectedItemValues={causeIds}
        isMulti={true}
        data={causeOptionsData}
        // data={[]}
        onItemSelect={onItemSelect}
        onSetItems={onSetItems}
        showSelectAllOption={false}
      />
    </>
  )
}
