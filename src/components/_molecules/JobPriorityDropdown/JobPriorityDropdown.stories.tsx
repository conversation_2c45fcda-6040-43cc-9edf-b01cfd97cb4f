import type { Meta, StoryObj } from '@storybook/react'
import React from 'react'
import { View } from 'react-native'
import { JobPriorityDropdown } from './JobPriorityDropdown'

const meta = {
  title: 'Components/Molecules/JobPriorityDropdown',
  component: JobPriorityDropdown,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof JobPriorityDropdown>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    onSelect: item => {},
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
