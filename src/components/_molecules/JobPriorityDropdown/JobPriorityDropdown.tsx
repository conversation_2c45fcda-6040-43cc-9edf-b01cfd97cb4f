import React from 'react'
import { StyleSheet, Text, View, ViewStyle } from 'react-native'
import { SeaDropdown, SeaDropdownItem } from '@src/components/_atoms/SeaDropdown/SeaDropdown'

export enum JobPriorityLevel {
  Urgent = 'urgent',
  High = 'high',
  Medium = 'medium',
  Low = 'low',
  Shipyard = 'shipyard',
}

interface JobPriorityDropdownProps {
  onSelect: (value: JobPriorityLevel) => void
  style?: ViewStyle
}

export const JobPriorityDropdown: React.FC<JobPriorityDropdownProps> = ({ onSelect, style }) => {
  const items = [
    { label: 'Urgent', value: JobPriorityLevel.Urgent },
    { label: 'High', value: JobPriorityLevel.High },
    { label: 'Medium', value: JobPriorityLevel.Medium },
    { label: 'Low', value: JobPriorityLevel.Low },
    { label: 'Shipyard', value: JobPriorityLevel.Shipyard },
  ] as SeaDropdownItem<JobPriorityLevel>[]
  const [selected, setSelected] = React.useState(items[0])

  return (
    <SeaDropdown
      items={items}
      value={selected}
      onSelect={item => {
        setSelected(item)
        onSelect(item.value)
      }}
      label={'Priority'}
      style={style}
    />
  )
}

const styles = StyleSheet.create({
  container: {},
})
