import React from 'react'
import { ControlType } from '@src/components/_organisms/HealthSafety/RiskRegister/ViewRegister'
import { SeaDropdown } from '@src/components/_atoms/SeaDropdown/SeaDropdown'
import { sharedState } from '@src/shared-state/shared-state'

export interface RiskRatingDropdownProps {
  type: 'likelihoods' | 'consequences'
  onSelect: (value: string) => void
  value?: string
  label: string
}

type AdditionalProps = 'type' | 'label'

export const RiskRatingDropdown = ({ onSelect, type, value, label }: RiskRatingDropdownProps) => {
  if (!type) return <></>

  // Shared Data
  const licenseeSettings = sharedState.licenseeSettings.use()
  const registerItems = licenseeSettings?.riskRegister[type]

  const dropdownData = Object.keys(registerItems ?? {})
    .sort()
    .map(id => {
      return {
        label: (registerItems?.[id] as ControlType).name,
        value: id?.substring(1),
      }
    })
    // Filter to remove additional items from the object list
    .filter(dataItem => !isNaN(dataItem?.value))

  return (
    <SeaDropdown
      label={label}
      items={dropdownData}
      value={`${value}`}
      onSelect={onSelect}
      style={{ flex: 1, width: '100%' }}
    />
  )
}

/** ++++++++++++++++++++ Risk Rating Likelihood Dropdown ++++++++++++++++++++ */
interface RiskRatingLikelihoodDropdownProps extends Omit<RiskRatingDropdownProps, AdditionalProps> {
  onSelect: (value: string) => void
  value?: string
}

export const RiskRatingLikelihoodDropdown = ({ onSelect, value }: RiskRatingLikelihoodDropdownProps) => {
  return <RiskRatingDropdown label={'Likelihood'} onSelect={onSelect} value={value} type={'likelihoods'} />
}

/** ++++++++++++++++++++ Risk Rating Consequence Dropdown ++++++++++++++++++++ */
interface RiskRatingConsequenceDropdownProps extends Omit<RiskRatingDropdownProps, AdditionalProps> {
  onSelect: (value: string) => void
  value?: string
}

export const RiskRatingConsequenceDropdown = ({ onSelect, value }: RiskRatingConsequenceDropdownProps) => {
  return (
    <>
      <RiskRatingDropdown label={'Consequence'} onSelect={onSelect} value={value} type={'consequences'} />
    </>
  )
}
