import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import React, { ReactNode } from 'react'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { StyleSheet, View } from 'react-native'

interface SeaSectionSubtitleProps {
  children: ReactNode
  color?: string
  showLeftBorder?: boolean
  showIcon?: boolean
}

export const SeaSectionSubtitle = ({
  children,
  color,
  showLeftBorder = false,
  showIcon = false,
}: SeaSectionSubtitleProps) => {
  const { styles, theme } = useStyles(styleSheet)

  return (
    <View style={styles.container}>
      <SeaStack direction={'row'} align={'center'} gap={10} style={styles.innerContainer}>
        <SeaTypography
          variant="subtitle"
          containerStyle={StyleSheet.flatten([
            styles.textContainerStyle,
            showLeftBorder ? styles.leftBorderStyle : {},
            color ? { borderLeftColor: color } : {},
          ])}
          textStyle={styles.textTextStyle}
          color={color ?? theme.colors.primary}>
          {children}
        </SeaTypography>
        {showIcon && <SeaIcon icon="north_east" size={20} color={theme.colors.primary} />}
      </SeaStack>
      <View style={styles.dottedLine} />
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    width: '100%',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  innerContainer: {
    flex: 1,
    width: '100%',
  },
  textContainerStyle: {
    marginBottom: 0,
  },
  textTextStyle: {},
  leftBorderStyle: {
    paddingLeft: 8,
    borderLeftWidth: 4,
    paddingVertical: 3,
    borderLeftColor: theme.colors.primary,
  },
  dottedLine: {
    width: '100%',
    height: 0,
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
    borderRadius: 1,
    marginVertical: 10,
  },
}))
