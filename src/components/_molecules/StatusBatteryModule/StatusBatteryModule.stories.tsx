import { View } from 'react-native'

import type { Meta, StoryObj } from '@storybook/react'
import { StatusBatteryModule } from './StatusBatteryModule'
import { SeaStatusType } from '@src/types/Common'
import { SeaStatusBattery } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBattery'

const meta = {
  title: 'Components/Molecules/StatusBatteryModule',
  component: StatusBatteryModule,
  decorators: [
    Story => {
      return (
        <View
          style={{
            padding: 16,
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}>
          <Story />
        </View>
      )
    },
  ],
} satisfies Meta<typeof StatusBatteryModule>

export default meta

type Story = StoryObj<typeof meta>

export const batteries = [
  {
    id: 'safety-check',
    label: 'Safety Check',
    statuses: [
      {
        status: SeaStatusType.Critical,
        count: 3,
        onPress: () => alert('Critical'),
      },
      {
        status: SeaStatusType.Error,
        count: 20,
        onPress: () => alert('Error'),
      },
      {
        status: SeaStatusType.Warning,
        count: 14,
        onPress: () => alert('Warning'),
      },
      { status: SeaStatusType.Ok, count: 12, onPress: () => alert('Ok') },
    ],
  },
  {
    id: 'safety-equipment-expiries',
    label: 'Safety Equipment Expiries',
    statuses: [
      {
        status: SeaStatusType.Error,
        count: 3,
        onPress: () => alert('Error'),
      },
      {
        status: SeaStatusType.Warning,
        count: 20,
        onPress: () => alert('Warning'),
      },
      {
        status: SeaStatusType.Attention,
        count: 14,
        onPress: () => alert('Attention'),
      },
      { status: SeaStatusType.Ok, count: 12, onPress: () => alert('Ok') },
    ],
  },
  {
    id: 'drills',
    label: 'Drills',
    statuses: [{ status: SeaStatusType.Ok, count: 300, onPress: () => alert('Ok') }],
  },
]

export const Default: Story = {
  args: {
    title: 'Battery Status',
    iconOptions: {
      icon: 'health_and_safety',
    },
    subModules: batteries.map(battery => (
      <SeaStatusBattery key={battery.id} label={battery.label} statuses={battery.statuses} />
    )),
  },
  parameters: {
    backgrounds: {
      default: 'White',
    },
  },
}
