import { SeaIcon, SeaIconProps } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaStatusBattery } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBattery'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import React, { ReactElement } from 'react'
import { StyleSheet, View, ViewStyle } from 'react-native'

interface StatusBatteryModuleProps {
  title: string
  iconOptions: SeaIconProps
  style?: ViewStyle
  subModules: ReactElement<typeof SeaStatusBattery>[]
}

export const StatusBatteryModule: React.FC<StatusBatteryModuleProps> = ({ title, iconOptions, style, subModules }) => {
  const { styles } = useStyles(styleSheet)

  return (
    <SeaStack
      direction="row"
      style={StyleSheet.flatten([styles.wrapper, style])}
      justify="start"
      align="start"
      gap={20}>
      <View style={styles.iconWrapper}>
        <SeaIcon {...iconOptions} size={24} color="#75787D" />
      </View>
      <SeaStack direction="column" style={{ flex: 1 }} align="start" justify="start">
        <View style={[styles.moduleWrapper, styles.bottomBorder]}>
          <SeaTypography
            variant="title"
            containerStyle={{ marginBottom: 0 }}
            textStyle={{
              fontSize: 16,
              marginBottom: 0,
            }}>
            {title}
          </SeaTypography>
        </View>
        <View
          style={{
            flex: 1,
            width: '100%',
          }}>
          {subModules.map((module, index) => (
            <View
              key={index}
              style={[styles.moduleWrapper, index !== subModules.length - 1 ? styles.bottomBorder : {}]}>
              {module}
            </View>
          ))}
        </View>
      </SeaStack>
    </SeaStack>
  )
}

const styleSheet = createStyleSheet(theme => ({
  wrapper: {
    paddingVertical: 20,
  },
  iconWrapper: {
    borderRadius: 8,
    padding: 8,
    backgroundColor: theme.colors.background.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moduleWrapper: {
    flex: 1,
    width: '100%',
    paddingVertical: 12,
  },
  bottomBorder: {
    borderBottomWidth: 1,
    borderColor: theme.colors.borderColor,
  },
}))
