import React, { useMemo, useState } from 'react'
import { Pressable, StyleSheet, Text, View, ViewStyle } from 'react-native'
import { SeaSelectModal, SimpleSelectionData } from '@src/components/_atoms/_inputs/SeaSelectModal/SeaSelectModal'
import { sharedState } from '@src/shared-state/shared-state'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'

interface AssignUserModalProps {
  style?: ViewStyle
}

export const AssignUserModal: React.FC<AssignUserModalProps> = ({ style }) => {
  const [showSelect, setShowSelect] = useState(false)

  const vesselId = sharedState.vesselId.use()
  const users = sharedState.users.use()
  const userOptions = useMemo(() => {
    if (!vesselId || !users) return []

    return users.byVesselId[vesselId].map(x => ({
      label: renderFullNameForUserId(x.id),
      value: x.id,
    })) as SimpleSelectionData
  }, [])

  return (
    <>
      <Pressable onPress={() => setShowSelect(true)}>
        <Text>Assign User</Text>
      </Pressable>
      <SeaSelectModal
        title={'Assigned To'}
        data={userOptions}
        visible={showSelect}
        onClose={() => setShowSelect(false)}
      />
    </>
  )
}

const styles = StyleSheet.create({
  container: {},
})
