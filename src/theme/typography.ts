export const fontFamily = {
  BODY_FONT: 'body',
  BODY_100_FONT: 'body100',
  BODY_200_FONT: 'body200',
  BODY_300_FONT: 'body300',
  BODY_400_FONT: 'body400',
  BODY_500_FONT: 'body500',
  BODY_600_FONT: 'body600',
  BODY_700_FONT: 'body700',
  BODY_800_FONT: 'body800',
  BODY_900_FONT: 'body900',
  TITLE_FONT: 'title',
  TITLE_BOLD_FONT: 'titleBold',
  TITLE_LIGHT_FONT: 'titleLight',
  TITLE_MEDIUM_FONT: 'titleMedium',
  TITLE_SEMI_BOLD_FONT: 'titleSemiBold',

  MATERIAL_ICON_OUTLINED: 'materialIconOutlined',
  MATERIAL_ICON_ROUNDED: 'materialIconRounded',
  MATERIAL_ICON_SHARP: 'materialIconSharp',
}

// export const mobileText = {
//   display: {
//     fontFamily: fontFamily.BODY_FONT,
//     fontSize: 19,
//     lineHeight: 28,
//   },
// };
//
// export const desktopText = {
//   display: {
//     fontFamily: fontFamily.BODY_FONT,
//     fontSize: 32,
//     lineHeight: 45,
//   },
// };

export const getTypography = () => {
  return {
    fontFamily,
  }
}
