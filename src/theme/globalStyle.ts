const colors = {
  primary: '#004896',
  status_bar: '#373946',
  sidebar_header: '#545561',
  grey: '#7e808b',
  grey_rgb: '126, 128, 139',
  icon_grey: '#bfc4ca',
  header: '#373946',
  sea_flux_bg: '#373946',
  light: '#f5f6f8',
  light_blue: '#4076B0',
  yellow_orange: '#F5A623',
  red: '#ff2c2c',
  white: '#ffffff',
  black: '#000000',
  completed: '#3caf6a',
  danger: '#eb445a',
  draft: '#cef2fe',
  warn_rgb: '253, 243, 209',
  fail_rgb: '245, 215, 215',
  text_on_white: '#373946', // Header
  text_on_grey: '#DCDDE1',
  line_on_grey: 'rgba(255, 255, 255, 0.6)',
  label_text: '#7f818c',
  input_border_color: '#DCDDE1',
  input_background_color: '#F3F4F6',
  dashboard_row_color: '#545666',
  dashboard_row_rgb: '84, 86, 102',
  dashboard_block_color: '#656878',
  dashboard_block_rgb: '101, 103, 118',
  view_only: '#eaedf0',
  view_only_rgb: '234, 237, 240',
}

const theme = {
  colors: {
    primary: {
      main: '#004896',
      rgb: '0,72,150',
      contrast: '#ffffff',
      contrastRgb: '255,255,255',
      shade: '#003f84',
      tint: '#1a5aa1',
    },
    secondary: {
      main: '#0369d9',
      rgb: '3,105,217',
      contrast: '#ffffff',
      contrastRgb: '255,255,255',
      shade: '#035cbf',
      tint: '#1c78dd',
    },
    tertiary: {
      main: '#5260ff',
      rgb: '82,96,255',
      contrast: '#ffffff',
      contrastRgb: '255,255,255',
      shade: '#4854e0',
      tint: '#6370ff',
    },
    success: {
      main: '#2dd36f',
      rgb: '45,211,111',
      contrast: '#ffffff',
      contrastRgb: '255,255,255',
      shade: '#28ba62',
      tint: '#42d77d',
    },
    warning: {
      main: '#ffc107',
      rgb: '255,196,9',
      contrast: '#000000',
      contrastRgb: '0,0,0',
      shade: '#e0ac08',
      tint: '#ffca22',
    },
    danger: {
      main: '#eb445a',
      rgb: '235,68,90',
      contrast: '#ffffff',
      contrastRgb: '255,255,255',
      shade: '#cf3c4f',
      tint: '#ed576b',
    },
    dark: {
      main: '#7f818c',
      rgb: '127,129,140',
      contrast: '#000000',
      contrastRgb: '0,0,0',
      shade: '#70727b',
      tint: '#8c8e98',
    },
    medium: {
      main: '#9c9ea7',
      rgb: '156,158,167',
      contrast: '#000000',
      contrastRgb: '0,0,0',
      shade: '#898b93',
      tint: '#a6a8b0',
    },
    light: {
      main: '#f5f6f8',
      rgb: '245,246,248',
      contrast: '#000000',
      contrastRgb: '0,0,0',
      shade: '#d8d8da',
      tint: '#f6f7f9',
    },
  },
}

const dimensions = {
  sidebar_width: 265,
}

const fonts = {
  sea: 'sea',
  sea_bold: 'sea-bold',
}

const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1300,
}

export { colors, theme, dimensions, fonts, breakpoints }
