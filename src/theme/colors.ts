export const colors = {
  primary: '#007AFF',
  white: '#FFF',
  black: '#0F0F0F',
  grey: '#7A7F85',
  lightGrey: '#E2E2E2',
  text: {
    primary: '#44546F',
    secondary: '#0C7CF7',
    placeholder: '#959292',
    default: '#0f0f0f',
    body: '#616367',
    label: 'rgba(0,0,0,0.8)',
    labelValue: 'rgba(0,0,0,0.7)',
    labelIcon: 'rgba(0,0,0,0.3)',
    input: '#0F0F0F',
    isSelected: '#FFF',
  },
  input: {
    background: '#FFF',
    placeholder: '#959292',
    disabledBackground: '#E2E2E2',
  },
  background: {
    primary: '#F1F2F5',
    ok: '#E6E7EA',
    attention: '#E6E7EA',
    warning: 'rgba(255,167,38,0.2)',
    error: 'rgba(255,190,186,0.5)',
    critical: '#E6E7EA',
  },
  status: {
    ok: '#D3FFE6',
    okOutline: '#89EBB9',
    minor: '#fff3df',
    minorOutline: '#ffe19a',
    warn: '#FCE0B1',
    warnOutline: '#FEC136',
    attention: '#EFE3FF',
    attentionOutline: '#BAB3FF',
    error: '#F8392C',
    errorOutline: '#FD9996',
    critical: '#FF788A',
    criticalOutline: '#ff2d49',

    // New Status Colours
    okPrimary: '#3DC03C',
    okOutlinePrimary: '#e3efe2',
    okOutlineSecondary: '#ecf9eb',

    notApplicablePrimary: '#9E9E9E', // A neutral mid-grey for primary elements
    notApplicableOutlinePrimary: '#f2f2f2', // Light grey for subtle background or borders
    notApplicableOutlineSecondary: '#F5F5F5',

    minorPrimary: '#FEC136',
    minorOutlinePrimary: '#ffe19a',
    minorOutlineSecondary: '#ffe19a',
    warnPrimary: '#FF7716',
    warnOutlinePrimary: '#f5e7df',
    warnOutlineSecondary: '#fff1e8',

    attentionPrimary: '#A31DDC',
    attentionOutlinePrimary: '#ECDFF1',
    attentionOutlineSecondary: '#F6E8FB',

    errorPrimary: '#f83a2c',
    errorOutlinePrimary: '#f4e2e1',
    errorOutlineSecondary: '#feebea',

    criticalTextPrimary: '#ffffff',
    criticalOutlinePrimary: '#f83a2c',
    criticalTextSecondary: '#fa574a',
    criticalOutlineSecondary: '#ffffff',
  },
  borderColor: '#E2E2E2',
  darkBorderColor: '#CCCDCE',
  sidebarActiveLinks: '#C4DEFF',

  blackFivePercent: 'F8F9FA',
}

/**
 * TODO: This is temporary. I didn't want to refactor the colors object above.
 * Ideally move the colors object into this common object
 */
export const common = {
  ...colors,
}

export const lightTheme = {
  ...common,
  skeleton: {
    primary: '#007AFF',
    secondary: '#e1e1e1',
  },
}

export const darkTheme = {
  ...common,
  background: {
    primary: '#7A7F85',
  },
  skeleton: {
    primary: '#111111',
    secondary: '#333333',
  },
}

export const customTheme = {
  ...common,
  skeleton: {
    primary: '#f4f6f7',
    secondary: '#e1e1e1',
  },
}

export const getThemeColor = (colorTheme: 'light' | 'dark' | 'custom' = 'light') => {
  if (colorTheme === 'dark') return darkTheme
  if (colorTheme === 'custom') return customTheme
  return lightTheme
}

export type AppTheme = typeof lightTheme | typeof darkTheme | typeof customTheme
