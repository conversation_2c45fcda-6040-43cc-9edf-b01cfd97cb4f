import { useContext, useMemo } from 'react'
import { Appearance, StyleSheet } from 'react-native'

import { ThemeContext } from '@src/providers/ThemeProvider'

/**
 * Use when you need a stylesheet to have access to the theme (can be generated with uts)
 * @param styleSheet The style object to call theme with
 * @returns A style object that has access to the theme
 */
export const useStyles = <S,>(styleSheet?: (theme: Theme) => S): { styles: S; theme: Theme } => {
  const theme = useContext(ThemeContext)

  return {
    styles: styleSheet ? styleSheet(theme) : ({} as S),
    theme,
  }
}

/**
 * Creates a stylesheet from the passed in object
 */
export const createStyleSheet =
  <S extends StyleSheet.NamedStyles<S>>(styleSheet: (theme: Theme) => S & StyleSheet.NamedStyles<any>) =>
  (theme: Theme) =>
    useMemo(() => StyleSheet.create(styleSheet(theme)), [theme])

/**
 * Checks if the app is in dark mode
 * @returns boolean
 */
export const isDarkMode = (): boolean => {
  /** Force set to light mode for now */
  return false
  // if (__DEV__) {
  //   return false;
  // }
  //
  // return Appearance.getColorScheme() === "dark";
}
