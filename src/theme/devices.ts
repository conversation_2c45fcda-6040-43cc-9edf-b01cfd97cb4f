import { deviceType } from 'react-device-detect'

import { getDeviceSize, getDeviceWidth, isNative, isWeb, staticViewportWidth } from '@src/lib/device'

export const getDevices = () => {
  const deviceSize = getDeviceSize(staticViewportWidth)

  const { isMobileWidth, isTabletWidth, isDesktopWidth } = getDeviceWidth(deviceSize)

  // Mobile
  const isMobileNative = isNative && isMobileWidth
  const isMobileBrowser = deviceType === 'mobile'
  const isMobileDevice = isMobileNative ?? isMobileBrowser

  // Tablet
  const isTabletNative = isNative && isTabletWidth
  const isTabletBrowser = deviceType === 'tablet'
  const isTabletDevice = isTabletNative ?? isTabletBrowser

  // Desktop
  const isDesktopDevice = isWeb && !isNative && !isMobileBrowser && !isTabletBrowser

  return {
    // Screen width

    /** If a screen width is below 540 */
    isMobileWidth,
    /** If a screen width is between 540 and 850 */
    isTabletWidth,
    /** If a screen width is above 850 */
    isDesktopWidth,

    // Device type

    /** If mobile native */
    isMobileNative,
    /** If mobile and web. */
    isMobileBrowser,
    /** If mobile native OR web */
    isMobileDevice,
    /** If tablet and native */
    isTabletNative,
    /** If tablet and web */
    isTabletBrowser,
    /** If tablet native OR tablet web */
    isTabletDevice,
    /** If desktop web browser */
    isDesktopDevice,
  }
}
