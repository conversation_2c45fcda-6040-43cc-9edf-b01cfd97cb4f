import { SeaModal } from '@src/components/_atoms/SeaModal/SeaModal'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import React, { useCallback, useEffect, useState } from 'react'
import { sharedState, SharedStateConfig } from '@src/shared-state/shared-state'
import { showToast, ToastType } from '@src/managers/ToastManager/ToastManager'
import { View } from 'react-native'

export enum SaveStateAction {
  SAVING = 'saving',
  SAVED = 'saved',
  ERROR = 'error',
  SUCCESS = 'success',
}

export type SaveStateMessage = {
  action: SaveStateAction
  message?: string
  onCloseTimeout?: number
  hideFollowOnToast?: boolean
}

export const saveMessageConfig: SharedStateConfig<string> = {
  isAlwaysActive: true,
  default: '',
  notes: 'Allows components to show a Modal with the saving state.',
}

export const setSavingStateManager = (message: SaveStateMessage) => {
  sharedState.saveMessage.set(message)
}

const clearSavingStateManager = () => {
  sharedState.saveMessage.clear()
}

export const SavingStateManager = () => {
  // Shared Data
  const saveMessage = sharedState.saveMessage.use()

  // Hooks
  const [isVisible, setIsVisible] = useState(false)

  const sendToast = useCallback(
    (toastMessage: { message: string; type: ToastType }) => {
      if (!saveMessage?.hideFollowOnToast) {
        showToast(toastMessage)
      }
    },
    [saveMessage?.hideFollowOnToast]
  )

  useEffect(() => {
    if (saveMessage?.action) {
      setIsVisible(true)

      // If we have a timeout, then auto close the modal
      const timeout = saveMessage?.onCloseTimeout
      if (timeout && !isNaN(timeout)) {
        setTimeout(() => {
          setIsVisible(false)

          // Set a success toast
          sendToast({
            message: 'Successfully saved!',
            type: ToastType.SUCCESS,
          })
          clearSavingStateManager()
        }, timeout)
      } else {
        switch (saveMessage?.action) {
          case SaveStateAction.SUCCESS: {
            setIsVisible(false)
            // Set a success toast
            sendToast({
              message: getDefaultMessage(saveMessage?.action),
              type: ToastType.SUCCESS,
            })
            clearSavingStateManager()
            break
          }
          case SaveStateAction.ERROR: {
            setIsVisible(false)
            // Set an error toast
            sendToast({
              message: getDefaultMessage(saveMessage?.action),
              type: ToastType.ERROR,
            })
            clearSavingStateManager()
            break
          }
        }
      }
    }
  }, [saveMessage?.action, saveMessage?.onCloseTimeout])

  return (
    <>
      {isVisible && (
        <SeaModal
          visible={isVisible}
          maxWidth={500}
          onClose={() => {
            /* Do nothing */
          }}>
          <SeaStack align={'center'} justify={'center'} direction={'column'}>
            <SeaLoadingSpinner />
            <SeaTypography variant={'input'}>
              {saveMessage?.message ?? getDefaultMessage(saveMessage?.action)}
            </SeaTypography>
          </SeaStack>
        </SeaModal>
      )}
    </>
  )
}

const getDefaultMessage = (action?: SaveStateAction) => {
  switch (action) {
    case SaveStateAction.SAVING:
      return 'Saving and uploading...'
    case SaveStateAction.SAVED:
      return 'Saved!'
    case SaveStateAction.ERROR:
      return 'Failed to Save!'
    case SaveStateAction.SUCCESS:
      return 'Successfully saved!'
    default:
      return 'Saving...'
  }
}
