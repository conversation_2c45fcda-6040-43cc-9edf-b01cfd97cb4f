import React, { useEffect, useMemo, useState } from 'react'
import * as ExpoFileSystem from 'expo-file-system'
import { View, Text, StyleSheet, Pressable, Platform } from 'react-native'
import {
  cachedFiles,
  CachedFileType,
  filesDirectory,
  fileSyncProfiling,
} from '@src/shared-state/FileSyncSystem/cachedFiles'
import { sharedState } from '@src/shared-state/shared-state'
import { formatDatetime } from '@src/lib/datesAndTime'

const CachedFilesTab: React.FC = () => {
  const fileSyncStatus = sharedState.fileSyncStatus.use()
  const [fileStats, setFileStats] = useState<any>()
  const cacheStats = useMemo(() => {
    const stats = {
      entries: 0,
      states: {} as any,
      collections: {} as any,
      stored: {} as any,
      zeroSizes: 0,
      filesDirectory: filesDirectory,
    } as any

    Object.values(cachedFiles).forEach(entry => {
      stats.entries++
      const state = entry[0]
      const collection = entry[3] as string
      const files = entry[5]

      if (stats.states[state] === undefined) {
        stats.states[state] = 0
      }
      stats.states[state]++

      if (stats.collections[collection] === undefined) {
        stats.collections[collection] = 0
      }
      stats.collections[collection]++

      Object.keys(files).forEach(type => {
        if (stats.stored[type] === undefined) {
          stats.stored[type] = 0
        }
        if (files[type as CachedFileType] === 0) {
          stats.zeroSizes++
        } else {
          stats.stored[type]++
        }
      })
    })

    return stats
  }, [])

  // Gather fileStats
  useEffect(() => {
    let isActive = true
    const path = `${filesDirectory}/${sharedState.licenseeId.current}`
    if (Platform.OS === 'web') {
      setFileStats({
        [path]: 'No file listing implemented for web',
      })
    } else {
      ExpoFileSystem.getInfoAsync(filesDirectory)
        .then(info => {
          if (isActive) {
            if (info.exists) {
              return ExpoFileSystem.readDirectoryAsync(path)
            }
          }
        })
        .then(info => {
          setFileStats({
            [path]: `${info?.length} files found in cache. The first one is ${info?.[0]}`,
          })
        })
    }

    return () => {
      isActive = false
    }
  }, [])

  return (
    <Pressable>
      <View>
        <Text style={style.head}>Sync Status</Text>
        <Text>{JSON.stringify(fileSyncStatus, undefined, 4)}</Text>
        <Text style={style.head}>Performance</Text>
        <View style={{ width: 650 }}>
          {fileSyncProfiling?.map((perf, index) => {
            return (
              <Pressable key={`${perf.when}${index}`} style={[style.row, style.card]}>
                <View style={{ flexBasis: 120 }}>
                  <Text style={{ fontSize: 12, textAlign: 'left' }}>{formatDatetime(perf.when, ' - ')}</Text>
                </View>
                <View style={{ flexBasis: 200 }}>
                  <Text style={{ fontSize: 12, textAlign: 'left' }}>{perf.what}</Text>
                </View>
                <View style={{ flexBasis: 80 }}>
                  <Text style={{ fontSize: 12, textAlign: 'right', paddingRight: 12 }}>{perf.took.toFixed(1)}</Text>
                </View>
                <View style={{}}>
                  <Text style={{ fontSize: 12, textAlign: 'left' }}>{perf.notes}</Text>
                </View>
              </Pressable>
            )
          })}
        </View>
        <Text style={style.head}>Cache Stats</Text>
        <Text>{JSON.stringify(cacheStats, undefined, 4)}</Text>
        <Text style={style.head}>File Stats</Text>
        <Text>{JSON.stringify(fileStats, undefined, 4)}</Text>
      </View>
    </Pressable>
  )
}

const style = StyleSheet.create({
  head: {
    paddingTop: 8,
    fontWeight: 'bold',
  },
  row: {
    flexDirection: 'row',
    columnGap: 8,
    padding: 8,
    maxWidth: 1000,
  },
  card: {
    backgroundColor: '#ffffff',
    borderRadius: 10,
    marginBottom: 4,
  },
})

export default CachedFilesTab
