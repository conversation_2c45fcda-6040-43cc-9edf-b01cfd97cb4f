import React, { useCallback } from 'react'
import { View, StyleSheet } from 'react-native'
import { DeleteableDocument, SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import { FieldValue } from '@src/lib/firebase/services/firestore.service'
import { SeaModal } from '@src/components/_atoms/SeaModal/SeaModal'

import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'

//
// <PERSON>les asking the user to confirm a particular action
//

interface ExtendedDeleteableDocument extends DeleteableDocument {
  state: 'deleted'
  touched: FieldValue
}

export type ConfirmDialog = {
  show: boolean
  header?: string
  confirmText: string
  cancelText: string
  subHeader?: string
  message?: string
  onConfirmed: () => void
  onCancelled: () => void
}

export const confirmDialogConfig: SharedStateConfig<ConfirmDialog> = {
  isAlwaysActive: true,
  default: {
    show: false,
    confirmText: 'Yes',
    cancelText: 'Cancel',
    onConfirmed: () => undefined,
    onCancelled: () => undefined,
  },
  notes: 'Allows components to request confirmation from the user for a particular action.',
}

export const confirmAction = (
  header = 'Are you sure?',
  confirmText?: string,
  cancelText?: string,
  subHeader?: string,
  message?: string
): Promise<any> => {
  return new Promise((resolve, reject) => {
    sharedState.confirmDialog.set({
      show: true,
      header,
      confirmText: confirmText ?? 'Yes',
      cancelText: cancelText ?? 'Cancel',
      subHeader,
      message,
      onConfirmed: () => {
        console.log('confirmed!')
        resolve('confirmed')
      },
      onCancelled: () => {
        console.log('cancelled!')
        reject()
      },
    })
  })
}

export const deleteIfConfirmed = ({
  itemType = 'item',
  confirmHeader = undefined,
  confirmText = undefined,
  confirmCancel = 'Cancel',
  onConfirmed,
  onCancelled,
}: {
  itemType?: string
  confirmHeader?: string
  confirmText?: string
  confirmCancel?: string
  onConfirmed?: () => void
  onCancelled?: () => void
}): Promise<any> => {
  return confirmAction(
    confirmHeader ?? `Are you sure you want to delete this ${itemType}?`,
    confirmText ?? `Yes, delete`,
    confirmCancel,
    undefined,
    undefined
  )
    .then(() => {
      if (onConfirmed) {
        onConfirmed()
      }
    })
    .catch(() => {
      if (onCancelled) {
        onCancelled()
      }
    })
}

const ConfirmDialogManager: React.FC = () => {
  const confirmDialog = sharedState.confirmDialog.use()!

  const onCloseConfirm = useCallback(
    (confirmed: boolean) => {
      sharedState.confirmDialog.set(
        current =>
          ({
            ...current,
            show: false,
          }) as ConfirmDialog
      )

      if (confirmed) {
        confirmDialog.onConfirmed()
      } else {
        confirmDialog.onCancelled()
      }
    },
    [confirmDialog]
  )

  if (!confirmDialog.show) return null

  return (
    <SeaModal
      visible={confirmDialog.show}
      title={confirmDialog.header}
      onClose={() => onCloseConfirm(false)}
      style={{
        width: 400,
        maxWidth: '90%',
      }}>
      <View style={styles.dialog}>
        <View style={styles.buttonContainer}>
          <SeaButton
            key={'confirm-false'}
            variant={SeaButtonVariant.Secondary}
            label={confirmDialog.cancelText}
            onPress={() => onCloseConfirm(false)}
            viewStyle={{ minWidth: 100 }}
          />
          <SeaButton
            key={'confirm-true'}
            variant={SeaButtonVariant.Primary}
            label={confirmDialog.confirmText}
            onPress={() => onCloseConfirm(true)}
            viewStyle={{ minWidth: 100 }}
          />
        </View>
      </View>
    </SeaModal>
  )
}

const styles = StyleSheet.create({
  dialog: {
    elevation: 5,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
})

export default ConfirmDialogManager
