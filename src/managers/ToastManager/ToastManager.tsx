import React from 'react'
import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'
import SeaToast from '@src/components/_legacy/SeaToast/SeaToast'

export enum ToastType {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
}

export type ToastStateMessage = {
  message: string
  type: ToastType
}

export const toastMessageConfig: SharedStateConfig<string> = {
  isAlwaysActive: true,
  default: '',
  notes: 'Allows components to show a toast message.',
}

export const showToast = ({ message, type = ToastType.INFO }: { message: string; type?: ToastType }) => {
  sharedState.toastMessage.set({ message, type })
}

const ToastManager: React.FC = () => {
  const toastMessage = sharedState.toastMessage.use()
  return (
    <SeaToast
      message={toastMessage?.message ?? ''}
      type={toastMessage?.type}
      setMessage={sharedState.toastMessage.set}
    />
  )
}

export default ToastManager
