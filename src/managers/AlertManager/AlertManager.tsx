// AlertManager.tsx
import React, { useState, useEffect } from 'react'
import { View, Text, Modal, TouchableOpacity, StyleSheet, Platform } from 'react-native'
import { SharedStateConfig, sharedState } from '@src/shared-state/shared-state'

export type AlertMessage = string | number | React.ReactNode | (() => void) | undefined

export const alertMessageConfig: SharedStateConfig<AlertMessage> = {
  isAlwaysActive: true,
  default: '',
  notes: 'Allows components to show an alert message.',
}

export const alertMessage = (message?: AlertMessage) => {
  sharedState.alertMessage.set(message)
}

export const todo = (message: string) => {
  sharedState.alertMessage.set(`${message}\n\nThis feature has not been implemented yet.`)
}

const AlertManager: React.FC = () => {
  const alertMessage = sharedState.alertMessage.use()
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    setVisible(!!alertMessage)
  }, [alertMessage])

  const handleDismiss = () => {
    setVisible(false)
    sharedState.alertMessage.set('')
  }

  if (!alertMessage) return null

  return (
    <Modal transparent visible={visible} onRequestClose={handleDismiss} animationType="fade">
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          {typeof alertMessage === 'string' || typeof alertMessage === 'number' ? (
            <Text style={styles.modalText}>{alertMessage}</Text>
          ) : typeof alertMessage === 'function' ? (
            <Text style={styles.modalText}>Function cannot be rendered</Text>
          ) : (
            alertMessage
          )}
          <TouchableOpacity style={styles.button} onPress={handleDismiss}>
            <Text style={styles.buttonText}>OK</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
  button: {
    borderRadius: 20,
    padding: 10,
    elevation: 2,
    backgroundColor: '#2196F3',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
})

export default AlertManager
