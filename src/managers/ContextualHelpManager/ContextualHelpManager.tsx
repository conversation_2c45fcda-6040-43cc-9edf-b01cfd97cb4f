import { sharedState } from '@src/shared-state/shared-state'
import { SeaHelp } from '@src/components/_legacy/SeaContextualHelp/SeaContextualHelp'
import { getExtFromString, getImgSrcFromExt, getImgSrcFromString, isImage } from '@src/lib/files'
import { getCachedFileSrc } from '@src/shared-state/FileSyncSystem/cachedFiles'
import { SharedStateConfig } from '@src/shared-state/shared-state'

export type ContextualHelp = {
  event: any
  showPopover: boolean
  help?: SeaHelp
  imgSrcs?: string[]
}

export const contextualHelpConfig: SharedStateConfig<ContextualHelp> = {
  isAlwaysActive: true,
  default: {
    event: undefined,
    showPopover: false,
    help: undefined,
    imgSrcs: undefined,
  },
  notes: 'Allows components to show contextual help.',
}

export const openContextualHelp = (help: SeaHelp, event?: any) => {
  if (event) {
    event.stopPropagation()
    // event.persist(); // No longer needed in React 17+
  }
  if (help.files && help.files.length > 0) {
    const promises = [] as Promise<any>[]
    const imgSrcs = [] as string[]
    help.files.forEach((file: string, index: number) => {
      const ext = getExtFromString(file)
      if (isImage(ext)) {
        promises.push(
          getCachedFileSrc(file, 'F')
            .then(src => {
              imgSrcs[index] = src
            })
            .catch(e => {
              return getImgSrcFromString(file, index === 0 ? 'medium' : 'tiny').then(_imgSrc => {
                imgSrcs[index] = _imgSrc
              })
            })
        )
      } else {
        imgSrcs[index] = getImgSrcFromExt(ext, 'medium')
      }
    })
    Promise.all(promises).then(() => {
      // setContextualHelp({
      //     event: event,
      //     showPopover: true,
      //     help: help,
      //     imgSrcs: imgSrcs
      // });
      sharedState.contextualHelp.set({
        event: event,
        showPopover: true,
        help: help,
        imgSrcs: imgSrcs,
      })
    })
  } else {
    // setContextualHelp({
    //     event: event,
    //     showPopover: true,
    //     help: help,
    //     imgSrcs: undefined
    // });
    sharedState.contextualHelp.set({
      event: event,
      showPopover: true,
      help: help,
      imgSrcs: undefined,
    })
  }
}
