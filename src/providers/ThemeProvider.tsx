import React, { PropsWithChildren, createContext, useEffect, useState, useMemo } from 'react'
import { theme } from '@src/theme'
import { getThemeColor } from '@src/theme/colors'
import { getTypography } from '@src/theme/typography'
import { getDeviceWidth, isJest } from '@src/lib/device'
import { isDarkMode } from '@src/theme/styles'

export const ThemeContext = createContext<Theme>(theme)

export const ThemeProvider = ({ children, appTheme = theme }: PropsWithChildren<{ appTheme?: Theme }>) => {
  const [newTheme, setNewTheme] = useState(appTheme)

  // Get the colour theme
  const colorTheme = useMemo(() => {
    return isDarkMode() ? 'dark' : 'light'
  }, [isDarkMode])

  useEffect(() => {
    setNewTheme({
      ...appTheme,
      colors: getThemeColor(colorTheme),
    })
  }, [colorTheme])

  return <ThemeContext.Provider value={newTheme}>{children}</ThemeContext.Provider>
}
