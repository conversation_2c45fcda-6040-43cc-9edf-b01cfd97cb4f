import { useNetInfo } from '@react-native-community/netinfo'
import { router } from 'expo-router'
import React, { PropsWithChildren, useEffect } from 'react'
import { Routes } from '@src/navigation/constants'
import { getRoutePath } from '@src/navigation/utils'

export const NetworkProvider = ({ children }: PropsWithChildren) => {
  const { isConnected } = useNetInfo()

  useEffect(() => {
    if (isConnected !== null && !isConnected) {
      // router.replace("/network");
      /** Temporarily redirect users to the login page when network is not available */
      //router.replace(getRoutePath(Routes.LOGIN));
    }
  }, [isConnected])

  return <>{children}</>
}
