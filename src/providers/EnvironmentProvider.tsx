import { useFonts } from 'expo-font'
import React, { PropsWithChildren } from 'react'
import { StyleSheet, View } from 'react-native'

export const globalFonts = {
  body: require('@assets/fonts/Inter-VariableFont.ttf'),
  body100: require('@assets/fonts/Inter-VariableFont-100-Thin.ttf'),
  body200: require('@assets/fonts/Inter-VariableFont-200-ExtraLight.ttf'),
  body300: require('@assets/fonts/Inter-VariableFont-300-Light.ttf'),
  body400: require('@assets/fonts/Inter-VariableFont-400-Regular.ttf'),
  body500: require('@assets/fonts/Inter-VariableFont-500-Medium.ttf'),
  body600: require('@assets/fonts/Inter-VariableFont-600-SemiBold.ttf'),
  body700: require('@assets/fonts/Inter-VariableFont-700-Bold.ttf'),
  body800: require('@assets/fonts/Inter-VariableFont-800-ExtraBold.ttf'),
  body900: require('@assets/fonts/Inter-VariableFont-900-Black.ttf'),
  title: require('@assets/fonts/Poppins-Regular.ttf'),
  titleBold: require('@assets/fonts/Poppins-Bold.ttf'),
  titleLight: require('@assets/fonts/Poppins-Light.ttf'),
  titleMedium: require('@assets/fonts/Poppins-Medium.ttf'),
  titleSemiBold: require('@assets/fonts/Poppins-SemiBold.ttf'),
  materialIconOutlined: require('@assets/fonts/google-material-icons/MaterialSymbolsOutlined.ttf'),
  materialIconRounded: require('@assets/fonts/google-material-icons/MaterialSymbolsRounded.ttf'),
  materialIconSharp: require('@assets/fonts/google-material-icons/MaterialSymbolsSharp.ttf'),
}

export const EnvironmentProvider = ({ children }: PropsWithChildren) => {
  const [fontsLoaded] = useFonts(globalFonts)

  if (!fontsLoaded) {
    return null
  }

  return <View style={styles.appContainer}>{children}</View>
}

const styles = StyleSheet.create({
  appContainer: {
    flex: 1,
  },
})
