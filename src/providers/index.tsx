import React from 'react'
import { PropsWithChildren } from 'react'

import { ThemeProvider } from '@src/providers/ThemeProvider'
import { NetworkProvider } from '@src/providers/NetworkProvider'
import { AuthProvider } from '@src/providers/AuthProvider'
import { EnvironmentProvider } from '@src/providers/EnvironmentProvider'
import { ServiceProvider } from '@src/providers/ServiceProvider'
import { BottomSheetProvider } from './BottomSheetProvider'

export const Providers = ({
  children,
  theme,
}: PropsWithChildren<{
  theme?: Theme
}>) => (
  <NetworkProvider>
    <EnvironmentProvider>
      <AuthProvider>
        <ThemeProvider appTheme={theme}>
          <ServiceProvider>
            <BottomSheetProvider>{children}</BottomSheetProvider>
          </ServiceProvider>
        </ThemeProvider>
      </AuthProvider>
    </EnvironmentProvider>
  </NetworkProvider>
)
