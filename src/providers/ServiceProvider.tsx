import React, { createContext, useContext } from 'react'
import { Container } from 'inversify'
import { ServiceContainer } from '@src/domain/di/ServiceContainer'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { SERVICES } from '@src/domain/di/ServiceRegistry'

const ServiceContext = createContext<Container | null>(null)

export const ServiceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <ServiceContext.Provider value={ServiceContainer}>{children}</ServiceContext.Provider>
}

export const useServiceContainer = () => {
  const container = useContext(ServiceContext)
  if (!container) {
    throw new Error('useService must be used within a ServiceProvider')
  }
  return container
}

export const useLogger = (scope: string, attributes?: Record<string, string | undefined>) => {
  const container = useServiceContainer()

  const logger = container.get<ILogger>(SERVICES.ILogger)

  return logger.scoped(scope, attributes ?? {})
}
