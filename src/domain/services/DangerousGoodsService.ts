import { DocRef } from '@src/domain/data/IFirestoreService'
import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { UpdateDangerousGoodDto } from '@src/domain/use-cases/dangerousGoods/UpdateDangerousGoodsUseCase'
import { CreateDangerousGoodDto } from '@src/domain/use-cases/dangerousGoods/CreateDangerousGoodsUseCase'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { injectable } from 'inversify'

@injectable()
export class DangerousGoodsService implements IBaseDataService<CreateDangerousGoodDto, UpdateDangerousGoodDto> {
  createDataItem(
    operation: FirestoreOperation,
    createItemDto: CreateDangerousGoodDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const dangerousGoodCreatedRecord = {
      ref: operation.makeRef('dangerousGoods'),
      data: {
        ...createItemDto,
        addedBy: userId,
        whenAdded: Date.now(),
        touched: operation.serverTimestamp(),
      },
    }

    return {
      ref: dangerousGoodCreatedRecord.ref,
      records: [dangerousGoodCreatedRecord],
    }
  }

  updateDataItem(
    operation: FirestoreOperation,
    updateItemDto: UpdateDangerousGoodDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, ...rest } = updateItemDto

    const dangerousGoodUpdatedRecord = {
      ref: operation.makeRef('dangerousGoods', id),
      data: {
        ...rest,
        updatedBy: userId,
        whenUpdated: Date.now(),
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return {
      ref: dangerousGoodUpdatedRecord.ref,
      records: [dangerousGoodUpdatedRecord],
    }
  }
}
