import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { DocRef } from '../data/IFirestoreService'
import { UpdateCompanyDocumentDto } from '../use-cases/companyDocumentRegister/UpdateCompanyDocumentUseCase'
import { CreateCompanyDocumentDto } from '../use-cases/companyDocumentRegister/CreateCompanyDocumentUseCase'
import { DeleteCompanyDocumentDto } from '../use-cases/companyDocumentRegister/DeleteCompanyDocumentUseCase'

export interface ICompanyDocumentService {
  updateCompanyDocument(
    operation: FirestoreOperation,
    updateCompanyDocumentDto: UpdateCompanyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
  createCompanyDocument(
    operation: FirestoreOperation,
    createCompanyDocumentDto: CreateCompanyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
  deleteVesselDocument(
    operation: FirestoreOperation,
    deleteVesselDocumentDto: DeleteCompanyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class CompanyDocumentService implements ICompanyDocumentService {
  updateCompanyDocument(
    operation: FirestoreOperation,
    updateCompanyDocumentDto: UpdateCompanyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, dateExpires, emailReminder, interval, categoryId, dateToRemind, sfdoc, files, ...rest } =
      updateCompanyDocumentDto

    const data = {
      ...rest,
      dateExpires: dateExpires ?? operation.deleteField(),
      emailReminder: emailReminder ?? operation.deleteField(),
      interval: interval ?? operation.deleteField(),
      categoryId: categoryId ?? operation.deleteField(),
      dateToRemind: dateToRemind ?? operation.deleteField(),
      files: files ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedCompanyDocumentRecord = {
      ref: operation.makeRef('companyDocuments', id),
      data,
      options: { merge: true },
    }

    //TODO Create a new category if it doesn't exist
    //TODO: Add Links
    const licenseeTouched = this.licenseeTouched(operation, licenseeId)

    return {
      ref: updatedCompanyDocumentRecord.ref,
      records: [updatedCompanyDocumentRecord, licenseeTouched],
    }
  }

  createCompanyDocument(
    operation: FirestoreOperation,
    createCompanyDocumentDto: CreateCompanyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { dateExpires, emailReminder, interval, categoryId, dateToRemind, sfdoc, files, ...rest } =
      createCompanyDocumentDto

    const data = {
      ...rest,
      ...(dateExpires ? { dateExpires: dateExpires } : {}),
      ...(emailReminder ? { emailReminder: emailReminder } : {}),
      ...(interval ? { interval: interval } : {}),
      ...(categoryId ? { categoryId: categoryId } : {}),
      ...(dateToRemind ? { dateToRemind: dateToRemind } : {}),
      ...(files ? { files: files } : {}),
      licenseeId,
      state: 'active',
      addedBy: userId,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedCompanyDocumentRecord = {
      ref: operation.makeRef('companyDocuments'),
      data,
    }

    //TODO Create a new category if it doesn't exist
    //TODO: Add Links
    const licenseeTouched = this.licenseeTouched(operation, licenseeId)
    return {
      ref: updatedCompanyDocumentRecord.ref,
      records: [updatedCompanyDocumentRecord, licenseeTouched],
    }
  }

  deleteVesselDocument(
    operation: FirestoreOperation,
    deleteVesselDocumentDto: Omit<DeleteCompanyDocumentDto, 'title'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id } = deleteVesselDocumentDto
    const data = {
      state: 'deleted',
      deletedBy: userId,
      whenDeleted: Date.now(),
      touched: operation.serverTimestamp(),
    }
    const updatedCompanyDocumentRecord = {
      ref: operation.makeRef('companyDocuments', id),
      data,
      options: { merge: true },
    }

    const licenseeTouched = this.licenseeTouched(operation, licenseeId)

    return {
      ref: updatedCompanyDocumentRecord.ref,
      records: [updatedCompanyDocumentRecord, licenseeTouched],
    }
  }

  private licenseeTouched(operation: FirestoreOperation, licenseeId: string) {
    const licenseeData = {
      touched: operation.serverTimestamp(),
    }

    const licenseeTouched = {
      ref: operation.makeRef('whenLicenseeTouched', licenseeId),
      data: licenseeData,
      options: { merge: true },
    }

    return licenseeTouched
  }
}
