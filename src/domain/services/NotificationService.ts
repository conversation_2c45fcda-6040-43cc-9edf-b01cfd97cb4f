import { inject, injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { SERVICES } from '../di/ServiceRegistry'
import { ILogger } from '@src/domain/util/logger/ILogger'

export interface SendVesselNotificationDto {
  userName: string
  vesselId: string
  type: string
  emailMeType: string
  metadata: any
  /**
   * @param files - TODO
   */
  files: string[]
  vesselIds?: string[]
  toIds?: string[]
}

interface INotificationService {
  sendVesselNotification(
    operation: FirestoreOperation,
    userId: string,
    licenseeId: string,
    dto: SendVesselNotificationDto
  ): FirestoreRecord
}

@injectable()
export class NotificationService implements INotificationService {
  private readonly logger: ILogger
  constructor(@inject(SERVICES.ILogger) logger: ILogger) {
    this.logger = logger.scoped('NotificationService')
  }

  public sendVesselNotification(
    operation: FirestoreOperation,
    userId: string,
    licenseeId: string,
    dto: SendVesselNotificationDto
  ): FirestoreRecord {
    const { userName, vesselId, type, emailMeType, metadata, files, vesselIds, toIds } = dto

    const vesselNotificationRecord = {
      licenseeId,
      vesselIds: vesselIds ?? [vesselId],
      whenRequested: Date.now(),
      requestedBy: userId,
      type,
      ...(toIds ? { toIds } : {}),
      emailMeType,
      state: 'waiting',
      reportedBy: userName,
      files: files ?? undefined,
      item: {
        ...metadata,
      },
    }

    this.logger.debug("Created 'Add' notification record: ", {
      vesselNotificationRecord,
    })

    return {
      ref: operation.makeRef('notifications'),
      data: vesselNotificationRecord,
    }
  }
}
