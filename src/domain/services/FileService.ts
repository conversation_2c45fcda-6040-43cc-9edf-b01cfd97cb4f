import { inject, injectable } from 'inversify'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { UpdateFileDto } from '@src/domain/use-cases/files/UpdateFileUseCase'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { DocRef, IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SeaFile, seaFilesToValue } from '@src/lib/fileImports'
import { v4 as uuidv4 } from 'uuid'
import { fieldOrEmptyObject } from '@src/domain/util/DataUtils'
import { isNative, isWeb } from '@src/lib/device'
import { convertBase64toBlob } from '@src/lib/files'
import { saveFileToLocalStorage } from '@src/shared-state/FileSyncSystem/cachedFiles'
import { sharedState } from '@src/shared-state/shared-state'
import { reportError } from '@src/managers/ErrorsManager/ErrorsManager'
import { deleteFile } from '@src/shared-state/FileSyncSystem/filesToDelete'
import { uploadFileInBackground } from '@src/shared-state/FileSyncSystem/filesToUpload'
import { uploadFilesSequentially } from '@src/managers/FileUploadManager/FileUploadManager'

@injectable()
export class FileService implements IBaseDataService<SeaFile, UpdateFileDto> {
  protected readonly logger: ILogger

  constructor(
    @inject(SERVICES.IFirestoreService)
    protected readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('FileService')
  }

  /** ++++++++++++++++++++++++++++++++ Firebase operations & functions - BELOW ++++++++++++++++++++++++++++++++ */
  async executeFileDataCreation(
    files: SeaFile[],
    collection: string,
    field: string,
    userId: string,
    licenseeId?: string
  ): Promise<SeaFile[]> {
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Files',
      maximumBatchSize: 20,
    })

    const filesToUploadWithRef = files.map(_f => {
      const { ref: createdFileRef, records: fileRecords } = this.createDataItem(
        operation,
        _f,
        collection,
        field,
        userId,
        licenseeId
      )
      operation.addMany(fileRecords)

      // add the reference id to the file
      return {
        ..._f,
        state: 0,
        id: createdFileRef.id,
      }
    })

    await operation.commit()

    return filesToUploadWithRef
  }

  createDataItem(
    operation: FirestoreOperation,
    createItemDto: SeaFile,
    collection: string,
    field: string,
    userId?: string,
    licenseeId?: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const fileCreatedRecord = {
      ref: operation.makeRef('files'),
      data: {
        state: 0,
        uploadFor: {
          collection: collection,
          field: field,
        },
        ext: createItemDto.ext,
        name: createItemDto.name,
        contentType: createItemDto.contentType,
        ...fieldOrEmptyObject('lastModified', createItemDto.lastModified),
        addedBy: userId,
        licenseeIds: licenseeId ? [licenseeId] : undefined,
        whenAdded: Date.now(),
        // Create a new email token
        emailToken: uuidv4(),
      },
      options: { merge: true },
    }

    return {
      ref: fileCreatedRecord.ref,
      records: [fileCreatedRecord],
    }
  }

  async createFileDataUpdateRecord(file: UpdateFileDto, operation: FirestoreOperation) {
    const { ref, records: fileRecords } = this.updateDataItem(operation, file)
    operation.addMany(fileRecords)
  }

  updateDataItem(
    operation: FirestoreOperation,
    updateItemDto: UpdateFileDto
  ): { ref: DocRef; records: FirestoreRecord[] } {
    const { id, ...rest } = updateItemDto

    const fileCreatedRecord = {
      ref: operation.makeRef('files', id),
      data: rest,
      options: { merge: true },
    }

    return {
      ref: fileCreatedRecord.ref,
      records: [fileCreatedRecord],
    }
  }

  /** ++++++++++++++++++++++++++++++++ Firebase operations & functions - ABOVE ++++++++++++++++++++++++++++++++ */

  /**
   * This is the main entry point function to upload files to Firebase
   *
   * @param files - The files to upload
   * @param collection - The collection to upload the files to
   * @param field - The field to upload the files to which will ideally be `files`
   * @param userId - The user id of the user uploading the files
   * @param licenseeId - The licensee id of the licensee uploading the files
   */
  async uploadFiles(
    files: SeaFile[] = [],
    collection: string,
    field: string,
    userId: string,
    licenseeId?: string
  ): Promise<string[]> {
    if (!files || files.length === 0) return []

    /** Step 1: Filter out files that must be uploaded and that are already uploaded */
    const fileCategories = this.getCategoriesFiles(files)
    let { filesToUpload } = fileCategories
    const { filesUploaded } = fileCategories

    if (filesToUpload.length === 0) return seaFilesToValue(filesUploaded) ?? []

    /** Step 2: Make Firebase request */
    // Execute the file data creation and get back the Firebase ref for the files
    filesToUpload = await this.executeFileDataCreation(filesToUpload, collection, field, userId, licenseeId)

    /** Step 3: Store Files Locally & Upload */
    // Has 2 parts

    /** Step 3.1: Store Files Locally & Upload - Native */
    if (!isWeb && sharedState.licenseeSettings.current?.hasOffline) {
      const result = await this.nativeSequentialFileUpload(filesToUpload, filesUploaded)
      return seaFilesToValue(result) ?? []
    }

    /** Step 3.2: Store Files Locally & Upload - Web */
    const result = await this.webSequentialFileUpload(filesToUpload, filesUploaded, licenseeId)
    return seaFilesToValue(result) ?? []
  }

  protected async nativeSequentialFileUpload(_filesToUpload: SeaFile[], _filesUploaded: SeaFile[]): Promise<SeaFile[]> {
    return new Promise((resolve, reject) => {
      // Hybrid mode with SmartSync
      //debugApp('File Uploading', `About to saveAllFilesLocally`);
      this.saveAllFilesLocally(_filesToUpload)
        .then(() => {
          console.log(`[File Upload] Succeeded saving all files (${_filesToUpload.length})`)
        })
        .catch(error => {
          console.log(`[File Upload] Failed to save files (${_filesToUpload.length}). error=${JSON.stringify(error)}`)
          reject({ message: 'Failed to save file(s) to local storage!' })
          reportError(`Failed to upload file(s)`, error.message, error, {
            _filesToUpload,
          })
          // If there is an error, we'll want to delete the files that did save
          _filesToUpload.forEach(file => {
            deleteFile(`O${file.id}.${file.ext}`)
          })
          reject({ message: 'Failed to submit file(s)' })
        })
        .then(() => {
          _filesToUpload.forEach(file => {
            uploadFileInBackground(`O${file.id}.${file.ext}`)
          })

          /** ****************** THIS IS INCREDIBLY IMPORTANT FOR NATIVE - Below *******************/
          // Assume that the file state has been updated to 1 (uploaded)
          _filesToUpload = _filesToUpload.map(file => {
            return { ...file, state: 1 }
          })
          /** ****************** THIS IS INCREDIBLY IMPORTANT FOR NATIVE - Above ****************** */

          const finalFilesList = [..._filesUploaded, ..._filesToUpload]
          resolve(finalFilesList)
        })
    })
  }

  protected async webSequentialFileUpload(
    _filesToUpload: SeaFile[],
    _filesUploaded: SeaFile[],
    licenseeId?: string
  ): Promise<SeaFile[]> {
    return new Promise((resolve, reject) => {
      // Desktop mode OR SmartSync is off
      // Upload while you wait
      if (!sharedState.onlineStatus.current?.isOnline) {
        reject({
          message: `Failed to upload file${_filesToUpload.length === 1 ? '' : 's'} due to being offline.`,
          code: 'offline',
        })
      } else {
        //setUploading(true);
        sharedState.fileUpload.set({
          message: 'Uploading...',
          progress: 0,
          fadeIn: false,
        })

        /** Removed this because we do not have a status bar */
        // setTimeout(() => {
        //   //setFadeIn(true);
        //   sharedState.fileUpload.set((current) => {
        //     return {
        //       ...current,
        //       fadeIn: true,
        //     };
        //   });
        // }, 10);

        // Create a new Operation to update the file state
        const updateOperation = this.firestoreService.createOperation({
          operationType: 'create',
          operationDescription: 'Update Files',
          maximumBatchSize: 20,
        })

        const firebaseUpdateCallback = (updateDto: UpdateFileDto) => {
          return this.createFileDataUpdateRecord(updateDto, updateOperation)
        }

        uploadFilesSequentially(_filesToUpload, 0, licenseeId, firebaseUpdateCallback)
          .then(() => {
            if (sharedState.licenseeSettings.current?.hasOffline) {
              // Also, let's cache the files locally so they're immediately available
              this.saveAllFilesLocally(_filesToUpload)
                .catch(error => {
                  reject(error)
                  // We don't care if this fails because it's an optional nicety
                  console.log('[File Upload] saveAllFilesLocally failed (ignoring)')
                  resolve(_filesUploaded)
                })
                .then(() => {
                  console.log('[File Upload] saveAllFilesLocally completed')
                  /** Return all the files - newly uploaded and already uploaded */

                  // Commit the update operation
                  updateOperation.commit()

                  const finalFilesList = [..._filesUploaded, ..._filesToUpload]
                  resolve(finalFilesList)
                })
            } else {
              resolve(_filesUploaded)
            }
          })
          .catch(error => {
            reject(error)
          })
          .finally(() => {
            // TODO: Recheck this value
            // uploadTask = undefined;
            // TODO: Recheck this value
            // setUploading(false);
            // setUploadProgress(0.0);
            // setFadeIn(false);
            sharedState.fileUpload.clear()
            console.log('[File Upload] uploadFilesSequentially finally')
          })
      }
    })
  }

  protected saveAllFilesLocally = (files: SeaFile[]) => {
    const promises = [] as Promise<any>[]

    const getFileDataForPlatform = (_f: SeaFile) => {
      if (isWeb) {
        return convertBase64toBlob(_f.base64 as string, _f.contentType) as Blob
      } else if (isNative) {
        // For Native - Send the base64 string instead
        return _f.base64 as Blob
      }
    }

    files.forEach((file: SeaFile) => {
      const fileData = getFileDataForPlatform(file)

      if (fileData) {
        promises.push(
          saveFileToLocalStorage(
            file.id as string,
            file.ext as string,
            'O',
            fileData,
            true // special state = 0 case
          )
        )
      }
    })
    return Promise.all(promises)
  }

  protected getCategoriesFiles(files: SeaFile[] = []): {
    filesUploaded: SeaFile[]
    filesToUpload: SeaFile[]
  } {
    const filesUploaded: SeaFile[] = []
    const filesToUpload: SeaFile[] = []

    files?.forEach(_f => {
      if (
        _f.id && // Having an id means it has been loaded from the database
        _f.state !== undefined &&
        // _f.state > 0 // <--- we might end up not doing nothing if trapped on another device
        _f.state >= 0 // Do no try and upload a file if it has already been attached to this document irrespective of if the file has been uploaded or not (state = 0)
      ) {
        filesUploaded.push(_f)
      } else {
        filesToUpload.push(_f)
      }
    })

    return { filesUploaded, filesToUpload }
  }
}
