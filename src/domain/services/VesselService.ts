import { ILogger } from '@src/domain/util/logger/ILogger'
import { DocRef, IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { inject, injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'

export interface IVesselService {
  createVesselLocation(
    operation: FirestoreOperation,
    locationName: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  createVesselSafetyItem(
    operation: FirestoreOperation,
    safetyItemName: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class VesselService implements IVesselService {
  private readonly logger: ILogger
  private readonly firestoreService: IFirestoreService

  constructor(
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(SERVICES.IFirestoreService) firestoreService: IFirestoreService
  ) {
    this.logger = logger.scoped('VesselCategoriesService')
    this.firestoreService = firestoreService
  }

  public createVesselLocation(
    operation: FirestoreOperation,
    locationName: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const locationRecord = {
      ref: operation.makeRef('vesselLocations'),
      data: {
        name: locationName,
        state: 'active',
        touched: operation.serverTimestamp(),
        vesselId: vesselId,
      },
    }

    const whenVesselTouchedRecord: FirestoreRecord = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: {
        licenseeId: licenseeId,
        vesselLocations: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return { ref: locationRecord.ref, records: [locationRecord, whenVesselTouchedRecord] }
  }

  public createVesselSafetyItem(
    operation: FirestoreOperation,
    safetyItemName: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const safetyItemRecord = {
      ref: operation.makeRef('vesselSafetyItems'),
      data: {
        name: safetyItemName,
        state: 'active',
        touched: operation.serverTimestamp(),
        vesselId: vesselId,
      },
    }

    const whenVesselTouchedRecord: FirestoreRecord = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: {
        licenseeId: licenseeId,
        vesselSafetyItems: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return { ref: safetyItemRecord.ref, records: [safetyItemRecord, whenVesselTouchedRecord] }
  }
}
