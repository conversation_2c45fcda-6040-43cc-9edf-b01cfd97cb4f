import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { DocRef } from '../data/IFirestoreService'
import {
  MaintenanceTaskDto,
  UpdateMaintenanceHistoryDto,
} from '../use-cases/maintenance/UpdateMaintenanceHistoryUseCase'
import { CreateMaintenanceHistoryDto } from '../use-cases/maintenance/CreateMaintenanceHistoryUseCase'
import { Console } from 'console'

export interface IMaintenanceHistoryService {
  createMaintenanceHistory(
    operation: FirestoreOperation,
    createSafetyCheckDto: CreateMaintenanceHistoryDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateMaintenanceHistory(
    operation: FirestoreOperation,
    updateMaintenanceHistoryDto: UpdateMaintenanceHistoryDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateScheduleMaintenanceTask(
    operation: FirestoreOperation,
    maintenanceTask: MaintenanceTaskDto | undefined,
    userId: string
  ): FirestoreRecord
}

@injectable()
export class MaintenanceHistoryService implements IMaintenanceHistoryService {
  public createMaintenanceHistory(
    operation: FirestoreOperation,
    createSafetyCheckDto: CreateMaintenanceHistoryDto,
    userId: string,
    licenseeId: string
  ) {
    const { description, itemId, ...rest } = createSafetyCheckDto

    const data = {
      ...rest,
      ...(description ? { description } : {}),
      type: 'unscheduled',
      state: 'completed',
      completedBy: userId,
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const newSafetyCheckRecord = {
      ref: operation.makeRef('maintenanceTasksCompleted'),
      data,
    }

    //TODO: SF-342 Add maintenance tags to the vessel.possibleMaintenanceTags
    // Refer to Ionic Project for the logic

    return {
      ref: newSafetyCheckRecord.ref,
      records: [newSafetyCheckRecord],
    }
  }

  public updateMaintenanceHistory(
    operation: FirestoreOperation,
    updateMaintenanceHistoryDto: UpdateMaintenanceHistoryDto,
    userId: string,
    licenseeId: string
  ) {
    const { maintenanceTask, job, vesselId, itemId, description, locationId, ...rest } = updateMaintenanceHistoryDto

    const data = {
      ...rest,
      description: description ?? operation.deleteField(),
      locationId: locationId ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedSafetyCheckRecord = {
      ref: operation.makeRef('maintenanceTasksCompleted', itemId),
      data,
      options: { merge: true },
    }

    return {
      ref: updatedSafetyCheckRecord.ref,
      records: [updatedSafetyCheckRecord],
    }
  }

  public updateScheduleMaintenanceTask(
    operation: FirestoreOperation,
    maintenanceTask: MaintenanceTaskDto,
    userId: string
  ) {
    const { id, ...rest } = maintenanceTask

    const data = {
      ...rest,
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    return {
      ref: operation.makeRef('scheduledMaintenanceTasks', id),
      data,
      options: { merge: true },
    }
  }
}
