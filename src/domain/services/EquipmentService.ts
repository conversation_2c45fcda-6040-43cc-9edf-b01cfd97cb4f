import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { DocRef } from '../data/IFirestoreService'
import { CreateEquipmentDto } from '../use-cases/maintenance/CreateEquipmentUseCase'
import { UpdateEquipmentDto } from '../use-cases/maintenance/UpdateEquipmentUseCase'

export interface IEquipmentService {
  createSparePart(
    operation: FirestoreOperation,
    updateJobListDto: UpdateEquipmentDto,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateSparePart(
    operation: FirestoreOperation,
    updateJobListDto: UpdateEquipmentDto,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

export class EquipmentService implements IEquipmentService {
  createSparePart(
    operation: FirestoreOperation,
    updateJobListDto: Omit<CreateEquipmentDto, 'vesselId'>,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { make, model, serial, notes, systemId, locationId, isCritical, ...rest } = updateJobListDto

    const data = {
      ...rest,
      vesselId,
      //TODO: make category id for system
      systemId,
      //TODO: make category id for location
      locationId: locationId ?? operation.deleteField(),
      make: make ?? operation.deleteField(),
      model: model ?? operation.deleteField(),
      serial: serial ?? operation.deleteField(),
      notes: notes ?? operation.deleteField(),
      isCritical: isCritical ?? operation.deleteField(),
      state: 'active',
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    // Implementation for updating spare part goes here
    const updatedEquipmentRecord = {
      ref: operation.makeRef('equipment'),
      data,
    }

    const vesselTouched = this.vesselTouched(operation, 'equipment', licenseeId, vesselId)

    return {
      ref: updatedEquipmentRecord.ref,
      records: [updatedEquipmentRecord, vesselTouched],
    }
  }

  updateSparePart(
    operation: FirestoreOperation,
    updateJobListDto: Omit<UpdateEquipmentDto, 'vesselId'>,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, make, model, serial, notes, systemId, locationId, isCritical, ...rest } = updateJobListDto

    const data = {
      ...rest,
      //TODO: make category id for system
      systemId,
      //TODO: make category id for location
      locationId: locationId ?? operation.deleteField(),
      make: make ?? operation.deleteField(),
      model: model ?? operation.deleteField(),
      serial: serial ?? operation.deleteField(),
      notes: notes ?? operation.deleteField(),
      isCritical: isCritical ?? operation.deleteField(),

      updatedBy: userId,
      whenUpdated: Date.now(),
      touched: operation.serverTimestamp(),
    }

    // Implementation for updating spare part goes here
    const updatedEquipmentRecord = {
      ref: operation.makeRef('equipment', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'equipment', licenseeId, vesselId)

    return {
      ref: updatedEquipmentRecord.ref,
      records: [updatedEquipmentRecord, vesselTouched],
    }
  }

  private vesselTouched(operation: FirestoreOperation, collectionName: string, licenseeId: string, vesselId: string) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    }

    const vesselTouched = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: vesselData,
      options: { merge: true },
    }

    return vesselTouched
  }
}
