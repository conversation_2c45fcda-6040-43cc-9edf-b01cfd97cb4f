import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { UpdateMaintenanceScheduleDto } from '../use-cases/maintenance/UpdateMaintenanceScheduleUseCase'
import { DocRef } from '../data/IFirestoreService'
import { CompleteMaintenanceScheduleDto } from '../use-cases/maintenance/CompleteMaintenanceScheduleUseCase'
import { CreateMaintenanceScheduleDto } from '../use-cases/maintenance/CreateMaintenanceScheduleUseCase'

export interface IMaintenanceScheduleService {
  createMaintenanceSchedule(
    operation: FirestoreOperation,
    createMaintenanceScheduleDto: Omit<CreateMaintenanceScheduleDto, 'newMaintenanceTags'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateMaintenanceSchedule(
    operation: FirestoreOperation,
    updateMaintenanceScheduleDto: UpdateMaintenanceScheduleDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  completeMaintenanceSchedule(
    operation: FirestoreOperation,
    completeMaintenanceScheduleDto: CompleteMaintenanceScheduleDto,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateEngineData(
    operation: FirestoreOperation,
    engineHoursDto: Pick<CompleteMaintenanceScheduleDto, 'engineId' | 'engineHours'>,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  createHistory(
    operation: FirestoreOperation,
    dto: CreateMaintenanceScheduleDto,
    userId: string,
    licenseeId: string,
    maintenanceTaskId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class MaintenanceScheduleService implements IMaintenanceScheduleService {
  createMaintenanceSchedule(
    operation: FirestoreOperation,
    createMaintenanceScheduleDto: Omit<CreateMaintenanceScheduleDto, 'newMaintenanceTags'>,
    userId: string,
    licenseeId: string
  ): { ref: DocRef; records: FirestoreRecord[] } {
    const {
      task,
      description,
      maintenanceTags,
      intervalWeekMonth,
      dateDue,
      intervalEngineHours,
      engineId,
      engineHoursDue,
      engineHoursLastService,
      ...rest
    } = createMaintenanceScheduleDto

    const data = {
      ...rest,
      ...(task ? { task } : {}),
      ...(description ? { description } : {}),
      ...(maintenanceTags ? { maintenanceTags } : {}),
      ...(intervalWeekMonth ? { intervalWeekMonth } : {}),
      ...(dateDue ? { dateDue } : {}),
      ...(intervalEngineHours ? { intervalEngineHours } : {}),
      ...(engineHoursLastService ? { engineHoursLastService } : {}),
      ...(engineId ? { engineId } : {}),
      ...(engineHoursDue ? { engineHoursDue } : {}),
      addedBy: userId,
      whenAdded: Date.now(),
      state: 'active',
      touched: operation.serverTimestamp(),
    }

    const createdMaintenanceScheduleRecord = {
      ref: operation.makeRef('scheduledMaintenanceTasks'),
      data,
    }

    const vesselTouched = this.vesselTouched(
      operation,
      'scheduledMaintenanceTasks',
      licenseeId,
      createMaintenanceScheduleDto.vesselId
    )

    const { records } = this.createHistory(
      operation,
      createMaintenanceScheduleDto,
      userId,
      licenseeId,
      createdMaintenanceScheduleRecord.ref.id
    )

    return {
      ref: createdMaintenanceScheduleRecord.ref,
      records: [createdMaintenanceScheduleRecord, vesselTouched, ...records],
    }
  }

  updateMaintenanceSchedule(
    operation: FirestoreOperation,
    updateMaintenanceScheduleDto: Omit<UpdateMaintenanceScheduleDto, 'newMaintenanceTags'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      id,
      vesselId,
      task,
      description,
      maintenanceTags,
      intervalWeekMonth,
      dateDue,
      intervalEngineHours,
      engineId,
      engineHoursDue,
      ...rest
    } = updateMaintenanceScheduleDto

    const data = {
      ...rest,
      task: task ?? operation.deleteField(),
      description: description ?? operation.deleteField(),
      maintenanceTags: maintenanceTags ?? operation.deleteField(),
      intervalWeekMonth: intervalWeekMonth ?? operation.deleteField(),
      dateDue: dateDue ?? operation.deleteField(),
      intervalEngineHours: intervalEngineHours ?? operation.deleteField(),
      engineId: engineId ?? operation.deleteField(),
      engineHoursDue: engineHoursDue ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedMaintenanceScheduleRecord = {
      ref: operation.makeRef('scheduledMaintenanceTasks', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'scheduledMaintenanceTasks', licenseeId, vesselId)

    //TODO: Handle Links

    return {
      ref: updatedMaintenanceScheduleRecord.ref,
      records: [updatedMaintenanceScheduleRecord, vesselTouched],
    }
  }

  completeMaintenanceSchedule(
    operation: FirestoreOperation,
    completeMaintenanceScheduleDto: Omit<CompleteMaintenanceScheduleDto, 'updateEngineData' | 'equipmentName'>,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      location,
      engineId,
      engineHours,
      whenLastService,
      engineHoursLastService,
      dateDue,
      engineHoursDue,
      ...rest
    } = completeMaintenanceScheduleDto
    const data = {
      ...rest,
      ...(location ? { location } : {}),
      ...(engineId ? { engineId } : {}),
      engineHours: engineHours ?? 0,
      type: 'scheduled',
      state: 'completed',
      completedBy: userId,
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const scheduledMaintenanceTask = {
      ref: operation.makeRef('scheduledMaintenanceTasks', data.maintenanceTaskId),
      data: {
        whenLastService: whenLastService,
        engineHoursLastService: engineHoursLastService,
        dateDue: dateDue ?? operation.deleteField(),
        engineHoursDue: engineHoursDue ?? operation.deleteField(),
        whenUpdated: Date.now(),
        updatedBy: userId,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const completedMaintenanceScheduleRecord = {
      ref: operation.makeRef('maintenanceTasksCompleted'),
      data,
    }

    const vesselTouchedSchedule = this.vesselTouched(operation, 'scheduledMaintenanceTasks', licenseeId, vesselId)

    const vesselTouchedMaintenance = this.vesselTouched(operation, 'maintenanceTasksCompleted', licenseeId, vesselId)

    //TODO Handle Files

    return {
      ref: scheduledMaintenanceTask.ref,
      records: [
        scheduledMaintenanceTask,
        completedMaintenanceScheduleRecord,
        vesselTouchedSchedule,
        vesselTouchedMaintenance,
      ],
    }
  }

  updateEngineData(
    operation: FirestoreOperation,
    engineHoursDto: Pick<CompleteMaintenanceScheduleDto, 'engineId' | 'engineHours'>,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const data = {
      hours: engineHoursDto.engineHours,
      whenUpdated: Date.now(),
      updatedVia: 'completeMaintenanceSchedule',
      updatedBy: userId,
      touched: operation.serverTimestamp(),
    }

    const udatedEngineData = {
      ref: operation.makeRef('engines', engineHoursDto.engineId),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'engines', licenseeId, vesselId)

    return {
      ref: udatedEngineData.ref,
      records: [udatedEngineData, vesselTouched],
    }
  }

  createHistory(
    operation: FirestoreOperation,
    dto: Omit<CreateMaintenanceScheduleDto, 'newMaintenanceTags'>,
    userId: string,
    licenseeId: string,
    maintenanceTaskId: string
  ): { ref: DocRef; records: FirestoreRecord[] } {
    const { vesselId, equipmentId, locationId, task, description, whenLastService, engineHoursLastService, engineId } =
      dto

    const data = {
      vesselId,
      maintenanceTaskId,
      equipmentId,
      locationId,
      task,
      description,
      whenCompleted: whenLastService,
      type: 'scheduled',
      notes: '',
      engineHours: engineHoursLastService ?? 0,
      engineId: engineId ?? '',
      state: 'completed',
      completedBy: userId,
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const updatedMaintenanceTaskCompletedRecord = {
      ref: operation.makeRef('maintenanceTasksCompleted'),
      data,
    }

    const vesselTouchedMaintenance = this.vesselTouched(operation, 'maintenanceTasksCompleted', licenseeId, vesselId)

    return {
      ref: updatedMaintenanceTaskCompletedRecord.ref,
      records: [updatedMaintenanceTaskCompletedRecord, vesselTouchedMaintenance],
    }
  }

  private vesselTouched(operation: FirestoreOperation, collectionName: string, licenseeId: string, vesselId: string) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    }

    const vesselTouched = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: vesselData,
      options: { merge: true },
    }

    return vesselTouched
  }
}
