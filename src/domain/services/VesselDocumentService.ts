import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { UpdateVesselDocumentDto } from '../use-cases/vesselDocumentRegister/UpdateVesselDocumentUseCase'
import { DocRef } from '../data/IFirestoreService'
import { CreateVesselDocumentDto } from '../use-cases/vesselDocumentRegister/CreateVesselDocumentUseCase'
import { DeleteVesselDocumentDto } from '../use-cases/vesselDocumentRegister/DeleteVesselDocumentUseCase'

export interface IVesselDocumentService {
  updateVesselDocument(
    operation: FirestoreOperation,
    updateVesselDocumentDto: UpdateVesselDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  createVesselDocument(
    operation: FirestoreOperation,
    createVesselDocumentDto: CreateVesselDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  deleteVesselDocument(
    operation: FirestoreOperation,
    deleteVesselDocumentDto: DeleteVesselDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class VesselDocumentService implements IVesselDocumentService {
  updateVesselDocument(
    operation: FirestoreOperation,
    updateVesselDocumentDto: UpdateVesselDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, id, dateExpires, emailReminder, interval, categoryId, dateToRemind, sfdoc, files, ...rest } =
      updateVesselDocumentDto

    const data = {
      ...rest,
      dateExpires: dateExpires ?? operation.deleteField(),
      emailReminder: emailReminder ?? operation.deleteField(),
      interval: interval ?? operation.deleteField(),
      categoryId: categoryId ?? operation.deleteField(),
      dateToRemind: dateToRemind ?? operation.deleteField(),
      files: files ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedVesselCertificateRecord = {
      ref: operation.makeRef('vesselDocuments', id),
      data,
      options: { merge: true },
    }

    //TODO Create a new category if it doesn't exist
    //TODO: Add Links

    const vesselTouched = this.vesselTouched(operation, 'vesselDocuments', licenseeId, vesselId)

    return {
      ref: updatedVesselCertificateRecord.ref,
      records: [updatedVesselCertificateRecord, vesselTouched],
    }
  }

  createVesselDocument(
    operation: FirestoreOperation,
    createVesselDocumentDto: CreateVesselDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, dateExpires, emailReminder, interval, categoryId, dateToRemind, sfdoc, files, ...rest } =
      createVesselDocumentDto

    const data = {
      ...rest,
      ...(dateExpires ? { dateExpires: dateExpires } : {}),
      ...(emailReminder ? { emailReminder: emailReminder } : {}),
      ...(interval ? { interval: interval } : {}),
      ...(categoryId ? { categoryId: categoryId } : {}),
      ...(dateToRemind ? { dateToRemind: dateToRemind } : {}),
      ...(files ? { files: files } : {}),
      vesselId,
      state: 'active',
      addedBy: userId,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedVesselCertificateRecord = {
      ref: operation.makeRef('vesselDocuments'),
      data,
    }

    //TODO Create a new category if it doesn't exist
    //TODO: Add Links

    const vesselTouched = this.vesselTouched(operation, 'vesselDocuments', licenseeId, vesselId)

    return {
      ref: updatedVesselCertificateRecord.ref,
      records: [updatedVesselCertificateRecord, vesselTouched],
    }
  }

  deleteVesselDocument(
    operation: FirestoreOperation,
    deleteVesselDocumentDto: Omit<DeleteVesselDocumentDto, 'title'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, id } = deleteVesselDocumentDto

    const data = {
      state: 'deleted',
      deletedBy: userId,
      whenDeleted: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const updatedVesselCertificateRecord = {
      ref: operation.makeRef('vesselDocuments', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'vesselDocuments', licenseeId, vesselId)

    return {
      ref: updatedVesselCertificateRecord.ref,
      records: [updatedVesselCertificateRecord, vesselTouched],
    }
  }

  private vesselTouched(operation: FirestoreOperation, collectionName: string, licenseeId: string, vesselId: string) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    }

    const vesselTouched = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: vesselData,
      options: { merge: true },
    }

    return vesselTouched
  }
}
