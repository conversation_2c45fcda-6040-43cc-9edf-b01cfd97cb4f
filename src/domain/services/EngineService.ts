import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { EngineDto, UpdateEngineHoursDto } from '../use-cases/engine/UpdateEngineHoursUseCase'
import { DocRef } from '../data/IFirestoreService'

export interface IEngineService {
  updateEngineHours(
    operation: FirestoreOperation,
    engineDto: UpdateEngineHoursDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class EngineService implements IEngineService {
  updateEngineHours(
    operation: FirestoreOperation,
    engineDto: UpdateEngineHoursDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, engines } = engineDto

    const updatedEngineRecords = engines.map(engine => {
      return this.updateEngineRecord(operation, engine, userId)
    })

    const vesselTouchedRecord = this.vesselTouched(operation, 'engines', licenseeId, vesselId)

    return {
      ref: updatedEngineRecords[0].ref ?? '',
      records: [...updatedEngineRecords, vesselTouchedRecord],
    }
  }

  private updateEngineRecord(operation: FirestoreOperation, engineDto: Omit<EngineDto, 'engineName'>, userId: string) {
    const { engineId, engineHours } = engineDto

    const data = {
      hours: engineHours,
      updatedBy: userId,
      updatedVia: 'editEngines',
      whenUpdated: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const updatedEngineRecords = {
      ref: operation.makeRef('engines', engineId),
      data,
      options: { merge: true },
    }

    return updatedEngineRecords
  }

  private vesselTouched(operation: FirestoreOperation, collectionName: string, licenseeId: string, vesselId: string) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    }

    const vesselTouched = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: vesselData,
      options: { merge: true },
    }

    return vesselTouched
  }
}
