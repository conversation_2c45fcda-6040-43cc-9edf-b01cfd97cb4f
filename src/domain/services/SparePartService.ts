import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { UpdateSparePartDto } from '../use-cases/maintenance/UpdateSparePartUseCase'
import { DocRef } from '../data/IFirestoreService'
import { CreateSparePartDto } from '../use-cases/maintenance/CreateSparePartUseCase'

interface UpdateDto extends Omit<UpdateSparePartDto, 'vesselId'> {}

interface ISparePartService {
  createSparePart(
    operation: FirestoreOperation,
    createJobListDto: CreateSparePartDto,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateSparePart(
    operation: FirestoreOperation,
    updateJobListDto: UpdateDto,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class SparePartService implements ISparePartService {
  createSparePart(
    operation: FirestoreOperation,
    createJobListDto: CreateSparePartDto,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      systemId,
      equipmentIds,
      locationId,
      locationDescription,
      minQuantity,
      manufacturer,
      model,
      partNum,
      unitPrice,
      notes,
      ...rest
    } = createJobListDto

    const data = {
      ...rest,
      ...(systemId ? { systemId: systemId } : {}),
      ...(equipmentIds ? { equipmentIds: equipmentIds } : {}),
      ...(locationId ? { locationId: locationId } : {}),
      ...(locationDescription ? { locationDescription: locationDescription } : {}),
      ...(minQuantity ? { minQuantity: minQuantity } : {}),
      ...(manufacturer ? { manufacturer: manufacturer } : {}),
      ...(model ? { model: model } : {}),
      ...(partNum ? { partNum: partNum } : {}),
      ...(unitPrice ? { unitPrice: unitPrice } : {}),
      ...(notes ? { notes: notes } : {}),
      state: 'active',
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const updatedSparePartRecord = {
      ref: operation.makeRef('spareParts'),
      data,
    }

    const vesselTouched = this.vesselTouched(operation, 'spareParts', licenseeId, vesselId)

    return {
      ref: updatedSparePartRecord.ref,
      records: [updatedSparePartRecord, vesselTouched],
    }
  }
  updateSparePart(
    operation: FirestoreOperation,
    updateJobListDto: UpdateDto,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      id,
      systemId,
      equipmentIds,
      locationId,
      locationDescription,
      minQuantity,
      manufacturer,
      model,
      partNum,
      unitPrice,
      notes,
      ...rest
    } = updateJobListDto

    const data = {
      ...rest,
      systemId: systemId ?? operation.deleteField(),
      equipmentIds: equipmentIds ?? operation.deleteField(),
      //TODO: make category id for location
      locationId: locationId ?? operation.deleteField(),
      locationDescription: locationDescription ?? operation.deleteField(),
      minQuantity: minQuantity ?? operation.deleteField(),
      manufacturer: manufacturer ?? operation.deleteField(),
      model: model ?? operation.deleteField(),
      partNum: partNum ?? operation.deleteField(),
      unitPrice: unitPrice ?? operation.deleteField(),
      notes: notes ?? operation.deleteField(),
      updatedVia: 'editSpareParts',
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedSparePartRecord = {
      ref: operation.makeRef('spareParts', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'spareParts', licenseeId, vesselId)

    return {
      ref: updatedSparePartRecord.ref,
      records: [updatedSparePartRecord, vesselTouched],
    }
  }

  private vesselTouched(operation: FirestoreOperation, collectionName: string, licenseeId: string, vesselId: string) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    }

    const vesselTouched = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: vesselData,
      options: { merge: true },
    }

    return vesselTouched
  }
}
