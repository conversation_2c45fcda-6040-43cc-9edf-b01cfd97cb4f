import { deleteValue, FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { serverTimestamp } from '@src/lib/firebase/services/firestore.service'
import { CreateSafetyCheckDto } from '@src/domain/use-cases/safety/CreateSafetyCheckUseCase'
import { inject, injectable } from 'inversify'
import { UpdateSafetyCheckDto } from '@src/domain/use-cases/safety/UpdateSafetyCheckUseCase'
import { DocRef, IFirestoreService } from '@src/domain/data/IFirestoreService'
import { addInterval, hours24ToMillis } from '@src/lib/datesAndTime'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { DateTime } from 'luxon'
import { CompleteSafetyCheckDto } from '@src/domain/use-cases/safety/CompleteSafetyCheckUseCase'
import { UpdateCompletedSafetyCheckDto } from '@src/domain/use-cases/safety/UpdateCompletedSafetyCheckUseCase'
import { seaFilesToValue } from '@src/lib/fileImports'
import { SafetyCheckCompleted } from '@src/shared-state/VesselSafety/useCompletedSafetyCheckItems'

export interface ISafetyCheckService {
  createSafetyCheck(
    operation: FirestoreOperation,
    createSafetyCheckDto: CreateSafetyCheckDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateSafetyCheck(
    operation: FirestoreOperation,
    updateSafetyCheckDto: UpdateSafetyCheckDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  completeSafetyCheck(
    operation: FirestoreOperation,
    updateSafetyCheckDto: CompleteSafetyCheckDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateCompletedSafetyCheck(
    operation: FirestoreOperation,
    updateCompletedSafetyCheckDto: UpdateCompletedSafetyCheckDto,
    userId: string,
    licenseeId: string
  ): Promise<{
    ref: DocRef
    records: FirestoreRecord[]
  }>

  deleteSafetyCheck(
    operation: FirestoreOperation,
    safetyCheckId: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  createSafetyCheckCategory(
    operation: FirestoreOperation,
    newCategoryName: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class SafetyCheckService implements ISafetyCheckService {
  private readonly logger: ILogger
  private readonly firestoreService: IFirestoreService

  constructor(
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(SERVICES.IFirestoreService) firestoreService: IFirestoreService
  ) {
    this.logger = logger.scoped('SafetyCheckService')
    this.firestoreService = firestoreService
  }

  createSafetyCheck(operation: FirestoreOperation, dto: CreateSafetyCheckDto, userId: string, licenseeId: string) {
    const dateDue = this.calculateDateDue(dto.whenLastChecked, dto.interval)
    console.debug('DTO', { dto })
    const newSafetyCheckRecord = {
      ref: operation.makeRef('safetyCheckItems'),
      data: {
        vesselId: dto.vesselId,
        addedBy: userId,
        itemId: dto.itemId,
        locationId: dto.locationId ?? undefined,
        categoryId: dto.categoryId ?? undefined,
        description: dto.description ?? undefined,
        interval: dto.interval,
        hasFault: false,
        whenLastChecked: dto.whenLastChecked,
        dateDue: dateDue,
        whenAdded: Date.now(), // TODO - Use batchTrace.whenAdded?
        state: 'active',
        files: dto.files,
        assignedTo: dto.assignedTo ?? [],
        touched: serverTimestamp(),
        estimatedTime: dto.estimatedTime ?? undefined,
      },
    }

    const vesselSafetyItemRecord = {
      ref: operation.makeRef('vesselSafetyItems', dto.itemId),
      data: {
        isCritical: dto.isCritical ?? false,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const whenVesselTouchedRecord: FirestoreRecord = this.getWhenVesselTouchedRecord(
      operation,
      dto.vesselId,
      licenseeId
    )

    const overdueStatsRecord: FirestoreRecord = this.getOverdueStatsRecord(operation, licenseeId, dto.vesselId)

    this.logger.debug('Created records for new Safety Check: ', {
      newSafetyCheckRecord,
      whenVesselTouchedRecord,
      overdueStatsRecord,
    })

    return {
      ref: newSafetyCheckRecord.ref,
      records: [newSafetyCheckRecord, vesselSafetyItemRecord, whenVesselTouchedRecord, overdueStatsRecord],
    }
  }

  private calculateDateDue(whenLastChecked: number, interval: string): string {
    const dateDue = addInterval(whenLastChecked, interval).toISODate()
    if (!dateDue) {
      throw new Error(`Unable to calculate DateDue from date and interval: ${whenLastChecked} and ${interval}`)
    }

    return dateDue
  }

  public updateSafetyCheck(
    operation: FirestoreOperation,
    dto: UpdateSafetyCheckDto,
    userId: string,
    licenseeId: string
  ) {
    const updatedSafetyCheckRecord = {
      ref: operation.makeRef('safetyCheckItems', dto.safetyCheckId),
      data: {
        updatedBy: userId,
        whenUpdated: DateTime.now().toUTC().toMillis(),
        itemId: dto.itemId,
        locationId: dto.locationId,
        categoryId: dto.categoryId,
        description: dto.description ?? deleteValue,
        interval: dto.interval,
        hasFault: false,
        whenLastChecked: dto.whenLastChecked,
        dateDue: addInterval(dto.whenLastChecked, dto.interval).toISODate(),
        assignedTo: dto.assignedTo,
        files: dto.files,
        touched: operation.serverTimestamp(),
        estimatedTime: dto.estimatedTime,
      },
      options: { merge: true },
    }

    const vesselSafetyItemRecord = {
      ref: operation.makeRef('vesselSafetyItems', dto.itemId),
      data: {
        isCritical: dto.isCritical ?? false,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const whenVesselTouchedRecord: FirestoreRecord = this.getWhenVesselTouchedRecord(
      operation,
      dto.vesselId,
      licenseeId
    )

    const overdueStatsRecord: FirestoreRecord = this.getOverdueStatsRecord(operation, licenseeId, dto.vesselId)

    return {
      ref: updatedSafetyCheckRecord.ref,
      records: [updatedSafetyCheckRecord, vesselSafetyItemRecord, whenVesselTouchedRecord, overdueStatsRecord],
    }
  }

  public completeSafetyCheck(
    operation: FirestoreOperation,
    dto: CompleteSafetyCheckDto,
    userId: string,
    licenseeId: string
  ) {
    const completedSafetyCheckRecord = {
      ref: operation.makeRef('safetyCheckCompleted'),
      data: {
        safetyCheckId: dto.safetyCheckId,
        completedBy: userId,
        vesselId: dto.vesselId,
        whenAdded: DateTime.now().toUTC().toMillis(),
        addedBy: userId,
        notes: dto.notes ?? undefined,
        whenCompleted: dto.whenCompleted,
        shouldReportFault: dto.shouldReportFault,
        files: dto.files,
        state: 'active',
        touched: operation.serverTimestamp(),
        actualTime: dto.actualTime ?? undefined,
      },
    }

    const updatedSafetyCheckRecord = {
      ref: operation.makeRef('safetyCheckItems', dto.safetyCheckId),
      data: {
        whenLastChecked: dto.whenCompleted,
        dateDue: addInterval(dto.whenCompleted, dto.interval).toISODate(),
        hasFault: dto.shouldReportFault,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const whenVesselTouchedRecord: FirestoreRecord = this.getWhenVesselTouchedRecord(
      operation,
      dto.vesselId,
      licenseeId
    )

    const overdueStatsRecord: FirestoreRecord = this.getOverdueStatsRecord(operation, licenseeId, dto.vesselId)

    return {
      ref: completedSafetyCheckRecord.ref,
      records: [completedSafetyCheckRecord, updatedSafetyCheckRecord, whenVesselTouchedRecord, overdueStatsRecord],
    }
  }

  private getOverdueStatsRecord(operation: FirestoreOperation, licenseeId: string, vesselId: string) {
    return {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [vesselId]: {
          safetyCheckItems: {
            stale: true,
          },
          vesselSafetyItems: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }
  }

  public async updateCompletedSafetyCheck(
    operation: FirestoreOperation,
    dto: UpdateCompletedSafetyCheckDto,
    userId: string,
    licenseeId: string
  ) {
    const updatedCompletedSafetyCheckRecord = {
      ref: operation.makeRef('safetyCheckCompleted', dto.completedSafetyCheckId),
      data: {
        updatedBy: userId,
        whenUpdated: DateTime.now().toUTC().toMillis(),
        whenCompleted: dto.whenCompleted,
        notes: dto.notes ?? deleteValue,
        files: dto.files,
        actualTime: dto.actualTime ?? deleteValue,
        shouldReportFault: dto.shouldReportFault,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const records: FirestoreRecord[] = [updatedCompletedSafetyCheckRecord]

    // Determine if and how to update the parent safety check
    const reconciliation = await this.getCompletionDateReconciliation(
      dto.safetyCheckId,
      dto.completedSafetyCheckId,
      dto.whenCompleted,
      dto.shouldReportFault
    )

    if (reconciliation.shouldUpdateParent && reconciliation.parentUpdateDate) {
      const updatedSafetyCheckRecord = {
        ref: operation.makeRef('safetyCheckItems', dto.safetyCheckId),
        data: {
          whenLastChecked: reconciliation.parentUpdateDate,
          dateDue: addInterval(reconciliation.parentUpdateDate, dto.interval).toISODate(),
          ...(reconciliation.parentUpdateFault !== undefined && {
            hasFault: reconciliation.parentUpdateFault,
          }),
          touched: operation.serverTimestamp(),
        },
        options: { merge: true },
      }
      records.push(updatedSafetyCheckRecord)
    }

    // Add standard tracking records
    const whenVesselTouchedRecord = this.getWhenVesselTouchedRecord(operation, dto.vesselId, licenseeId)
    const overdueStatsRecord = this.getOverdueStatsRecord(operation, licenseeId, dto.vesselId)

    records.push(whenVesselTouchedRecord, overdueStatsRecord)

    return {
      ref: updatedCompletedSafetyCheckRecord.ref,
      records,
    }
  }

  private async getCompletionDateReconciliation(
    safetyCheckId: string,
    currentCompletionId: string,
    newCompletionDate: number,
    shouldReportFault: boolean
  ): Promise<{
    shouldUpdateParent: boolean
    parentUpdateDate?: number
    parentUpdateFault?: boolean
  }> {
    // Fetch all completed safety checks for this safety check
    const completedChecks = await this.firestoreService.getDocuments<SafetyCheckCompleted>(
      'safetyCheckCompleted',
      [
        { field: 'safetyCheckId', operator: '==', value: safetyCheckId },
        { field: 'state', operator: '==', value: 'active' },
      ],
      []
    )

    // Filter out the current completion being edited
    const otherCompletions = completedChecks.filter(check => check.id !== currentCompletionId)

    // Sort by completion date (most recent first)
    const sortedOtherCompletions = otherCompletions.sort((a, b) => b.whenCompleted - a.whenCompleted)

    // Find the current completion in the original list
    const currentCompletion = completedChecks.find(check => check.id === currentCompletionId)
    const wasOriginallyMostRecent =
      !sortedOtherCompletions.length ||
      (currentCompletion && currentCompletion.whenCompleted >= sortedOtherCompletions[0].whenCompleted)

    // Determine if the new date will be the most recent
    const willBecomeMostRecent =
      !sortedOtherCompletions.length || newCompletionDate >= sortedOtherCompletions[0].whenCompleted

    if (willBecomeMostRecent) {
      // New date becomes the most recent - use it for parent update
      return {
        shouldUpdateParent: true,
        parentUpdateDate: newCompletionDate,
        parentUpdateFault: shouldReportFault,
      }
    } else if (wasOriginallyMostRecent && !willBecomeMostRecent) {
      // Was most recent but now isn't - fall back to the next most recent
      if (sortedOtherCompletions.length > 0) {
        const nextMostRecent = sortedOtherCompletions[0]
        return {
          shouldUpdateParent: true,
          parentUpdateDate: nextMostRecent.whenCompleted,
          parentUpdateFault: nextMostRecent.shouldReportFault,
        }
      } else {
        // No other completions exist, don't update parent
        return { shouldUpdateParent: false }
      }
    } else {
      // Was not most recent and doesn't become most recent - no parent update needed
      return { shouldUpdateParent: false }
    }
  }

  public deleteSafetyCheck(
    operation: FirestoreOperation,
    safetyCheckId: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ) {
    const deletedSafetyCheckRecord = {
      ref: operation.makeRef('safetyCheckItems', safetyCheckId),
      data: {
        deletedBy: userId,
        whenDeleted: DateTime.now().toUTC().toMillis(),
        state: 'deleted',
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const whenVesselTouchedRecord: FirestoreRecord = this.getWhenVesselTouchedRecord(operation, vesselId, licenseeId)

    const overdueStatsRecord: FirestoreRecord = this.getOverdueStatsRecord(operation, licenseeId, vesselId)

    return {
      ref: deletedSafetyCheckRecord.ref,
      records: [deletedSafetyCheckRecord, whenVesselTouchedRecord, overdueStatsRecord],
    }
  }

  public createSafetyCheckCategory(
    operation: FirestoreOperation,
    newCategoryName: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): { ref: DocRef; records: FirestoreRecord[] } {
    const categoryRecord = {
      ref: operation.makeRef('safetyCheckCategories'),
      data: {
        name: newCategoryName,
        state: 'active',
        touched: operation.serverTimestamp(),
        vesselId: vesselId,
      },
    }

    const whenVesselTouchedRecord: FirestoreRecord = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: {
        licenseeId: licenseeId,
        vesselLocations: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return { ref: categoryRecord.ref, records: [categoryRecord, whenVesselTouchedRecord] }
  }

  private getWhenVesselTouchedRecord(operation: FirestoreOperation, vesselId: string, licenseeId: string) {
    return {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        safetyCheckItems: operation.serverTimestamp(),
        vesselSafetyItems: operation.serverTimestamp(),
      },
      options: { merge: true },
    }
  }
}
