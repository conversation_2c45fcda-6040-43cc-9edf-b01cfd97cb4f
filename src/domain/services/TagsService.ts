import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'

interface ITagsService {
  addMaintenanceTag(operation: FirestoreOperation, vesselId: string, tag: string): FirestoreRecord

  addJobTag(operation: FirestoreOperation, vesselId: string, tag: string): FirestoreRecord
}

@injectable()
export class TagsService implements ITagsService {
  addMaintenanceTag(operation: FirestoreOperation, vesselId: string, tag: string) {
    const data = {
      possibleMaintenanceTags: operation.arrayUnion(tag),
    }

    const updatedTags = {
      ref: operation.makeRef('vessels', vesselId),
      data,
      options: { merge: true },
    }

    return updatedTags
  }

  addJobTag(operation: FirestoreOperation, vesselId: string, tag: string) {
    const data = {
      possibleTags: operation.arrayUnion(tag),
    }

    const updatedTags = {
      ref: operation.makeRef('vessels', vesselId),
      data,
      options: { merge: true },
    }

    return updatedTags
  }
}
