import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { DocRef } from '../data/IFirestoreService'
import { CreateEquipmentManualsDto } from '../use-cases/maintenance/CreateEquipmentManualsUseCase'
import { UpdateEquipmentManualsDto } from '../use-cases/maintenance/UpdateEquipmentManualsUseCase'

export interface IEquipmentManualService {
  createEquipmentManuals(
    operation: FirestoreOperation,
    createEquipmentManualsDto: Omit<CreateEquipmentManualsDto, 'equipmentIdsToAdd'>,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateEquipmentManuals(
    operation: FirestoreOperation,
    updateMaintenanceHistoryDto: Pick<UpdateEquipmentManualsDto, 'id' | 'title' | 'files'>,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  addEquipmentManualsToEquipment(
    operation: FirestoreOperation,
    equipmentId: string,
    equipmentManualId: string,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  removeEquipmentManualsFromEquipment(
    operation: FirestoreOperation,
    equipmentId: string,
    equipmentManualId: string,
    userId: string,
    licenseeId: string,
    vesselId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

export class EquipmentManualService implements IEquipmentManualService {
  public createEquipmentManuals(
    operation: FirestoreOperation,
    createEquipmentManualsDto: Omit<CreateEquipmentManualsDto, 'equipmentIdsToAdd'>,
    userId: string,
    licenseeId: string,
    vesselId: string
  ) {
    const data = {
      ...createEquipmentManualsDto,
      state: 'active',
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const createEquipmentManualsRecord = {
      ref: operation.makeRef('equipmentManualDocuments'),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'equipmentManualDocuments', licenseeId, vesselId)

    //TODO: Add the logic to save the files

    return {
      ref: createEquipmentManualsRecord.ref,
      records: [createEquipmentManualsRecord, vesselTouched],
    }
  }

  public updateEquipmentManuals(
    operation: FirestoreOperation,
    updateEquipmentManualsDto: Pick<UpdateEquipmentManualsDto, 'id' | 'title' | 'files'>,
    userId: string,
    licenseeId: string,
    vesselId: string
  ) {
    const { id, ...rest } = updateEquipmentManualsDto

    const data = {
      ...rest,
      updatedBy: userId,
      whenUpdated: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const updatedEquipmentManualsRecord = {
      ref: operation.makeRef('equipmentManualDocuments', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'equipmentManualDocuments', licenseeId, vesselId)

    //TODO: Add the logic to save the files

    return {
      ref: updatedEquipmentManualsRecord.ref,
      records: [updatedEquipmentManualsRecord, vesselTouched],
    }
  }

  public addEquipmentManualsToEquipment(
    operation: FirestoreOperation,
    equipmentId: string,
    equipmentManualId: string,
    userId: string,
    licenseeId: string,
    vesselId: string
  ) {
    const data = {
      equipmentDocumentIds: operation.arrayUnion(equipmentManualId),
      whenUpdated: Date.now(),
      updatedBy: userId,
      touched: operation.serverTimestamp(),
    }

    const addManualToEquipmentRecord = {
      ref: operation.makeRef('equipment', equipmentId),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'equipment', licenseeId, vesselId)

    return {
      ref: addManualToEquipmentRecord.ref,
      records: [addManualToEquipmentRecord, vesselTouched],
    }
  }

  public removeEquipmentManualsFromEquipment(
    operation: FirestoreOperation,
    equipmentId: string,
    equipmentManualId: string,
    userId: string,
    licenseeId: string,
    vesselId: string
  ) {
    const data = {
      equipmentDocumentIds: operation.arrayRemove(equipmentManualId),
      whenUpdated: Date.now(),
      updatedBy: userId,
      touched: operation.serverTimestamp(),
    }

    const removeManualFromEquipmentRecord = {
      ref: operation.makeRef('equipment', equipmentId),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'equipment', licenseeId, vesselId)

    return {
      ref: removeManualFromEquipmentRecord.ref,
      records: [removeManualFromEquipmentRecord, vesselTouched],
    }
  }

  private vesselTouched(operation: FirestoreOperation, collectionName: string, licenseeId: string, vesselId: string) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    }

    const vesselTouched = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: vesselData,
      options: { merge: true },
    }

    return vesselTouched
  }
}
