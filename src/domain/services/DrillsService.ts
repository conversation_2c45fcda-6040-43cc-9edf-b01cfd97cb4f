import { inject, injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { CreateDrillReportDto } from '@src/domain/use-cases/safety/CreateDrillReportUseCase'
import { DocRef } from '@src/domain/data/IFirestoreService'
import { DateTime } from 'luxon'
import { Drill } from '@src/shared-state/VesselSafety/drills'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { addInterval } from '@src/lib/datesAndTime'
import { UpdateAssignedDrillsDto } from '../use-cases/safety/UpdateAssignedDrillsUseCase'
import { UpdateDrillDto } from '../use-cases/safety/UpdateDrillUseCase'

export type DrillCrewData = Record<string, { dateDue: string; dateLastCompleted: string }>

export interface IDrillsService {
  createDrill(
    operation: FirestoreOperation,
    vesselId: string,
    name: string,
    interval: string,
    notAssignedTo: string[],
    userId: string,
    licenseeId: string
  ): { ref: DocRef; records: FirestoreRecord[] }

  createDrillReport(
    operation: FirestoreOperation,
    createDto: CreateDrillReportDto,
    userId: string,
    licenseeId: string
  ): { ref: DocRef; records: FirestoreRecord[] }

  updateDrillReport(): void

  updateDrill(
    operation: FirestoreOperation,
    dto: UpdateDrillDto,
    currentDrill: Drill,
    userId: string,
    licenseeId: string
  ): FirestoreRecord[]

  updateAssignedDrills(
    operation: FirestoreOperation,
    dto: UpdateAssignedDrillsDto,
    userId: string,
    licenseeId: string
  ): FirestoreRecord[]

  deleteDrill(
    operation: FirestoreOperation,
    drillId: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): FirestoreRecord[]
}

@injectable()
export class DrillsService implements IDrillsService {
  private readonly logger: ILogger
  constructor(@inject(SERVICES.ILogger) logger: ILogger) {
    this.logger = logger.scoped('DrillsService')
  }

  public createDrill(
    operation: FirestoreOperation,
    vesselId: string,
    name: string,
    interval: string,
    notAssignedTo: string[],
    userId: string,
    licenseeId: string
  ): { ref: DocRef; records: FirestoreRecord[] } {
    this.logger.info(`Creating drill: ${name}`)

    const drillRef = operation.makeRef('drills')

    const drillRecord = {
      ref: drillRef,
      data: {
        vesselId,
        name,
        interval,
        notAssignedTo,
        state: 'active',
        addedBy: userId,
        whenAdded: DateTime.now().toUTC().toMillis(),
        touched: operation.serverTimestamp(),
        crew: {},
      },
    }

    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        drills: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [vesselId]: {
          drills: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }

    return {
      ref: drillRef,
      records: [drillRecord, whenVesselTouchedRecord, overdueStatsRecord],
    }
  }

  public createDrillReport(
    operation: FirestoreOperation,
    dto: CreateDrillReportDto,
    userId: string,
    licenseeId: string
  ) {
    const drillReportRecord = this.getDrillReportRecord(operation, dto, userId)
    const drillRecords = this.getDrillRecords(operation, dto, userId)

    const isUpdatingDrills = drillRecords.some(dr => Object.keys(dr.data.crew).length > 0)

    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', dto.vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        drillReports: operation.serverTimestamp(),
        ...(isUpdatingDrills
          ? {
              drills: operation.serverTimestamp(),
            }
          : {}),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [dto.vesselId]: {
          drillReports: {
            stale: true,
          },
          ...(isUpdatingDrills
            ? {
                drills: {
                  stale: true,
                },
              }
            : {}),
        },
      },
      options: { merge: true },
    }

    return {
      ref: drillReportRecord.ref as DocRef,
      records: [drillReportRecord, ...drillRecords, whenVesselTouchedRecord, overdueStatsRecord] as FirestoreRecord[],
    }
  }

  public updateDrillReport() {
    console.log('Not implemented')
  }

  public updateDrill(
    operation: FirestoreOperation,
    dto: UpdateDrillDto,
    currentDrill: Drill,
    userId: string,
    licenseeId: string
  ): FirestoreRecord[] {
    this.logger.info(`Updating drill: ${dto.drillId}`)

    // Check if interval changed
    const intervalChanged = currentDrill.interval !== dto.interval

    // Prepare base drill update data
    const drillUpdateData: any = {
      name: dto.name,
      interval: dto.interval,
      notAssignedTo: dto.notAssignedTo,
      updatedBy: userId,
      whenUpdated: DateTime.now().toUTC().toMillis(),
      touched: operation.serverTimestamp(),
    }

    // If interval changed, recalculate due dates for all crew members
    if (intervalChanged && currentDrill.crew) {
      this.logger.info(
        `Interval changed from ${currentDrill.interval} to ${dto.interval}, recalculating crew due dates`
      )

      const updatedCrew: DrillCrewData = {}

      Object.entries(currentDrill.crew).forEach(([crewId, crewData]) => {
        // Calculate new due date based on last completed date + new interval
        const lastCompletedDate = DateTime.fromISO(crewData.dateLastCompleted)
        const newDueDate = addInterval(lastCompletedDate, dto.interval)

        updatedCrew[crewId] = {
          dateDue: newDueDate.toISODate() ?? crewData.dateDue,
          dateLastCompleted: crewData.dateLastCompleted,
        }
      })

      drillUpdateData.crew = updatedCrew
    }

    const drillRecord = {
      ref: operation.makeRef('drills', dto.drillId),
      data: drillUpdateData,
      options: { merge: true },
    }

    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', dto.vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        drills: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [dto.vesselId]: {
          drills: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }

    return [drillRecord, whenVesselTouchedRecord, overdueStatsRecord]
  }

  public updateAssignedDrills(
    operation: FirestoreOperation,
    dto: UpdateAssignedDrillsDto,
    userId: string,
    licenseeId: string
  ): FirestoreRecord[] {
    const drillRecords = dto.vesselDrills.map(drillId => {
      const isAssigned = dto.assignedDrills.includes(drillId)

      return {
        ref: operation.makeRef('drills', drillId),
        data: {
          updatedBy: userId,
          whenUpdated: DateTime.now().toUTC().toMillis(),
          touched: operation.serverTimestamp(),
          notAssignedTo: isAssigned ? operation.arrayRemove(dto.drillUserId) : operation.arrayUnion(dto.drillUserId),
        },
        options: {
          merge: true,
        },
      }
    })

    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', dto.vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        drills: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [dto.vesselId]: {
          drills: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }

    return [...drillRecords, whenVesselTouchedRecord, overdueStatsRecord]
  }

  public deleteDrill(
    operation: FirestoreOperation,
    drillId: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): FirestoreRecord[] {
    this.logger.info(`Deleting drill: ${drillId}`)

    const drillRecord = {
      ref: operation.makeRef('drills', drillId),
      data: {
        state: 'deleted',
        deletedBy: userId,
        whenDeleted: DateTime.now().toUTC().toMillis(),
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        drills: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [vesselId]: {
          drills: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }

    return [drillRecord, whenVesselTouchedRecord, overdueStatsRecord]
  }

  private getDrillReportRecord(
    operation: FirestoreOperation,
    dto: CreateDrillReportDto,
    userId: string
  ): FirestoreRecord {
    const drillReportRef = operation.makeRef('drillReports')
    return {
      ref: drillReportRef,
      data: {
        vesselId: dto.vesselId,
        addedBy: userId,
        whenAdded: DateTime.now().toUTC().toMillis(),
        dateCompleted: dto.dateCompleted,
        location: dto.location ?? undefined,
        scenario: dto.scenario ?? undefined,
        equipment: dto.equipment ?? undefined,
        furtherTraining: dto.furtherTraining ?? undefined,
        modification: dto.modification ?? undefined,
        state: 'active',
        files: dto.files,
        signature: dto.signature,
        crewInvolvedIds: dto.crewInvolvedIds ?? undefined,
        drillIds: dto.drills.map(d => d.id) ?? undefined,
        touched: operation.serverTimestamp(),
      },
    }
  }

  private getDrillRecords(operation: FirestoreOperation, dto: CreateDrillReportDto, userId: string): FirestoreRecord[] {
    return dto.drills.map(drill => this.processDrill(operation, dto, drill))
  }

  private processDrill(operation: FirestoreOperation, dto: CreateDrillReportDto, drill: Drill): FirestoreRecord {
    this.logger.debug(`Processing drill: ${drill.name}`)

    const crew = dto.crewInvolvedIds
    const reportDate = DateTime.fromISO(dto.dateCompleted)

    const newCrewData: DrillCrewData = {}

    crew.forEach(crewId => {
      const existingCrewData: DrillCrewData = drill.crew ?? {}

      const newDateDue = addInterval(reportDate, drill.interval).toISODate()

      if (!newDateDue) {
        throw new Error(`Unable to calculate new dateDue for Drill: {${drill.name}} and User: {${crewId}}`)
      }

      const userDrillData = existingCrewData[crewId]
      if (!userDrillData) {
        this.logger.debug(`User with ID {${crewId}} does not exist on drill. Will treat as new.`)

        return (newCrewData[crewId] = {
          dateDue: newDateDue,
          dateLastCompleted: dto.dateCompleted,
        })
      }

      const crewDate = DateTime.fromISO(userDrillData.dateLastCompleted)
      const isReportMostRecent = reportDate > crewDate

      if (!isReportMostRecent) {
        return this.logger.debug(
          `New drill report is older than existing drill report for User: {${crewId}} on Drill: {${drill.id}}. Will not update drill.`
        )
      }

      return (newCrewData[crewId] = {
        dateDue: newDateDue,
        dateLastCompleted: dto.dateCompleted,
      })
    })

    return {
      ref: operation.makeRef('drills', drill.id),
      data: {
        crew: newCrewData,
      },
      options: { merge: true },
    }
  }
}
