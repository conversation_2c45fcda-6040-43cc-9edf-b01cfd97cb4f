import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { DocRef } from '@src/domain/data/IFirestoreService'

export interface IBaseDataService<CreateDto = undefined, UpdateDto = undefined> {
  createDataItem(
    operation: FirestoreOperation,
    createItemDto: CreateDto,
    userId: string,
    licenseeId?: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateDataItem(
    operation: FirestoreOperation,
    updateItemDto: UpdateDto,
    userId: string,
    licenseeId?: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}
