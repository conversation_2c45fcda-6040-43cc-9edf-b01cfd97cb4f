import { IBaseDataService } from '@src/domain/services/BaseService'
import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { DocRef } from '@src/domain/data/IFirestoreService'
import { CreateSafetyMeetingReportDto } from '@src/domain/use-cases/safetyMeetingReports/CreateSafetyMeetingReportUseCase'
import { injectable } from 'inversify'
import { UpdateSafetyMeetingReportDto } from '@src/domain/use-cases/safetyMeetingReports/UpdateSafetyMeetingReportUseCase.ts'

@injectable()
export class SafetyMeetingReportService
  implements IBaseDataService<CreateSafetyMeetingReportDto, UpdateSafetyMeetingReportDto>
{
  createDataItem(
    operation: FirestoreOperation,
    createItemDto: CreateSafetyMeetingReportDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const safetyMeetingReportCreatedRecord = {
      ref: operation.makeRef('safetyMeetingReports'),
      data: {
        ...createItemDto,
        addedBy: userId,
        whenAdded: Date.now(),
        touched: operation.serverTimestamp(),
      },
    }

    return {
      ref: safetyMeetingReportCreatedRecord.ref,
      records: [safetyMeetingReportCreatedRecord],
    }
  }

  updateDataItem(
    operation: FirestoreOperation,
    updateItemDto: UpdateSafetyMeetingReportDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, ...rest } = updateItemDto

    const safetyMeetingReportUpdatedRecord = {
      ref: operation.makeRef('safetyMeetingReports', id),
      data: {
        ...rest,
        updatedBy: userId,
        whenUpdated: Date.now(),
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return {
      ref: safetyMeetingReportUpdatedRecord.ref,
      records: [safetyMeetingReportUpdatedRecord],
    }
  }
}
