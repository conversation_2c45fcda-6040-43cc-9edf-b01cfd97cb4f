import { deleteValue, FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { DocRef } from '@src/domain/data/IFirestoreService'
import { CreateSafetyEquipmentExpiryDto } from '@src/domain/use-cases/safety/CreateSafetyEquipmentExpiryUseCase'
import { DateTime } from 'luxon'
import { addInterval, subtractInterval } from '@src/lib/datesAndTime'
import { UpdateSafetyEquipmentExpiryDto } from '@src/domain/use-cases/safety/UpdateSafetyEquipmentExpiryUseCase'
import { CompleteSafetyEquipmentExpiryDto } from '@src/domain/use-cases/safety/CompleteSafetyEquipmentExpiryUseCase'
import { inject, injectable } from 'inversify'
import { fieldOrEmptyObject } from '@src/domain/util/DataUtils'
import { UpdateCompletedSafetyEquipmentExpiryDto } from '@src/domain/use-cases/safety/UpdateCompletedSafetyEquipmentExpiryUseCase'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { SafetyEquipmentTaskCompleted } from '@src/shared-state/VesselSafety/useCompletedSafetyEquipmentItems'
import { seaFilesToValue } from '@src/lib/fileImports'

export interface ISafetyEquipmentExpiryService {
  createSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    createDto: CreateSafetyEquipmentExpiryDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
  updateSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    updateDto: UpdateSafetyEquipmentExpiryDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
  completeSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    dto: CompleteSafetyEquipmentExpiryDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
  updateCompletedSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    dto: UpdateCompletedSafetyEquipmentExpiryDto,
    userId: string,
    licenseeId: string
  ): Promise<{
    ref: DocRef
    records: FirestoreRecord[]
  }>
  deleteSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    safetyEquipmentId: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class SafetyEquipmentExpiryService implements ISafetyEquipmentExpiryService {
  private readonly logger: ILogger
  private readonly firestoreService: IFirestoreService

  constructor(
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(SERVICES.IFirestoreService) firestoreService: IFirestoreService
  ) {
    this.logger = logger.scoped('SafetyEquipmentExpiryService')
    this.firestoreService = firestoreService
  }
  public createSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    dto: CreateSafetyEquipmentExpiryDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const ref = operation.makeRef('safetyEquipmentItems')

    const isServicableOrExpiring = dto.type === 'servicable' || dto.type === 'expiring'

    const description = isServicableOrExpiring ? dto.description : undefined
    const emailReminder = isServicableOrExpiring ? dto.emailReminder : undefined
    const dateDue = this.getDateDue(dto)
    const dateToRemind = isServicableOrExpiring ? this.getDateToRemind(dateDue, dto.emailReminder) : undefined

    const data = {
      vesselId: dto.vesselId,
      addedBy: userId,
      whenAdded: DateTime.now().toUTC().toMillis(),
      itemId: dto.safetyItemId,
      type: dto.type,
      locationId: dto.locationId,
      ...fieldOrEmptyObject('quantity', dto.quantity),
      ...fieldOrEmptyObject('description', description),
      ...fieldOrEmptyObject('dateDue', dateDue),
      ...fieldOrEmptyObject('interval', dto.interval),
      ...fieldOrEmptyObject('whenLastChecked', dto.lastCheck),
      ...fieldOrEmptyObject('emailReminder', emailReminder),
      ...fieldOrEmptyObject('dateToRemind', dateToRemind),
      files: dto.files,
      state: 'active',
      touched: operation.serverTimestamp(),
    }

    const safetyEquipmentExpiryRecord = {
      ref,
      data,
    }

    const vesselSafetyItemRecord = {
      ref: operation.makeRef('vesselSafetyItems', dto.safetyItemId),
      data: {
        isCritical: dto.isCritical ?? false,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', dto.vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        safetyEquipmentItems: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [dto.vesselId]: {
          safetyEquipmentItems: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }

    return {
      ref,
      records: [safetyEquipmentExpiryRecord, vesselSafetyItemRecord, whenVesselTouchedRecord, overdueStatsRecord],
    }
  }

  updateSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    dto: UpdateSafetyEquipmentExpiryDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const ref = operation.makeRef('safetyEquipmentItems', dto.docId)

    console.log('DTO:', { dto, ref })
    const isServicableOrExpiring = dto.type === 'servicable' || dto.type === 'expiring'

    const description = isServicableOrExpiring ? dto.description : undefined
    const emailReminder = isServicableOrExpiring ? dto.emailReminder : undefined
    const dateDue = this.getDateDue(dto)
    const dateToRemind = isServicableOrExpiring ? this.getDateToRemind(dateDue, dto.emailReminder) : undefined

    const data = {
      updatedBy: userId,
      whenUpdated: DateTime.now().toUTC().toMillis(),
      itemId: dto.safetyItemId,
      type: dto.type,
      locationId: dto.locationId,
      quantity: dto.quantity ?? deleteValue,
      description: description ?? deleteValue,
      dateDue: dateDue,
      interval: dto.type === 'servicable' ? dto.interval : deleteValue,
      emailReminder: emailReminder ?? deleteValue,
      dateToRemind: dateToRemind,
      files: dto.files,
      touched: operation.serverTimestamp(),
    }

    const safetyEquipmentExpiryRecord = {
      ref,
      data,
      options: { merge: true },
    }

    const vesselSafetyItemRecord = {
      ref: operation.makeRef('vesselSafetyItems', dto.safetyItemId),
      data: {
        isCritical: dto.isCritical ?? false,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', dto.vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        safetyEquipmentItems: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [dto.vesselId]: {
          safetyEquipmentItems: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }

    return {
      ref,
      records: [safetyEquipmentExpiryRecord, vesselSafetyItemRecord, whenVesselTouchedRecord, overdueStatsRecord],
    }
  }

  completeSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    dto: CompleteSafetyEquipmentExpiryDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const completedRecordRef = operation.makeRef('safetyEquipmentTaskCompleted')

    const whenLatestCompleted = Math.max(dto.whenCompleted, dto.whenLastChecked ?? 0)
    const dateExpires = dto.type === 'expiring' ? dto.newExpiryDate : '' // TODO - Return undefined instead of string
    const dateDue = addInterval(whenLatestCompleted, dto.interval).toISODate()
    const dateToRemind = dto.emailReminder ? subtractInterval(dateDue, dto.emailReminder).toISODate() : undefined

    const completedRecord = {
      ref: completedRecordRef,
      data: {
        safetyEquipmentId: dto.safetyEquipmentId,
        completedBy: userId,
        whenAdded: DateTime.now().toUTC().toMillis(),
        addedBy: userId,
        vesselId: dto.vesselId,
        notes: dto.notes ?? undefined,
        whenCompleted: dto.whenCompleted,
        dateExpires: dateExpires,
        files: dto.files,
        state: 'active',
        touched: operation.serverTimestamp(),
      },
    }

    const updatedSafetyEquipmentItemRecord = {
      ref: operation.makeRef('safetyEquipmentItems', dto.safetyEquipmentId),
      data: {
        whenLastChecked: dto.whenCompleted,
        dateDue: dateDue,
        dateToRemind: dateToRemind,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', dto.vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        safetyEquipmentItems: operation.serverTimestamp(),
        safetyEquipmentTaskCompleted: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [dto.vesselId]: {
          safetyEquipmentItems: {
            stale: true,
          },
          safetyEquipmentTaskCompleted: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }

    return {
      ref: completedRecordRef,
      records: [completedRecord, updatedSafetyEquipmentItemRecord, whenVesselTouchedRecord, overdueStatsRecord],
    }
  }

  private getDateToRemind(dateDue: string | undefined, emailReminder: string | undefined) {
    if (dateDue && emailReminder) {
      return subtractInterval(dateDue, emailReminder).toISODate()
    }
    return undefined
  }

  private getDateDue(dto: CreateSafetyEquipmentExpiryDto) {
    if (dto.type === 'nonExpiring') return undefined

    if (dto.type === 'expiring') return dto.expiryDate

    if (!dto.lastCheck || !dto.interval) {
      throw new Error('Unable to calculate dateDue as lastCheck or interval is missing')
    }

    return addInterval(dto.lastCheck, dto.interval).toISODate() ?? undefined
  }

  public deleteSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    safetyEquipmentId: string,
    vesselId: string,
    userId: string,
    licenseeId: string
  ) {
    const deletedSafetyEquipmentRecord = {
      ref: operation.makeRef('safetyEquipmentItems', safetyEquipmentId),
      data: {
        deletedBy: userId,
        whenDeleted: DateTime.now().toUTC().toMillis(),
        state: 'deleted',
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        safetyEquipmentItems: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [vesselId]: {
          safetyEquipmentItems: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }

    return {
      ref: deletedSafetyEquipmentRecord.ref,
      records: [deletedSafetyEquipmentRecord, whenVesselTouchedRecord, overdueStatsRecord],
    }
  }

  public async updateCompletedSafetyEquipmentExpiry(
    operation: FirestoreOperation,
    dto: UpdateCompletedSafetyEquipmentExpiryDto,
    userId: string,
    licenseeId: string
  ): Promise<{
    ref: DocRef
    records: FirestoreRecord[]
  }> {
    const updatedCompletedSafetyEquipmentExpiryRecord = {
      ref: operation.makeRef('safetyEquipmentTaskCompleted', dto.completedSafetyEquipmentExpiryId),
      data: {
        updatedBy: userId,
        whenUpdated: DateTime.now().toUTC().toMillis(),
        whenCompleted: dto.whenCompleted,
        notes: dto.notes ?? deleteValue,
        files: dto.files,
        dateExpires: dto.newExpiryDate ?? deleteValue,
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const records: FirestoreRecord[] = [updatedCompletedSafetyEquipmentExpiryRecord]

    // Determine if and how to update the parent safety equipment item
    const reconciliation = await this.getCompletionDateReconciliation(
      dto.safetyEquipmentId,
      dto.completedSafetyEquipmentExpiryId,
      dto.whenCompleted,
      dto.newExpiryDate
    )

    if (reconciliation.shouldUpdateParent && reconciliation.parentUpdateDate) {
      const updatedSafetyEquipmentItemRecord = {
        ref: operation.makeRef('safetyEquipmentItems', dto.safetyEquipmentId),
        data: {
          whenLastChecked: reconciliation.parentUpdateDate,
          ...(reconciliation.parentNewExpiryDate && {
            dateDue: reconciliation.parentNewExpiryDate,
          }),
          touched: operation.serverTimestamp(),
        },
        options: { merge: true },
      }
      records.push(updatedSafetyEquipmentItemRecord)
    }

    // Add standard tracking records
    const whenVesselTouchedRecord = {
      ref: operation.makeRef('whenVesselTouched', dto.vesselId),
      data: {
        licenseeId,
        touched: operation.serverTimestamp(),
        safetyEquipmentItems: operation.serverTimestamp(),
        safetyEquipmentTaskCompleted: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    const overdueStatsRecord = {
      ref: operation.makeRef('overdueStats', licenseeId),
      data: {
        [dto.vesselId]: {
          safetyEquipmentItems: {
            stale: true,
          },
          safetyEquipmentTaskCompleted: {
            stale: true,
          },
        },
      },
      options: { merge: true },
    }

    records.push(whenVesselTouchedRecord, overdueStatsRecord)

    return {
      ref: updatedCompletedSafetyEquipmentExpiryRecord.ref,
      records,
    }
  }

  private async getCompletionDateReconciliation(
    safetyEquipmentId: string,
    currentCompletionId: string,
    newCompletionDate: number,
    newExpiryDate?: string
  ): Promise<{
    shouldUpdateParent: boolean
    parentUpdateDate?: number
    parentNewExpiryDate?: string
  }> {
    // Fetch all completed safety equipment tasks for this safety equipment item
    const completedTasks = await this.firestoreService.getDocuments<SafetyEquipmentTaskCompleted>(
      'safetyEquipmentTaskCompleted',
      [
        {
          field: 'safetyEquipmentId',
          operator: '==',
          value: safetyEquipmentId,
        },
        { field: 'state', operator: '==', value: 'active' },
      ],
      []
    )

    // Filter out the current completion being edited
    const otherCompletions = completedTasks.filter(task => task.id !== currentCompletionId)

    // Sort by completion date (most recent first)
    const sortedOtherCompletions = otherCompletions.sort((a, b) => b.whenCompleted - a.whenCompleted)

    // Find the current completion in the original list
    const currentCompletion = completedTasks.find(task => task.id === currentCompletionId)
    const wasOriginallyMostRecent =
      !sortedOtherCompletions.length ||
      (currentCompletion && currentCompletion.whenCompleted >= sortedOtherCompletions[0].whenCompleted)

    // Determine if the new date will be the most recent
    const willBecomeMostRecent =
      !sortedOtherCompletions.length || newCompletionDate >= sortedOtherCompletions[0].whenCompleted

    if (willBecomeMostRecent) {
      // New date becomes the most recent - use it for parent update
      return {
        shouldUpdateParent: true,
        parentUpdateDate: newCompletionDate,
        parentNewExpiryDate: newExpiryDate,
      }
    } else if (wasOriginallyMostRecent && !willBecomeMostRecent) {
      // Was most recent but now isn't - fall back to the next most recent
      if (sortedOtherCompletions.length > 0) {
        const nextMostRecent = sortedOtherCompletions[0]
        return {
          shouldUpdateParent: true,
          parentUpdateDate: nextMostRecent.whenCompleted,
          parentNewExpiryDate: nextMostRecent.dateExpires,
        }
      } else {
        // No other completions exist, don't update parent
        return { shouldUpdateParent: false }
      }
    } else {
      // Was not most recent and doesn't become most recent - no parent update needed
      return { shouldUpdateParent: false }
    }
  }
}
