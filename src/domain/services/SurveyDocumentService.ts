import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { DocRef } from '../data/IFirestoreService'
import { UpdateSurveyDocumentDto } from '../use-cases/vesselDocumentRegister/UpdateSurveyDocumentUseCase'
import { CreateSurveyDocumentDto } from '../use-cases/vesselDocumentRegister/CreateSurveyDocumentUseCase'
import { DeleteSurveyDocumentDto } from '../use-cases/vesselDocumentRegister/DeleteSurveyDocumentUseCase'

export interface ISurveyDocumentService {
  updateSurveyDocument(
    operation: FirestoreOperation,
    updateSurveyDocumentDto: UpdateSurveyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  createSurveyDocument(
    operation: FirestoreOperation,
    createSurveyDocumentDto: CreateSurveyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  deleteSOP(
    operation: FirestoreOperation,
    deleteSurveyDocumentDto: DeleteSurveyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class SurveyDocumentService implements ISurveyDocumentService {
  updateSurveyDocument(
    operation: FirestoreOperation,
    updateSurveyDocumentDto: UpdateSurveyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, id, dateSurveyed, surveyor, personnelPresent, location, inOrOutWater, files, ...rest } =
      updateSurveyDocumentDto

    const data = {
      ...rest,
      dateSurveyed: dateSurveyed ?? operation.deleteField(),
      surveyor: surveyor ?? operation.deleteField(),
      personnelPresent: personnelPresent ?? operation.deleteField(),
      location: location ?? operation.deleteField(),
      inOrOutWater: inOrOutWater ?? operation.deleteField(),
      files: files ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedSurveyDocumentRecord = {
      ref: operation.makeRef('surveyReports', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'surveyReports', licenseeId, vesselId)

    return {
      ref: updatedSurveyDocumentRecord.ref,
      records: [updatedSurveyDocumentRecord, vesselTouched],
    }
  }

  createSurveyDocument(
    operation: FirestoreOperation,
    createSurveyDocumentDto: CreateSurveyDocumentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, dateSurveyed, surveyor, personnelPresent, location, inOrOutWater, files, ...rest } =
      createSurveyDocumentDto

    const data = {
      ...rest,
      ...(dateSurveyed ? { dateSurveyed } : {}),
      ...(surveyor ? { surveyor } : {}),
      ...(personnelPresent ? { personnelPresent } : {}),
      ...(location ? { location } : {}),
      ...(inOrOutWater ? { inOrOutWater } : {}),
      ...(files ? { files } : {}),
      vesselId,
      state: 'active',
      addedBy: userId,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const newSurveyDocumentRecord = {
      ref: operation.makeRef('surveyReports'),
      data,
    }

    const vesselTouched = this.vesselTouched(operation, 'surveyReports', licenseeId, vesselId)

    return {
      ref: newSurveyDocumentRecord.ref,
      records: [newSurveyDocumentRecord, vesselTouched],
    }
  }

  deleteSOP(
    operation: FirestoreOperation,
    deleteSurveyDocumentDto: Omit<DeleteSurveyDocumentDto, 'title'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, id } = deleteSurveyDocumentDto

    const data = {
      state: 'deleted',
      deletedBy: userId,
      whenDeleted: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const updatedSurveyDocumentRecord = {
      ref: operation.makeRef('surveyReports', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'surveyReports', licenseeId, vesselId)

    return {
      ref: updatedSurveyDocumentRecord.ref,
      records: [updatedSurveyDocumentRecord, vesselTouched],
    }
  }

  private vesselTouched(operation: FirestoreOperation, collectionName: string, licenseeId: string, vesselId: string) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    }

    const vesselTouched = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: vesselData,
      options: { merge: true },
    }

    return vesselTouched
  }
}
