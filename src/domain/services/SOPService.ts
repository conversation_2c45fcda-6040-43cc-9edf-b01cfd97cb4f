import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { DocRef } from '../data/IFirestoreService'
import { UpdateSOPDto } from '../use-cases/vesselDocumentRegister/UpdateSOPUseCase'
import { CreateSOPDto } from '../use-cases/vesselDocumentRegister/CreateSOPUseCase'
import { DeleteSOPDto } from '../use-cases/vesselDocumentRegister/DeleteSOPUseCase'

export interface ISOPService {
  updateSOP(
    operation: FirestoreOperation,
    updateSOPDto: UpdateSOPDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  createSOP(
    operation: FirestoreOperation,
    createSOPDto: CreateSOPDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  deleteSOP(
    operation: FirestoreOperation,
    deleteSOPDto: DeleteSOPDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class SOPService implements ISOPService {
  updateSOP(
    operation: FirestoreOperation,
    updateSOPDto: UpdateSOPDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, id, categoryId, sfdoc, files, dateIssued, ...rest } = updateSOPDto

    const data = {
      ...rest,
      dateIssued: dateIssued ?? operation.deleteField(),
      categoryId: categoryId ?? operation.deleteField(),
      files: files ?? operation.deleteField(),
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedSOPRecord = {
      ref: operation.makeRef('SOPs', id),
      data,
      options: { merge: true },
    }

    //TODO Create a new category if it doesn't exist
    //TODO: Add Links

    const vesselTouched = this.vesselTouched(operation, 'SOPs', licenseeId, vesselId)

    return {
      ref: updatedSOPRecord.ref,
      records: [updatedSOPRecord, vesselTouched],
    }
  }

  createSOP(
    operation: FirestoreOperation,
    createSOPDto: CreateSOPDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, dateIssued, categoryId, sfdoc, files, ...rest } = createSOPDto

    const data = {
      ...rest,
      ...(dateIssued ? { dateIssued } : {}),
      ...(categoryId ? { categoryId } : {}),
      ...(files ? { files: files } : {}),
      vesselId,
      state: 'active',
      addedBy: userId,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const updatedSOPRecord = {
      ref: operation.makeRef('SOPs'),
      data,
    }

    //TODO Create a new category if it doesn't exist
    //TODO: Add Links

    const vesselTouched = this.vesselTouched(operation, 'SOPs', licenseeId, vesselId)

    return {
      ref: updatedSOPRecord.ref,
      records: [updatedSOPRecord, vesselTouched],
    }
  }

  deleteSOP(
    operation: FirestoreOperation,
    deleteSOPDto: Omit<DeleteSOPDto, 'title'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { vesselId, id } = deleteSOPDto

    const data = {
      state: 'deleted',
      deletedBy: userId,
      whenDeleted: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const updatedSOPRecord = {
      ref: operation.makeRef('SOPs', id),
      data,
      options: { merge: true },
    }

    const vesselTouched = this.vesselTouched(operation, 'SOPs', licenseeId, vesselId)

    return {
      ref: updatedSOPRecord.ref,
      records: [updatedSOPRecord, vesselTouched],
    }
  }

  private vesselTouched(operation: FirestoreOperation, collectionName: string, licenseeId: string, vesselId: string) {
    const vesselData = {
      licenseeId,
      touched: operation.serverTimestamp(),
      [collectionName]: operation.serverTimestamp(),
    }

    const vesselTouched = {
      ref: operation.makeRef('whenVesselTouched', vesselId),
      data: vesselData,
      options: { merge: true },
    }

    return vesselTouched
  }
}
