import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { DocRef } from '@src/domain/data/IFirestoreService'
import { inject, injectable } from 'inversify'
import { CreateIncidentDto } from '@src/domain/use-cases/incident/CreateIncidentUseCase'
import { UpdateIncidentDto } from '@src/domain/use-cases/incident/UpdateIncidentUseCase'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { SERVICES } from '@src/domain/di/ServiceRegistry'

export interface IIncidentService {
  createIncident(
    operation: FirestoreOperation,
    createIncidentDto: CreateIncidentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateIncident(
    operation: FirestoreOperation,
    updateIncidentDto: UpdateIncidentDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class IncidentService implements IIncidentService {
  private readonly logger: ILogger
  constructor(@inject(SERVICES.ILogger) logger: ILogger) {
    this.logger = logger.scoped('IncidentService')
  }

  createIncident(
    operation: FirestoreOperation,
    createIncidentDto: CreateIncidentDto,
    userId: string,
    licenseeId: string
  ) {
    const incidentCreateRecord = {
      ref: operation.makeRef('incidents'),
      data: {
        ...createIncidentDto,
        // Note: Do not use the enum here because the ServiceContainer will be loaded first before the enum file
        state: createIncidentDto.state ?? 'draft',
        addedBy: userId,
        licenseeId: licenseeId,
        whenAdded: Date.now(),
        touched: operation.serverTimestamp(),
      },
    }

    return {
      ref: incidentCreateRecord.ref,
      records: [incidentCreateRecord],
    }
  }

  updateIncident(
    operation: FirestoreOperation,
    updateIncidentDto: UpdateIncidentDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, ...rest } = updateIncidentDto

    const incidentUpdateRecord = {
      ref: operation.makeRef('incidents', updateIncidentDto.id),
      data: {
        ...rest,
        updatedBy: userId,
        whenUpdated: Date.now(),
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return {
      ref: incidentUpdateRecord.ref,
      records: [incidentUpdateRecord],
    }
  }
}
