import { injectable } from 'inversify'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { CreateCompanyPlanDto } from '@src/domain/use-cases/companyDocumentRegister/CreateCompanyPlanUseCase'
import { UpdateCompanyPlanDto } from '@src/domain/use-cases/companyDocumentRegister/UpdateCompanyPlanUseCase'
import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { DocRef } from '@src/domain/data/IFirestoreService'

@injectable()
export class CompanyPlanService implements IBaseDataService<CreateCompanyPlanDto, UpdateCompanyPlanDto> {
  createDataItem(
    operation: FirestoreOperation,
    createItemDto: CreateCompanyPlanDto,
    userId: string
    // licenseeId: string,
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const companyPlanCreatedRecord = {
      ref: operation.makeRef('companyPlans'),
      data: {
        sfdoc: createItemDto.sfdoc,
        addedBy: userId,
        whenAdded: Date.now(),
        touched: operation.serverTimestamp(),
      },
    }

    return {
      ref: companyPlanCreatedRecord.ref,
      records: [companyPlanCreatedRecord],
    }
  }

  updateDataItem(
    operation: FirestoreOperation,
    updateItemDto: UpdateCompanyPlanDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { sfdoc } = updateItemDto

    const companyPlanUpdatedRecord = {
      ref: operation.makeRef('companyPlans', licenseeId),
      data: {
        sfdoc: sfdoc,
        updatedBy: userId,
        whenUpdated: Date.now(),
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return {
      ref: companyPlanUpdatedRecord.ref,
      records: [companyPlanUpdatedRecord],
    }
  }
}
