import { injectable } from 'inversify'
import { FirestoreOperation, FirestoreRecord } from '../data/FirestoreOperation'
import { CompleteCustomFormDto } from '../use-cases/companyDocumentRegister/CompleteCustomFormUseCase'
import { DocRef } from '../data/IFirestoreService'
import { UpdateCompletedCustomFormDto } from '../use-cases/companyDocumentRegister/UpdateCompletedCustomFormUseCase'
import { CreateCustomFormDto } from '../use-cases/companyDocumentRegister/CreateCustomFormUseCase'

export interface ICustomFormService {
  completeCustomForm(
    operation: FirestoreOperation,
    dto: Omit<CompleteCustomFormDto, 'title' | 'filesPosition' | 'signaturePosition'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  updateCompletedCustomForm(
    operation: FirestoreOperation,
    dto: Omit<UpdateCompletedCustomFormDto, 'filesPosition' | 'signaturePosition'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }

  createCustomForm(
    operation: FirestoreOperation,
    dto: CreateCustomFormDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  }
}

@injectable()
export class CustomFormService implements ICustomFormService {
  completeCustomForm(
    operation: FirestoreOperation,
    dto: Omit<CompleteCustomFormDto, 'title' | 'filesPosition' | 'signaturePosition'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      vesselIds,
      personnelIds,
      formData,
      attachTo,
      attachToId,
      attachToTrainingTaskId,
      attachToVesselId,
      versionId,
      ...rest
    } = dto

    const data = {
      ...rest,
      licenseeId,
      vesselIds: vesselIds && vesselIds.length > 0 ? vesselIds : ['none'],
      personnelIds: personnelIds && personnelIds.length > 0 ? personnelIds : [],
      data: formData,
      attachTo: attachTo ?? null,
      ...(attachToId
        ? {
            attachToId,
          }
        : {}),
      ...(attachToTrainingTaskId
        ? {
            attachToTrainingTaskId,
          }
        : {}),
      ...(attachToVesselId
        ? {
            attachToVesselId,
          }
        : {}),
      state: 'active',
      addedBy: userId,
      whenAdded: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
    }

    const records: FirestoreRecord[] = []

    const updatedSOPRecord = {
      ref: operation.makeRef('customFormsCompleted'),
      data,
    }

    records.push(updatedSOPRecord)

    const versionData = {
      numCompleted: operation.increment(1),
      touched: operation.serverTimestamp(),
    }

    const updatedVersionRecord = {
      ref: operation.makeRef('customFormVersions', versionId),
      data: versionData,
      options: { merge: true },
    }

    records.push(updatedVersionRecord)

    if (!dto.isDraft) {
      const updatedCustomFormData = this.updateCustomForm(operation, dto.customFormId, userId, vesselIds)

      records.push(updatedCustomFormData)
    }

    const licenseeTouchedCustomForms = this.licenseeTouched(operation, 'customForms', licenseeId)

    records.push(licenseeTouchedCustomForms)

    const licenseeTouchedCustomFormsCompleted = this.licenseeTouched(operation, 'customFormsCompleted', licenseeId)

    records.push(licenseeTouchedCustomFormsCompleted)

    return {
      ref: updatedSOPRecord.ref,
      records,
    }
  }

  updateCompletedCustomForm(
    operation: FirestoreOperation,
    dto: Omit<UpdateCompletedCustomFormDto, 'title' | 'filesPosition' | 'signaturePosition'>,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, customFormId, vesselIds, personnelIds, formData, ...rest } = dto

    const data = {
      ...rest,
      vesselIds: vesselIds && vesselIds.length > 0 ? vesselIds : ['none'],
      personnelIds: personnelIds && personnelIds.length > 0 ? personnelIds : [],
      data: formData,
      updatedBy: userId,
      whenUpdated: Date.now(), // Use batchTrace.whenUpdated?
      touched: operation.serverTimestamp(),
      ...(!dto.isDraft
        ? {
            whenAdded: Date.now(), // Use batchTrace.whenUpdated?
            addedBy: userId,
          }
        : {}),
    }

    const records: FirestoreRecord[] = []

    const updatedCompletedFormData = {
      ref: operation.makeRef('customFormsCompleted', id),
      data,
      options: { merge: true },
    }

    records.push(updatedCompletedFormData)

    if (!dto.isDraft) {
      const updatedCustomFormData = this.updateCustomForm(operation, dto.customFormId, userId, vesselIds)

      records.push(updatedCustomFormData)
    }

    const licenseeTouchedCustomForms = this.licenseeTouched(operation, 'customForms', licenseeId)

    records.push(licenseeTouchedCustomForms)

    const licenseeTouchedCustomFormsCompleted = this.licenseeTouched(operation, 'customFormsCompleted', licenseeId)

    records.push(licenseeTouchedCustomFormsCompleted)

    return {
      ref: updatedCompletedFormData.ref,
      records,
    }
  }

  createCustomForm(
    operation: FirestoreOperation,
    dto: CreateCustomFormDto,
    userId: string,
    licenseeId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const {
      categoryId,
      forCrew,
      forVesselIds,
      isTemplate,
      templateCategory,
      files,
      latestVersion,
      form,
      historyElement,
      ...rest
    } = dto

    const version = latestVersion ?? Date.now()

    const data = {
      ...rest,
      licenseeId,
      ...(categoryId ? { categoryId } : {}),
      forVesselIds: forVesselIds && forVesselIds.length > 0 ? forVesselIds : ['none'],
      forCrew: forCrew ? true : false,
      ...(isTemplate !== undefined ? { isTemplate: isTemplate ? true : false } : {}),
      ...(templateCategory ? { templateCategory } : {}),
      latestVersion: version,
      state: 'draft',
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const records: FirestoreRecord[] = []

    const customFormRecord = {
      ref: operation.makeRef('customForms'),
      data,
    }

    records.push(customFormRecord)

    if (isTemplate) {
      files?.forEach(file => {
        if (file.id) {
          const filesRecord = {
            ref: operation.makeRef('files', file.id),
            data: { canShare: true },
            options: { merge: true },
          }

          records.push(filesRecord)
        }
      })
    }

    const versionData = {
      customFormId: customFormRecord.ref.id,
      licenseeId,
      version,
      numCompleted: 0,
      state: 'active',
      forVesselIds: forVesselIds && forVesselIds.length > 0 ? forVesselIds : ['none'],
      historyElementN: historyElement ?? 0,
      form,
      addedBy: userId,
      whenAdded: Date.now(),
      touched: operation.serverTimestamp(),
    }

    const customFormVersionsRecord = {
      ref: operation.makeRef('customFormVersions'),
      data: versionData,
    }

    records.push(customFormVersionsRecord)

    const licenseeTouchedCustomForms = this.licenseeTouched(operation, 'customForms', licenseeId)

    records.push(licenseeTouchedCustomForms)

    //TODO Links Logic

    return {
      ref: customFormRecord.ref,
      records,
    }
  }

  private updateCustomForm(operation: FirestoreOperation, customFormId: string, userId: string, vesselIds?: string[]) {
    const customFormData = {
      whenLastCompleted: Date.now(),
      lastCompletedBy: userId,
      lastCompletedVesselIds: vesselIds && vesselIds.length > 0 ? vesselIds : [],
      touched: operation.serverTimestamp(),
    }

    const updatedCustomFormData = {
      ref: operation.makeRef('customForms', customFormId),
      data: customFormData,
      options: { merge: true },
    }

    return updatedCustomFormData
  }

  private licenseeTouched(operation: FirestoreOperation, collection: string, licenseeId: string) {
    const licenseeData = {
      touched: operation.serverTimestamp(),
      [collection]: operation.serverTimestamp(),
    }

    const licenseeTouched = {
      ref: operation.makeRef('whenLicenseeTouched', licenseeId),
      data: licenseeData,
      options: { merge: true },
    }

    return licenseeTouched
  }
}
