import { FirestoreOperation, FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { DocRef } from '@src/domain/data/IFirestoreService'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { CreateRiskDto } from '@src/domain/use-cases/risks/CreateRiskUseCase'
import { injectable } from 'inversify'
import { UpdateRiskDto } from '@src/domain/use-cases/risks/UpdateRiskUseCase'

@injectable()
export class RiskService implements IBaseDataService<CreateRiskDto, UpdateRiskDto> {
  createDataItem(
    operation: FirestoreOperation,
    createItemDto: CreateRiskDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const riskCreatedRecord = {
      ref: operation.makeRef('risks'),
      data: {
        ...createItemDto,
        addedBy: userId,
        whenAdded: Date.now(),
        touched: operation.serverTimestamp(),
      },
    }

    return {
      ref: riskCreatedRecord.ref,
      records: [riskCreatedRecord],
    }
  }

  updateDataItem(
    operation: FirestoreOperation,
    updateItemDto: UpdateRiskDto,
    userId: string
  ): {
    ref: DocRef
    records: FirestoreRecord[]
  } {
    const { id, ...rest } = updateItemDto

    const riskUpdatedRecord = {
      ref: operation.makeRef('risks', id),
      data: {
        ...rest,
        updatedBy: userId,
        whenUpdated: Date.now(),
        touched: operation.serverTimestamp(),
      },
      options: { merge: true },
    }

    return {
      ref: riskUpdatedRecord.ref,
      records: [riskUpdatedRecord],
    }
  }
}
