import { FirebaseFirestoreTypes } from '@react-native-firebase/firestore'
import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth'
import firestore from '@react-native-firebase/firestore'
import functions, { FirebaseFunctionsTypes } from '@react-native-firebase/functions'
import { IFirebase, NativeFirestore } from './IFirebase' // adjust import paths if needed

export class FirebaseNative implements IFirebase {
  private initialized = false
  private firestoreInstance: FirebaseFirestoreTypes.Module | null = null
  private authInstance: FirebaseAuthTypes.Module | null = null
  private functionsInstance: FirebaseFunctionsTypes.Module | null = null

  public async initialize(): Promise<void> {
    if (this.initialized) return

    // These return already-initialized instances, but calling them here ensures clarity and symmetry
    this.firestoreInstance = firestore()
    this.authInstance = auth()
    this.functionsInstance = functions()

    this.initialized = true
  }

  public getFirestore(): NativeFirestore {
    if (!this.firestoreInstance) {
      throw new Error('Firestore not initialized')
    }
    return this.firestoreInstance
  }

  public getAuth(): FirebaseAuthTypes.Module {
    if (!this.authInstance) {
      throw new Error('Auth not initialized')
    }
    return this.authInstance
  }

  public getFunctions(): FirebaseFunctionsTypes.Module {
    if (!this.functionsInstance) {
      throw new Error('Functions not initialized')
    }
    return this.functionsInstance
  }
}
