import { FirebaseFirestoreTypes } from '@react-native-firebase/firestore'
import { FirebaseFunctionsTypes } from '@react-native-firebase/functions'
import { FirebaseAuthTypes } from '@react-native-firebase/auth'
import { Firestore as WFirestore } from '@firebase/firestore'
import { Functions as WebFunctions } from '@firebase/functions'
import { Auth as WebAuth } from '@firebase/auth'

export const FUNCTIONS_REGION = 'australia-southeast1'

export type WebFirestore = WFirestore
export type NativeFirestore = FirebaseFirestoreTypes.Module
export type Firestore = WebFirestore | NativeFirestore
export type Functions = WebFunctions | FirebaseFunctionsTypes.Module
export type Auth = WebAuth | FirebaseAuthTypes.Module

export interface IFirebase {
  initialize(): Promise<void>
  getFirestore(): Firestore
  getAuth(): Auth
  getFunctions(): Functions
}
