import { FirebaseApp, initializeApp } from 'firebase/app'
import { Auth, getAuth } from 'firebase/auth'
import { Functions, getFunctions } from 'firebase/functions'
import firebaseConfig from '@src/lib/firebase/config/firebase.config'
import {
  CACHE_SIZE_UNLIMITED,
  initializeFirestore,
  persistentLocalCache,
  persistentMultipleTabManager,
} from 'firebase/firestore'
import { FUNCTIONS_REGION, IFirebase, WebFirestore } from '@src/domain/IFirebase'

export class FirebaseWeb implements IFirebase {
  private app: FirebaseApp | null = null
  private auth: Auth | null = null
  private firestore: WebFirestore | null = null
  private functions: Functions | null = null

  public async initialize() {
    if (this.app) {
      return
    }

    this.app = initializeApp(firebaseConfig)

    this.auth = getAuth(this.app)

    this.firestore = initializeFirestore(this.app, {
      localCache: persistentLocalCache({
        tabManager: persistentMultipleTabManager(),
        cacheSizeBytes: CACHE_SIZE_UNLIMITED,
      }),
    })

    this.functions = getFunctions(this.app, FUNCTIONS_REGION)
  }

  public getFirestore(): WebFirestore {
    if (!this.firestore) {
      throw new Error('Firestore not initialized')
    }
    return this.firestore
  }

  public getAuth(): Auth {
    if (!this.auth) {
      throw new Error('Auth not initialized')
    }
    return this.auth
  }

  public getFunctions(): Functions {
    if (!this.functions) {
      throw new Error('Functions not initialized')
    }
    return this.functions
  }
}
