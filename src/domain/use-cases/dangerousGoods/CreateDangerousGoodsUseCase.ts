import { BaseCreateDto, BaseDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { Timestamp } from '@react-native-firebase/firestore'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { DangerousGoodsService } from '@src/domain/services/DangerousGoodsService'
import { FileService } from '@src/domain/services/FileService'

import { SeaFile } from '@src/lib/fileImports'

export interface DangerousGoodDto extends BaseDto {
  class?: string
  dateExpires?: string
  imageFiles: SeaFile[]
  isHazardous: boolean
  location?: string
  msdsFiles: SeaFile[]
  name: string
  quantity?: string
  state: 'active' | 'deleted'
  touched?: Timestamp
  vesselIds: string[]
  // TODO: Cater for Delete
  // deletedBy?: string;
  // whenDeleted?: number;
  // whenExpires_old?: number; // TODO: Can possibly remove this
}

export interface CreateDangerousGoodDto extends DangerousGoodDto, BaseCreateDto {}

@injectable()
export class CreateDangerousGoodsUseCase implements IUseCase<CreateDangerousGoodDto> {
  constructor(
    @inject(DangerousGoodsService)
    private readonly dangerousGoodsService: IBaseDataService<CreateDangerousGoodDto>,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {}
  public async execute(createDangerousGoodDto: CreateDangerousGoodDto, userId: string, licenseeId: string) {
    // Upload Image Files
    const imageFilesList = await this.fileService.uploadFiles(
      createDangerousGoodDto?.imageFiles,
      'dangerousGoods',
      'files',
      userId,
      licenseeId
    )
    createDangerousGoodDto.imageFiles = imageFilesList as SeaFile[]

    // Upload MSDS Files
    const msdsFilesList = await this.fileService.uploadFiles(
      createDangerousGoodDto?.msdsFiles,
      'dangerousGoods',
      'files',
      userId,
      licenseeId
    )
    createDangerousGoodDto.msdsFiles = msdsFilesList as SeaFile[]

    // Create Dangerous Good
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Dangerous Good',
      maximumBatchSize: 20,
    })

    const { ref: createdDangerousGoodRef, records: dangerousGoodRecords } = this.dangerousGoodsService.createDataItem(
      operation,
      createDangerousGoodDto,
      userId
    )

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      createDangerousGoodDto.vesselIds,
      'dangerousGoods',
      createdDangerousGoodRef.id,
      createDangerousGoodDto.name
    )

    operation.addMany(dangerousGoodRecords).add(actionLogRecord)

    await operation.commit()
  }
}
