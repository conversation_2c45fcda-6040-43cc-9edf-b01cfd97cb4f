import { DangerousGoodDto } from '@src/domain/use-cases/dangerousGoods/CreateDangerousGoodsUseCase'
import { BaseUpdateDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { DangerousGoodsService } from '@src/domain/services/DangerousGoodsService'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'

import { SeaFile } from '@src/lib/fileImports'

export interface UpdateDangerousGoodDto extends DangerousGoodDto, BaseUpdateDto {}

@injectable()
export class UpdateDangerousGoodsUseCase implements IUseCase<UpdateDangerousGoodDto> {
  constructor(
    @inject(DangerousGoodsService)
    private readonly dangerousGoodsService: IBaseDataService<undefined, UpdateDangerousGoodDto>,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {}
  public async execute(updateDangerousGoodDto: UpdateDangerousGoodDto, userId: string, licenseeId: string) {
    // Upload Image Files
    const imageFilesList = await this.fileService.uploadFiles(
      updateDangerousGoodDto?.imageFiles,
      'dangerousGoods',
      'files',
      userId,
      licenseeId
    )
    updateDangerousGoodDto.imageFiles = imageFilesList as SeaFile[]

    // Upload MSDS Files
    const msdsFilesList = await this.fileService.uploadFiles(
      updateDangerousGoodDto?.msdsFiles,
      'dangerousGoods',
      'files',
      userId,
      licenseeId
    )
    updateDangerousGoodDto.msdsFiles = msdsFilesList as SeaFile[]

    // Update Dangerous Good
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Dangerous Good',
      maximumBatchSize: 20,
    })

    const { ref: updatedDangerousGoodRef, records: dangerousGoodRecords } = this.dangerousGoodsService.updateDataItem(
      operation,
      updateDangerousGoodDto,
      userId
    )

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      updateDangerousGoodDto.vesselIds,
      'dangerousGoods',
      updatedDangerousGoodRef.id,
      updateDangerousGoodDto.name
    )

    operation.addMany(dangerousGoodRecords).add(actionLogRecord)

    await operation.commit()
  }
}
