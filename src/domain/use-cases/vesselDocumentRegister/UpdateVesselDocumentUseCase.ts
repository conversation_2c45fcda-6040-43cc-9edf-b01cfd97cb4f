import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { SFDoc } from '@src/shared-state/CompanyDocuments/companyDocuments'
import { VesselDocumentService } from '@src/domain/services/VesselDocumentService'

export interface UpdateVesselDocumentDto {
  vesselId: string
  id: string
  title: string
  type: string
  interval?: string
  categoryId?: string
  dateExpires?: string
  emailReminder?: string
  dateToRemind?: string
  files: SeaFile[]
  sfdoc?: Partial<SFDoc>
}

export interface IUpdateVesselDocumentUseCase extends IUseCase<UpdateVesselDocumentDto> {}

export class UpdateVesselDocumentUseCase implements IUpdateVesselDocumentUseCase {
  private readonly vesselDocumentService: VesselDocumentService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(VesselDocumentService)
    vesselDocumentService: VesselDocumentService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.vesselDocumentService = vesselDocumentService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateVesselDocumentDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'vesselDocuments', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Update Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update Vessel Document ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateVesselDocumentsRef, records: vesselDocumentsRecord } =
      this.vesselDocumentService.updateVesselDocument(operation, dto, userId, licenseeId)

    operation.addMany(vesselDocumentsRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'vesselDocuments',
      updateVesselDocumentsRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
