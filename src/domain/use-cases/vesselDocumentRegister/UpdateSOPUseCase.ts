import { VesselCertificateService } from '@src/domain/services/VesselCertificateService'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { SFDoc } from '@src/shared-state/CompanyDocuments/companyDocuments'
import { SOPService } from '@src/domain/services/SOPService'

export interface UpdateSOPDto {
  vesselId: string
  id: string
  title: string
  categoryId?: string
  dateIssued?: string
  files: SeaFile[]
  sfdoc?: Partial<SFDoc>
}

export interface IUpdateSOPUseCase extends IUseCase<UpdateSOPDto> {}

export class UpdateSOPUseCase implements IUpdateSOPUseCase {
  private readonly sopService: SOPService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SOPService)
    sopService: SOPService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.sopService = sopService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateSOPDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'SOPs', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Update Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update SOP ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateSOPRef, records: sopRecord } = this.sopService.updateSOP(operation, dto, userId, licenseeId)

    operation.addMany(sopRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'SOPs',
      updateSOPRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
