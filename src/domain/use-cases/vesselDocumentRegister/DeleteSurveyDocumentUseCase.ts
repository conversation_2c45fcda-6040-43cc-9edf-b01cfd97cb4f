import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SurveyDocumentService } from '@src/domain/services/SurveyDocumentService'

export interface DeleteSurveyDocumentDto {
  id: string
  title: string
  vesselId: string
}

export interface IDeleteSurveyDocumentUseCase extends IUseCase<DeleteSurveyDocumentDto> {}

export class DeleteSurveyDocumentUseCase implements IDeleteSurveyDocumentUseCase {
  private readonly surveyDocumentService: SurveyDocumentService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SurveyDocumentService)
    surveyDocumentService: SurveyDocumentService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.surveyDocumentService = surveyDocumentService
    this.actionLogService = actionLogService
  }

  async execute(dto: DeleteSurveyDocumentDto, userId: string, licenseeId: string) {
    // Update Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Delete Survey Document ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { title, ...rest } = dto

    const { ref: updateSOPRef, records: sopRecord } = this.surveyDocumentService.deleteSOP(
      operation,
      rest,
      userId,
      licenseeId
    )

    operation.addMany(sopRecord)

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'surveyReports',
      updateSOPRef.id,
      title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
