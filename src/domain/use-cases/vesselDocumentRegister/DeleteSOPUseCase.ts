import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SOPService } from '@src/domain/services/SOPService'

export interface DeleteSOPDto {
  id: string
  title: string
  vesselId: string
}

export interface IDeleteSOPUseCase extends IUseCase<DeleteSOPDto> {}

export class DeleteSOPUseCase implements IDeleteSOPUseCase {
  private readonly sopService: SOPService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SOPService)
    sopService: SOPService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.sopService = sopService
    this.actionLogService = actionLogService
  }

  async execute(dto: DeleteSOPDto, userId: string, licenseeId: string) {
    // Update Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Delete SOP ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { title, ...rest } = dto

    const { ref: updateSOPRef, records: sopRecord } = this.sopService.deleteSOP(operation, rest, userId, licenseeId)

    operation.addMany(sopRecord)

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'SOPs',
      updateSOPRef.id,
      title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
