import { VesselCertificateService } from '@src/domain/services/VesselCertificateService'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { UpdateVesselCertificateDto } from './UpdateVesselCertificateUseCase'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface CreateVesselCertificateDto extends Omit<UpdateVesselCertificateDto, 'id'> {
  isShoreFacility?: boolean
  type: string
}

export interface ICreateVesselCertificateUseCase extends IUseCase<CreateVesselCertificateDto> {}

export class CreateVesselCertificateUseCase implements ICreateVesselCertificateUseCase {
  private readonly vesselCertificateService: VesselCertificateService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(VesselCertificateService)
    vesselCertificateService: VesselCertificateService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.vesselCertificateService = vesselCertificateService
    this.actionLogService = actionLogService
  }

  async execute(dto: CreateVesselCertificateDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'vesselCertificates', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Create Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Create Vessel Certificate ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateVesselCertificateRef, records: vesselCertificateRecord } =
      this.vesselCertificateService.createVesselCertificate(operation, dto, userId, licenseeId)

    operation.addMany(vesselCertificateRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'vesselCertificates',
      updateVesselCertificateRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
