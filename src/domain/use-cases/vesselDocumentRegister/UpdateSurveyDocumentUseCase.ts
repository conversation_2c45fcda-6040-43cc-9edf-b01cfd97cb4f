import { SeaFile } from '@src/lib/fileImports'
import { IUseCase } from '../UseCase'
import { SurveyDocumentService } from '@src/domain/services/SurveyDocumentService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { FileService } from '@src/domain/services/FileService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'

export interface UpdateSurveyDocumentDto {
  vesselId: string
  id: string
  title: string
  dateSurveyed?: string
  surveyor?: string
  personnelPresent?: string
  location?: string
  inOrOutWater?: string
  files: SeaFile[]
}

export interface IUpdateSurveyDocumentUseCase extends IUseCase<UpdateSurveyDocumentDto> {}

export class UpdateSurveyDocumentUseCase implements IUpdateSurveyDocumentUseCase {
  private readonly surveyDocumentService: SurveyDocumentService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SurveyDocumentService)
    surveyDocumentService: SurveyDocumentService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.surveyDocumentService = surveyDocumentService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateSurveyDocumentDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'surveyReports', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update survey document ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateSOPRef, records: sopRecord } = this.surveyDocumentService.updateSurveyDocument(
      operation,
      dto,
      userId,
      licenseeId
    )

    operation.addMany(sopRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'surveyReports',
      updateSOPRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
