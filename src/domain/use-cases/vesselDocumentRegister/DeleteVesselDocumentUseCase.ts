import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { SFDoc } from '@src/shared-state/CompanyDocuments/companyDocuments'
import { VesselDocumentService } from '@src/domain/services/VesselDocumentService'

export interface DeleteVesselDocumentDto {
  id: string
  title: string
  vesselId: string
}

export interface IDeleteVesselDocumentUseCase extends IUseCase<DeleteVesselDocumentDto> {}

export class DeleteVesselDocumentUseCase implements IDeleteVesselDocumentUseCase {
  private readonly vesselDocumentService: VesselDocumentService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(VesselDocumentService)
    vesselDocumentService: VesselDocumentService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.vesselDocumentService = vesselDocumentService
    this.actionLogService = actionLogService
  }

  async execute(dto: DeleteVesselDocumentDto, userId: string, licenseeId: string) {
    // Delete Vessel Document
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Delete Vessel Document ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { title, ...rest } = dto

    const { ref: updateVesselDocumentsRef, records: vesselDocumentsRecord } =
      this.vesselDocumentService.deleteVesselDocument(operation, rest, userId, licenseeId)

    operation.addMany(vesselDocumentsRecord)

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'vesselDocuments',
      updateVesselDocumentsRef.id,
      title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
