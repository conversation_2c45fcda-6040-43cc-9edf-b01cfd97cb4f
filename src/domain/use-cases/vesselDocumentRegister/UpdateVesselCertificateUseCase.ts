import { VesselCertificateService } from '@src/domain/services/VesselCertificateService'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface UpdateVesselCertificateDto {
  id: string
  title: string
  vesselId: string
  certNum?: string
  issuedBy?: string
  dateIssued: string
  dateExpires?: string
  emailReminder?: string
  categoryId?: string
  dateToRemind?: string
  files: SeaFile[]
}

export interface IUpdateVesselCertificateUseCase extends IUseCase<UpdateVesselCertificateDto> {}

export class UpdateVesselCertificateUseCase implements IUpdateVesselCertificateUseCase {
  private readonly vesselCertificateService: VesselCertificateService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(VesselCertificateService)
    vesselCertificateService: VesselCertificateService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.vesselCertificateService = vesselCertificateService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateVesselCertificateDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'vesselCertificates', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Update Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update Vessel Certificate ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateVesselCertificateRef, records: vesselCertificateRecord } =
      this.vesselCertificateService.updateVesselCertificate(operation, dto, userId, licenseeId)

    operation.addMany(vesselCertificateRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'vesselCertificates',
      updateVesselCertificateRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
