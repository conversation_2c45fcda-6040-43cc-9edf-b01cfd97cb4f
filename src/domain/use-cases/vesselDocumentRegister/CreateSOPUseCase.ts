import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { SOPService } from '@src/domain/services/SOPService'
import { UpdateSOPDto } from './UpdateSOPUseCase'

export interface CreateSOPDto extends Omit<UpdateSOPDto, 'id'> {}

export interface ICreateSOPUseCase extends IUseCase<CreateSOPDto> {}

export class CreateSOPUseCase implements ICreateSOPUseCase {
  private readonly sopService: SOPService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SOPService)
    sopService: SOPService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.sopService = sopService
    this.actionLogService = actionLogService
  }

  async execute(dto: CreateSOPDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'SOPs', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Update Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Create SOP ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateSOPRef, records: sopRecord } = this.sopService.createSOP(operation, dto, userId, licenseeId)

    operation.addMany(sopRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'SOPs',
      updateSOPRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
