import { SurveyDocumentService } from '@src/domain/services/SurveyDocumentService'
import { IUseCase } from '../UseCase'
import { UpdateSurveyDocumentDto } from './UpdateSurveyDocumentUseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { FileService } from '@src/domain/services/FileService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SeaFile } from '@src/lib/fileImports'

export interface CreateSurveyDocumentDto extends Omit<UpdateSurveyDocumentDto, 'id'> {}

export interface ICreateSurveyDocumentUseCase extends IUseCase<CreateSurveyDocumentDto> {}

export class CreateSurveyDocumentUseCase implements ICreateSurveyDocumentUseCase {
  private readonly surveyDocumentService: SurveyDocumentService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SurveyDocumentService)
    surveyDocumentService: SurveyDocumentService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.surveyDocumentService = surveyDocumentService
    this.actionLogService = actionLogService
  }

  async execute(dto: CreateSurveyDocumentDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'surveyReports', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Create survey document ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: createSurveyDocumentRef, records: surveyDocumentRecord } =
      this.surveyDocumentService.createSurveyDocument(operation, dto, userId, licenseeId)

    operation.addMany(surveyDocumentRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'surveyReports',
      createSurveyDocumentRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
