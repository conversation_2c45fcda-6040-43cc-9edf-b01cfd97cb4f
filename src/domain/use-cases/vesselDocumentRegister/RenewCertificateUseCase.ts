import { VesselCertificateService } from '@src/domain/services/VesselCertificateService'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { UpdateVesselCertificateDto } from './UpdateVesselCertificateUseCase'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/files'

export interface RenewVesselCertificateDto extends UpdateVesselCertificateDto {
  isShoreFacility?: boolean
}

export interface IRenewVesselCertificateUseCase extends IUseCase<RenewVesselCertificateDto> {}

export class RenewVesselCertificateUseCase implements IRenewVesselCertificateUseCase {
  private readonly vesselCertificateService: VesselCertificateService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(VesselCertificateService)
    vesselCertificateService: VesselCertificateService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.vesselCertificateService = vesselCertificateService
    this.actionLogService = actionLogService
  }

  async execute(dto: RenewVesselCertificateDto, userId: string, licenseeId: string): Promise<void> {
    /** First - Archive Vessel Certificate */
    const archiveOperation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Archive Vessel Certificate ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { id, ...rest } = dto

    const archiveVesselRecord = this.vesselCertificateService.archiveVesselCertificate(archiveOperation, id, userId)

    archiveOperation.add(archiveVesselRecord)

    /** Then - Renew Vessel Certificate */
    // Upload Files
    const filesList = await this.fileService.uploadFiles(rest.files, 'vesselCertificates', 'files', userId, licenseeId)
    rest.files = filesList as SeaFile[]

    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Renew Vessel Certificate ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: renewVesselCertificateRef, records: renewVesselCertificateRecord } =
      this.vesselCertificateService.renewVesselCertificate(operation, rest, userId, licenseeId)

    operation.addMany(renewVesselCertificateRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'vesselCertificates',
      renewVesselCertificateRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await archiveOperation.commit()
    await operation.commit()
  }
}
