import { ISafetyCheckService, SafetyCheckService } from '@src/domain/services/SafetyCheckService'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { IUseCase } from '@src/domain/use-cases/UseCase'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface UpdateCompletedSafetyCheckDto {
  completedSafetyCheckId: string
  vesselId: string
  safetyCheckId: string
  whenCompleted: number
  notes?: string
  files: SeaFile[]
  actualTime?: number
  shouldReportFault: boolean
  interval: string
}

export interface IUpdateCompletedSafetyCheckUseCase extends IUseCase<UpdateCompletedSafetyCheckDto> {}

@injectable()
export class UpdateCompletedSafetyCheckUseCase implements IUpdateCompletedSafetyCheckUseCase {
  private readonly logger: ILogger
  constructor(
    @inject(SafetyCheckService)
    private readonly safetyCheckService: ISafetyCheckService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.logger = logger.scoped('UpdateCompletedSafetyCheckUseCase')
  }

  public async execute(dto: UpdateCompletedSafetyCheckDto, userId: string, licenseeId: string) {
    this.logger.info('Executing UpdateCompletedSafetyCheckUseCase', { userId, licenseeId, dto })

    this.logger.info('Uploading files', { userId, licenseeId, files: dto.files })
    const filesList = await this.fileService.uploadFiles(dto.files, 'safetyCheckCompleted', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Create operation
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Completed Safety Check',
      maximumBatchSize: 20,
    })

    const { ref: updatedCompletedSafetyCheckRef, records: safetyCheckRecords } =
      await this.safetyCheckService.updateCompletedSafetyCheck(operation, dto, userId, licenseeId)
    this.logger.info('Updating existing Safety Check', { userId, licenseeId, records: safetyCheckRecords })

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'safetyCheckCompleted',
      updatedCompletedSafetyCheckRef.id,
      'Updated completed safety check' // TODO - Include item name
    )
    this.logger.info('Creating Action Log record', { userId, licenseeId, record: actionLogRecord })

    operation.addMany(safetyCheckRecords).add(actionLogRecord)

    await operation.commit()
  }
}
