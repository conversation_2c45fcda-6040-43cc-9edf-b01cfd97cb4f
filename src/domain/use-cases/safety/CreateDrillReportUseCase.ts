import { IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { DrillsService, IDrillsService } from '@src/domain/services/DrillsService'
import { Drill } from '@src/shared-state/VesselSafety/drills'

export interface CreateDrillReportDto {
  vesselId: string
  drills: Drill[]
  crewInvolvedIds: string[]
  dateCompleted: string
  location?: string
  scenario?: string
  equipment?: string
  furtherTraining?: string
  modification?: string
  files?: string[]
  signature?: string
}

export interface ICreateDrillReportUseCase extends IUseCase<CreateDrillReportDto> {}

@injectable()
export class CreateDrillReportUseCase implements ICreateDrillReportUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(DrillsService)
    private readonly drillsService: IDrillsService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('CreateDrillReportUseCase')
  }

  public async execute(dto: CreateDrillReportDto, userId: string, licenseeId: string) {
    this.logger.info('Executing CreateDrillReportUseCase')

    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Drill Report',
      maximumBatchSize: 20,
    })

    const { ref: drillReportRef, records } = this.drillsService.createDrillReport(operation, dto, userId, licenseeId)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'drillReports',
      drillReportRef.id,
      'TODO - Details'
    )

    this.logger.info('Committing records: ', {
      records: [...records, actionLogRecord],
    })

    operation.addMany(records).add(actionLogRecord)

    await operation.commit()
  }
}
