import { IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { DrillsService, IDrillsService } from '@src/domain/services/DrillsService'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'

export interface UpdateDrillReportDto {}

export interface IUpdateDrillReportUseCase extends IUseCase<UpdateDrillReportDto> {}

@injectable()
export class UpdateDrillReportUseCase implements IUpdateDrillReportUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(DrillsService)
    private readonly drillsService: IDrillsService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('UpdateDrillReportUseCase')
  }

  public async execute(dto: UpdateDrillReportDto, userId: string, licenseeId: string) {
    this.logger.info('Executing UpdateDrillReportUseCase')

    throw new Error('Operation not implemented')
  }
}
