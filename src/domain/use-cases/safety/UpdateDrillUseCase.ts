import { IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { DrillsService, IDrillsService } from '@src/domain/services/DrillsService'
import { Drill } from '@src/shared-state/VesselSafety/drills'
import { ILogger } from '@src/domain/util/logger/ILogger'

export interface UpdateDrillDto {
  drillId: string
  vesselId: string
  name: string
  interval: string
  notAssignedTo: string[]
  currentDrill: Drill
}

export interface IUpdateDrillUseCase extends IUseCase<UpdateDrillDto> {}

@injectable()
export class UpdateDrillUseCase implements IUpdateDrillUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(DrillsService)
    private readonly drillsService: IDrillsService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('UpdateDrillUseCase')
  }

  public async execute(dto: UpdateDrillDto, userId: string, licenseeId: string) {
    this.logger.info('Updating drill', { drillId: dto.drillId })

    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Drill',
      maximumBatchSize: 20,
    })

    const drillRecords = this.drillsService.updateDrill(operation, dto, dto.currentDrill, userId, licenseeId)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'drills',
      dto.drillId,
      `Updated drill: ${dto.name}`
    )

    this.logger.info('Committing records: ', {
      records: [...drillRecords, actionLogRecord],
    })

    operation.addMany(drillRecords).add(actionLogRecord)

    await operation.commit()
  }
}
