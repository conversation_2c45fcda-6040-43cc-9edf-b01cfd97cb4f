import { ISafetyCheckService, SafetyCheckService } from '@src/domain/services/SafetyCheckService'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { IUseCase } from '@src/domain/use-cases/UseCase'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { IVesselService, VesselService } from '@src/domain/services/VesselService'

export interface CreateSafetyCheckDto {
  vesselId: string
  itemId?: string
  isNewItem?: boolean
  newItemName?: string
  locationId?: string
  isNewLocation?: boolean
  newLocationName?: string
  categoryId?: string
  isNewCategory?: boolean
  newCategoryName?: string
  isCritical: boolean
  whenLastChecked: number
  description: string
  files: SeaFile[]
  interval: string
  assignedTo: string[]
  estimatedTime?: number
  // TODO - Add links
}

export interface ICreateSafetyCheckUseCase extends IUseCase<CreateSafetyCheckDto> {}

@injectable()
export class CreateSafetyCheckUseCase implements ICreateSafetyCheckUseCase {
  private readonly logger: ILogger
  constructor(
    @inject(VesselService)
    private readonly vesselService: IVesselService,
    @inject(SafetyCheckService)
    private readonly safetyCheckService: ISafetyCheckService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.logger = logger.scoped('CreateSafetyCheckUseCase')
  }

  // TODO
  // - Handle links
  public async execute(dto: CreateSafetyCheckDto, userId: string, licenseeId: string) {
    this.logger.info('Executing CreateSafetyCheck Use Case')

    const filesList = await this.fileService.uploadFiles(dto.files, 'safetyCheckItems', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Create Safety Check
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Safety Check',
      maximumBatchSize: 20,
    })

    const categoryRecords = []

    if (dto.isNewCategory && dto.newCategoryName) {
      const { ref, records } = this.safetyCheckService.createSafetyCheckCategory(
        operation,
        dto.newCategoryName,
        dto.vesselId,
        userId,
        licenseeId
      )
      dto.categoryId = ref.id

      this.logger.info(`Creating a new Safety Check Category: [${dto.newCategoryName}] with ID: ${ref.id}`, {
        createSafetyCheckDto: dto,
        records,
      })

      categoryRecords.push(...records)
    }

    if (dto.isNewLocation && dto.newLocationName) {
      const { ref, records } = this.vesselService.createVesselLocation(
        operation,
        dto.newLocationName,
        dto.vesselId,
        userId,
        licenseeId
      )
      dto.locationId = ref.id

      this.logger.info(`Creating a new Vessel Location: [${dto.newLocationName}] with ID: ${ref.id}`, {
        createSafetyCheckDto: dto,
        records,
      })

      categoryRecords.push(...records)
    }

    if (dto.isNewItem && dto.newItemName) {
      const { ref, records } = this.vesselService.createVesselSafetyItem(
        operation,
        dto.newItemName,
        dto.vesselId,
        userId,
        licenseeId
      )
      dto.itemId = ref.id

      this.logger.info(`Creating a new Safety Equipment Item: [${dto.newItemName}] with ID: ${ref.id}`, {
        createSafetyCheckDto: dto,
        records,
      })

      categoryRecords.push(...records)
    }

    const { ref: createdSafetyCheckRef, records: safetyCheckRecords } = this.safetyCheckService.createSafetyCheck(
      operation,
      dto,
      userId,
      licenseeId
    )

    this.logger.debug('Safety Check Records', { safetyCheckRecords })

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'safetyCheckItems',
      createdSafetyCheckRef.id,
      'TODO - Details' // TODO - This needs to be the name e.g. EPIRB, Life Raft
    )

    operation.addMany(safetyCheckRecords).addMany(categoryRecords).add(actionLogRecord)

    await operation.commit()
  }
}
