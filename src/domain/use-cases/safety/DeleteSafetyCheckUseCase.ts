import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SafetyCheckService } from '@src/domain/services/SafetyCheckService'

export interface DeleteSafetyCheckDto {
  id: string
  itemName: string
  vesselId: string
}

export interface IDeleteSafetyCheckUseCase extends IUseCase<DeleteSafetyCheckDto> {}

export class DeleteSafetyCheckUseCase implements IDeleteSafetyCheckUseCase {
  private readonly safetyCheckService: SafetyCheckService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SafetyCheckService)
    safetyCheckService: SafetyCheckService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.safetyCheckService = safetyCheckService
    this.actionLogService = actionLogService
  }

  async execute(dto: DeleteSafetyCheckDto, userId: string, licenseeId: string) {
    const operation = this.firestoreService.createOperation({
      operationType: 'delete',
      operationDescription: `Delete Safety Check ${dto.itemName}`,
      maximumBatchSize: 20,
    })

    const { itemName, ...rest } = dto

    const { ref: updateSafetyCheckRef, records: safetyCheckRecord } = this.safetyCheckService.deleteSafetyCheck(
      operation,
      rest.id,
      rest.vesselId,
      userId,
      licenseeId
    )

    operation.addMany(safetyCheckRecord)

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'safetyCheckItems',
      updateSafetyCheckRef.id,
      itemName
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
