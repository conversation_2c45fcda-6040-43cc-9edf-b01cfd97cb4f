import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject, injectable } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { DrillsService } from '@src/domain/services/DrillsService'
import Yup from '@src/lib/yup'
import { ILogger } from '@src/domain/util/logger/ILogger'

export interface CreateDrillDto {
  vesselId: string
  name: string
  interval: string
  notAssignedTo: string[]
}

const createDrillSchema = Yup.object({
  vesselId: Yup.string().required(),
  name: Yup.string().required().min(1, 'Drill name cannot be empty'),
  interval: Yup.string().required(),
  notAssignedTo: Yup.array().of(Yup.string()),
})

export interface ICreateDrillUseCase extends IUseCase<CreateDrillDto> {}

@injectable()
export class CreateDrillUseCase implements ICreateDrillUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(DrillsService)
    private readonly drillsService: DrillsService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('CreateDrillUseCase')
  }

  async execute(dto: CreateDrillDto, userId: string, licenseeId: string) {
    this.logger.info('Creating drill', { drillName: dto.name })

    // Validate the DTO
    await createDrillSchema.validate(dto)

    // Create Drill
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Create Drill ${dto.name}`,
      maximumBatchSize: 20,
    })

    const { ref: createdRef, records } = this.drillsService.createDrill(
      operation,
      dto.vesselId,
      dto.name,
      dto.interval,
      dto.notAssignedTo,
      userId,
      licenseeId
    )

    operation.addMany(records)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'drills',
      createdRef.id,
      `Created drill: ${dto.name}`
    )

    operation.add(actionLogRecord)

    this.logger.info('Committing drill creation', { drillId: createdRef.id })

    await operation.commit()
  }
}
