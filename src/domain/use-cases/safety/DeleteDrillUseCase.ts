import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { DrillsService } from '@src/domain/services/DrillsService'

export interface DeleteDrillDto {
  id: string
  name: string
  vesselId: string
}

export interface IDeleteDrillUseCase extends IUseCase<DeleteDrillDto> {}

export class DeleteDrillUseCase implements IDeleteDrillUseCase {
  private readonly drillsService: DrillsService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(DrillsService)
    drillsService: DrillsService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.drillsService = drillsService
    this.actionLogService = actionLogService
  }

  async execute(dto: DeleteDrillDto, userId: string, licenseeId: string) {
    const operation = this.firestoreService.createOperation({
      operationType: 'delete',
      operationDescription: `Delete Drill ${dto.name}`,
      maximumBatchSize: 20,
    })

    const drillRecords = this.drillsService.deleteDrill(operation, dto.id, dto.vesselId, userId, licenseeId)

    operation.addMany(drillRecords)

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'drills',
      dto.id,
      dto.name
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
