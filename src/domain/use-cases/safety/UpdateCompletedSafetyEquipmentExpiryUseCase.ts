import {
  ISafetyEquipmentExpiryService,
  SafetyEquipmentExpiryService,
} from '@src/domain/services/SafetyEquipmentExpiryService'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { IUseCase } from '@src/domain/use-cases/UseCase'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/files'

export interface UpdateCompletedSafetyEquipmentExpiryDto {
  completedSafetyEquipmentExpiryId: string
  vesselId: string
  safetyEquipmentId: string
  whenCompleted: number
  notes?: string
  files: SeaFile[]
  newExpiryDate?: string
}

export interface IUpdateCompletedSafetyEquipmentExpiryUseCase
  extends IUseCase<UpdateCompletedSafetyEquipmentExpiryDto> {}

@injectable()
export class UpdateCompletedSafetyEquipmentExpiryUseCase implements IUpdateCompletedSafetyEquipmentExpiryUseCase {
  private readonly logger: ILogger
  constructor(
    @inject(SafetyEquipmentExpiryService)
    private readonly safetyEquipmentExpiryService: ISafetyEquipmentExpiryService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.logger = logger.scoped('UpdateCompletedSafetyEquipmentExpiryUseCase')
  }

  public async execute(
    updateCompletedSafetyEquipmentExpiryDto: UpdateCompletedSafetyEquipmentExpiryDto,
    userId: string,
    licenseeId: string
  ) {
    this.logger.info('Executing UpdateCompletedSafetyEquipmentExpiry Use Case')

    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      updateCompletedSafetyEquipmentExpiryDto.files,
      'safetyEquipmentTaskCompleted',
      'files',
      userId,
      licenseeId
    )
    updateCompletedSafetyEquipmentExpiryDto.files = filesList as SeaFile[]

    // Create operation
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Completed Safety Equipment Expiry',
      maximumBatchSize: 20,
    })

    const { ref: updatedCompletedSafetyEquipmentExpiryRef, records: safetyEquipmentRecords } =
      await this.safetyEquipmentExpiryService.updateCompletedSafetyEquipmentExpiry(
        operation,
        updateCompletedSafetyEquipmentExpiryDto,
        userId,
        licenseeId
      )

    this.logger.debug('Completed Safety Equipment Expiry Records', {
      safetyEquipmentRecords,
    })

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      updateCompletedSafetyEquipmentExpiryDto.vesselId,
      'safetyEquipmentTaskCompleted',
      updatedCompletedSafetyEquipmentExpiryRef.id,
      'Updated completed safety equipment expiry'
    )

    operation.addMany(safetyEquipmentRecords).add(actionLogRecord)

    await operation.commit()
  }
}
