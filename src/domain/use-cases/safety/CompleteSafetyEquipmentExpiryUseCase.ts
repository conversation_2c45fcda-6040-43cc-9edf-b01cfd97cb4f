import { IUseCase } from '@src/domain/use-cases/UseCase'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { inject } from 'inversify'
import {
  ISafetyEquipmentExpiryService,
  SafetyEquipmentExpiryService,
} from '@src/domain/services/SafetyEquipmentExpiryService'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/files'

export interface CompleteSafetyEquipmentExpiryDto {
  vesselId: string
  safetyEquipmentId: string
  type: string
  interval?: string
  emailReminder?: string
  notes?: string
  whenCompleted: number
  whenLastChecked?: number
  newExpiryDate?: string
  files?: SeaFile[]
}

export class CompleteSafetyEquipmentExpiryUseCase implements IUseCase<CompleteSafetyEquipmentExpiryDto> {
  private readonly logger: ILogger

  constructor(
    @inject(SafetyEquipmentExpiryService)
    private readonly safetyEquipmentExpiryService: ISafetyEquipmentExpiryService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.logger = logger.scoped('CreateSafetyEquipmentExpiryUseCase')
  }

  public async execute(dto: CompleteSafetyEquipmentExpiryDto, userId: string, licenseeId: string) {
    this.logger.info('Executing CreateSafetyEquipmentExpiryUseCase')

    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.files ?? [],
      'safetyEquipmentTaskCompleted',
      'files',
      userId,
      licenseeId
    )
    console.debug('FILE UPLOAD:', { files: dto.files ?? [], filesList })
    dto.files = filesList as SeaFile[]

    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Safety Equipment Expiry',
      maximumBatchSize: 20 - 1, // Minus one due to requiring an extra security call
    })

    const { ref: safetyEquipmentExpiryRef, records } = this.safetyEquipmentExpiryService.completeSafetyEquipmentExpiry(
      operation,
      dto,
      userId,
      licenseeId
    )

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'safetyEquipmentTaskCompleted',
      safetyEquipmentExpiryRef.id,
      'TODO - Details' // TODO - This needs to be the name e.g. EPIRB, Life Raft
    )

    operation.addMany(records).add(actionLogRecord)

    await operation.commit()
  }
}
