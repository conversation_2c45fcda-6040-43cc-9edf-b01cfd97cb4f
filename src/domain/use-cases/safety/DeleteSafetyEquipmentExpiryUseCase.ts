import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject, injectable } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SafetyEquipmentExpiryService } from '@src/domain/services/SafetyEquipmentExpiryService'
import { ILogger } from '@src/domain/util/logger/ILogger'

export interface DeleteSafetyEquipmentExpiryDto {
  id: string
  itemName: string
  vesselId: string
}

export interface IDeleteSafetyEquipmentExpiryUseCase extends IUseCase<DeleteSafetyEquipmentExpiryDto> {}

@injectable()
export class DeleteSafetyEquipmentExpiryUseCase implements IDeleteSafetyEquipmentExpiryUseCase {
  private readonly logger: ILogger
  private readonly safetyEquipmentExpiryService: SafetyEquipmentExpiryService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SafetyEquipmentExpiryService)
    safetyEquipmentExpiryService: SafetyEquipmentExpiryService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.safetyEquipmentExpiryService = safetyEquipmentExpiryService
    this.actionLogService = actionLogService
    this.logger = logger.scoped('DeleteSafetyEquipmentExpiryUseCase')
  }

  async execute(dto: DeleteSafetyEquipmentExpiryDto, userId: string, licenseeId: string) {
    this.logger.info('Executing DeleteSafetyEquipmentExpiry Use Case', {
      safetyEquipmentExpiryId: dto.id,
      itemName: dto.itemName,
      vesselId: dto.vesselId,
      userId,
      licenseeId,
    })

    const operation = this.firestoreService.createOperation({
      operationType: 'delete',
      operationDescription: `Delete Safety Equipment Expiry ${dto.itemName}`,
      maximumBatchSize: 20,
    })

    const { itemName, ...rest } = dto

    const { ref: deleteSafetyEquipmentExpiryRef, records: safetyEquipmentExpiryRecord } =
      this.safetyEquipmentExpiryService.deleteSafetyEquipmentExpiry(
        operation,
        rest.id,
        rest.vesselId,
        userId,
        licenseeId
      )

    this.logger.debug('Safety Equipment Expiry Delete Records', { safetyEquipmentExpiryRecord })

    operation.addMany(safetyEquipmentExpiryRecord)

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'safetyEquipmentItems',
      deleteSafetyEquipmentExpiryRef.id,
      itemName
    )

    operation.add(actionLogRecord)

    await operation.commit()

    this.logger.info('Successfully deleted safety equipment expiry', {
      safetyEquipmentExpiryId: dto.id,
      itemName: dto.itemName,
    })
  }
}
