import { IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { ISafetyCheckService, SafetyCheckService } from '@src/domain/services/SafetyCheckService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { FirestoreRecord } from '@src/domain/data/FirestoreOperation'
import { IVesselService, VesselService } from '@src/domain/services/VesselService'

export interface UpdateSafetyCheckDto {
  vesselId: string
  safetyCheckId: string
  itemId?: string
  isNewItem?: boolean
  newItemName?: string
  locationId?: string
  isNewLocation?: boolean
  newLocationName?: string
  categoryId?: string
  isNewCategory?: boolean
  newCategoryName?: string
  isCritical: boolean
  description: string
  files: SeaFile[]
  interval: string
  assignedTo: string[]
  whenLastChecked: number
  estimatedTime: number
  // links: string[];
}

export interface IUpdateSafetyCheckUseCase extends IUseCase<UpdateSafetyCheckDto> {}

@injectable()
export class UpdateSafetyCheckUseCase implements IUpdateSafetyCheckUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(VesselService)
    private readonly vesselService: IVesselService,
    @inject(SafetyCheckService)
    private readonly safetyCheckService: ISafetyCheckService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.logger = logger.scoped('UpdateSafetyCheckUseCase')
  }

  public async execute(dto: UpdateSafetyCheckDto, userId: string, licenseeId: string) {
    this.logger.info('Executing UpdateSafetyCheckUseCase', { dto })

    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'safetyCheckItems', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Safety Check',
      maximumBatchSize: 20,
    })

    const categoryRecords: FirestoreRecord[] = []

    if (dto.isNewCategory && dto.newCategoryName) {
      const { ref, records } = this.safetyCheckService.createSafetyCheckCategory(
        operation,
        dto.newCategoryName,
        dto.vesselId,
        userId,
        licenseeId
      )
      dto.categoryId = ref.id

      this.logger.info(`Creating a new Safety Check Category: [${dto.newCategoryName}] with ID: ${ref.id}`, {
        records,
      })

      categoryRecords.push(...records)
    }

    if (dto.isNewLocation && dto.newLocationName) {
      const { ref, records } = this.vesselService.createVesselLocation(
        operation,
        dto.newLocationName,
        dto.vesselId,
        userId,
        licenseeId
      )
      dto.locationId = ref.id

      this.logger.info(`Creating a new Vessel Location: [${dto.newLocationName}] with ID: ${ref.id}`, {
        records,
      })

      categoryRecords.push(...records)
    }

    if (dto.isNewItem && dto.newItemName) {
      const { ref, records } = this.vesselService.createVesselSafetyItem(
        operation,
        dto.newItemName,
        dto.vesselId,
        userId,
        licenseeId
      )
      dto.itemId = ref.id

      this.logger.info(`Creating a new Safety Equipment Item: [${dto.newItemName}] with ID: ${ref.id}`, {
        records,
      })

      categoryRecords.push(...records)
    }

    const { ref: updatedSafetyCheckRef, records: safetyCheckRecords } = this.safetyCheckService.updateSafetyCheck(
      operation,
      dto,
      userId,
      licenseeId
    )
    this.logger.info('Updating existing Safety Check', { records: safetyCheckRecords })

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'safetyCheckItems',
      updatedSafetyCheckRef.id,
      'TODO - Details' // This needs to be the name e.g. EPIRB, Life Raft
    )
    this.logger.info('Creating Action Log record', { record: actionLogRecord })

    operation.addMany(safetyCheckRecords).addMany(categoryRecords).add(actionLogRecord)

    await operation.commit()
  }
}
