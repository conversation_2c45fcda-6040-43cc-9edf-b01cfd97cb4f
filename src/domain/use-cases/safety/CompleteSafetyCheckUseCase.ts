import { IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { ISafetyCheckService, SafetyCheckService } from '@src/domain/services/SafetyCheckService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface CompleteSafetyCheckDto {
  vesselId: string
  safetyCheckId: string
  interval: string
  itemId: string
  notes?: string
  whenCompleted: number
  shouldReportFault: boolean
  actualTime?: number
  files?: SeaFile[]
}

export interface ICompleteSafetyCheckUseCase extends IUseCase<CompleteSafetyCheckDto> {}

@injectable()
export class CompleteSafetyCheckUseCase implements ICompleteSafetyCheckUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(SafetyCheckService)
    private readonly safetyCheckService: ISafetyCheckService,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.logger = logger.scoped('CompleteSafetyCheckUseCase')
  }

  public async execute(dto: CompleteSafetyCheckDto, userId: string, licenseeId: string) {
    this.logger.info('Executing CompleteSafetyCheckUseCase', { dto, userId, licenseeId })

    // Upload Files
    this.logger.info('Uploading files', { userId, licenseeId, files: dto.files })
    const filesList = await this.fileService.uploadFiles(dto.files, 'safetyCheckCompleted', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Complete Safety Check
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Complete Safety Check',
      maximumBatchSize: 20,
    })

    const { ref: updatedSafetyCheckRef, records: safetyCheckRecords } = this.safetyCheckService.completeSafetyCheck(
      operation,
      dto,
      userId,
      licenseeId
    )

    this.logger.info('Creating new Safety Check', { userId, licenseeId, records: safetyCheckRecords })

    // TODO - Handle the FAULT scenario

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'safetyCheckItems',
      updatedSafetyCheckRef.id,
      'TODO - Details' // This needs to be the name e.g. EPIRB, Life Raft
    )
    this.logger.info('Creating Action Log record', { userId, licenseeId, record: actionLogRecord })

    operation.addMany(safetyCheckRecords).add(actionLogRecord)

    await operation.commit()
  }
}
