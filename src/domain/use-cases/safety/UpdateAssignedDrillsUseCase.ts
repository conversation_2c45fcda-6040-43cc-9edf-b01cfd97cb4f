import { IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { DocRef, IFirestoreService } from '@src/domain/data/IFirestoreService'
import { DrillsService, IDrillsService } from '@src/domain/services/DrillsService'
import { Drill } from '@src/shared-state/VesselSafety/drills'

export interface UpdateAssignedDrillsDto {
  vesselId: string
  vesselDrills: string[]
  assignedDrills: string[]
  drillUserId: string
}

export interface IUpdateAssignedDrillsUseCase extends IUseCase<UpdateAssignedDrillsDto> {}

@injectable()
export class UpdateAssignedDrillsUseCase implements IUpdateAssignedDrillsUseCase {
  private readonly logger: ILogger

  constructor(
    @inject(DrillsService)
    private readonly drillsService: IDrillsService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SERVICES.ILogger) logger: ILogger
  ) {
    this.logger = logger.scoped('UpdateAssignedDrillsUseCase')
  }

  public async execute(dto: UpdateAssignedDrillsDto, userId: string, licenseeId: string) {
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Drill Report',
      maximumBatchSize: 20,
    })

    const drillRecords = this.drillsService.updateAssignedDrills(operation, dto, userId, licenseeId)

    // TODO - This is maybe too naive as it will add a record for every drill
    const actionLogRecords = dto.vesselDrills.map(drillId =>
      this.actionLogService.createUpdatedAction(
        operation,
        userId,
        licenseeId,
        dto.vesselId,
        'drills',
        drillId,
        'Updated drill assignees'
      )
    )

    this.logger.info('Committing records: ', {
      records: [...drillRecords, actionLogRecords],
    })

    operation.addMany(drillRecords).addMany(actionLogRecords)

    await operation.commit()
  }
}
