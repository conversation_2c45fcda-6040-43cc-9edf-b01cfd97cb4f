import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { CrewCertificateService } from '@src/domain/services/CrewCertificateService'

export interface UpdateCrewCertificateDto {
  id: string
  title: string
  titleId: string
  vesselId: string
  issuedBy?: string
  dateIssued: string
  dateExpires?: string
  emailReminder?: string
  dateToRemind?: string
  files: SeaFile[]
}

export interface ICrewVesselCertificateUseCase extends IUseCase<UpdateCrewCertificateDto> {}

export class UpdateCrewCertificateUseCase implements ICrewVesselCertificateUseCase {
  private readonly crewCertificateService: CrewCertificateService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CrewCertificateService)
    crewCertificateService: CrewCertificateService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.crewCertificateService = crewCertificateService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateCrewCertificateDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'crewCertificates', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    const { title, vesselId, ...rest } = dto

    // Update Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update Crew Certificate ${title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateVesselCertificateRef, records: vesselCertificateRecord } =
      this.crewCertificateService.updateCrewCertificate(operation, rest, userId, licenseeId)

    operation.addMany(vesselCertificateRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      vesselId,
      'crewCertificates',
      updateVesselCertificateRef.id,
      title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
