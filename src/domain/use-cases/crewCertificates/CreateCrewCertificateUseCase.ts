import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { UpdateCrewCertificateDto } from './UpdateCrewCertificateUseCase'
import { CrewCertificateService } from '@src/domain/services/CrewCertificateService'

export interface CreateCrewCertificateDto extends Omit<UpdateCrewCertificateDto, 'id'> {
  heldBy: string
  type: 'renewable' | 'nonExpiring'
}

export interface ICreateCrewCertificateUseCase extends IUseCase<CreateCrewCertificateDto> {}

export class CreateCrewCertificateUseCase implements ICreateCrewCertificateUseCase {
  private readonly crewCertificateService: CrewCertificateService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CrewCertificateService)
    crewCertificateService: CrewCertificateService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.crewCertificateService = crewCertificateService
    this.actionLogService = actionLogService
  }

  async execute(dto: CreateCrewCertificateDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'crewCertificates', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    const { title, vesselId, ...rest } = dto

    // Create Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Create Crew Certificate ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateCrewCertificateRef, records: crewCertificateRecord } =
      this.crewCertificateService.createCrewCertificate(operation, rest, userId, licenseeId)

    operation.addMany(crewCertificateRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      [],
      'crewCertificates',
      updateCrewCertificateRef.id,
      title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
