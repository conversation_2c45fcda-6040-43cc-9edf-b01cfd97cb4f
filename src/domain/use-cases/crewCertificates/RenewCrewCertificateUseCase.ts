import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { CrewCertificateService } from '@src/domain/services/CrewCertificateService'
import { SeaFile } from '@src/lib/fileImports'
import { CreateCrewCertificateDto } from './CreateCrewCertificateUseCase'

export interface RenewCrewCertificateDto extends CreateCrewCertificateDto {
  id: string
}

export interface IRenewCrewCertificateUseCase extends IUseCase<RenewCrewCertificateDto> {}

export class RenewCrewCertificateUseCase implements IRenewCrewCertificateUseCase {
  private readonly crewCertificateService: CrewCertificateService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CrewCertificateService)
    crewCertificateService: CrewCertificateService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.crewCertificateService = crewCertificateService
    this.actionLogService = actionLogService
  }

  async execute(dto: RenewCrewCertificateDto, userId: string, licenseeId: string): Promise<void> {
    /** First - Archive Vessel Certificate */
    const archiveOperation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Archive Vessel Certificate ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { id, vesselId, ...rest } = dto

    const archiveVesselRecord = this.crewCertificateService.archiveCrewCertificate(archiveOperation, id, userId)

    archiveOperation.add(archiveVesselRecord)

    /** Then - Renew Vessel Certificate */
    // Upload Files
    const filesList = await this.fileService.uploadFiles(rest.files, 'crewCertificates', 'files', userId, licenseeId)
    rest.files = filesList as SeaFile[]

    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Renew Crew Certificate ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: renewVesselCertificateRef, records: renewVesselCertificateRecord } =
      this.crewCertificateService.renewCrewCertificate(operation, rest, userId, licenseeId)

    operation.addMany(renewVesselCertificateRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      vesselId,
      'crewCertificates',
      renewVesselCertificateRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await archiveOperation.commit()
    await operation.commit()
  }
}
