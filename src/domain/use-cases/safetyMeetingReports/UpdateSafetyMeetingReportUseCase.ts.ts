import { SafetyMeetingReportDto } from '@src/domain/use-cases/safetyMeetingReports/CreateSafetyMeetingReportUseCase'
import { BaseUpdateDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { SafetyMeetingReportService } from '@src/domain/services/SafetyMeetingReportService'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface UpdateSafetyMeetingReportDto extends SafetyMeetingReportDto, BaseUpdateDto {}

@injectable()
export class UpdateSafetyMeetingReportUseCase implements IUseCase<UpdateSafetyMeetingReportDto> {
  constructor(
    @inject(SafetyMeetingReportService)
    private readonly safetyMeetingReportService: IBaseDataService<undefined, UpdateSafetyMeetingReportDto>,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {}

  public async execute(dto: UpdateSafetyMeetingReportDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto?.files,
      'safetyMeetingReports',
      'files',
      userId,
      licenseeId
    )
    dto.files = filesList as SeaFile[]

    // Update Safety Meeting Report
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Safety Meeting Report',
      maximumBatchSize: 20,
    })

    const { ref: updatedSafetyMeetingReportRef, records: safetyMeetingReportRecords } =
      this.safetyMeetingReportService.updateDataItem(operation, dto, userId)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselIds,
      'safetyMeetingReports',
      updatedSafetyMeetingReportRef.id,
      dto?.notes ?? 'Safety Meeting Report'
    )

    operation.addMany(safetyMeetingReportRecords).add(actionLogRecord)

    await operation.commit()
  }
}
