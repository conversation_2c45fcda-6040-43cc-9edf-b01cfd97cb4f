import { BaseCreateDto, BaseDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { Timestamp } from '@react-native-firebase/firestore'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { SafetyMeetingReportService } from '@src/domain/services/SafetyMeetingReportService'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface SafetyMeetingReportDto extends BaseDto {
  vesselIds: string[]
  jobIds?: string[]
  notes?: string
  personnelPresentIds?: string[]
  personnelPresentNames?: string[]
  sendToCrew?: boolean
  signature?: string
  state: 'active' | 'deleted'
  touched?: Timestamp
  type?: string
  whenMeeting: number
  files: SeaFile[]
  // TODO: Cater for delete
  // whenDeleted?: number;
  // deletedBy?: string;
}

export interface CreateSafetyMeetingReportDto extends SafetyMeetingReportDto, BaseCreateDto {}

@injectable()
export class CreateSafetyMeetingReportUseCase implements IUseCase<CreateSafetyMeetingReportDto> {
  constructor(
    @inject(SafetyMeetingReportService)
    private readonly safetyMeetingReportService: IBaseDataService<CreateSafetyMeetingReportDto>,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {}
  public async execute(createSafetyMeetingReportDto: CreateSafetyMeetingReportDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      createSafetyMeetingReportDto?.files,
      'safetyMeetingReports',
      'files',
      userId,
      licenseeId
    )
    createSafetyMeetingReportDto.files = filesList as SeaFile[]

    // Create Safety Meeting Report
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Safety Meeting Report',
      maximumBatchSize: 20,
    })

    const { ref: createdSafetyMeetingReportRef, records: safetyMeetingReportRecords } =
      this.safetyMeetingReportService.createDataItem(operation, createSafetyMeetingReportDto, userId)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      createSafetyMeetingReportDto.vesselIds,
      'safetyMeetingReports',
      createdSafetyMeetingReportRef.id,
      createSafetyMeetingReportDto.notes ?? 'Safety Meeting Report'
    )

    operation.addMany(safetyMeetingReportRecords).add(actionLogRecord)

    await operation.commit()
  }
}
