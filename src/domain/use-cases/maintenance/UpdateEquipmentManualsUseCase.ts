import { EquipmentManualService, IEquipmentManualService } from '@src/domain/services/EquipmentManualService'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface UpdateEquipmentManualsDto {
  id: string
  title: string
  files: SeaFile[]
  vesselId: string
  equipmentIdsToAdd: string[]
  equipmentIdsToRemove: string[]
}

export interface IUpdateEquipmentManualsUseCase extends IUseCase<UpdateEquipmentManualsDto> {}

export class UpdateEquipmentManualsUseCase implements IUpdateEquipmentManualsUseCase {
  private readonly equipmentManualService: IEquipmentManualService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(EquipmentManualService)
    equipmentManualService: EquipmentManualService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.equipmentManualService = equipmentManualService
    this.actionLogService = actionLogService
  }

  public async execute(dto: UpdateEquipmentManualsDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.files,
      'equipmentManualDocuments',
      'files',
      userId,
      licenseeId
    )
    dto.files = filesList as SeaFile[]

    // Update Equipment Manuals
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Equipment Manuals Document',
      maximumBatchSize: 20,
    })

    const { equipmentIdsToAdd, equipmentIdsToRemove, vesselId, ...rest } = dto

    const { ref: updateEquipmentManualsRef, records: equipmentManualsRecords } =
      this.equipmentManualService.updateEquipmentManuals(operation, rest, userId, licenseeId, vesselId)

    operation.addMany(equipmentManualsRecords)

    if (equipmentIdsToAdd.length > 0) {
      equipmentIdsToAdd.forEach(equipmentId => {
        const { records: addEquipmentManualToEquipmentRecords } =
          this.equipmentManualService.addEquipmentManualsToEquipment(
            operation,
            equipmentId,
            dto.id,
            userId,
            licenseeId,
            vesselId
          )

        operation.addMany(addEquipmentManualToEquipmentRecords)
      })
    }

    if (equipmentIdsToRemove.length > 0) {
      equipmentIdsToRemove.forEach(equipmentId => {
        const { records: removeEquipmentManualToEquipmentRecords } =
          this.equipmentManualService.removeEquipmentManualsFromEquipment(
            operation,
            equipmentId,
            dto.id,
            userId,
            licenseeId,
            vesselId
          )
        operation.addMany(removeEquipmentManualToEquipmentRecords)
      })
    }

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'equipmentManualDocuments',
      updateEquipmentManualsRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
