import { inject } from 'inversify'
import { IUseCase } from '../UseCase'
import { UpdateJobListDto } from './UpdateJoblistUseCase'
import { JobListService } from '@src/domain/services/JobListService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { TagsService } from '@src/domain/services/TagsService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface CreateJobListDto extends Omit<UpdateJobListDto, 'id'> {}

export interface ICreateJobListUseCase extends IUseCase<CreateJobListDto> {}

export class CreateJobListUseCase implements ICreateJobListUseCase {
  private readonly jobListService: JobListService
  private readonly actionLogService: ActionLogService
  private readonly TagsService: TagsService

  constructor(
    @inject(JobListService)
    jobListService: JobListService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(TagsService)
    tagsService: TagsService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.jobListService = jobListService
    this.actionLogService = actionLogService
    this.TagsService = tagsService
  }

  async execute(dto: CreateJobListDto, userId: string, licenseeId: string): Promise<void> {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'jobs', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Create Job List
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Job List',
      maximumBatchSize: 20,
    })
    const { newMaintenanceTags, newTags, ...rest } = dto

    const { ref: createJobListRef, records: jobListRecord } = this.jobListService.createJobList(
      operation,
      rest,
      userId,
      licenseeId
    )

    operation.addMany(jobListRecord)

    if (newMaintenanceTags.length > 0) {
      newMaintenanceTags.forEach(tag => {
        const addMaintenanceTagRecord = this.TagsService.addMaintenanceTag(operation, dto.vesselId, tag)

        operation.add(addMaintenanceTagRecord)
      })
    }

    if (newTags.length > 0) {
      newTags.forEach(tag => {
        const addJobTagRecord = this.TagsService.addJobTag(operation, dto.vesselId, tag)

        operation.add(addJobTagRecord)
      })
    }

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'jobs',
      createJobListRef.id,
      dto.task
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
