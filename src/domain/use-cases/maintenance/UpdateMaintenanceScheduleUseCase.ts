import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { TagsService } from '@src/domain/services/TagsService'
import { MaintenanceScheduleService } from '@src/domain/services/MaintenanceScheduleService'

export interface UpdateMaintenanceScheduleDto {
  id: string
  equipmentId: string
  locationId: string
  task: string
  description: string
  maintenanceTags: string
  intervalType: string
  intervalWeekMonth?: string
  dateDue?: string
  intervalEngineHours?: number
  engineId?: string
  engineHoursDue?: number
  estimatedTime: number
  vesselId: string

  newMaintenanceTags: string[]
}

export interface IUpdateMaintenanceScheduleUseCase extends IUseCase<UpdateMaintenanceScheduleDto> {}

@injectable()
export class UpdateMaintenanceScheduleUseCase implements IUpdateMaintenanceScheduleUseCase {
  private readonly maintenanceScheduleService: MaintenanceScheduleService
  private readonly actionLogService: ActionLogService
  private readonly TagsService: TagsService

  constructor(
    @inject(MaintenanceScheduleService)
    maintenanceScheduleService: MaintenanceScheduleService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(TagsService)
    tagsService: TagsService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.maintenanceScheduleService = maintenanceScheduleService
    this.actionLogService = actionLogService
    this.TagsService = tagsService
  }

  public async execute(dto: UpdateMaintenanceScheduleDto, userId: string, licenseeId: string) {
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Maintenance Schedule',
      maximumBatchSize: 20,
    })

    const { newMaintenanceTags, ...rest } = dto

    const { ref: updateEquipmentManualsRef, records: miantenanceScheduleRecords } =
      this.maintenanceScheduleService.updateMaintenanceSchedule(operation, rest, userId, licenseeId)

    operation.addMany(miantenanceScheduleRecords)

    if (newMaintenanceTags.length > 0) {
      newMaintenanceTags.forEach(tag => {
        const addMaintenanceTagRecord = this.TagsService.addMaintenanceTag(operation, dto.vesselId, tag)

        operation.add(addMaintenanceTagRecord)
      })
    }

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'scheduledMaintenanceTasks',
      updateEquipmentManualsRef.id,
      dto.task
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
