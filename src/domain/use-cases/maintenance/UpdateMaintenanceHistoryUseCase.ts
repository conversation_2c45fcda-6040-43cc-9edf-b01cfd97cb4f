import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { FirestoreOperation } from '@src/domain/data/FirestoreOperation'
import { IMaintenanceHistoryService, MaintenanceHistoryService } from '@src/domain/services/MaintenanceHistoryService'
import { FieldValue } from '@src/lib/firebase/services/firestore.service'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'

import { SeaFile } from '@src/lib/fileImports'

export interface MaintenanceTaskDto {
  id: string
  whenLastService?: number
  dateDue?: string
  engineHoursDue?: number | FieldValue
}

export interface JobDto {
  id: string
  task: string
  jobNum?: string
  description: string
  system?: string
  equipment?: string
  location?: string
  isCritical?: boolean
  completedByName: string
  notes?: string
  maintenanceTags?: string[]
  actualTime?: number
  partsUsed?: string
  whenCompleted: string
}

export interface UpdateMaintenanceHistoryDto {
  vesselId: string
  itemId: string
  task: string
  description: string
  maintenanceTags: string[] | FieldValue
  whenCompleted: number | FieldValue
  equipmentId: string | FieldValue
  locationId: string
  //TODO SpareParts
  spareParts: Record<string, { used?: number; added?: number }>
  files?: SeaFile[]
  maintenanceTask?: MaintenanceTaskDto
  job?: JobDto
}

export interface IUpdateMaintenanceHistoryUseCase extends IUseCase<UpdateMaintenanceHistoryDto> {}

@injectable()
export class UpdateMaintenanceHistoryUseCase implements IUpdateMaintenanceHistoryUseCase {
  private readonly maintenanceHistoryService: IMaintenanceHistoryService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(MaintenanceHistoryService)
    maintenanceHistoryService: MaintenanceHistoryService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.maintenanceHistoryService = maintenanceHistoryService
    this.actionLogService = actionLogService
  }

  public async execute(
    dto: UpdateMaintenanceHistoryDto,
    userId: string,
    licenseeId: string,
    executeMaintenanceTask = false
  ) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.files,
      'maintenanceTasksCompleted',
      'files',
      userId,
      licenseeId
    )
    dto.files = filesList as SeaFile[]

    // Create Maintenance History
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Maintenance History',
      maximumBatchSize: 20,
    })

    const { maintenanceTask, ...rest } = dto

    const { ref: updateMaintenanceHistoryRef, records: maintenanceHistoryRecords } =
      this.maintenanceHistoryService.updateMaintenanceHistory(operation, rest, userId, licenseeId)

    operation.addMany(maintenanceHistoryRecords)

    if (executeMaintenanceTask && maintenanceTask) {
      const maintenanceTaskRecords = this.maintenanceHistoryService.updateScheduleMaintenanceTask(
        operation,
        maintenanceTask,
        userId
      )

      operation.add(maintenanceTaskRecords)
    }

    //TODO: Vessel Notifications Service

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'maintenanceTasksCompleted',
      updateMaintenanceHistoryRef.id,
      dto.task
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
