import { JobListService } from '@src/domain/services/JobListService'
import { IUseCase } from '../UseCase'
import { CreateJobListDto } from './CreateJobListUseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'

export interface UpdateJobListTaskDto
  extends Omit<
    CreateJobListDto,
    | 'equipmentId'
    | 'locationId'
    | 'emailReminder'
    | 'dateToRemind'
    | 'emailToIds'
    | 'estimatedTime'
    | 'newMaintenanceTags'
    | 'newTags'
  > {
  id: string
}

export interface IUpdateUpdateJobListTaskUseCase extends IUseCase<UpdateJobListTaskDto> {}

export class UpdateJobListTaskUseCase implements IUpdateUpdateJobListTaskUseCase {
  private readonly jobListService: JobListService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(JobListService)
    jobListService: JobListService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.jobListService = jobListService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateJobListTaskDto, userId: string, licenseeId: string): Promise<void> {
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Append Job List',
      maximumBatchSize: 20,
    })

    const { ref: updateJobListTaskRef, records: jobListRecord } = this.jobListService.updateTask(
      operation,
      dto,
      userId,
      licenseeId
    )

    operation.addMany(jobListRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'jobs',
      updateJobListTaskRef.id,
      dto.task
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
