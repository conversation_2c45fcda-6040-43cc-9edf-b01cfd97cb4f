import { SparePartService } from '@src/domain/services/SparePartService'
import { IUseCase } from '../UseCase'
import { UpdateSparePartDto } from './UpdateSparePartUseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface CreateSparePartDto extends Omit<UpdateSparePartDto, 'id'> {}

export interface ICreateSparePartUseCase extends IUseCase<CreateSparePartDto> {}

export class CreateSparePartUseCase implements ICreateSparePartUseCase {
  private readonly sparePartService: SparePartService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SparePartService)
    sparePartService: SparePartService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.sparePartService = sparePartService
    this.actionLogService = actionLogService
  }

  async execute(dto: CreateSparePartDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'spareParts', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Create Spare Part
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Spare Part',
      maximumBatchSize: 20,
    })

    const { ref: updateSparePartRef, records: sparePartRecord } = this.sparePartService.createSparePart(
      operation,
      dto,
      userId,
      licenseeId,
      dto.vesselId
    )

    operation.addMany(sparePartRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'spareParts',
      updateSparePartRef.id,
      dto.item
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
