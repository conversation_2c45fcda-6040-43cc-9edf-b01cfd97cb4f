import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { UpdateMaintenanceHistoryDto } from './UpdateMaintenanceHistoryUseCase'
import { IMaintenanceHistoryService, MaintenanceHistoryService } from '@src/domain/services/MaintenanceHistoryService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface CreateMaintenanceHistoryDto extends Omit<UpdateMaintenanceHistoryDto, 'maintenanceTask' | 'job'> {}

export interface ICreateMaintenanceHistoryUseCase extends IUseCase<CreateMaintenanceHistoryDto> {}

@injectable()
export class CreateMaintenanceHistoryUseCase implements ICreateMaintenanceHistoryUseCase {
  private readonly maintenanceHistoryService: IMaintenanceHistoryService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(MaintenanceHistoryService)
    maintenanceHistoryService: MaintenanceHistoryService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.maintenanceHistoryService = maintenanceHistoryService
    this.actionLogService = actionLogService
  }

  public async execute(dto: UpdateMaintenanceHistoryDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.files,
      'maintenanceTasksCompleted',
      'files',
      userId,
      licenseeId
    )
    dto.files = filesList as SeaFile[]

    // Update Maintenance History
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Add Maintenance History',
      maximumBatchSize: 20,
    })

    const { ref: createMaintenanceHistoryRef, records: maintenanceHistoryRecords } =
      this.maintenanceHistoryService.createMaintenanceHistory(operation, dto, userId, licenseeId)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'maintenanceTasksCompleted',
      createMaintenanceHistoryRef.id,
      dto.task
    )

    operation.addMany(maintenanceHistoryRecords).add(actionLogRecord)

    await operation.commit()
  }
}
