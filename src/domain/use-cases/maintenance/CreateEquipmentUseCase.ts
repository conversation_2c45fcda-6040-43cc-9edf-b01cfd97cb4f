import { EquipmentService } from '@src/domain/services/EquipmentService'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { inject } from 'inversify'
import { UpdateEquipmentDto } from './UpdateEquipmentUseCase'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface CreateEquipmentDto extends Omit<UpdateEquipmentDto, 'id'> {}

export interface ICreateEquipmentUseCase extends IUseCase<CreateEquipmentDto> {}

export class CreateEquipmentUseCase implements ICreateEquipmentUseCase {
  private readonly equipmentService: EquipmentService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(EquipmentService)
    equipmentService: EquipmentService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.equipmentService = equipmentService
    this.actionLogService = actionLogService
  }

  async execute(dto: CreateEquipmentDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'equipment', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Create Equipment
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Spare Part',
      maximumBatchSize: 20,
    })

    const { vesselId, ...rest } = dto

    const { ref: updateEquipmentRef, records: equipmentRecord } = this.equipmentService.createSparePart(
      operation,
      rest,
      userId,
      licenseeId,
      vesselId
    )

    operation.addMany(equipmentRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      vesselId,
      'equipment',
      updateEquipmentRef.id,
      dto.equipment
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
