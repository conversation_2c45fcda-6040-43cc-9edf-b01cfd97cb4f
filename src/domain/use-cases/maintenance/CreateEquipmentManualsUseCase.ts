import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { UpdateEquipmentManualsDto } from './UpdateEquipmentManualsUseCase'
import { EquipmentManualService, IEquipmentManualService } from '@src/domain/services/EquipmentManualService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface CreateEquipmentManualsDto extends Omit<UpdateEquipmentManualsDto, 'id' | 'equipmentIdsToRemove'> {}

export interface ICreateEquipmentManualsUseCase extends IUseCase<CreateEquipmentManualsDto> {}

@injectable()
export class CreateEquipmentManualsUseCase implements ICreateEquipmentManualsUseCase {
  private readonly equipmentManualService: IEquipmentManualService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(EquipmentManualService)
    equipmentManualService: EquipmentManualService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.equipmentManualService = equipmentManualService
    this.actionLogService = actionLogService
  }

  public async execute(dto: CreateEquipmentManualsDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.files,
      'equipmentManualDocuments',
      'files',
      userId,
      licenseeId
    )
    dto.files = filesList as SeaFile[]

    // Create Equipment Manuals
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Equipment Manuals Document',
      maximumBatchSize: 20,
    })

    const { equipmentIdsToAdd, ...rest } = dto

    const { ref: createEquipmentManualsRef, records: equipmentManualsRecords } =
      this.equipmentManualService.createEquipmentManuals(operation, rest, userId, licenseeId, rest.vesselId)

    operation.addMany(equipmentManualsRecords)

    if (equipmentIdsToAdd.length > 0) {
      equipmentIdsToAdd.forEach(equipmentId => {
        const { records: addEquipmentManualToEquipmentRecords } =
          this.equipmentManualService.addEquipmentManualsToEquipment(
            operation,
            equipmentId,
            createEquipmentManualsRef.id,
            userId,
            licenseeId,
            rest.vesselId
          )

        operation.addMany(addEquipmentManualToEquipmentRecords)
      })
    }

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'equipmentManualDocuments',
      createEquipmentManualsRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
