import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { UpdateMaintenanceScheduleDto } from './UpdateMaintenanceScheduleUseCase'
import { MaintenanceScheduleService } from '@src/domain/services/MaintenanceScheduleService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { TagsService } from '@src/domain/services/TagsService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'

export interface CreateMaintenanceScheduleDto extends Omit<UpdateMaintenanceScheduleDto, 'id'> {
  whenLastService?: number
  engineHoursLastService?: number
  updateEngineData?: boolean
  engineName?: string
}

export interface ICreateMaintenanceScheduleUseCase extends IUseCase<CreateMaintenanceScheduleDto> {}

@injectable()
export class CreateMaintenanceScheduleUseCase implements ICreateMaintenanceScheduleUseCase {
  private readonly maintenanceScheduleService: MaintenanceScheduleService
  private readonly actionLogService: ActionLogService
  private readonly TagsService: TagsService

  constructor(
    @inject(MaintenanceScheduleService)
    maintenanceScheduleService: MaintenanceScheduleService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(TagsService)
    tagsService: TagsService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.maintenanceScheduleService = maintenanceScheduleService
    this.actionLogService = actionLogService
    this.TagsService = tagsService
  }

  public async execute(dto: CreateMaintenanceScheduleDto, userId: string, licenseeId: string) {
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Maintenance Schedule',
      maximumBatchSize: 20,
    })

    const { newMaintenanceTags, updateEngineData, engineName, ...rest } = dto

    const { ref: createMaintenanceScheduleRef, records: maintenanceScheduleRecords } =
      this.maintenanceScheduleService.createMaintenanceSchedule(operation, rest, userId, licenseeId)

    operation.addMany(maintenanceScheduleRecords)

    if (newMaintenanceTags.length > 0) {
      newMaintenanceTags.forEach(tag => {
        const addMaintenanceTagRecord = this.TagsService.addMaintenanceTag(operation, dto.vesselId, tag)

        operation.add(addMaintenanceTagRecord)
      })
    }

    if (updateEngineData && dto.engineId) {
      const engineHoursDto = {
        engineId: dto.engineId,
        engineHours: dto.engineHoursLastService,
      }
      const { ref: engineRef, records: engineRecords } = this.maintenanceScheduleService.updateEngineData(
        operation,
        engineHoursDto,
        userId,
        licenseeId,
        dto.vesselId
      )

      operation.addMany(engineRecords)

      const engineActionLogRecord = this.actionLogService.createUpdatedAction(
        operation,
        userId,
        licenseeId,
        dto.vesselId,
        'engines',
        engineRef.id,
        `${engineName}, ${dto.engineHoursLastService} hours`
      )

      operation.add(engineActionLogRecord)
    }

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'scheduledMaintenanceTasks',
      createMaintenanceScheduleRef.id,
      dto.task
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
