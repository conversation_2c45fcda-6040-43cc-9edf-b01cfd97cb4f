import { SparePartService } from '@src/domain/services/SparePartService'
import { IUseCase } from '../UseCase'
import { inject } from 'inversify'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'

import { SeaFile } from '@src/lib/fileImports'

export interface UpdateSparePartDto {
  id: string
  vesselId: string
  item: string
  systemId: string
  equipmentIds: string[]
  locationId: string
  locationDescription?: string
  quantity: number
  minQuantity?: number
  manufacturer?: string
  model?: string
  partNum?: string
  unitPrice?: number
  notes?: string
  files: SeaFile[]
  contactIds: string[]
}

export interface IUpdateSparePartUseCase extends IUseCase<UpdateSparePartDto> {}

export class UpdateSparePartUseCase implements IUpdateSparePartUseCase {
  private readonly sparePartService: SparePartService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(SparePartService)
    sparePartService: SparePartService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.sparePartService = sparePartService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateSparePartDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'spareParts', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Create Spare Part
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Spare Part',
      maximumBatchSize: 20,
    })

    const { vesselId, ...rest } = dto

    const { ref: updateSparePartRef, records: sparePartRecord } = this.sparePartService.updateSparePart(
      operation,
      rest,
      userId,
      licenseeId,
      vesselId
    )

    operation.addMany(sparePartRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'spareParts',
      updateSparePartRef.id,
      dto.item
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
