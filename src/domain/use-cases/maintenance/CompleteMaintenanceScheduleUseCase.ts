import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { MaintenanceScheduleService } from '@src/domain/services/MaintenanceScheduleService'
import { ActionLogService, ActionLogType } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface CompleteMaintenanceScheduleDto {
  vesselId: string
  maintenanceTaskId: string
  equipmentName: string
  equipmentId?: string
  location?: string
  task?: string
  whenCompleted?: number
  whenLastService?: number
  engineHoursLastService?: number
  dateDue?: string
  engineHoursDue?: number
  files: SeaFile[]
  notes?: string
  engineHours?: number
  engineId?: string
  /**
   * @param spareParts TODO: Need to do the logic on the UI and data layer
   */
  spareParts?: any
  actualTime?: number

  updateEngineData?: boolean
  engineName?: string
}

export interface ICompleteMaintenanceScheduleUseCase extends IUseCase<CompleteMaintenanceScheduleDto> {}

@injectable()
export class CompleteMaintenanceScheduleUseCase implements ICompleteMaintenanceScheduleUseCase {
  private readonly maintenanceScheduleService: MaintenanceScheduleService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(MaintenanceScheduleService)
    maintenanceScheduleService: MaintenanceScheduleService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.maintenanceScheduleService = maintenanceScheduleService
    this.actionLogService = actionLogService
  }

  public async execute(dto: CompleteMaintenanceScheduleDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      dto.files,
      'maintenanceTasksCompleted',
      'files',
      userId,
      licenseeId
    )
    dto.files = filesList as SeaFile[]

    // Complete Maintenance Schedule
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Scheduled Maintenance Task Completed',
      maximumBatchSize: 20,
    })

    const { updateEngineData, equipmentName, engineName, ...rest } = dto

    const { ref, records } = this.maintenanceScheduleService.completeMaintenanceSchedule(
      operation,
      rest,
      userId,
      licenseeId,
      dto.vesselId
    )

    operation.addMany(records)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselId,
      'maintenanceTasksCompleted',
      ref.id,
      equipmentName,
      ActionLogType.SCHEDULED
    )

    operation.add(actionLogRecord)

    if (updateEngineData && dto.engineId) {
      const engineHoursDto = {
        engineId: dto.engineId,
        engineHours: dto.engineHours,
      }
      const { ref: engineRef, records: engineRecords } = this.maintenanceScheduleService.updateEngineData(
        operation,
        engineHoursDto,
        userId,
        licenseeId,
        dto.vesselId
      )

      operation.addMany(engineRecords)

      const engineActionLogRecord = this.actionLogService.createUpdatedAction(
        operation,
        userId,
        licenseeId,
        dto.vesselId,
        'engines',
        engineRef.id,
        `${engineName}, ${dto.engineHours} hours`
      )

      operation.add(engineActionLogRecord)
    }

    await operation.commit()
  }
}
