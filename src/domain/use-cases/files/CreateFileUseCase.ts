import { BaseCreateDto, BaseDto, BaseWithLicenseeIdDto, BaseWithLicenseeIdsDto } from '@src/domain/use-cases/UseCase'
import { FileCollection } from '@src/shared-state/FileSyncSystem/cachedFiles'
import { Timestamp } from '@react-native-firebase/firestore'
import { FileExt, FileState } from '@src/lib/fileImports'

/** Similar to interface `SeaFile` */
export interface FileDto extends BaseDto, BaseWithLicenseeIdDto, BaseWithLicenseeIdsDto {
  contentType?: string
  docId?: string
  emailToken: string
  isSignature?: boolean
  lastModified?: number
  ext: FileExt
  name?: string
  refs: Ref[]
  state: FileState
  triggerCheck?: Timestamp
  uploadFor: UploadFor
  whenOrphaned?: number
}

interface Ref {
  collection: string
  docId: string
}

interface UploadFor {
  collection: FileCollection
  field: string
}

export interface CreateFileDto extends FileDto, BaseCreateDto {}
