import { Injury } from '@src/shared-state/HealthSafety/incidents'
import { Timestamp } from '@react-native-firebase/firestore'
import {
  BaseCreateDto,
  BaseDto,
  BaseWithLicenseeIdDto,
  BaseWithVesselIdDto,
  IUseCase,
} from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { IIncidentService, IncidentService } from '@src/domain/services/IncidentService'
import { FileService } from '@src/domain/services/FileService'

import { SeaFile } from '@src/lib/fileImports'

export interface IncidentDto extends BaseDto, BaseWithVesselIdDto, BaseWithLicenseeIdDto {
  /** Created below data from the Data Schema in Firestore */
  // addedBy: string;
  categoryId?: string
  causeIds?: string[]
  completedBy?: string
  conditions?: string
  // deletedBy?: string;
  description: string
  files: SeaFile[]
  initialActions?: string
  injuries?: Injury[]
  licenseeId: string
  location?: string
  name: string
  notifiedAuthorities: string
  prevention?: string
  propertyDamage?: string
  reportNum?: number | string // auto generated
  reportedBy?: string
  role?: string
  // shouldReportToManagement?: boolean;
  signature: string
  state: string
  touched?: Timestamp
  type?: string
  // updatedBy?: string;  // NOTE: Used in Update
  vesselId: string
  // whenAdded: number;
  whenCompleted?: number
  // whenDeleted?: number;
  whenEvent: number
  // whenUpdated?: number; // NOTE: Used in Update
  whoInvolved?: string
  whoInvolvedTypes?: string[]
  witnesses?: string
}

export interface CreateIncidentDto extends IncidentDto, BaseCreateDto {}

export interface ICreateIncidentUseCase extends IUseCase<CreateIncidentDto> {}

@injectable()
export class CreateIncidentUseCase implements ICreateIncidentUseCase {
  constructor(
    @inject(IncidentService)
    private readonly incidentService: IIncidentService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {}

  public async execute(createIncidentDto: CreateIncidentDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(
      createIncidentDto.files,
      'incidents',
      'files',
      userId,
      licenseeId
    )
    createIncidentDto.files = filesList as SeaFile[]

    // Create Incident
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Incident',
      maximumBatchSize: 20,
    })

    const { ref: createdIncidentRef, records: incidentRecords } = this.incidentService.createIncident(
      operation,
      createIncidentDto,
      userId,
      licenseeId
    )

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      createIncidentDto.vesselId,
      'incidents',
      createdIncidentRef.id,
      createIncidentDto.name
    )

    operation.addMany(incidentRecords).add(actionLogRecord)

    await operation.commit()
  }
}
