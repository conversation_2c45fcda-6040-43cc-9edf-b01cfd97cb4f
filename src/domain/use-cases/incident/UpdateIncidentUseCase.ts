import { IncidentDto } from '@src/domain/use-cases/incident/CreateIncidentUseCase'
import { inject, injectable } from 'inversify'
import { IIncidentService, IncidentService } from '@src/domain/services/IncidentService'
import { BaseUpdateDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface UpdateIncidentDto extends Omit<IncidentDto, 'reportNum'>, BaseUpdateDto {}

export interface IUpdateIncidentUseCase extends IUseCase<UpdateIncidentDto> {}

@injectable()
export class UpdateIncidentUseCase implements IUpdateIncidentUseCase {
  constructor(
    @inject(IncidentService)
    private readonly incidentService: IIncidentService,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {}

  public async execute(dto: UpdateIncidentDto, userId: string, licenseeId: string, vesselId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'incidents', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Update Incident
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Incident',
      maximumBatchSize: 20,
    })

    const { ref: updatedIncidentRef, records: incidentRecords } = this.incidentService.updateIncident(
      operation,
      dto,
      userId,
      licenseeId
    )

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      vesselId,
      'incidents',
      updatedIncidentRef.id,
      dto.name
    )

    operation.addMany(incidentRecords).add(actionLogRecord)

    await operation.commit()
  }
}
