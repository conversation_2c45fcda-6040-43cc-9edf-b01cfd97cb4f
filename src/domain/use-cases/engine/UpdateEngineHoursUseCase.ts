import { EngineService } from '@src/domain/services/EngineService'
import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'

export interface EngineDto {
  engineId: string
  engineHours: number
  engineName: string
}
export interface UpdateEngineHoursDto {
  vesselId: string
  engines: EngineDto[]
}

export interface IUpdateEngineHoursUseCase extends IUseCase<UpdateEngineHoursDto> {}

export class UpdateEngineHoursUseCase implements IUpdateEngineHoursUseCase {
  private readonly engineService: EngineService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(EngineService)
    engineService: EngineService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.engineService = engineService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateEngineHoursDto, userId: string, licenseeId: string): Promise<void> {
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Append Engine/Equipment Hours',
      maximumBatchSize: 20,
    })

    const { engines } = dto

    const { ref: updateEngineHoursTaskRef, records: engineHoursRecord } = this.engineService.updateEngineHours(
      operation,
      dto,
      userId,
      licenseeId
    )

    operation.addMany(engineHoursRecord)

    engines.forEach(engine => {
      const actionLogRecord = this.actionLogService.createUpdatedAction(
        operation,
        userId,
        licenseeId,
        dto.vesselId,
        'engines',
        updateEngineHoursTaskRef.id,
        `${engine.engineName}, ${engine.engineHours} hours`
      )
      operation.add(actionLogRecord)
    })

    await operation.commit()
  }
}
