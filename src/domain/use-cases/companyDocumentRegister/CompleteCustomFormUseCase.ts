import { IUseCase } from '../UseCase'
import { CustomFormService } from '@src/domain/services/CustomFormService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject, injectable } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { FileService } from '@src/domain/services/FileService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'

export type CustomFormCompletedAttachTo = null | 'voyage' | 'trainingTaskReport'

export interface CompleteCustomFormDto {
  customFormId: string
  title: string
  versionId: string
  version: number
  vesselIds?: string[]
  personnelIds?: string[]
  isDraft: boolean
  formData: Record<string, any>
  attachTo?: CustomFormCompletedAttachTo
  attachToId?: string
  attachToTrainingTaskId?: string // for trainingTaskReports so we can query by taskId
  attachToVesselId?: string
  filesPosition?: string
  signaturePosition?: string
}

export interface ICompleteCustomFormUseCase extends IUseCase<CompleteCustomFormDto> {}

@injectable()
export class CompleteCustomFormUseCase implements ICompleteCustomFormUseCase {
  private readonly customFormService: CustomFormService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CustomFormService)
    customFormService: CustomFormService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.customFormService = customFormService
    this.actionLogService = actionLogService
  }

  async execute(dto: CompleteCustomFormDto, userId: string, licenseeId: string) {
    const { filesPosition, signaturePosition, title, ...rest } = dto

    if (filesPosition && rest.formData[filesPosition].length > 0) {
      const files = rest.formData[filesPosition]
      const filesList = await this.fileService.uploadFiles(files, 'customFormsCompleted', 'files', userId, licenseeId)

      rest.formData[filesPosition] = filesList
    }

    //TODO: Handle signature upload if signaturePosition is provided

    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Add Completed Custom From ${title}`,
      maximumBatchSize: 20,
    })

    const { ref: addCompleteFormRef, records: completeFromRecord } = this.customFormService.completeCustomForm(
      operation,
      rest,
      userId,
      licenseeId
    )

    operation.addMany(completeFromRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselIds ?? [],
      'customFormsCompleted',
      addCompleteFormRef.id,
      title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
