import { Timestamp } from '@react-native-firebase/firestore'
import { inject, injectable } from 'inversify'
import { BaseCreateDto, BaseDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { CompanyPlanService } from '@src/domain/services/CompanyPlanService'
import { SFDocFileService } from '@src/domain/services/SFDocFileService'
import { SeaFile } from '@src/lib/fileImports'

export type SFDocNew = {
  [key: string | number]: string | undefined
}

export interface CompanyPlanDto extends BaseDto {
  dateDue?: string
  dateLastReviewed?: string
  dateToRemind?: string
  emailReminder?: string
  interval?: string
  lastReviewDate_old?: number
  sfdoc?: SFDocNew
  touched?: Timestamp
  whenDue_old?: number
  whenToRemind_old?: number
}

export interface CreateCompanyPlanDto extends CompanyPlanDto, BaseCreateDto {}

@injectable()
export class CreateCompanyPlanUseCase implements IUseCase<CreateCompanyPlanDto> {
  constructor(
    @inject(CompanyPlanService)
    private readonly companyPlanService: IBaseDataService<CreateCompanyPlanDto>,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SFDocFileService)
    private readonly sfDocFileService: SFDocFileService
  ) {}

  async execute(dto: CompanyPlanDto, userId: string, licenseeId: string): Promise<void> {
    // Upload Files
    const sfDocFile = await this.sfDocFileService.uploadSFDocFile(
      dto.sfdoc as SeaFile,
      'companyPlans',
      'sfdoc',
      userId,
      licenseeId
    )
    dto.sfdoc = sfDocFile

    // Create Company Plan
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Create Company Plan`,
      maximumBatchSize: 20,
    })

    const { ref: updateCompanyPlanRef, records: companyPlanRecord } = this.companyPlanService.createDataItem(
      operation,
      dto,
      userId,
      licenseeId
    )

    operation.addMany(companyPlanRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      [],
      'companyPlans',
      updateCompanyPlanRef.id,
      'Company Plan'
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
