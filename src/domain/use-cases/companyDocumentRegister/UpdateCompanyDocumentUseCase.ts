import { SeaFile } from '@src/lib/fileImports'
import { SFDoc } from '@src/shared-state/CompanyDocuments/companyDocuments'
import { IUseCase } from '../UseCase'
import { CompanyDocumentService } from '@src/domain/services/CompanyDocumentService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject, injectable } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'

export interface UpdateCompanyDocumentDto {
  id: string
  title: string
  type: string
  interval?: string
  categoryId?: string
  dateExpires?: string
  emailReminder?: string
  dateToRemind?: string
  files: SeaFile[]
  sfdoc?: Partial<SFDoc>
}

export interface IUpdateCompanyDocumentUseCase extends IUseCase<UpdateCompanyDocumentDto> {}

@injectable()
export class UpdateCompanyDocumentUseCase implements IUpdateCompanyDocumentUseCase {
  private readonly companyDocumentService: CompanyDocumentService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CompanyDocumentService)
    companyDocumentService: CompanyDocumentService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.companyDocumentService = companyDocumentService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateCompanyDocumentDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'companyDocuments', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Update Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update Company Document ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateCompanyDocumentsRef, records: companyDocumentsRecord } =
      this.companyDocumentService.updateCompanyDocument(operation, dto, userId, licenseeId)

    operation.addMany(companyDocumentsRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      [],
      'companyDocuments',
      updateCompanyDocumentsRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
