import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject, injectable } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { CompanyDocumentService } from '@src/domain/services/CompanyDocumentService'

export interface DeleteCompanyDocumentDto {
  id: string
  title: string
}

export interface IDeleteCompanyDocumentUseCase extends IUseCase<DeleteCompanyDocumentDto> {}

@injectable()
export class DeleteCompanyDocumentUseCase implements IDeleteCompanyDocumentUseCase {
  private readonly companyDocumentService: CompanyDocumentService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CompanyDocumentService)
    companyDocumentService: CompanyDocumentService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService
  ) {
    this.companyDocumentService = companyDocumentService
    this.actionLogService = actionLogService
  }

  async execute(dto: DeleteCompanyDocumentDto, userId: string, licenseeId: string) {
    // Delete Vessel Document
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Delete Company Document ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { title, ...rest } = dto

    const { ref: updateVesselDocumentsRef, records: vesselDocumentsRecord } =
      this.companyDocumentService.deleteVesselDocument(operation, rest, userId, licenseeId)

    operation.addMany(vesselDocumentsRecord)

    const actionLogRecord = this.actionLogService.createDeletedAction(
      operation,
      userId,
      licenseeId,
      [],
      'companyDocuments',
      updateVesselDocumentsRef.id,
      title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
