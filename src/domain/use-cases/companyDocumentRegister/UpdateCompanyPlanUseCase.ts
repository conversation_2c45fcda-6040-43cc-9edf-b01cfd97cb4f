import { CompanyPlanDto } from '@src/domain/use-cases/companyDocumentRegister/CreateCompanyPlanUseCase'
import { BaseUpdateDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { CompanyPlanService } from '@src/domain/services/CompanyPlanService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { SFDocFileService } from '@src/domain/services/SFDocFileService'
import { SeaFile } from '@src/lib/fileImports'

export interface UpdateCompanyPlanDto extends CompanyPlanDto, BaseUpdateDto {}

export interface IUpdateCompanyPlanUseCase extends IUseCase<UpdateCompanyPlanDto> {}

@injectable()
export class UpdateCompanyPlanUseCase implements IUpdateCompanyPlanUseCase {
  constructor(
    @inject(CompanyPlanService)
    private readonly companyPlanService: IBaseDataService<undefined, UpdateCompanyPlanDto>,
    @inject(ActionLogService)
    private readonly actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(SFDocFileService)
    private readonly sfDocFileService: SFDocFileService
  ) {}

  async execute(dto: UpdateCompanyPlanDto, userId: string, licenseeId: string): Promise<void> {
    // Upload Files
    const sfDocFile = await this.sfDocFileService.uploadSFDocFile(
      dto.sfdoc as SeaFile,
      'companyPlans',
      'sfdoc',
      userId,
      licenseeId
    )
    dto.sfdoc = sfDocFile

    // Update Company Plan
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update Company Plan`,
      maximumBatchSize: 20,
    })

    const { ref: updateCompanyPlanRef, records: companyPlanRecord } = this.companyPlanService.updateDataItem(
      operation,
      dto,
      userId,
      licenseeId
    )

    operation.addMany(companyPlanRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      [],
      'companyPlans',
      updateCompanyPlanRef.id,
      'Company Plan'
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
