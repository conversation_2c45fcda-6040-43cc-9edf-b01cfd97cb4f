import { inject, injectable } from 'inversify'
import { IUseCase } from '../UseCase'
import { CustomFormService } from '@src/domain/services/CustomFormService'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { FileService } from '@src/domain/services/FileService'
import { DocRef, IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SeaFile } from '@src/lib/fileImports'
import { CustomFormElementType } from '@src/components/_atoms/SeaCustomFormElement/SeaCustomFormElement'

export interface CreateCustomFormDto {
  title: string
  categoryId?: string
  forVesselIds?: string[]
  forCrew?: boolean
  isTemplate?: boolean
  templateCategory?: string
  files?: SeaFile[]
  latestVersion?: number
  form: Record<string, CustomFormElementType>
  historyElement?: number
}

export interface ICreateCustomFormUseCase extends IUseCase<CreateCustomFormDto, { ref: DocRef }> {}

@injectable()
export class CreateCustomFormUseCase implements ICreateCustomFormUseCase {
  private readonly customFormService: CustomFormService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CustomFormService)
    customFormService: CustomFormService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.customFormService = customFormService
    this.actionLogService = actionLogService
  }

  async execute(dto: CreateCustomFormDto, userId: string, licenseeId: string) {
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Create Form/Checklist ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: createCustomFormRef, records: createCustomFromRecord } = this.customFormService.createCustomForm(
      operation,
      dto,
      userId,
      licenseeId
    )

    operation.addMany(createCustomFromRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      [],
      'customForms',
      createCustomFormRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()

    return {
      ref: createCustomFormRef,
    }
  }
}
