import { CustomFormService } from '@src/domain/services/CustomFormService'
import { IUseCase } from '../UseCase'
import { CompleteCustomFormDto } from './CompleteCustomFormUseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject, injectable } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { FileService } from '@src/domain/services/FileService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'

export interface UpdateCompletedCustomFormDto
  extends Pick<
    CompleteCustomFormDto,
    | 'customFormId'
    | 'title'
    | 'vesselIds'
    | 'personnelIds'
    | 'isDraft'
    | 'formData'
    | 'filesPosition'
    | 'signaturePosition'
  > {
  id: string
}

export interface IUpdateCompletedCustomFormUseCase extends IUseCase<UpdateCompletedCustomFormDto> {}

@injectable()
export class UpdateCompletedCustomFormUseCase implements IUpdateCompletedCustomFormUseCase {
  private readonly customFormService: CustomFormService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CustomFormService)
    customFormService: CustomFormService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.customFormService = customFormService
    this.actionLogService = actionLogService
  }

  async execute(dto: UpdateCompletedCustomFormDto, userId: string, licenseeId: string) {
    const { title, filesPosition, signaturePosition, ...rest } = dto

    if (filesPosition && rest.formData[filesPosition].length > 0) {
      const files = rest.formData[filesPosition]
      const filesList = await this.fileService.uploadFiles(files, 'customFormsCompleted', 'files', userId, licenseeId)

      rest.formData[filesPosition] = filesList
    }

    //TODO: Handle signature upload if signaturePosition is provided

    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: `Update Completed Custom From ${title}`,
      maximumBatchSize: 20,
    })

    const { ref: addCompleteFormRef, records: completeFromRecord } = this.customFormService.updateCompletedCustomForm(
      operation,
      rest,
      userId,
      licenseeId
    )

    operation.addMany(completeFromRecord)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselIds ?? [],
      'customFormsCompleted',
      addCompleteFormRef.id,
      title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
