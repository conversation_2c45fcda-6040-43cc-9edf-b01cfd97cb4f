import { IUseCase } from '../UseCase'
import { ActionLogService } from '@src/domain/services/ActionLogService'
import { inject, injectable } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'
import { SFDoc } from '@src/shared-state/CompanyDocuments/companyDocuments'
import { CompanyDocumentService } from '@src/domain/services/CompanyDocumentService'
import { UpdateCompanyDocumentDto } from './UpdateCompanyDocumentUseCase'

export interface CreateCompanyDocumentDto extends Omit<UpdateCompanyDocumentDto, 'id'> {}

export interface ICreateCompanyDocumentUseCase extends IUseCase<CreateCompanyDocumentDto> {}

@injectable()
export class CreateCompanyDocumentUseCase implements ICreateCompanyDocumentUseCase {
  private readonly companyDocumentService: CompanyDocumentService
  private readonly actionLogService: ActionLogService

  constructor(
    @inject(CompanyDocumentService)
    companyDocumentService: CompanyDocumentService,
    @inject(ActionLogService)
    actionLogService: ActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {
    this.companyDocumentService = companyDocumentService
    this.actionLogService = actionLogService
  }

  async execute(dto: CreateCompanyDocumentDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'companyDocuments', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Update Vessel Certificate
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: `Add Company Document ${dto.title}`,
      maximumBatchSize: 20,
    })

    const { ref: updateVesselDocumentsRef, records: vesselDocumentsRecord } =
      this.companyDocumentService.createCompanyDocument(operation, dto, userId, licenseeId)

    operation.addMany(vesselDocumentsRecord)

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      [],
      'companyDocuments',
      updateVesselDocumentsRef.id,
      dto.title
    )

    operation.add(actionLogRecord)

    await operation.commit()
  }
}
