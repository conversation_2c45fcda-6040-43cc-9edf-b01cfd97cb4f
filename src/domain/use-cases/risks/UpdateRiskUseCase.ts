import { RiskDto } from '@src/domain/use-cases/risks/CreateRiskUseCase'
import { BaseUpdateDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { inject, injectable } from 'inversify'
import { RiskService } from '@src/domain/services/RiskService'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { FileService } from '@src/domain/services/FileService'
import { SeaFile } from '@src/lib/fileImports'

export interface UpdateRiskDto extends RiskDto, BaseUpdateDto {}

@injectable()
export class UpdateRiskUseCase implements IUseCase<UpdateRiskDto> {
  constructor(
    @inject(RiskService)
    private readonly riskService: IBaseDataService<undefined, UpdateRiskDto>,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {}

  public async execute(dto: UpdateRiskDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(dto.files, 'risks', 'files', userId, licenseeId)
    dto.files = filesList as SeaFile[]

    // Update Risk
    const operation = this.firestoreService.createOperation({
      operationType: 'update',
      operationDescription: 'Update Risk',
      maximumBatchSize: 20,
    })

    const { ref: updatedRiskRef, records: riskRecords } = this.riskService.updateDataItem(operation, dto, userId)

    const actionLogRecord = this.actionLogService.createUpdatedAction(
      operation,
      userId,
      licenseeId,
      dto.vesselIds,
      'risks',
      updatedRiskRef.id,
      dto.name
    )

    operation.addMany(riskRecords).add(actionLogRecord)

    await operation.commit()
  }
}
