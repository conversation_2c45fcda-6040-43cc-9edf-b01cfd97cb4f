import { BaseCreateDto, BaseDto, IUseCase } from '@src/domain/use-cases/UseCase'
import { Timestamp } from '@react-native-firebase/firestore'
import { inject, injectable } from 'inversify'
import { ActionLogService, IActionLogService } from '@src/domain/services/ActionLogService'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirestoreService } from '@src/domain/data/IFirestoreService'
import { RiskService } from '@src/domain/services/RiskService'
import { IBaseDataService } from '@src/domain/services/BaseService'
import { FileService } from '@src/domain/services/FileService'

import { SeaFile } from '@src/lib/fileImports'

export interface ControlDto {
  consequence?: number | string
  likelihood?: number | string
}

export interface RiskDto extends BaseDto {
  categoryId: string
  controls: string
  dateDue: string
  dateLastReviewed?: number | string
  // TODO: Cater for delete
  // deletedBy?: string;
  files: SeaFile[]
  interval: string
  name: string
  postControls: ControlDto
  preControls: ControlDto
  risks: string
  shouldReportToManagement: boolean
  state: 'active' | 'deleted'
  touched: Timestamp
  vesselIds: string[]
  // TODO: Cater for delete
  // whenDeleted?: number;
  // whenDue_old?: number; // TODO: Can possibly remove this
  // whenLastReviewed_old?: number; // TODO: Can possibly remove this
  whoResponsible?: string
}

export interface CreateRiskDto extends RiskDto, BaseCreateDto {}

export interface ICreateRiskUseCase extends IUseCase<CreateRiskDto> {}

@injectable()
export class CreateRiskUseCase implements ICreateRiskUseCase {
  constructor(
    @inject(RiskService)
    private readonly riskService: IBaseDataService<CreateRiskDto>,
    @inject(ActionLogService)
    private readonly actionLogService: IActionLogService,
    @inject(SERVICES.IFirestoreService)
    private readonly firestoreService: IFirestoreService,
    @inject(FileService)
    private readonly fileService: FileService
  ) {}

  public async execute(createRiskDto: CreateRiskDto, userId: string, licenseeId: string) {
    // Upload Files
    const filesList = await this.fileService.uploadFiles(createRiskDto?.files, 'risks', 'files', userId, licenseeId)
    createRiskDto.files = filesList as SeaFile[]

    // Create Risks
    const operation = this.firestoreService.createOperation({
      operationType: 'create',
      operationDescription: 'Create Risk',
      maximumBatchSize: 20,
    })

    const { ref: createdRiskRef, records: riskRecords } = this.riskService.createDataItem(
      operation,
      createRiskDto,
      userId
    )

    const actionLogRecord = this.actionLogService.createAddedAction(
      operation,
      userId,
      licenseeId,
      createRiskDto.vesselIds,
      'risks',
      createdRiskRef.id,
      createRiskDto.name
    )

    operation.addMany(riskRecords).add(actionLogRecord)

    await operation.commit()
  }
}
