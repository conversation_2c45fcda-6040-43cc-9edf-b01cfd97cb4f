import { <PERSON>ogger } from '@src/domain/util/logger/ILogger'
import * as Sentry from '@sentry/react-native'

export class SentryLogger implements ILogger {
  private readonly scope: string
  private readonly attributes: Record<string, string | undefined>

  constructor(scope?: string, attributes?: Record<string, string | undefined>) {
    this.scope = scope ?? ''
    this.attributes = attributes ?? {}
  }

  scoped(scope: string, attributes?: Record<string, string>): ILogger {
    return new SentryLogger(scope, attributes)
  }

  debug(message: string, meta?: Record<string, any>): void {
    Sentry.logger.debug(this.getScopedMessage(message), this.buildData(meta))
    Sentry.addBreadcrumb({
      level: 'debug',
      message: this.getScopedMessage(message),
      data: meta,
    })
  }

  info(message: string, meta?: Record<string, any>): void {
    Sentry.logger.info(this.getScopedMessage(message), this.buildData(meta))
    Sentry.addBreadcrumb({
      level: 'info',
      message: this.getScopedMessage(message),
      data: meta,
    })
  }

  warn(message: string, meta?: Record<string, any>): void {
    Sentry.logger.warn(this.getScopedMessage(message), this.buildData(meta))
    Sentry.captureMessage(this.getScopedMessage(message), {
      level: 'warning',
      extra: meta,
    })
  }

  error(message: string, meta?: Record<string, any>, error?: Error): void {
    Sentry.logger.error(this.getScopedMessage(message), this.buildData(meta))
    Sentry.captureException(error ?? new Error(this.getScopedMessage(message)), {
      extra: meta,
    })
  }

  private getScopedMessage(message: string) {
    return this.scope ? `[${this.scope}] ${message}` : message
  }

  private buildData(data?: Record<string, any>) {
    return { ...this.attributes, data: data ?? {} }
  }
}
