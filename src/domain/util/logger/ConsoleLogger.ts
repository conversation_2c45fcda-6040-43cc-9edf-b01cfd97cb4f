import { ILogger } from '@src/domain/util/logger/ILogger'

export class ConsoleLogger implements ILogger {
  private readonly scope: string

  constructor(scope?: string) {
    this.scope = scope ?? ''
  }

  public scoped(scope: string): ILogger {
    return new ConsoleLogger(scope)
  }

  public debug(message: string, meta?: Record<string, any>): void {
    console.debug(this.getScope(), message, meta ?? {})
  }
  public info(message: string, meta?: Record<string, any>): void {
    console.log(this.getScope(), message, meta ?? {})
  }
  public warn(message: string, meta?: Record<string, any>): void {
    console.warn(this.getScope(), message, meta ?? {})
  }
  public error(message: string, meta?: Record<string, any>, error?: Error): void {
    console.error(this.getScope(), message, meta ?? {}, error ?? undefined)
  }

  private getScope() {
    return this.scope ? `[${this.scope}] ` : ''
  }
}
