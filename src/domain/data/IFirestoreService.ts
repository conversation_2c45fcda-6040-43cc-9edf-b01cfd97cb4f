import { FirebaseFirestoreTypes } from '@react-native-firebase/firestore'
import { FirestoreOperation, FirestoreOperationOptions } from '@src/domain/data/FirestoreOperation'
import { WriteBatch as wWriteBatch, DocumentReference } from '@src/lib/firebase/services/firestore.service'
import { FirestoreFilter } from '@src/domain/data/FirestoreFilter'
import { FirestoreSorter } from '@src/domain/data/FirestoreSorter'
import { CollectionReference } from 'firebase/firestore'

type nWriteBatch = FirebaseFirestoreTypes.WriteBatch

export type WriteBatch = wWriteBatch | nWriteBatch

export type DocRef = DocumentReference | FirebaseFirestoreTypes.DocumentReference

export type CollectionRef = CollectionReference | FirebaseFirestoreTypes.CollectionReference

export interface IFirestoreService {
  createOperation(options: FirestoreOperationOptions): FirestoreOperation

  createWriteBatch(): Write<PERSON>atch

  createCollectionRef(collectionName: string): DocRef

  createDocumentRef(collectionName: string, docId: string): DocRef

  subscribeToCollection<T>(
    collectionName: string,
    filters: FirestoreFilter[],
    sorters: FirestoreSorter[],
    onNext: (docs: T[]) => void,
    onError: (error: unknown) => void
  ): () => void

  subscribeToDocument<T>(
    collectionName: string,
    docId: string,
    onNext: (doc: T) => void,
    onError: (error: unknown) => void
  ): () => void

  setDocument<T>(collectionName: string, docId: string, data: Partial<T>, options?: unknown): Promise<void>

  getDocuments<T>(collectionName: string, filters: FirestoreFilter[], sorters: FirestoreSorter[]): Promise<T[]>

  serverTimestamp(): any
  deleteField(): any
  arrayUnion(...elements: unknown[]): any
  arrayRemove(...elements: unknown[]): any
  increment(value: number): any
}

// TODO
// getDoc
// getDocs
// addDoc
// deleteDoc
// serverTimestamp
// disableNetwork
// enableNetwork
