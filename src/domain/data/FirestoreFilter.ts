export type FirestoreFilter = {
  field: string
  operator: string
  value: any
}

export const whereEqualTo = (field: string, value: unknown): FirestoreFilter => {
  return {
    field,
    value,
    operator: '==',
  }
}

export const whereIn = (field: string, value: unknown[]): FirestoreFilter => {
  return {
    field,
    value,
    operator: 'in',
  }
}

export const whereGreaterOrEqualTo = (field: string, value: unknown): FirestoreFilter => {
  return {
    field,
    value,
    operator: '>=',
  }
}

export const whereLicenseeIs = (licenseeId: string): FirestoreFilter => {
  return {
    field: 'licenseeId',
    value: licenseeId,
    operator: '==',
  }
}

export const whereVesselIs = (vesselId: string): FirestoreFilter => {
  return {
    field: 'vesselId',
    value: vesselId,
    operator: '==',
  }
}

export const whereStateIs = (state: string): FirestoreFilter => {
  return {
    field: 'state',
    value: state,
    operator: '==',
  }
}
