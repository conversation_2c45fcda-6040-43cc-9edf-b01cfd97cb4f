import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { IFirebase, NativeFirestore } from '@src/domain/IFirebase'
import { FirestoreOperation, FirestoreOperationOptions } from '@src/domain/data/FirestoreOperation'
import { IFirestoreService, WriteBatch } from '@src/domain/data/IFirestoreService'
import {
  arrayRemove as arrayRemoveNative,
  arrayUnion as arrayUnionNative,
  collection,
  deleteField as deleteFieldNative,
  doc,
  FirebaseFirestoreTypes,
  increment,
  orderBy,
  query,
  serverTimestamp as serverTimestampNative,
  where,
  WhereFilterOp,
  writeBatch,
} from '@react-native-firebase/firestore'
import { FirestoreFilter } from '@src/domain/data/FirestoreFilter'
import { FirestoreSorter } from '@src/domain/data/FirestoreSorter'
import { ILogger } from '@src/domain/util/logger/ILogger'

export class NativeFirestoreService implements IFirestoreService {
  private firestore: NativeFirestore
  private logger: ILogger

  constructor(@inject(SERVICES.IFirebase) firebase: IFirebase, @inject(SERVICES.ILogger) logger: ILogger) {
    this.firestore = firebase.getFirestore() as NativeFirestore
    this.logger = logger
  }

  subscribeToCollection<T>(
    collectionName: string,
    filters: FirestoreFilter[],
    sorters: FirestoreSorter[],
    onNext: (docs: T[]) => void,
    onError: (error: unknown) => void
  ): () => void {
    const queryRef = this.getQuery(collectionName, filters, sorters)

    return queryRef.onSnapshot(snapshot => {
      onNext(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as T))
    }, onError)
  }

  subscribeToDocument<T>(
    collectionName: string,
    docId: string,
    onNext: (doc: T) => void,
    onError: (error: unknown) => void
  ): () => void {
    return doc(this.firestore, collectionName, docId).onSnapshot(snapshot => {
      onNext({ id: snapshot.id, ...snapshot.data() } as T)
    }, onError)
  }

  async getDocuments<T>(collectionName: string, filters: FirestoreFilter[], sorters: FirestoreSorter[]): Promise<T[]> {
    const queryRef = this.getQuery(collectionName, filters, sorters)
    const result = await queryRef.get()

    return result.docs.map(doc => ({ id: doc.id, ...doc.data() }) as T)
  }

  setDocument<T>(collectionName: string, docId: string, data: Partial<T>, options?: unknown): Promise<void> {
    return doc(this.firestore, collectionName, docId).set(data, options as FirebaseFirestoreTypes.SetOptions)
  }

  private getQuery(collectionName: string, filters: FirestoreFilter[], sorters: FirestoreSorter[]) {
    let queryRef: FirebaseFirestoreTypes.Query<FirebaseFirestoreTypes.DocumentData> = collection(
      this.firestore,
      collectionName
    )

    // Apply filters
    filters.forEach(filter => {
      queryRef = query(queryRef, where(filter.field, filter.operator as WhereFilterOp, filter.value))
    })

    // Apply sorting
    sorters.forEach(sorter => {
      queryRef = query(queryRef, orderBy(sorter.field, sorter.direction))
    })

    return queryRef
  }

  createOperation(options: FirestoreOperationOptions): FirestoreOperation {
    return FirestoreOperation.create(this, options, this.logger)
  }

  createWriteBatch(): WriteBatch {
    return writeBatch(this.firestore)
  }

  createCollectionRef(collectionName: string): FirebaseFirestoreTypes.DocumentReference {
    return doc(collection(this.firestore, collectionName))
  }

  createDocumentRef(collectionName: string, docId: string): FirebaseFirestoreTypes.DocumentReference {
    return doc(this.firestore, collectionName, docId)
  }

  serverTimestamp() {
    return serverTimestampNative()
  }

  deleteField() {
    return deleteFieldNative()
  }

  arrayUnion(...elements: unknown[]) {
    return arrayUnionNative(...elements)
  }

  arrayRemove(...elements: unknown[]) {
    return arrayRemoveNative(...elements)
  }

  increment(value: number) {
    return increment(value)
  }
}
