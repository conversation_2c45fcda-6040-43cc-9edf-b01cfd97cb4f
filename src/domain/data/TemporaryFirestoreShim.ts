import { getFirestoreService } from '@src/domain/di/ServiceContainer'
import { FirestoreFilter } from './FirestoreFilter'
import { FirestoreSorter } from './FirestoreSorter'

/**
 * IMPORTANT: This is a temporary adaptor to allow us to use the new IFirestoreService interface
 * without having to refactor every single call site within src/shared-state.  Once React Native shared-state
 * is the source of truth (and we have pulled in all the latest changes from React Ionic's shared-state), we
 * can remove this shim and refactor to use the service directly.
 */

/** Empty export so call sites can use firestore from here */
export const firestore = null

export type DocumentData = Record<string, any>

export interface DocumentSnapshot<T extends DocumentData = DocumentData> {
  id: string
  exists: boolean
  data(): T
}

export interface QuerySnapshot<T extends DocumentData = DocumentData> {
  docs: {
    id: string
    data(): T
  }[]
}

/** Shim for where that is set up to be compatible with IFirestoreService */
export const where = (field: string, op: string, value: any): FirestoreFilter => ({
  field,
  operator: op,
  value,
})

/** Shim for orderBy that is set up to be compatible with IFirestoreService */
export const orderBy = (field: string, direction: 'asc' | 'desc' = 'asc'): FirestoreSorter => ({
  field,
  direction,
})

/** Shim for collection that is set up to be compatible with IFirestoreService */
export const collection = (_: any, collectionName: string): string => {
  return collectionName
}

export interface InternalDocRef {
  __isDocumentRef: true
  collectionName: string
  docId: string
}

export const doc = (_: any, collectionName: string, docId: string): InternalDocRef => {
  return {
    __isDocumentRef: true,
    collectionName,
    docId,
  }
}

// Internal structure to simulate Firestore's Query object
interface InternalQuery {
  __isQueryRef: true
  collectionName: string
  filters: FirestoreFilter[]
  sorters: FirestoreSorter[]
}

function isFilter(clause: FirestoreFilter | FirestoreSorter): clause is FirestoreFilter {
  return typeof clause === 'object' && 'operator' in clause
}

/** Shim for query that is set up to be compatible with IFirestoreService */
export const query = (ref: string, ...clauses: (FirestoreFilter | FirestoreSorter)[]): InternalQuery => {
  const filters: FirestoreFilter[] = []
  const sorters: FirestoreSorter[] = []

  for (const clause of clauses) {
    if (isFilter(clause)) {
      filters.push(clause)
    } else {
      sorters.push(clause)
    }
  }

  return {
    __isQueryRef: true,
    collectionName: ref,
    filters,
    sorters,
  }
}

type InternalRef = InternalQuery | InternalDocRef

export function onSnapshot<T extends DocumentData>(
  ref: InternalDocRef,
  onNext: (snapshot: DocumentSnapshot<T>) => void,
  onError: (error: any) => void
): () => void

export function onSnapshot<T extends DocumentData>(
  ref: InternalQuery,
  onNext: (snapshot: QuerySnapshot<T>) => void,
  onError: (error: any) => void
): () => void

/** Shim for onSnapshot that is set up to be compatible with IFirestoreService */
export function onSnapshot<T extends DocumentData>(
  ref: InternalRef,
  onNext: (snapshot: any) => void,
  onError: (error: any) => void
): () => void {
  if ((ref as InternalQuery).__isQueryRef) {
    // Collection query
    const queryRef = ref as InternalQuery
    return getFirestoreService().subscribeToCollection<T>(
      queryRef.collectionName,
      queryRef.filters,
      queryRef.sorters,
      (docs: T[]) => {
        const snap: QuerySnapshot<T> = {
          docs: docs.map((doc: any) => ({
            id: doc.id,
            data: () => {
              const { id, ...rest } = doc
              return rest as T
            },
          })),
        }
        onNext(snap)
      },
      onError
    )
  } else if ((ref as InternalDocRef).__isDocumentRef) {
    // Single document
    const docRef = ref as InternalDocRef

    return getFirestoreService().subscribeToDocument<T>(
      docRef.collectionName,
      docRef.docId,
      (doc: any) => {
        const exists = !!doc && typeof doc === 'object'

        const snap: DocumentSnapshot<T> = {
          id: doc?.id ?? docRef.docId,
          exists,
          data: () => ({ ...doc }),
        }

        onNext(snap)
      },
      onError
    )
  }

  throw new Error('Unknown reference type passed to onSnapshot')
}

export async function getDocs<T extends DocumentData>(ref: InternalQuery): Promise<QuerySnapshot<T>> {
  // export async function getDocs<T>(ref: InternalQuery): Promise<{
  //   docs: { id: string; data: () => T }[];
  // }>
  if (!ref.__isQueryRef) {
    throw new Error('getDocs currently only supports query() objects.')
  }

  const { collectionName, filters, sorters } = ref

  const results = await getFirestoreService().getDocuments<T>(collectionName, filters, sorters)

  return {
    docs: results.map((doc: any) => ({
      id: doc.id,
      data: () => {
        const { id, ...rest } = doc
        return rest as T
      },
    })),
  }
}
