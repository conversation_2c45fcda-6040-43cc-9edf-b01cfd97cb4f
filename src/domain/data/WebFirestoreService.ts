import { IFirebase, WebFirestore } from '@src/domain/IFirebase'
import { inject } from 'inversify'
import { SERVICES } from '@src/domain/di/ServiceRegistry'
import { FirestoreOperation, FirestoreOperationOptions } from '@src/domain/data/FirestoreOperation'
import {
  collection,
  doc,
  setDoc,
  onSnapshot,
  orderBy,
  query,
  where,
  WhereFilterOp,
  writeBatch,
  SetOptions,
  getDocs,
  serverTimestamp as serverTimestampWeb,
  deleteField as deleteFieldWeb,
  arrayUnion as arrayUnionWeb,
  arrayRemove as arrayRemoveWeb,
  DocumentReference,
  increment,
} from 'firebase/firestore'
import { DocRef, IFirestoreService, WriteBatch } from '@src/domain/data/IFirestoreService'
import { FirestoreFilter } from '@src/domain/data/FirestoreFilter'
import { FirestoreSorter } from './FirestoreSorter'
import { ILogger } from '@src/domain/util/logger/ILogger'
import { string } from 'yup'

export class WebFirestoreService implements IFirestoreService {
  private readonly firestore: WebFirestore
  private readonly logger: ILogger

  constructor(@inject(SERVICES.IFirebase) firebase: IFirebase, @inject(SERVICES.ILogger) logger: ILogger) {
    this.firestore = firebase.getFirestore() as WebFirestore
    this.logger = logger
  }

  public subscribeToCollection<T>(
    collectionName: string,
    filters: FirestoreFilter[],
    sorters: FirestoreSorter[],
    onNext: (data: T[]) => void,
    onError: (error: unknown) => void
  ): () => void {
    const query = this.getQuery(collectionName, filters, sorters)

    return onSnapshot(
      query,
      snapshot => {
        onNext(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as T))
      },
      onError
    )
  }

  public subscribeToDocument<T>(
    collectionName: string,
    docId: string,
    onNext: (doc: T) => void,
    onError: (error: unknown) => void
  ): () => void {
    return onSnapshot(
      doc(this.firestore, collectionName, docId),
      snapshot => {
        onNext({ id: snapshot.id, ...snapshot.data() } as T)
      },
      onError
    )
  }

  public async getDocuments<T>(collectionName: string, filters: FirestoreFilter[], sorters: FirestoreSorter[]) {
    const query = this.getQuery(collectionName, filters, sorters)
    const result = await getDocs(query)

    return result.docs.map(doc => ({ id: doc.id, ...doc.data() }) as T)
  }

  public setDocument<T>(collectionName: string, docId: string, data: Partial<T>, options?: unknown) {
    return setDoc(doc(this.firestore, collectionName, docId), data, options as SetOptions)
  }

  private getQuery(collectionName: string, filters: FirestoreFilter[], sorters: FirestoreSorter[]) {
    const constraints = [
      ...filters.map(filter => where(filter.field, filter.operator as WhereFilterOp, filter.value)),
      ...sorters.map(sorter => orderBy(sorter.field, sorter.direction)),
    ]

    return query(collection(this.firestore, collectionName), ...constraints)
  }

  createOperation(options: FirestoreOperationOptions): FirestoreOperation {
    return FirestoreOperation.create(this, options, this.logger)
  }

  createWriteBatch(): WriteBatch {
    return writeBatch(this.firestore)
  }

  createCollectionRef(collectionName: string): DocumentReference {
    return doc(collection(this.firestore, collectionName))
  }

  createDocumentRef(collectionName: string, docId: string): DocumentReference {
    return doc(collection(this.firestore, collectionName), docId)
  }

  serverTimestamp(): any {
    return serverTimestampWeb()
  }

  deleteField(): any {
    return deleteFieldWeb()
  }

  arrayUnion(...elements: unknown[]) {
    return arrayUnionWeb(...elements)
  }

  arrayRemove(...elements: unknown[]) {
    return arrayRemoveWeb(...elements)
  }

  increment(value: number) {
    return increment(value)
  }
}
