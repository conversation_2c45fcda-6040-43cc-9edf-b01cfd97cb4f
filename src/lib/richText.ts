import { getBytes, ref as storageRef } from '@src/lib/firebase/services/storage.service'
import { storage } from './firebase'
import { toByteArray } from 'base64-js'
import { debugApp } from '../shared-state/Core/debugging'
import { SFDoc } from '../shared-state/CompanyDocuments/companyDocuments'
import { cachedFiles, getCachedFileSrc, saveFileToLocalStorage } from '../shared-state/FileSyncSystem/cachedFiles'
// import { defaultRichTextJson } from "../components/SeaEditDocumentation/SeaEditDocumentation";
import lzString from 'lz-string'
import { isNative, isWeb } from '@src/lib/device'
import { Platform } from 'react-native'
import { SeaFile } from '@src/lib/fileImports'

export interface RichTextState {
  versions: any | undefined
  loadedVersion: number | undefined
  loadedJson: any | undefined
  loading: boolean
  downloading: boolean
  message: string | undefined
}

export const initialRichTextState = {
  versions: undefined,
  loadedVersion: undefined,
  loadedJson: undefined,
  loading: false,
  downloading: false,
  message: undefined,
} as RichTextState

const defaultContent = `{
    "root": {
        "direction": "ltr",
        "format": "",
        "indent": 0,
        "type": "root",
        "version": 1,
        "children": [{
            "indent": 0,
            "type": "page",
            "version": 1,
            "children": [{
            "direction": "ltr",
                "format": "",
                "indent": 0,
                "type": "sea-section",
                "version": 1,
                "tag": "h1",
                "children": [{
                    "detail": 0,
                    "format": 0,
                    "mode": "normal",
                    "style": "",
                    "text": "Section 1",
                    "type": "text",
                    "version": 1
                }]
            }, {
                "children": [],
                "direction": "ltr",
                "format": "",
                "indent": 0,
                "type": "paragraph",
                "version": 1
            }]
        }]
    }
}`

export const defaultRichTextJson = JSON.parse(defaultContent)

const regulateJson = (json: any) => {
  if (json?.root?.children?.length > 0) {
    const children = json.root.children
    if (children[0].type === 'page') {
      // Looks like this json is already using pages
      return json // No need to modify
    }
    // Pagify node structure
    const newChildren = []
    const createPage = () => {
      return {
        children: [] as any[],
        direction: null,
        format: '',
        indent: 0,
        type: 'page',
        verison: 1,
      }
    }
    let currentPage = createPage()
    currentPage.children.push(children[0])
    newChildren.push(currentPage)
    for (let i = 1; i < children.length; i++) {
      if (children[i].tag && children[i].tag === 'h1') {
        // An H1 tag indicates we should start a new page
        currentPage = createPage()
        newChildren.push(currentPage)
      }
      currentPage.children.push(children[i])
    }
    json.root.children = newChildren
    return json
  }
  console.error('Rich Text json is broken!', json)
  return defaultRichTextJson
}

// export const loadSfdoc = (
//   sfdoc: SFDoc | undefined,
//   setRichTextState: React.Dispatch<React.SetStateAction<RichTextState>>,
//   getDefaultContent?: () => string,
//   hasPrerequisites = true,
// ) => {
//   let isActive = true;
//   debugApp("sfdoc", `Load SFDoc...`);
//
//   if (hasPrerequisites && sfdoc && Object.keys(sfdoc).length > 0) {
//     const versions = Object.keys(sfdoc).map((key) => {
//       return parseInt(key);
//     });
//     versions.sort((a, b) => {
//       return b - a;
//     });
//     const versionToView = versions[0];
//
//     const fileState = parseInt(sfdoc[versionToView][0]);
//     const fileId = sfdoc[versionToView].substring(1, 21);
//     let isOptimised = fileState === 2;
//
//     const onSuccess = (content: string | undefined) => {
//       if (!isActive) return;
//       if (content === undefined) return;
//
//       let json;
//       if (isOptimised) {
//         if (content.startsWith('{"root":')) {
//           // Not compressed using lz-string
//           json = JSON.parse(content);
//         } else {
//           const st = Date.now();
//           const decompressed = lzString.decompressFromUTF16(content);
//           console.log(
//             `Decompressed sfdoc (${((decompressed.length - content.length) * 100) / decompressed.length}% compression) in ${Date.now() - st}ms`,
//           );
//           if (!isActive) return;
//           json = JSON.parse(decompressed);
//         }
//       } else {
//         json = JSON.parse(content);
//       }
//
//       json = regulateJson(json);
//
//       if (!isActive) return;
//       // Successfully loaded document
//       console.log(
//         `loadSfdoc Successfully loaded document ${sfdoc[versionToView]}`,
//       );
//       debugApp(
//         "sfdoc",
//         `Loaded SFDoc versionToView=${versionToView} json.length=${JSON.stringify(json).length}`,
//       );
//       setRichTextState({
//         versions: versions,
//         loadedVersion: versionToView,
//         loadedJson: json,
//         loading: false,
//         downloading: false,
//         message: undefined,
//       });
//     };
//
//     const onFailed = (message: string, error?: any) => {
//       console.log("Failed to get document", error);
//       debugApp(
//         "sfdoc",
//         `Failed to load SFDoc versionToView=${versionToView} error`,
//         error,
//       );
//       if (!isActive) return;
//       setRichTextState({
//         versions: undefined,
//         loadedVersion: undefined,
//         loadedJson: undefined,
//         loading: false,
//         downloading: false,
//         message: message,
//       });
//     };
//
//     const downloadDocument = (): Promise<string | undefined> => {
//       // Obtain document via download
//       // Returns document JSON on success
//       // Returns undefined if no longer active
//
//       //  Update state
//       setRichTextState((current) => {
//         if (current.loadedVersion === versionToView) {
//           console.log(
//             "loadSfdoc downloadDocument already loaded version",
//             versionToView,
//           );
//           debugApp(
//             "sfdoc",
//             `loadSfdoc downloadDocument already loaded versionToView=${versionToView}`,
//           );
//           return current; // (Already loaded)
//         }
//         return {
//           versions: undefined,
//           loadedVersion: undefined,
//           loadedJson: undefined,
//           loading: true,
//           downloading: true,
//           message: undefined,
//         };
//       });
//
//       const fileRef = storageRef(
//         storage,
//         `files/${fileId}${fileState === 2 ? "_opt" : ""}.sfdoc`,
//       );
//
//       return getBytes(fileRef).then((buffer: ArrayBuffer) => {
//         console.log("loadSfdoc got document via download.");
//         if (!isActive) return Promise.resolve();
//
//         const decoder = new TextDecoder();
//         const content = decoder.decode(buffer);
//
//         // if (!isPlatform('hybrid')) {
//         if (isWeb) {
//           // Cache sfdoc for next time we view it (desktop only)
//           return saveFileToLocalStorage(
//             fileId,
//             "sfdoc",
//             "R",
//             new Blob([buffer], { type: "application/seaflux" }),
//           ).then(() => {
//             console.log(`[FileSync] Cached sfdoc on desktop ${fileId}`);
//             return Promise.resolve(content as any);
//           });
//         }
//
//         return Promise.resolve(content as any);
//       });
//     };
//
//     const getCachedDocument = (): Promise<string | undefined> => {
//       // Obtain document via download
//       // Returns document JSON on success
//       // Returns undefined if no longer active
//
//       //  Update state
//       setRichTextState((current) => {
//         if (current.loadedVersion === versionToView) {
//           console.log(
//             "loadSfdoc getCachedDocument already loaded version",
//             versionToView,
//           );
//           debugApp(
//             "sfdoc",
//             `getCachedDocument already loaded version versionToView=${versionToView}`,
//           );
//           return current; // (Already loaded)
//         }
//         return {
//           versions: undefined,
//           loadedVersion: undefined,
//           loadedJson: undefined,
//           loading: true,
//           downloading: false,
//           message: undefined,
//         };
//       });
//
//       return getCachedFileSrc(sfdoc[versionToView], "R").then(
//         (value: string) => {
//           // Got back cached file (base64 string)
//           console.log("sfdoc got back cached file");
//           const data = value.substring(value.indexOf(",") + 2);
//           const utf8Bytes = toByteArray(data);
//
//           if (!isActive) return;
//           const decoder = new TextDecoder();
//           return Promise.resolve(decoder.decode(utf8Bytes));
//         },
//       );
//     };
//
//     if (fileState === 0) {
//       // The only chance we have this document is if it is cached
//       getCachedDocument()
//         .then((content: string | undefined) => {
//           onSuccess(content);
//         })
//         .catch((error) => {
//           console.log("Failed to getCachedDocument (state=0)", error);
//           onFailed(
//             `The latest version hasn't been uploaded by the device it was created on yet. To resolve this, please keep Sea Flux open on the originating device while connected to the internet.`,
//           );
//         });
//     } else if (
//       fileState === 2 &&
//       cachedFiles[fileId] &&
//       cachedFiles[fileId][5].R === undefined // We don't have optimised version cached
//     ) {
//       // Cache is old, therefore attempt download with getCachedDocument() as a fallback
//       console.log("Our cache is old!");
//       downloadDocument()
//         .catch((error) => {
//           console.log("Failed to download new state=2 document", error);
//           isOptimised = false;
//           return getCachedDocument();
//         })
//         .then((content: string | undefined) => {
//           onSuccess(content);
//         })
//         .catch((error) => {
//           onFailed(
//             `Sea Flux is currently unable to access the latest version.`,
//             error,
//           );
//         });
//     } else {
//       // Attempt to get cached document with downloadDocument() as a fallback
//       getCachedDocument()
//         .catch((error) => {
//           console.log("Failed to getCachedDocument", error);
//           return downloadDocument();
//         })
//         .then((content: string | undefined) => {
//           onSuccess(content);
//         })
//         .catch((error) => {
//           onFailed(
//             `Sea Flux is currently unable to access the latest version.`,
//             error,
//           );
//         });
//     }
//   } else {
//     // No document to load
//     debugApp("sfdoc", `No document to load.`);
//
//     setRichTextState({
//       versions: undefined,
//       loadedVersion: undefined,
//       loadedJson: getDefaultContent
//         ? regulateJson(JSON.parse(getDefaultContent()))
//         : undefined, // NOTE: regulateJson is needed because CompanyDocument defaultContent doesn't use pages yet (TODO! getDefaultContent)
//       loading: false,
//       downloading: false,
//       message: undefined,
//     });
//   }
//
//   return () => {
//     isActive = false;
//   };
// };

export const loadSfdocNew = async (
  sfdoc: SFDoc | undefined,
  getDefaultContent?: () => string,
  hasPrerequisites = true
) => {
  const isActive = true
  debugApp('sfdoc', `Load SFDoc...`)

  if (hasPrerequisites && sfdoc && Object.keys(sfdoc).length > 0) {
    const versions = Object.keys(sfdoc).map(key => {
      return parseInt(key)
    })
    versions.sort((a, b) => {
      return b - a
    })
    const versionToView = versions[0]

    const fileState = parseInt(sfdoc[versionToView][0])
    const fileId = sfdoc[versionToView].substring(1, 21)
    let isOptimised = fileState === 2

    const saveFileLocally = (_content: string) => {
      if (isWeb) {
        // Cache sfdoc for next time we view it (web only)
        return saveFileToLocalStorage(fileId, 'sfdoc', 'R', new Blob([_content], { type: 'application/seaflux' })).then(
          () => {
            console.log(`[FileSync] [Web] Cached sfdoc ${fileId}`)
          }
        )
      } else if (isNative) {
        return saveFileToLocalStorage(fileId, 'sfdoc', 'R', JSON.stringify(_content))
          .then(() => {
            console.log(`[FileSync] [Native] Cached sfdoc ${fileId}`)
          })
          .catch(error => {
            console.error('Failed to cache sfdoc', error)
          })
      }
    }

    const onSuccess = (content: string | undefined, toSaveLocally: boolean) => {
      if (!isActive) return
      if (content === undefined) return

      let json
      if (isOptimised) {
        if (content.startsWith('{"root":')) {
          // Not compressed using lz-string
          json = JSON.parse(content)
        } else {
          const st = Date.now()
          const decompressed = lzString.decompressFromUTF16(content)
          console.log(
            `Decompressed sfdoc (${((decompressed.length - content.length) * 100) / decompressed.length}% compression) in ${Date.now() - st}ms`
          )
          if (!isActive) return
          json = JSON.parse(decompressed)
        }
      } else {
        json = JSON.parse(content)
      }

      json = regulateJson(json)

      /** Save the file locally */
      if (toSaveLocally) {
        saveFileLocally(json)
      }

      if (!isActive) return
      // Successfully loaded document
      console.log(`loadSfdoc Successfully loaded document ${sfdoc[versionToView]}`)
      debugApp('sfdoc', `Loaded SFDoc versionToView=${versionToView} json.length=${JSON.stringify(json).length}`)
      return Promise.resolve({
        versions: versions,
        loadedVersion: versionToView,
        loadedJson: json,
        loading: false,
        downloading: false,
        message: undefined,
      })
    }

    const onFailed = (message: string, error?: any) => {
      console.log('Failed to get document', error)
      debugApp('sfdoc', `Failed to load SFDoc versionToView=${versionToView} error`, error)
      if (!isActive) return

      return Promise.resolve({
        versions: undefined,
        loadedVersion: undefined,
        loadedJson: undefined,
        loading: false,
        downloading: false,
        message: message,
      })
    }

    const downloadDocument = async () => {
      // Obtain document via download
      // Returns document JSON on success
      // Returns undefined if no longer active

      /** TODO: Check the version of the document */

      const fileRef = storageRef(storage, `files/${fileId}${fileState === 2 ? '_opt' : ''}.sfdoc`)

      return await getBytes(fileRef).then((buffer: ArrayBuffer) => {
        if (!isActive) return Promise.resolve()
        const decoder = new TextDecoder()
        const content = decoder.decode(buffer)
        return Promise.resolve(content as any)
      })
    }

    const getCachedDocument = async () => {
      // Obtain document via download
      // Returns document JSON on success
      // Returns undefined if no longer active

      //  Update state
      /** TODO: Check the version of the document */
      return await getCachedFileSrc(sfdoc[versionToView], 'R').then((value: string) => {
        if (Platform.OS === 'web') {
          // Got back cached file (base64 string)
          const data = value.substring(value.indexOf(',') + 2)
          const utf8Bytes = toByteArray(data)

          if (!isActive) return
          const decoder = new TextDecoder()
          return Promise.resolve(decoder.decode(utf8Bytes))
        } else {
          if (!isActive) return
          return Promise.resolve(value)
        }
      })
    }

    if (fileState === 0) {
      // The only chance we have this document is if it is cached
      try {
        const content = await getCachedDocument()
        return onSuccess(content, false)
      } catch (error) {
        console.log('Failed to getCachedDocument (state=0)', error)
        return onFailed(
          `The latest version hasn't been uploaded by the device it was created on yet. To resolve this, please keep Sea Flux open on the originating device while connected to the internet.`
        )
      }
    } else if (
      fileState === 2 &&
      cachedFiles[fileId] &&
      cachedFiles[fileId][5].R === undefined // We don't have optimised version cached
    ) {
      // Cache is old, therefore attempt download with getCachedDocument() as a fallback
      console.log('Our cache is old!')
      try {
        const content = await downloadDocument()
        return onSuccess(content, true)
      } catch (error) {
        console.log('Failed to download new state=2 document', error)
        isOptimised = false

        // 2nd Try
        try {
          const content = await getCachedDocument()
          return onSuccess(content, false)
        } catch (error) {
          return onFailed(`Sea Flux is currently unable to access the latest version. - download doc`, error)
        }
      }
    } else {
      // Attempt to get cached document with downloadDocument() as a fallback
      try {
        const content = await getCachedDocument()
        return onSuccess(content, false)
      } catch (error) {
        console.log('Failed to getCachedDocument', error)
        try {
          const content = await downloadDocument()
          return onSuccess(content, true)
        } catch (error) {
          return onFailed(`Sea Flux is currently unable to access the latest version. - cached doc`, error)
        }
      }
    }
  }
  // No document to load
  debugApp('sfdoc', `No document to load.`)

  return Promise.resolve({
    versions: undefined,
    loadedVersion: undefined,
    loadedJson: getDefaultContent ? regulateJson(JSON.parse(getDefaultContent())) : undefined, // NOTE: regulateJson is needed because CompanyDocument defaultContent doesn't use pages yet (TODO! getDefaultContent)
    loading: false,
    downloading: false,
    message: undefined,
  })
}
