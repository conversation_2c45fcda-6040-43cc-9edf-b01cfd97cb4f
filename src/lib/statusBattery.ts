import { ReactElement } from 'react'
import { getToday, makeDateTime } from './datesAndTime'
import { SeaStatusType } from '@src/types/Common'
import { BatteryStatus, LinkType } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBattery'
import { SeaStatusBatteryPopover } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBatteryPopover'

export type StatusBatteryCount = {
  [key in keyof typeof SeaStatusType]: number
}

export const emptyStatusBattery: StatusBatteryCount = {
  Critical: 0,
  Error: 0,
  Attention: 0,
  Warning: 0,
  Minor: 0,
  Ok: 0,
}

export const calculateStatusBatteryCount = (date: string | undefined, warnDays: number, count: StatusBatteryCount) => {
  if (!date) {
    return count
  }
  const now = makeDateTime()
  const dueDate = makeDateTime(date)
  const dueDays = dueDate.diff(now, ['days']).days
  const today = getToday()

  if (date < today) {
    return {
      ...count,
      Error: count.Error + 1,
    }
  } else if (dueDays < 0) {
    return {
      ...count,
      Critical: count.Critical + 1,
    }
  } else if (dueDays < warnDays || dueDays === 0) {
    return {
      ...count,
      Warning: count.Warning + 1,
    }
  } else if (dueDays < warnDays) {
    //TODO: check if this is correct logic
    return {
      ...count,
      Minor: count.Minor + 1,
    }
  } else {
    return {
      ...count,
      Ok: count.Ok + 1,
    }
  }
}

type StatusHandlers = {
  [key in keyof typeof SeaStatusType]: {
    onPress?: () => void
    popover?: ReactElement<typeof SeaStatusBatteryPopover> // Optional popover component
  }
}

export const buildStatuses = (
  countObj: Record<keyof typeof SeaStatusType, number>,
  handlers: StatusHandlers
): BatteryStatus[] => {
  return Object.entries(countObj).flatMap(([key, count]) => {
    let status: SeaStatusType

    // Map the key to the corresponding SeaStatusType
    switch (key) {
      case 'Critical':
        status = SeaStatusType.Critical
        break
      case 'Error':
        status = SeaStatusType.Error
        break
      case 'Attention':
        status = SeaStatusType.Attention
        break
      case 'Warning':
        status = SeaStatusType.Warning
        break
      case 'Minor':
        status = SeaStatusType.Minor
        break
      default:
        status = SeaStatusType.Ok
        break
    }

    // Get the handler and popover for the current status
    const { onPress, popover } = handlers[key as keyof typeof SeaStatusType]

    // Return a BatteryStatus object if count > 0
    return count > 0
      ? [
          {
            status,
            count,
            ...(popover
              ? {
                  type: LinkType.Popover,
                  onShowPopover: onPress ?? (() => console.error('Function not found')), // Default to no-op if onPress is not provided
                  popover,
                }
              : {
                  type: LinkType.NotPopover,
                  onPress,
                }),
          },
        ]
      : []
  })
}

export const isStatusBatteryEmpty = (statuses: Partial<typeof emptyStatusBattery>): boolean => {
  return Object.values(statuses).every(count => count === 0)
}
