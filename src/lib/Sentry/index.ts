import * as Sentry from '@sentry/react-native'
import { Platform } from 'react-native'

/** Initializes Sentry SDK based on current environment */
export const initializeSentryMonitoring = () => {
  const env = process.env.EXPO_PUBLIC_SENTRY_ENVIRONMENT ?? 'development'

  console.debug(`Initializing Sentry for environment: ${env}`)

  Sentry.init({
    dsn: 'https://<EMAIL>/4509245899669584',
    environment: env,

    // Configure Session Replay
    replaysSessionSampleRate: env === 'production' ? 0.1 : 1,
    replaysOnErrorSampleRate: 1,
    integrations: Platform.select({
      web: [
        Sentry.browserReplayIntegration({
          maskAllText: false,
          maskAllInputs: false,
        }),
      ],
      native: [
        Sentry.mobileReplayIntegration({
          maskAllText: false,
          maskAllImages: false,
          maskAllVectors: false,
        }),
      ],
    }),
    _experiments: {
      enableLogs: true,
    },
  })
}
