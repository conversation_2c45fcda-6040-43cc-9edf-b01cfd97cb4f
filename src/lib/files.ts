import * as FileSystem from 'expo-file-system'
import { getDownloadURL, ref as storageRef } from '@src/lib/firebase/services/storage.service'
import { storage } from '@src/lib/firebase'
import { toInt } from '@src/lib/util'
import { Platform } from 'react-native'
import { Directory, Filesystem } from '@src/shared-state/FileSyncSystem/fileSystem'
import { isWeb } from '@src/lib/device'
import NativeBlob from 'react-native-blob-util'
import { mimeTypes, SeaFile } from '@src/lib/fileImports'

export const convertBase64toBlob = (base64: string, contentType = '', sliceSize = 512) => {
  if (isWeb) {
    // Source: https://stackoverflow.com/questions/16245767/creating-a-blob-from-a-base64-string-in-javascript
    const byteCharacters = atob(base64)
    const byteArrays = []
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize)
      const byteNumbers = new Array(slice.length)
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      byteArrays.push(byteArray)
    }
    return new Blob(byteArrays, { type: contentType })
    // return new Blob(byteArrays, { type: "" });
  } else {
    return NativeBlob.base64.decode(base64)
  }
}

export const convertBlobToBase64 = (blob: any) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onerror = reject
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.readAsDataURL(blob)
  })

export const getExtFromString = (file: string | undefined) => {
  if (file?.includes('.')) {
    return file.substring(file.lastIndexOf('.') + 1)
  }
  return ''
}

export const getContentType = (ext: string) => {
  if (mimeTypes[ext.toLowerCase()]) {
    return mimeTypes[ext.toLowerCase()]
  }
  //return undefined;
  return 'application/octet-stream'
}
export const isImage = (ext: string) => {
  if (getContentType(ext)?.startsWith('image/')) {
    return true
  }
  return false
}

export const getImgSrcPlaceholder = (imageSize: string) => {
  return `/assets/file-null${imageSize === 'tiny' ? '_tiny' : ''}@2x.png`
}

export const getFileMissingSrc = (imageSize: string) => {
  return `/assets/missing${imageSize === 'tiny' ? '_tiny' : ''}@2x.png`
}

export const getFileTrappedSrc = (imageSize: string) => {
  return `/assets/trapped${imageSize === 'tiny' ? '_tiny' : ''}@2x.png`
}

export const getFileOfflineSrc = (imageSize: string) => {
  return `/assets/offline${imageSize === 'tiny' ? '_tiny' : ''}@2x.png`
}

// export const getFileSrc = async (id: string, ext: string): Promise<string> => {
//     try {
//         const reference = storage().ref(`files/${id}.${ext}`);
//         const url = await reference.getDownloadURL();
//         return url;
//     } catch (error) {
//         console.error('Error getting download URL:', error);
//         throw error;
//     }
// };
export const getFileSrc = (id: string, ext: string): Promise<string> => {
  return getDownloadURL(storageRef(storage, `files/${id}.${ext}`))
}

export const getImgSrcFromExt = (ext: string | undefined, imageSize: string): string => {
  if (ext) {
    switch (ext.toLowerCase()) {
      case 'pdf':
        return `/assets/file-pdf${imageSize === 'tiny' ? '_tiny' : ''}@2x.png`
      case 'sfdoc':
        return `/assets/file-sfdoc${imageSize === 'tiny' ? '_tiny' : ''}@2x.png`
      case 'doc':
      case 'docx':
      case 'odt':
      case 'txt':
      case 'xls':
      case 'xlsx':
        return `/assets/file-doc${imageSize === 'tiny' ? '_tiny' : ''}@2x.png`
    }
  }
  return `/assets/file-attached${imageSize === 'tiny' ? '_tiny' : ''}@2x.png`
}

export const getImgSrc = (state: number, id: string, ext: string, imageSize: string): Promise<string> => {
  // if (state === 0) {
  //     return getFileMissingSrc(imageSize);
  // }
  if (isImage(ext)) {
    //return getStorageUrl(licenseeId, id, ext, (state === 2) ? ('_'+imageSize) : '');

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        //reject(`Timeout getting downloadUrl for files/${id}${(state === 2) ? ('_'+imageSize) : ''}.${ext}`);
        resolve(getFileOfflineSrc(imageSize))
      }, 2000)
      return getDownloadURL(storageRef(storage, `files/${id}${state === 2 ? '_' + imageSize : ''}.${ext}`))
        .then(url => {
          clearTimeout(timeout)
          resolve(url)
        })
        .catch(e => {
          clearTimeout(timeout)
          console.log(`Failed to getDownloadURL error`, e)
          resolve(getImgSrcFromExt(ext, imageSize))
        })
    })
  }
  return Promise.resolve(getImgSrcFromExt(ext, imageSize))
}

export const getImgSrcFromString = (file: string, imageSize: string): Promise<string> => {
  return getImgSrc(
    toInt(file.substring(0, 1), 0),
    file.substring(1, 21),
    file.substring(file.lastIndexOf('.') + 1),
    imageSize
  )
}

//
// Wrapper function to write blob files.
// Implementation differs depending on platform.
// write_blob can be really slow on desktop - at least using Chrome on my windows 11 laptop, so we stick with the standard Capacitor method.
// write_blob is however a lot better for hybrid, especially iOS.
//

export const writeBlobFile = (buffer: Blob | string, path: string, directory: Directory | string) => {
  return Filesystem.writeFile({
    path: path,
    data: buffer, // blob is only supported on web!
    directory: directory,
    recursive: true, // create any missing parent directories
  }).then(result => {
    return result.uri
  })
}

// Helper function to get file size
export const getFileSize = async (path: string): Promise<number> => {
  try {
    const fileInfo = await FileSystem.getInfoAsync(path)
    console.log('getFileSize fileInfo', fileInfo)
    return fileInfo.exists ? fileInfo.size : 0
  } catch (error) {
    console.error('[FileSystem] Error getting file size:', error)
    return 0
  }
}

// Will render an img src depending on file
// nonImage: will render an icon
// noCache: will render a URL
// else: assumes it is base64 encoded
export const renderSrc = (file: string, src: string, size: string): Promise<string> => {
  if (
    file &&
    typeof file === 'string' &&
    (file?.startsWith('data:image/') || // Check if it is a base64 encoded uri image
      file?.startsWith('file:/')) // Must be a local file that is loaded, but not uploaded
  ) {
    return Promise.resolve(file)
  }
  if (src === 'nonImage') {
    // Else get the default image for the file extension
    return Promise.resolve(getImgSrcFromExt(getExtFromString(file), size))
  } else if (src === 'noCache') {
    if (file.startsWith('0')) {
      // Not yet uploaded by another device
      return Promise.resolve(getFileTrappedSrc(size))
    }
    return getImgSrcFromString(file, size)
  }
  return Promise.resolve(src)
}

export const getFileNameWithExtension = (file: string): string => {
  const fileParts = file.split('_')
  if (fileParts.length > 1) {
    return fileParts[1]
  }
  return ''
}

export const isPdf = (ext: string | undefined) => {
  if (!ext) {
    return false
  }
  if (ext.toLowerCase().endsWith('.pdf') || ext.toLowerCase() === 'pdf') {
    return true
  }
  if (ext.endsWith('.pdf')) {
    return true
  }
  return false
}

export const getFileSrcFromString = (file: string, imageSize = 'full'): Promise<string> => {
  const ext = file.substring(file.lastIndexOf('.') + 1)
  const state = toInt(file.substring(0, 1), 0)
  const id = file.substring(1, 21)
  if (isImage(ext)) {
    return getDownloadURL(storageRef(storage, `files/${id}${state === 2 ? '_' + imageSize : ''}.${ext}`))
    //return storage.ref().child(`files/${id}${(state === 2) ? ('_'+imageSize) : ''}.${ext}`).getDownloadURL();
  } else {
    return getFileSrc(id, ext)
  }
}

// make files from firestore values
export const makeSeaFiles = (values: string[] | undefined): SeaFile[] => {
  if (values && values.length > 0) {
    const files = [] as SeaFile[]
    values.forEach((value: string) => {
      const file = {
        state: toInt(value[0], 0),
        ext: value.substring(value.lastIndexOf('.') + 1),
        /** Set the uri here so the Images are loaded on the UI - in `SeaFileUploader` -> `SeaMediaCard` */
        uri: value,
      } as SeaFile
      if (value.includes('_')) {
        file.id = value.substring(1, value.indexOf('_'))
        file.name = value.substring(value.indexOf('_') + 1)
      } else {
        file.id = value.substring(1, value.indexOf('.'))
      }
      files.push(file)
    })
    return files
  }
  return []
}

export const makeSignature = (value: string | undefined): SeaFile | undefined => {
  if (value && value.length > 0) {
    return {
      state: toInt(value[0], 0),
      ext: value.substring(value.lastIndexOf('.') + 1),
      id: value.substring(1, value.indexOf('.')),
      isSignature: true,
    }
  }
  return undefined
}

/**
 * Converts an image URI to base64 string on mobile devices
 * @param uri The image URI to convert
 * @returns Promise resolving to base64 string
 */
export const convertUriToBase64 = async (uri: string): Promise<string> => {
  try {
    // For web, the URI might already be a base64 string
    if (Platform.OS === 'web' && uri.includes('base64')) {
      return uri.substring(uri.indexOf(',') + 1)
    }

    // For native platforms, read the file as base64
    const base64 = await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64,
    })

    return base64
  } catch (error) {
    console.error('Error converting URI to base64:', error)
    throw error
  }
}

export const getBase64FromFirebasePDF = async (url: string) => {
  const response = await fetch(url)
  const blob = await response.blob()

  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      resolve(reader.result as string) // Data URI
    }
    reader.onerror = reject
    reader.readAsDataURL(blob) // 👈 Convert to base64
  })
}
