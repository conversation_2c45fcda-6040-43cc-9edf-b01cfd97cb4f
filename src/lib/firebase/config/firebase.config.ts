export interface FirebaseConfig {
  apiKey: string
  authDomain: string
  projectId: string
  storageBucket: string
  messagingSenderId: string
  appId: string
  measurementId?: string
}

export const firebaseConfig: FirebaseConfig = {
  // @ts-ignore
  apiKey: process.env.EXPO_PUBLIC_REACT_APP_FIREBASE_API_KEY,
  // @ts-ignore
  authDomain: process.env.EXPO_PUBLIC_REACT_APP_FIREBASE_AUTH_DOMAIN,
  // @ts-ignore
  projectId: process.env.EXPO_PUBLIC_REACT_APP_FIREBASE_PROJECT_ID,
  // @ts-ignore
  storageBucket: process.env.EXPO_PUBLIC_REACT_APP_FIREBASE_STORAGE_BUCKET,
  // @ts-ignore
  messagingSenderId: process.env.EXPO_PUBLIC_REACT_APP_FIREBASE_MESSAGING_SENDER_ID,
  // @ts-ignore
  appId: process.env.EXPO_PUBLIC_REACT_APP_FIREBASE_APP_ID,
  // @ts-ignore
  measurementId: process.env.EXPO_PUBLIC_REACT_APP_FIREBASE_MEASUREMENT_ID,
}

export default firebaseConfig
