import { initializeApp } from 'firebase/app'
import {
  CACHE_SIZE_UNLIMITED,
  initializeFirestore,
  persistentLocalCache,
  persistentMultipleTabManager,
} from 'firebase/firestore'
import { firebaseConfig } from './firebase.config'
import { getFunctions } from 'firebase/functions'
import { getAuth } from 'firebase/auth'

// This is NOT to be used for any new code and will eventually be phased out
const app = initializeApp(firebaseConfig, 'OLD_IMPLEMENTATION')

console.log('firebase.web app initialized')

// Initialize Firestore with persistence configuration
const db = initializeFirestore(app, {
  localCache: persistentLocalCache({
    tabManager: persistentMultipleTabManager(),
    cacheSizeBytes: CACHE_SIZE_UNLIMITED,
  }),
})

export const functionsRegion = 'australia-southeast1'
const functions = getFunctions(app, functionsRegion)

const auth = getAuth(app)

export { db, app, functions, auth }
