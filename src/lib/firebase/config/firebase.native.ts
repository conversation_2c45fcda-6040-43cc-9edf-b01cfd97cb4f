import firebase from '@react-native-firebase/app'
import '@react-native-firebase/firestore'
import { firebaseConfig } from './firebase.config'
import auth from '@react-native-firebase/auth'

if (!firebase.apps.length) {
  console.log('Initializing Firebase no app length')
  firebase.initializeApp(firebaseConfig)
}

console.log('firebase.native app initialized')

const db = firebase.firestore()

export { db, auth }
