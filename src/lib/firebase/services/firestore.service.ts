import { Platform } from 'react-native'
import {
  CollectionReference as WebCollectionReference,
  DocumentData as WebDocumentData,
  DocumentReference as WebDocumentReference,
  DocumentSnapshot as WebDocumentSnapshot,
  FieldPath,
  Query,
  QueryConstraint as WebQueryConstraint,
  QueryConstraintType,
  QueryFieldFilterConstraint,
  QuerySnapshot as WebQuerySnapshot,
  Firestore as WebFirestore,
  Where<PERSON><PERSON>erO<PERSON>,
  WriteBatch as WebWriteBatch,
  writeBatch as webWriteBatch,
  FieldValue as WebFieldValue,
  increment as WebIncrement,
  Timestamp as WebTimestamp,
  QueryDocumentSnapshot as WebQueryDocumentSnapshot,
  QueryOrderByConstraint as WebQueryOrderByConstraint,
  Unsubscribe as WebUnsubscribe,
  FirestoreError as WebFirestoreError,
  SnapshotListenOptions,
  FirestoreError,
  Unsubscribe,
} from 'firebase/firestore'
import {
  FirebaseFirestoreTypes,
  FieldValue as NativeFieldValue,
  Timestamp as NativeTimestamp,
  QueryOrderByConstraint as NativeQueryOrderByConstraint,
  FirestoreError as NativeFirestoreError,
  Unsubscribe as NativeUnsubscribe,
} from '@react-native-firebase/firestore'

export type Timestamp = WebTimestamp | NativeTimestamp
export type DocumentData = WebDocumentData | FirebaseFirestoreTypes.DocumentData

export type QueryDocumentSnapshot<AppModelType = DocumentData, DbModelType extends DocumentData = DocumentData> =
  | WebQueryDocumentSnapshot<AppModelType, DbModelType>
  | FirebaseFirestoreTypes.QueryDocumentSnapshot<FirebaseFirestoreTypes.DocumentData>
export type CollectionReference<T = DocumentData> =
  | WebCollectionReference<T>
  | FirebaseFirestoreTypes.CollectionReference<FirebaseFirestoreTypes.DocumentData>
export type DocumentReference<T = DocumentData> =
  | WebDocumentReference<T>
  | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>
// export type DocumentSnapshot<T = DocumentData> = WebDocumentSnapshot<T> | FirebaseFirestoreTypes.DocumentSnapshot<FirebaseFirestoreTypes.DocumentData>
export type DocumentSnapshot<AppModelType = DocumentData, DbModelType extends DocumentData = DocumentData> =
  | WebDocumentSnapshot<AppModelType, DbModelType>
  | FirebaseFirestoreTypes.DocumentSnapshot<FirebaseFirestoreTypes.DocumentData>
export type QuerySnapshot<T = DocumentData> =
  | WebQuerySnapshot<T>
  | FirebaseFirestoreTypes.QuerySnapshot<FirebaseFirestoreTypes.DocumentData>
export type QueryConstraint =
  | WebQueryConstraint
  | [string, string, unknown]
  | [string, unknown]
  | [string, string, string, unknown]
export type QueryOrderByConstraint = WebQueryOrderByConstraint | NativeQueryOrderByConstraint

type QuerySnapshotArg =
  | DocumentSnapshot<DocumentData>
  | FirebaseFirestoreTypes.DocumentSnapshot<FirebaseFirestoreTypes.DocumentData>
  | unknown[]

type WebFunctions = {
  collection: Function
  query: Function
  where: Function
  getDocs: Function
  addDoc: Function
  doc: Function
  getDoc: Function
  setDoc: Function
  deleteDoc: Function
  orderBy: Function
  limit: Function
  startAt: Function
  startAfter: Function
  endAt: Function
  endBefore: Function
  onSnapshot: Function
  writeBatch: Function
  serverTimestamp: Function
  increment: Function
  disableNetwork: Function
  enableNetwork: Function
}

let firestoreInstance: WebFirestore | FirebaseFirestoreTypes.Module

export type BatchType = WebWriteBatch | FirebaseFirestoreTypes.WriteBatch

export type FieldValue = WebFieldValue | NativeFieldValue

export interface WriteBatch {
  set: (
    documentRef: DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference,
    data: DocumentData | FirebaseFirestoreTypes.DocumentData,
    options?: any
  ) => void
  delete: (documentRef: DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference) => void
  commit: () => Promise<any>
}
export interface SplittableBatch {
  set: (
    documentRef: DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference,
    data: DocumentData | FirebaseFirestoreTypes.DocumentData,
    options?: any
  ) => void
  delete: (documentRef: DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference) => void
  commit: () => Promise<any>
  getBatches: () => BatchType[]
}

const createFirestoreService = () => {
  let db: WebFirestore | FirebaseFirestoreTypes.Module

  let webFunctions: WebFunctions = {
    collection: () => {
      console.log('Collection -> Firestore not initialized')
    },
    query: () => {
      console.log('Query -> Firestore not initialized')
    },
    where: () => {
      console.log('Where -> Firestore not initialized')
    },
    getDocs: () => {
      console.log('GetDocs -> Firestore not initialized')
    },
    addDoc: () => {
      console.log('AddDoc -> Firestore not initialized')
    },
    doc: () => {
      console.log('Doc -> Firestore not initialized')
    },
    getDoc: () => {
      console.log('GetDoc -> Firestore not initialized')
    },
    setDoc: () => {
      console.log('SetDoc -> Firestore not initialized')
    },
    deleteDoc: () => {
      console.log('DeleteDoc -> Firestore not initialized')
    },
    orderBy: () => {
      console.log('OrderBy -> Firestore not initialized')
    },
    limit: () => {
      console.log('Limit -> Firestore not initialized')
    },
    startAt: () => {
      console.log('StartAt -> Firestore not initialized')
    },
    startAfter: () => {
      console.log('StartAfter -> Firestore not initialized')
    },
    endAt: () => {
      console.log('EndAt -> Firestore not initialized')
    },
    endBefore: () => {
      console.log('EndBefore -> Firestore not initialized')
    },
    onSnapshot: () => {
      console.log('OnSnapshot -> Firestore not initialized')
    },
    writeBatch: () => {
      console.log('WriteBatch -> Firestore not initialized')
    },
    serverTimestamp: () => {
      console.log('ServerTimestamp -> Firestore not initialized')
    },
    increment: () => {
      console.log('Increment -> Firestore not initialized')
    },
    disableNetwork: () => {
      console.log('DisableNetwork -> Firestore not initialized')
    },
    enableNetwork: () => {
      console.log('EnableNetwork -> Firestore not initialized')
    },
  }

  const initializeWeb = async () => {
    const {
      collection,
      query,
      where,
      getDocs,
      addDoc,
      doc,
      deleteDoc,
      orderBy,
      limit,
      startAt,
      startAfter,
      endAt,
      endBefore,
      getDoc,
      onSnapshot,
      writeBatch,
      setDoc,
      serverTimestamp,
      increment,
      disableNetwork,
      enableNetwork,
    } = await import('firebase/firestore')
    const { db: webDb } = await import('../config/firebase.web')

    db = webDb
    firestoreInstance = webDb
    if (webDb) {
      webFunctions = {
        collection,
        query,
        where,
        getDocs,
        addDoc,
        doc,
        deleteDoc,
        orderBy,
        limit,
        startAt,
        startAfter,
        endAt,
        endBefore,
        getDoc,
        onSnapshot,
        setDoc,
        writeBatch,
        serverTimestamp,
        increment,
        disableNetwork,
        enableNetwork,
      }
    }
  }

  const initializeNative = async () => {
    const { db: nativeDb } = await import('../config/firebase.native')
    db = nativeDb
    firestoreInstance = nativeDb
  }

  const getNativeDB = (): FirebaseFirestoreTypes.Module => {
    if (Platform.OS === 'web') {
      throw new Error('getNativeDB called on web platform')
    }
    return db as FirebaseFirestoreTypes.Module
  }

  const initialize = async () => {
    if (Platform.OS === 'web') {
      await initializeWeb()
    } else {
      await initializeNative()
    }
  }

  initialize()

  // Define the overloads as a type
  type DocFunction = {
    (
      firestore: WebFirestore | FirebaseFirestoreTypes.Module,
      path: string,
      ...pathSegments: string[]
    ): DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>
    (
      firestore: FirebaseFirestoreTypes.Module,
      path: string,
      ...pathSegments: string[]
    ): DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>
    (
      collection: CollectionReference,
      path?: string
    ): DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>
    (
      collection: FirebaseFirestoreTypes.CollectionReference<FirebaseFirestoreTypes.DocumentData>,
      path: string
    ): DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>
  }

  // Implement the function broadly
  const docImplementation: DocFunction = (
    firestore:
      | WebFirestore
      | FirebaseFirestoreTypes.Module
      | CollectionReference<DocumentData>
      | FirebaseFirestoreTypes.CollectionReference<FirebaseFirestoreTypes.DocumentData>,
    collectionName?: string,
    docId?: string
    // ): DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData> => {
  ) => {
    if (Platform.OS === 'web') {
      if (firestore && typeof (firestore as any).path === 'string') {
        return webFunctions.doc(firestore as CollectionReference<DocumentData>, collectionName ?? '', docId ?? '')
      } else {
        return webFunctions.doc(firestore || db, collectionName, docId)
      }
      // return webFunctions.doc(firestore || db, collectionName, docId);
    } else {
      if (firestore && typeof (firestore as any).path === 'string') {
        return (firestore as FirebaseFirestoreTypes.CollectionReference).doc(collectionName ?? '')
      } else {
        return getNativeDB()
          .collection(collectionName ?? '')
          .doc(docId ?? '')
      }
      // return getNativeDB().collection(collectionName || '').doc(docId || '');
    }
  }

  type OnSnapshotFunction = {
    // <AppModelType = DocumentData, DbModelType extends DocumentData = AppModelType>(
    //     reference: DocumentReference<AppModelType, DbModelType> | FirebaseFirestoreTypes.DocumentReference,
    //     options?: { includeMetadataChanges?: boolean },
    //     callback: (snapshot: DocumentSnapshot<AppModelType, DbModelType>) => void,
    //     onError?: (error: Error) => void
    // ): () => void;

    (
      reference: DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference,
      options?: any,
      callback?: (
        snapshot:
          | DocumentSnapshot<DocumentData>
          | FirebaseFirestoreTypes.DocumentSnapshot<FirebaseFirestoreTypes.DocumentData>
      ) => void,
      onError?: (error: Error) => void
    ): () => void

    (
      reference: DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference,
      // options?: any,
      callback?: (
        snapshot:
          | DocumentSnapshot<DocumentData>
          | FirebaseFirestoreTypes.DocumentSnapshot<FirebaseFirestoreTypes.DocumentData>
      ) => void,
      onError?: (error: Error) => void
    ): () => void

    (
      reference: DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference,
      // options?: any,
      callback?: (
        snapshot:
          | DocumentSnapshot<DocumentData>
          | FirebaseFirestoreTypes.DocumentSnapshot<FirebaseFirestoreTypes.DocumentData>
      ) => void,
      onError?: (error: Error) => void
    ): () => void

    (
      queries: Query<DocumentData, DocumentData> | FirebaseFirestoreTypes.Query<FirebaseFirestoreTypes.DocumentData>,
      // options?: any,
      callback?: (snapshot: QuerySnapshot) => void,
      onError?: (error: Error) => void
    ): () => void
  }

  // type OnSnapshotFunction = {
  //     (
  //         query: Query<DocumentData> | FirebaseFirestoreTypes.Query<FirebaseFirestoreTypes.DocumentData>,
  //         options: SnapshotListenOptions,
  //         observer: {
  //             next?: (snapshot: QuerySnapshot<DocumentData> | DocumentSnapshot<DocumentData>) => void;
  //             error?: (error: WebFirestoreError | NativeFirestoreError) => void;
  //             complete?: () => void;
  //         }
  //     ): WebUnsubscribe | NativeUnsubscribe;

  //     (
  //         query: Query<DocumentData, DocumentData> | FirebaseFirestoreTypes.Query<FirebaseFirestoreTypes.DocumentData>,
  //         observer: {
  //             next?: (snapshot: QuerySnapshot<DocumentData> | DocumentSnapshot<DocumentData>) => void;
  //             error?: (error: WebFirestoreError | NativeFirestoreError) => void;
  //             complete?: () => void;
  //         }
  //     ): WebUnsubscribe | NativeUnsubscribe;

  //     (
  //         reference: DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>,
  //         options: SnapshotListenOptions,
  //         observer: {
  //             next?: (snapshot: DocumentSnapshot<DocumentData> | FirebaseFirestoreTypes.DocumentSnapshot<FirebaseFirestoreTypes.DocumentData>) => void;
  //             error?: (error: WebFirestoreError | NativeFirestoreError) => void;
  //             complete?: () => void;
  //         }
  //     ): WebUnsubscribe | NativeUnsubscribe;
  // };

  const onSnapshotImplementation: OnSnapshotFunction = (
    reference: any,
    options?: any,
    callback?: any,
    onError?: (error: Error) => void
  ): (() => void) => {
    if (Platform.OS === 'web') {
      return webFunctions.onSnapshot(reference, options, callback, onError)
    }
    if ('where' in reference) {
      return (reference as FirebaseFirestoreTypes.Query).onSnapshot(options, callback, onError)
    }
    return (reference as FirebaseFirestoreTypes.DocumentReference).onSnapshot(options, callback, onError)
  }

  return {
    firestore: firestoreInstance,
    orderBy: (
      field: string,
      direction: 'asc' | 'desc' = 'asc'
    ): WebQueryConstraint | [string, string, 'asc' | 'desc'] | QueryOrderByConstraint => {
      if (Platform.OS === 'web') {
        return webFunctions.orderBy(field, direction)
      }
      return ['orderBy', field, direction]
    },
    limit: (limitCount: number): WebQueryConstraint | [string, number] => {
      if (Platform.OS === 'web') {
        return webFunctions.limit(limitCount)
      }
      return ['limit', limitCount]
    },
    startAt: (snapshot: QuerySnapshotArg): WebQueryConstraint | [string, QuerySnapshotArg] => {
      if (Platform.OS === 'web') {
        return webFunctions.startAt(snapshot)
      }
      return ['startAt', snapshot]
    },
    startAfter: (snapshot: QuerySnapshotArg): WebQueryConstraint | [string, QuerySnapshotArg] => {
      if (Platform.OS === 'web') {
        return webFunctions.startAfter(snapshot)
      }
      return ['startAfter', snapshot]
    },
    endAt: (snapshot: QuerySnapshotArg): WebQueryConstraint | [string, QuerySnapshotArg] => {
      if (Platform.OS === 'web') {
        return webFunctions.endAt(snapshot)
      }
      return ['endAt', snapshot]
    },
    endBefore: (snapshot: QuerySnapshotArg): WebQueryConstraint | [string, QuerySnapshotArg] => {
      if (Platform.OS === 'web') {
        return webFunctions.endBefore(snapshot)
      }
      return ['endBefore', snapshot]
    },
    query: (
      collectionRef:
        | CollectionReference<DocumentData>
        | FirebaseFirestoreTypes.CollectionReference<FirebaseFirestoreTypes.DocumentData>,
      ...queryConstraints: QueryConstraint[]
    ): Query<DocumentData, DocumentData> | FirebaseFirestoreTypes.Query<FirebaseFirestoreTypes.DocumentData> => {
      if (Platform.OS === 'web') {
        return webFunctions.query(collectionRef, ...queryConstraints)
      }
      let ref = collectionRef as FirebaseFirestoreTypes.Query<FirebaseFirestoreTypes.DocumentData>
      queryConstraints.forEach(constraint => {
        if (Array.isArray(constraint)) {
          const [type, ...args] = constraint
          switch (type) {
            case 'orderBy':
              ref = ref.orderBy(args[0] as string | number | FieldPath, args[1] as 'asc' | 'desc')
              break
            case 'limit':
              ref = ref.limit(args[0] as number)
              break
            case 'where':
              ref = ref.where(args[0] as string, args[1] as WhereFilterOp, args[2] as unknown)
              break
            case 'startAt':
              ref = ref.startAt(args[0], args[1])
              break
            case 'startAfter':
              ref = ref.startAfter(args[0] as QuerySnapshotArg)
              break
            case 'endAt':
              ref = ref.endAt(args[0], args[1])
              break
            case 'endBefore':
              ref = ref.endBefore(args[0])
              break
            default:
              ref = ref.where(constraint[0] as string, constraint[1] as WhereFilterOp, constraint[2] as unknown)
          }
        } else {
          console.warn('Implement custom constraint logic here', constraint)
        }
      })
      return ref
    },
    where: (field: string, operator: string, value: unknown): WebQueryConstraint | [string, string, unknown] => {
      if (Platform.OS === 'web') {
        return webFunctions.where(field, operator, value)
      }
      return [field, operator, value]
    },
    collection: (
      firestore: WebFirestore | FirebaseFirestoreTypes.Module,
      collectionName: string
    ): CollectionReference => {
      if (Platform.OS === 'web') {
        return webFunctions.collection(firestore || db, collectionName)
      }
      return getNativeDB().collection(collectionName)
    },
    getDocs: async (
      query: Query<DocumentData> | FirebaseFirestoreTypes.Query<FirebaseFirestoreTypes.DocumentData>
    ): Promise<QuerySnapshot> => {
      try {
        let snapshot
        if (Platform.OS === 'web') {
          snapshot = await webFunctions.getDocs(query)
        } else {
          snapshot = await (query as FirebaseFirestoreTypes.Query<FirebaseFirestoreTypes.DocumentData>).get()
        }

        return {
          empty: snapshot.empty,
          size: snapshot.size,
          docs: snapshot.docs.map((doc: DocumentSnapshot | FirebaseFirestoreTypes.DocumentSnapshot) => ({
            id: doc.id,
            data: () => doc.data(),
            // @ts-ignore
            exists: Platform.OS === 'web' ? doc.exists() : doc.exists,
          })),
        } as QuerySnapshot
      } catch (error) {
        console.error('Error in getDocs:', error)
        throw error
      }
    },
    getDoc: async (
      docRef:
        | DocumentReference<DocumentData>
        | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>
    ): Promise<
      DocumentSnapshot<DocumentData> | FirebaseFirestoreTypes.DocumentSnapshot<FirebaseFirestoreTypes.DocumentData>
    > => {
      if (Platform.OS === 'web') {
        return webFunctions.getDoc(docRef)
      }
      return (docRef as FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>).get()
    },
    setDoc: async (
      docRef:
        | DocumentReference<DocumentData>
        | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>,
      data: DocumentData | FirebaseFirestoreTypes.DocumentData,
      options?: any
    ): Promise<void> => {
      if (Platform.OS === 'web') {
        return webFunctions.setDoc(docRef, data, options)
      }
      return (docRef as FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>).set(
        data,
        options
      )
    },
    doc: docImplementation,
    docExists: (doc: DocumentSnapshot<DocumentData>) => {
      // This is to abstract away the contradictory ways you need check a document exists
      if (Platform.OS === 'web') {
        return (doc as any).exists()
      }
      return doc.exists
    },
    addDoc: async (
      collectionName:
        | string
        | CollectionReference<DocumentData>
        | FirebaseFirestoreTypes.CollectionReference<FirebaseFirestoreTypes.DocumentData>,
      data: DocumentData | FirebaseFirestoreTypes.DocumentData
    ): Promise<
      DocumentReference<DocumentData> | FirebaseFirestoreTypes.DocumentReference<FirebaseFirestoreTypes.DocumentData>
    > => {
      if (Platform.OS === 'web') {
        const collectionRef = webFunctions.collection(db, collectionName)
        return webFunctions.addDoc(collectionRef, data)
      }
      if (typeof collectionName === 'string') {
        return getNativeDB().collection(collectionName).add(data)
      }
      return (collectionName as FirebaseFirestoreTypes.CollectionReference<FirebaseFirestoreTypes.DocumentData>).add(
        data
      )
    },
    deleteDoc: async (collectionName: string, docId: string): Promise<void> => {
      if (Platform.OS === 'web') {
        const docRef = webFunctions.doc(db, collectionName, docId)
        return webFunctions.deleteDoc(docRef)
      }
      return getNativeDB().collection(collectionName).doc(docId).delete()
    },
    onSnapshot: onSnapshotImplementation,
    splittableBatch: (
      firestore: WebFirestore | FirebaseFirestoreTypes.Module,
      maxQueriesPerBatch = 20
    ): SplittableBatch => {
      const createBatch = (): BatchType => {
        if (Platform.OS === 'web') {
          // const { writeBatch } = require('firebase/firestore');
          return webFunctions.writeBatch((firestore || db) as WebFirestore)
        }
        return getNativeDB().batch()
      }

      const batches: BatchType[] = [createBatch()]
      let count = 1

      return {
        set: (documentRef, data, options?) => {
          const batch = batches[batches.length - 1]
          if (Platform.OS === 'web') {
            ;(batch as WebWriteBatch).set(documentRef as WebDocumentReference<DocumentData>, data, options)
          } else {
            ;(batch as FirebaseFirestoreTypes.WriteBatch).set(
              documentRef as FirebaseFirestoreTypes.DocumentReference,
              data,
              options
            )
          }
          count++
          if (count > maxQueriesPerBatch) {
            batches.push(createBatch())
            count = 1
          }
        },
        delete: documentRef => {
          const batch = batches[batches.length - 1]
          if (Platform.OS === 'web') {
            ;(batch as WebWriteBatch).delete(documentRef as WebDocumentReference<DocumentData>)
          } else {
            ;(batch as FirebaseFirestoreTypes.WriteBatch).delete(
              documentRef as FirebaseFirestoreTypes.DocumentReference
            )
          }
          count++
          if (count > maxQueriesPerBatch) {
            batches.push(createBatch())
            count = 1
          }
        },
        commit: async () => Promise.all(batches.map(batch => batch.commit())),
        getBatches: () => batches,
      }
    },
    writeBatch: (firestore: WebFirestore | FirebaseFirestoreTypes.Module) => {
      if (Platform.OS === 'web') {
        return webFunctions.writeBatch((firestore || db) as WebFirestore)
      }
      return getNativeDB().batch()
    },
    serverTimestamp: () => {
      if (Platform.OS === 'web') {
        return webFunctions.serverTimestamp()
      }
      const nativeFireStore = require('@react-native-firebase/firestore')
      return nativeFireStore.FieldValue.serverTimestamp()
    },
    increment: (value: number) => {
      if (Platform.OS === 'web') {
        return webFunctions.increment(value)
      }
      const nativeFireStore = require('@react-native-firebase/firestore')
      return nativeFireStore.FieldValue.increment(value)
    },
    disableNetwork: async (firestore?: WebFirestore | FirebaseFirestoreTypes.Module) => {
      if (Platform.OS === 'web') {
        return webFunctions.disableNetwork(firestore ?? (db as WebFirestore))
      }
      return ((firestore as FirebaseFirestoreTypes.Module) || getNativeDB()).disableNetwork()
    },
    enableNetwork: async (firestore?: WebFirestore | FirebaseFirestoreTypes.Module) => {
      if (Platform.OS === 'web') {
        return webFunctions.enableNetwork(firestore ?? (db as WebFirestore))
      }
      return ((firestore as FirebaseFirestoreTypes.Module) || getNativeDB()).enableNetwork()
    },
    Timestamp: {
      fromMillis: (milliseconds: number) => {
        if (Platform.OS === 'web') {
          return WebTimestamp.fromMillis(milliseconds)
        }
        return NativeTimestamp.fromMillis(milliseconds)
      },
      fromDate: (date: Date) => {
        if (Platform.OS === 'web') {
          return WebTimestamp.fromDate(date)
        }
        return NativeTimestamp.fromDate(date)
      },
      now: () => {
        if (Platform.OS === 'web') {
          return WebTimestamp.now()
        }
        return NativeTimestamp.now()
      },
    },
  }
}

export const {
  firestore,
  collection,
  query,
  where,
  getDocs,
  getDoc,
  setDoc,
  doc,
  docExists,
  addDoc,
  deleteDoc,
  orderBy,
  limit,
  startAfter,
  endBefore,
  onSnapshot,
  splittableBatch,
  writeBatch,
  serverTimestamp,
  increment,
  disableNetwork,
  enableNetwork,
  Timestamp,
} = createFirestoreService()
