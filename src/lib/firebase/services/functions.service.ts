import {
  Functions as FirebaseFunctions,
  HttpsCallableOptions,
  httpsCallable as httpsCallableWeb,
  getFunctions,
  connectFunctionsEmulator,
} from 'firebase/functions'
import { FirebaseFunctionsTypes } from '@react-native-firebase/functions'

// import firebaseFunctionsNative from '@react-native-firebase/functions';
import { firebase } from '@react-native-firebase/functions'

import { Platform } from 'react-native'

type WebFunctions = FirebaseFunctions
type NativeFunctions = FirebaseFunctionsTypes.Module
// type NativeFunctions = FirebaseFunctionsNative.Functions;

let functionsInstance: WebFunctions | NativeFunctions
export const functionsRegion = 'australia-southeast1'

const initializeWeb = async () => {
  try {
    const { app } = await import('../config/firebase.web')
    const functions = getFunctions(app, functionsRegion)
    functionsInstance = functions
    return { functions, httpsCallable: httpsCallableWeb }
  } catch (error) {
    console.error('Failed to initialize web functions:', error)
    return null
  }
}

const initializeNative = async () => {
  try {
    const instance = firebase.app().functions(functionsRegion)
    console.log('Native functions initialized successfully')
    return instance
  } catch (error) {
    console.error('Failed to initialize native functions:', error)
    return null
  }
}

const createFunctionsService = () => {
  let functions: WebFunctions | NativeFunctions

  const initialize = async () => {
    if (Platform.OS === 'web') {
      const result = await initializeWeb()
      if (result) {
        functions = result.functions
        functionsInstance = result.functions
      }
    } else {
      const result = await initializeNative()
      if (result) {
        functions = result
      }
    }
  }

  initialize()

  type HttpsCallableFunction = <RequestData = unknown, ResponseData = unknown>(
    functionsInstance: WebFunctions | NativeFunctions,
    name: string,
    options?: HttpsCallableOptions
  ) => (data?: RequestData) => Promise<any>

  const httpsCallableImplementation: HttpsCallableFunction = (functionsInstance, name, options?) => {
    // This function is called with the data
    return async (data?) => {
      if (Platform.OS === 'web') {
        try {
          const result = await httpsCallableWeb(functions as WebFunctions, name, options)(data)
          return result
        } catch (error) {
          console.error('Error calling web function:', error)
          throw error
        }
        // return Promise.resolve(result);
      }

      const result = await (functions as NativeFunctions).httpsCallable(name, options)(data)
      return result
    }
  }

  // type HttpsCallable<RequestData, ResponseData> = (data?: RequestData) => Promise<{ data: ResponseData }>;

  // type HttpsCallableFunction = <RequestData = unknown, ResponseData = unknown>(
  //     functionsInstance: WebFunctions | NativeFunctions,
  //     name: string,
  //     options?: HttpsCallableOptions
  // ) => HttpsCallable<RequestData, ResponseData>;

  // const httpsCallableImplementation: HttpsCallableFunction = (
  //     functionsInstance,
  //     name,
  //     options?
  // ) => {
  //     return async (data?) => {
  //         if (Platform.OS === 'web') {
  //             const result = await httpsCallableWeb(functionsInstance as WebFunctions, name, options)(data);
  //             return result as { data: any };
  //         }
  //         const result = await (functionsInstance as NativeFunctions).httpsCallable(name, options)(data);
  //         return result as { data: any };
  //     };
  // };

  return {
    functions: functionsInstance,
    httpsCallable: httpsCallableImplementation,
  }
}

export const { functions, httpsCallable } = createFunctionsService()
