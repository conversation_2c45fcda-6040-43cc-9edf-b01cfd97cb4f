import { Platform } from 'react-native'
import type { Auth, Auth as WebAuth, User as WebUser } from 'firebase/auth'
import type authType from '@react-native-firebase/auth'
import type { FirebaseAuthTypes } from '@react-native-firebase/auth'
import { ServiceContainer } from '@src/domain/di/ServiceContainer'
import { IFirebase } from '@src/domain/IFirebase'
import { SERVICES } from '@src/domain/di/ServiceRegistry'

export type NativeAuth = ReturnType<typeof authType>
export type User = WebUser | FirebaseAuthTypes.User

export let auth: WebAuth | NativeAuth

// const initializeAuth = async () => {
//   if (Platform.OS === 'web') {
//     const { getAuth } = await import('firebase/auth');
//     const { app } = await import('../config/firebase.web');
//     auth = getAuth(app);
//   } else {
//     const authInstance = await import('@react-native-firebase/auth');
//     auth = authInstance.default();
//   }
//   return auth;
// };

export const login = async (email: string, password: string): Promise<User> => {
  console.log('HELLO PRETTY BOY')
  try {
    if (Platform.OS === 'web') {
      const { signInWithEmailAndPassword } = await import('firebase/auth')
      const { auth } = await import('../config/firebase.web')
      const result = await signInWithEmailAndPassword(auth, email, password)
      return result.user
    } else {
      const { default: auth } = await import('@react-native-firebase/auth')
      const result = await auth().signInWithEmailAndPassword(email, password)
      return result.user
    }
  } catch (error: any) {
    throw new Error(error.message.replace(/\[.*?\]\s*/, '').replace(/\.$/, ''))
  }
}

export const signOut = async (auth: WebAuth | NativeAuth): Promise<void> => {
  try {
    if (Platform.OS === 'web') {
      const { signOut } = await import('firebase/auth')
      await signOut(auth as WebAuth)
    } else {
      await (auth as NativeAuth).signOut()
    }
  } catch (error: any) {
    throw new Error(error.message)
  }
}

// export const onAuthStateChanged = (authInstance: WebAuth | NativeAuth, callback: (user: User | null) => void) => {
//   if (!auth) return;
//   return auth.onAuthStateChanged(callback);
// };

export const onAuthStateChanged = async (authInstance: WebAuth | NativeAuth, callback: (user: User | null) => void) => {
  if (Platform.OS === 'web') {
    const { onAuthStateChanged } = await import('firebase/auth')
    const { auth } = await import('../config/firebase.web')
    return onAuthStateChanged(auth, callback)
  } else {
    const { auth } = await import('../config/firebase.native')
    return auth().onAuthStateChanged(callback)
  }
}

export const getCurrentUser = async (): Promise<User | null> => {
  if (Platform.OS === 'web') {
    const { auth } = await import('../config/firebase.web')
    return auth.currentUser
  } else {
    const { auth } = await import('../config/firebase.native')
    return auth().currentUser
  }
}

export const getIdToken = async (): Promise<string> => {
  if (Platform.OS === 'web') {
    const { auth } = await import('../config/firebase.web')
    const token = await auth.currentUser?.getIdToken()
    if (!token) throw new Error('No user logged in')
    return token
  } else {
    const { auth } = await import('../config/firebase.native')
    const token = await auth().currentUser?.getIdToken()
    if (!token) throw new Error('No user logged in')
    return token
  }
}

/** This is a TEMPORARY workaround while we have multiple instances of Firebase.
 * We currently authenticate against the OLD instance, but when doing data updates,
 * we use the NEW instance. If we do not do this 'piggy-back' sign-in, then the Firestore
 * Rules will deny the data update due to request.auth being missing. Ticket to remove: SF-343 */
const doPiggybackSignInAgainstNewFirebaseInstance = async (token: string) => {
  const firebase = ServiceContainer.get<IFirebase>(SERVICES.IFirebase)
  const auth = firebase.getAuth()
  const { signInWithCustomToken } = await import('firebase/auth')

  if (Platform.OS === 'web') {
    await signInWithCustomToken(auth as Auth, token)
  } else {
    const nativeAuth = auth as FirebaseAuthTypes.Module
    await nativeAuth.signInWithCustomToken(token)
  }
}

export const signInWithCustomToken = async (authInstance: WebAuth | NativeAuth, token: string): Promise<any> => {
  // if (!auth) throw new Error('No user logged in');
  if (Platform.OS === 'web') {
    const { signInWithCustomToken } = await import('firebase/auth')
    const { auth } = await import('../config/firebase.web')
    const result = await signInWithCustomToken(auth, token)

    // TODO: Remove as part of SF-343
    await doPiggybackSignInAgainstNewFirebaseInstance(token)

    return result
  } else {
    const { auth } = await import('../config/firebase.native')
    const result = await auth().signInWithCustomToken(token)

    // TODO: Remove as part of SF-343
    await doPiggybackSignInAgainstNewFirebaseInstance(token)

    return result
  }
}

export const createUserWithEmailAndPassword = async (
  authInstance: WebAuth | NativeAuth,
  email: string,
  password: string
): Promise<User> => {
  // if (!authInstance) throw new Error('No user logged in');
  if (Platform.OS === 'web') {
    const { createUserWithEmailAndPassword } = await import('firebase/auth')
    const { auth } = await import('../config/firebase.web')
    const result = await createUserWithEmailAndPassword((authInstance as WebAuth) || auth, email, password)
    return result.user
  } else {
    const { auth } = await import('../config/firebase.native')
    const result = await ((authInstance as NativeAuth) || auth()).createUserWithEmailAndPassword(email, password)
    return result.user
  }
}
