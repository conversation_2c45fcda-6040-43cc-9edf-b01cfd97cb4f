import { onPageViewed } from '@src/shared-state/General/appActivity'
import { profileQuery } from '../performance'
import { logEvent } from './services/analytics.service'
import { analytics } from './services/analytics.service'
import {
  CollectionReference,
  getDocs,
  onSnapshot,
  query,
  QueryConstraint,
  QueryDocumentSnapshot,
  QueryOrderByConstraint,
  QuerySnapshot,
  where,
} from './services/firestore.service'
import { DocumentData } from './services/firestore.service'

export { auth } from './services/auth.service'
export { analytics } from './services/analytics.service'
export { firestore } from './services/firestore.service'
export { storage } from './services/storage.service'
export { functions } from './services/functions.service'

export const logPageView = (pageName: string) => {
  logEvent(analytics, 'screen_view', {
    screen_name: pageName,
    page_title: pageName,
    screen_class: pageName,
  } as any)
  onPageViewed(pageName)
}

export type ArrayComparison = 'in' | 'not-in' | 'array-contains-any'
/**
 * This function serves to handle array queries (in, not-in, or array-contains-any)
 * that can have more than 10 elements in the array (Firestore limit)
 */
export const setupArrayQueryListener = (
  what: string,
  collectionReference: CollectionReference,
  baseConstraints: QueryConstraint[],
  arrayField: any,
  arrayComparison: ArrayComparison,
  array: string[],
  orderByConstraints: QueryOrderByConstraint[],
  processDocs: (docs: QueryDocumentSnapshot<DocumentData>[], isCombined: boolean) => any | undefined,
  onError?: (error: any) => void
) => {
  let numBasePossibleOptions = 1
  baseConstraints?.forEach((constraint: any, index) => {
    if (Array.isArray(constraint._value) && constraint._value.length > 1) {
      numBasePossibleOptions *= constraint._value.length
    }
  })
  const maxArrayItems = Math.min(10, Math.floor(30 / numBasePossibleOptions))

  if (array.length <= maxArrayItems) {
    const constraints = [...baseConstraints]
    constraints.push(where(arrayField, arrayComparison, array))
    constraints.push(...orderByConstraints)
    const profiler = profileQuery(what)
    return onSnapshot(
      query(collectionReference, ...constraints),
      (snap: QuerySnapshot<DocumentData>) => {
        profiler.record(snap)
        processDocs(snap.docs, false)
      },
      error => {
        // This should be very rare
        console.error(`Failed to access ${what}`, error)
      }
    )
  } else {
    let isActive = true
    const numParts = Math.ceil(array.length / maxArrayItems)
    const cleanUp = [] as any[]
    const results = [] as any[]
    let combineTaskN = 0 // unique identifier for combining results tasks

    const combineResults = (_combineTaskN: number) => {
      if (_combineTaskN !== combineTaskN) {
        return // There is a newer combine task being requested, therefore ignore this one
      }
      for (let i = 0; i < numParts; i++) {
        if (results[i] === undefined) {
          return // Not all results are ready yet
        }
      }
      if (numParts === 1) {
        // no combination necessary
        return results[0]
      }
      // Combine!
      // It's possible there are duplicates, so we'll need to remove them
      // Sometimes duplicates are introduced because array-contains-any can match multiple times
      // Can also be due to parts of the query reevaluating
      const combinedDocs = [...results[0]]
      const ids = {} as any
      results[0].forEach((doc: any) => {
        ids[doc.id] = true
      })
      for (let i = 1; i < numParts; i++) {
        const r = results[i]
        const n = r.length
        for (let j = 0; j < n; j++) {
          if (ids[r[j].id] === undefined) {
            combinedDocs.push(r[j])
            ids[r[j].id] = true
          }
        }
      }
      if (_combineTaskN === combineTaskN) {
        // This is still the latest task to be done?
        processDocs(combinedDocs, true)
      }
    }

    const handlePart = (index: number, arrayFrom: number, arrayTo: number) => {
      const subArray: string[] = []
      for (let i = arrayFrom; i <= arrayTo; i++) {
        subArray.push(array[i])
      }

      const constraints = [...baseConstraints]
      constraints.push(where(arrayField, arrayComparison, subArray))
      constraints.push(...orderByConstraints)
      // queries[index] = baseQuery.where(arrayField, arrayComparison as any, subArray);
      // for (let i = 0; i < orderBy.length; i += 2) {
      //     queries[index] = queries[index].orderBy(orderBy[i], orderBy[i+1] as any);
      // }

      const profiler = profileQuery(`${what}:${index}`)
      cleanUp[index] = onSnapshot(
        query(collectionReference, ...constraints),
        (snap: QuerySnapshot<DocumentData>) => {
          profiler.record(snap)
          results[index] = snap.docs
          combineTaskN++
          const _combineTaskN = combineTaskN
          // Combine results after a short delay.
          // This is too prevent multiple combine tasks being processed at the same time
          setTimeout(() => {
            if (!isActive) return
            combineResults(_combineTaskN)
          }, 10)
        },
        error => {
          // This should be very rare
          console.error(`Failed to access ${what} (index=${index})`, error)
          if (onError) onError(error)
        }
      )
      // cleanUp[index] = queries[index].onSnapshot((snapshot) => {
      //     profiler.record(snapshot);
      //     results[index] = snapshot.docs;
      //     combineTaskN++;
      //     const _combineTaskN = combineTaskN;
      //     // Combine results after a short delay.
      //     // This is too prevent multiple combine tasks being processed at the same time
      //     setTimeout(() => {
      //         if (!isActive) return;
      //         combineResults(_combineTaskN);
      //     }, 10);
      // }, (error) => {
      //     // This should be very rare
      //     console.error(`Failed to access ${what} (index=${index})`, error);
      // });
    }

    for (let i = 0; i < array.length; i += maxArrayItems) {
      handlePart(Math.floor(i / maxArrayItems), i, Math.min(array.length - 1, i + maxArrayItems - 1))
    }

    // Return cleanup function
    return () => {
      isActive = false
      combineTaskN++ // Prevent any further combine tasks from running
      for (let i = 0; i < numParts; i++) {
        if (cleanUp[i]) {
          try {
            cleanUp[i]() // Call snapshot listener cleanup functions
          } catch (e) {
            // We want to keep going if one snapshot's cleanup failed
          }
        }
      }
    }
  }
}

// Note: Currently composite queries may not be sorted
export const getArrayQueryResults = (
  what: string,
  collectionReference: CollectionReference,
  baseConstraints: QueryConstraint[],
  arrayField: any,
  arrayComparison: ArrayComparison,
  array: string[],
  orderByConstraints: QueryOrderByConstraint[]
): Promise<void | QueryDocumentSnapshot<DocumentData, DocumentData>[]> => {
  // Work out number of possible of options (each contraints possibility count multiplied together)
  // The total number of possible options can not be more than 30
  // See: https://firebase.google.com/docs/firestore/query-data/queries#limits_on_or_queries
  let numBasePossibleOptions = 1
  baseConstraints?.forEach((constraint: any, index) => {
    if (Array.isArray(constraint._value) && constraint._value.length > 1) {
      numBasePossibleOptions *= constraint._value.length
    }
  })
  const maxArrayItems = Math.min(10, Math.floor(30 / numBasePossibleOptions))

  if (array.length <= maxArrayItems) {
    const constraints = [...baseConstraints]
    constraints.push(where(arrayField, arrayComparison, array))
    constraints.push(...orderByConstraints)

    return getDocs(query(collectionReference, ...constraints))
      .then(snap => {
        return Promise.resolve(snap.docs)
      })
      .catch(error => {
        console.error(`Failed to access ${what}`, error)
        return Promise.reject(error)
      })
  } else {
    const numParts = Math.ceil(array.length / maxArrayItems)
    const results = [] as any[]

    const processQuery = (i: number) => {
      const _array: string[] = []
      for (let j = i * maxArrayItems; j < (i + 1) * maxArrayItems && j < array.length; j++) {
        _array.push(array[j])
      }

      const constraints = [...baseConstraints]
      constraints.push(where(arrayField, arrayComparison, _array))
      constraints.push(...orderByConstraints)

      return getDocs(query(collectionReference, ...constraints)).then(snap => {
        results[i] = snap.docs
        return Promise.resolve()
      })
    }

    const promises: any[] = []
    for (let i = 0; i < numParts; i++) {
      promises.push(processQuery(i))
    }

    return Promise.all(promises)
      .then(() => {
        const docs = [] as any[]
        for (let i = 0; i < numParts; i++) {
          results[i].forEach((doc: any) => {
            docs.push(doc)
          })
        }
        return Promise.resolve(docs)
      })
      .catch(error => {
        console.error(`Failed to access ${what} (composite query)`, error)
        return Promise.reject(error)
      })
  }
}
