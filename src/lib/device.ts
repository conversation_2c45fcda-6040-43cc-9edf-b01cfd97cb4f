import { Dimensions, Platform } from 'react-native'

export const isNative = Platform.OS === 'ios' || Platform.OS === 'android'
export const isWeb = Platform.OS === 'web'
export const isAndroid = Platform.OS === 'android'
export const isIOS = Platform.OS === 'ios'
export const isDev = isNative ? __DEV__ : process.env.EXPO_PUBLIC_TARGET_ENV !== 'prod'
export const isProd = isNative ? !__DEV__ : process.env.EXPO_PUBLIC_TARGET_ENV === 'prod'
export const isLocal = process.env.NODE_ENV === 'development'
export const isJest = process.env.NODE_ENV === 'test'

export const getOS = (): 'ios' | 'windows' | 'macOS' | undefined => {
  if (/windows/i.test(navigator.userAgent)) {
    return 'windows'
  } else if (/iphone/i.test(navigator.userAgent)) {
    return 'ios'
  } else if (/ipad/i.test(navigator.userAgent)) {
    return 'ios'
  } else if (/macintosh/i.test(navigator.userAgent)) {
    return 'macOS'
  }
}

/** Static width of the window when the app is loaded */
export const staticViewportWidth = Dimensions.get('window').width
/** Static height of the window when the app is loaded */
export const staticViewportHeight = Dimensions.get('window').height

export enum DEVICE_SIZES {
  EXTRA_SMALL_DEVICE = 'extrasmall',
  SMALL_DEVICE = 'small',
  MEDIUM_DEVICE = 'medium',
  LARGE_DEVICE = 'large',
  EXTRA_LARGE_DEVICE = 'extralarge',
}

/**
 * Returns a enum value based on the width of the screen thats determined by the breakpoints
 * @param width 500
 * @returns extrasmall
 */
export const getDeviceSize = (width: number): DEVICE_SIZES => {
  if (width >= 1200) return DEVICE_SIZES.EXTRA_LARGE_DEVICE
  if (width >= 1024) return DEVICE_SIZES.LARGE_DEVICE
  if (width >= 768) return DEVICE_SIZES.MEDIUM_DEVICE
  if (width >= 540) return DEVICE_SIZES.SMALL_DEVICE
  return DEVICE_SIZES.EXTRA_SMALL_DEVICE
}

/**
 * Returns an object that will return true for isMobileWidth, isTabletWidth or isDesktopWidth and false for the rest
 *
 * @param size extrasmall
 *
 * returns { isMobileWidth: true, isTabletWidth: false, isDesktopWidth: false }
 */
export const getDeviceWidth = (size: DEVICE_SIZES) => ({
  isMobileWidth: size === DEVICE_SIZES.EXTRA_SMALL_DEVICE,
  isTabletWidth: size === DEVICE_SIZES.SMALL_DEVICE || size === DEVICE_SIZES.MEDIUM_DEVICE,
  isDesktopWidth: size === DEVICE_SIZES.LARGE_DEVICE || size === DEVICE_SIZES.EXTRA_LARGE_DEVICE,
  isLargeDesktopWidth: size === DEVICE_SIZES.EXTRA_LARGE_DEVICE,
})
