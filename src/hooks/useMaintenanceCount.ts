import { sharedState } from '@src/shared-state/shared-state'
import { useMemo } from 'react'
import { calculateStatusBatteryCount, emptyStatusBattery } from '../lib/statusBattery'
import { warnDays } from '../lib/datesAndTime'
import { jobPriorities } from '@src/shared-state/VesselMaintenance/jobs'

/**
 * Custom hook to get the maintenance counts for a vessel.
 *
 * @returns An object containing the maintenance schedule count, job list count, and spare parts count.
 */
export const useMaintenanceCount = () => {
  const scheduledMaintenanceTasks = sharedState.scheduledMaintenanceTasks.use(2)
  const vesselSystems = sharedState.vesselSystems.use(2)

  const jobs = sharedState.jobs.use(2)
  const spareParts = sharedState.spareParts.use(2)

  const maintenanceScheduleCount = useMemo(() => {
    if (!vesselSystems?.ids || !scheduledMaintenanceTasks) {
      return {
        ...emptyStatusBattery,
      }
    }

    const warnThreshold = warnDays.safetyEquipmentChecks[0]

    return vesselSystems.ids.reduce(
      (parentAcc, systemId) => {
        if (!scheduledMaintenanceTasks.bySystemId[systemId]) {
          return parentAcc
        }

        const systemCount = scheduledMaintenanceTasks.bySystemId[systemId].reduce(
          (acc, item) => {
            if (item.useHours) {
              if (item.engineHoursLeft) {
                const engineHoursLeft = item.engineHoursLeft
                if (engineHoursLeft < 0) {
                  return {
                    ...acc,
                    Error: acc.Error + 1,
                  }
                } else if (engineHoursLeft < 50) {
                  return {
                    ...acc,
                    Warning: acc.Warning + 1,
                  }
                }
              }
            } else {
              const count = calculateStatusBatteryCount(item.dateDue, warnThreshold, acc)

              return count
                ? {
                    ...acc,
                    ...count,
                  }
                : acc
            }
            return acc
          },
          {
            ...emptyStatusBattery,
          }
        )

        return {
          ...parentAcc,
          Critical: parentAcc.Critical + systemCount.Critical,
          Error: parentAcc.Error + systemCount.Error,
          Attention: parentAcc.Attention + systemCount.Attention,
          Warning: parentAcc.Warning + systemCount.Warning,
          Minor: parentAcc.Minor + systemCount.Minor,
          Ok: parentAcc.Ok + systemCount.Ok,
        }
      },
      {
        ...emptyStatusBattery,
      }
    )
  }, [scheduledMaintenanceTasks, vesselSystems, warnDays])

  const jobListCount = useMemo(() => {
    return Object.keys(jobPriorities).reduce(
      (acc, priorityId) => {
        switch (priorityId) {
          case '8urgent':
            return {
              ...acc,
              Critical: jobs?.byPriority[priorityId]?.length ?? acc.Critical,
            }
          case '6high':
            return {
              ...acc,
              Error: jobs?.byPriority[priorityId]?.length ?? acc.Error,
            }
          case '4medium':
            return {
              ...acc,
              Warning: jobs?.byPriority[priorityId]?.length ?? acc.Warning,
            }
          case '2low':
            return {
              ...acc,
              Minor: jobs?.byPriority[priorityId]?.length ?? acc.Minor,
            }
          default:
            return acc
        }
      },
      {
        ...emptyStatusBattery,
      }
    )
  }, [jobs])

  const sparePartsCount = useMemo(() => {
    if (!spareParts) {
      return {
        ...emptyStatusBattery,
      }
    }

    return spareParts?.all.reduce(
      (acc, item) => {
        if (item.orderQuantity && item.quantity === 0) {
          return {
            ...acc,
            Critical: acc.Critical + 1,
          }
        } else if (item.orderQuantity) {
          return {
            ...acc,
            Error: acc.Error + 1,
          }
        } else {
          return {
            ...acc,
            Ok: acc.Ok + 1,
          }
        }
      },
      {
        ...emptyStatusBattery,
      }
    )
  }, [spareParts])

  return {
    maintenanceScheduleCount,
    jobListCount,
    sparePartsCount,
  }
}
