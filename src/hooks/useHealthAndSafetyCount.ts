import { sharedState } from '@src/shared-state/shared-state'
import { useMemo } from 'react'
import { emptyStatusBattery } from '../lib/statusBattery'
import { getDayOffset, getToday, makeDateTime, warnDays } from '../lib/datesAndTime'

/**
 * Custom hook to get the health and safety counts for a vessel.
 *
 * @param vesselId
 * @returns An object containing the incident count, risk assessment count, and dangerous goods count.
 */
export const useHealthAndSafetyCount = ({ vesselId }: { vesselId?: string }) => {
  const incidents = sharedState.incidents.use(4)
  const risks = sharedState.risks.use(4)
  const dangerousGoods = sharedState.dangerousGoods.use(4)

  const todayMillis = sharedState.todayMillis.use(4)! as number

  const incidentCount = useMemo(() => {
    if (!incidents || !vesselId || !incidents.byVesselId[vesselId]) {
      return {
        ...emptyStatusBattery,
      }
    }

    return incidents.byVesselId[vesselId].reduce(
      (acc, incident) => {
        switch (incident.state) {
          case 'forReview':
            return {
              ...acc,
              Error: acc.Error + 1,
            }
          case 'inReview':
            return {
              ...acc,
              Warning: acc.Warning + 1,
            }
          default:
            return {
              ...acc,
              Ok: acc.Ok + 1,
            }
        }
      },
      {
        ...emptyStatusBattery,
      }
    )
  }, [vesselId, incidents])

  const today = useMemo(() => {
    return getToday()
  }, [todayMillis])

  const dayUpcoming = useMemo(() => {
    return getDayOffset(warnDays.riskRegister[0])
  }, [todayMillis])

  const riskAssessmentCount = useMemo(() => {
    if (!risks || !vesselId || !risks.byVesselId[vesselId]) {
      return {
        ...emptyStatusBattery,
      }
    }

    return Object.keys(risks.byVesselId[vesselId].byCategoryId).reduce(
      (parentAcc, categoryId) => {
        const countByEachCategory = risks.byVesselId[vesselId].byCategoryId[categoryId].reduce(
          (acc, risk) => {
            if (risk.dateDue < today) {
              return {
                ...acc,
                Error: acc.Error + 1,
              }
            } else if (risk.dateDue < dayUpcoming || risk.dateDue === today) {
              return {
                ...acc,
                Warning: acc.Warning + 1,
              }
            } else {
              return {
                ...acc,
                Ok: acc.Ok + 1,
              }
            }
          },
          {
            ...emptyStatusBattery,
          }
        )

        return {
          ...parentAcc,
          Critical: parentAcc.Critical + countByEachCategory.Critical,
          Error: parentAcc.Error + countByEachCategory.Error,
          Warning: parentAcc.Warning + countByEachCategory.Warning,
          Attention: parentAcc.Attention + countByEachCategory.Attention,
          Minor: parentAcc.Minor + countByEachCategory.Minor,
          Ok: parentAcc.Ok + countByEachCategory.Ok,
        }
      },
      {
        ...emptyStatusBattery,
      }
    )
  }, [risks, today, dayUpcoming, vesselId])

  const dangerousGoodsCount = useMemo(() => {
    if (!dangerousGoods || !vesselId || !dangerousGoods.byVesselId[vesselId]) {
      return {
        ...emptyStatusBattery,
      }
    }

    const warnDaysThreshold = warnDays.dangerousGoods[0]

    return dangerousGoods.byVesselId[vesselId].reduce(
      (acc, item) => {
        if (!item.dateExpires) {
          return {
            ...acc,
            Ok: acc.Ok + 1,
          }
        }

        const due = makeDateTime(item.dateExpires) // Crunch day to match local timezone
        const days = Math.round(due.diff(makeDateTime(getToday()), ['days']).days)

        if (days < 0) {
          return {
            ...acc,
            Error: acc.Error + 1,
          }
        } else if (days === 0 || days < warnDaysThreshold) {
          return {
            ...acc,
            Warning: acc.Warning + 1,
          }
        } else {
          return {
            ...acc,
            Ok: acc.Ok + 1,
          }
        }
      },
      {
        ...emptyStatusBattery,
      }
    )
  }, [dangerousGoods, vesselId])

  return {
    incidentCount,
    riskAssessmentCount,
    dangerousGoodsCount,
  }
}
