import { useEffect, useMemo, useState } from 'react'
import { useWindowDimensions } from 'react-native'
import { DEVICE_SIZES, getDeviceSize, getDeviceWidth } from '@src/lib/device'

/**
 * Custom hook to determine the device size based on window dimensions.
 *
 * @returns {Object} An object containing the width and boolean flags for device types.
 */
export const useDeviceSize = (): DEVICE_SIZES => {
  const { width } = useWindowDimensions()
  const [deviceSize, setDeviceSize] = useState(getDeviceSize(width))

  useEffect(() => {
    const newDeviceSize = getDeviceSize(width)
    if (deviceSize !== newDeviceSize) {
      setDeviceSize(newDeviceSize)
    }
  }, [width])

  return deviceSize
}
/**
 * Custom hook to determine the width size based on window dimensions.
 * Returns an object that will return true for isMobileWidth, isTabletWidth or isDesktopWidth and false for the rest
 *
 * returns { isMobileWidth: true, isTabletWidth: false, isDesktopWidth: false }
 */
export const useDeviceWidth = () => {
  const deviceSize = useDeviceSize()
  return useMemo(() => getDeviceWidth(deviceSize), [deviceSize])
}
