import { useMemo } from 'react'
import { getDateDifferenceInDays, warnDays } from '@src/lib/datesAndTime'
import { DateTime } from 'luxon'

export const useStatusFilter = <T>(
  items: T[],
  getDateField: (data: T) => string | undefined,
  warnThreshold: number
) => {
  return useMemo(() => {
    return items.reduce(
      (result, item) => {
        const dateDue = getDateField(item)

        if (!dateDue) return result

        const daysRemaining = getDateDifferenceInDays(DateTime.fromISO(dateDue))

        if (daysRemaining < 0) result.overdue.push(item)

        if (daysRemaining > 0 && daysRemaining < warnThreshold) result.upcoming.push(item)

        return result
      },
      {
        overdue: [] as T[],
        upcoming: [] as T[],
      }
    )
  }, [items])
}
