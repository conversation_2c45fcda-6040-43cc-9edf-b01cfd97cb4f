import { PermissionRole } from '@src/shared-state/Core/userPermissions'
import { sharedState } from '@src/shared-state/shared-state'
import { useMemo } from 'react'

type RequireLicensee = {
  level?: number
  requireLicensee: boolean
}

type RequireLevel = {
  level: number
  requireLicensee?: boolean
}

type LevelBasedPermission = Partial<Record<PermissionRole, RequireLevel>>

type LicenseeBasedPermission = Partial<Record<'hasIncidents' | 'hasReporting', RequireLicensee>>

interface PermissionProps {
  modules: LevelBasedPermission | LicenseeBasedPermission
}

/**
 * Custom hook to check user permissions based on the provided modules.
 *
 * @param modules - An object containing module permissions.
 * @returns An object indicating whether the user has permission for each module.
 */
export const usePermission = <T extends PermissionProps['modules']>({ modules }: PermissionProps) => {
  const userPermissions = sharedState.userPermissions.use()
  const licenseeSettings = sharedState.licenseeSettings.use()

  const hasPermission = useMemo(() => {
    if (!userPermissions) return {} as Record<keyof T, boolean> // Handle undefined state

    const filteredModules: Record<keyof T, boolean> = {} as Record<keyof T, boolean>

    Object.entries(modules).forEach(([moduleId, { level, requireLicensee }]) => {
      if (requireLicensee && licenseeSettings) {
        filteredModules[moduleId as keyof T] = licenseeSettings[moduleId as keyof typeof licenseeSettings] !== false
      } else if (!requireLicensee && level) {
        filteredModules[moduleId as keyof T] = userPermissions[moduleId as PermissionRole] >= level
      } else {
        filteredModules[moduleId as keyof T] = false
      }
    })

    return filteredModules
  }, [userPermissions])

  return hasPermission as Record<keyof T, boolean>
}
