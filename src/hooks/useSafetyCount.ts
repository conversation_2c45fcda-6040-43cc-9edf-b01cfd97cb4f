import { sharedState } from '@src/shared-state/shared-state'
import { useMemo } from 'react'
import { warnDays } from '../lib/datesAndTime'
import { calculateStatusBatteryCount, emptyStatusBattery } from '../lib/statusBattery'
import { VesselDrillsData } from '@src/shared-state/VesselSafety/vesselDrills'
import { DrillReportsData } from '@src/shared-state/VesselSafety/drillReports'
import { UserType } from '@src/shared-state/Core/user'
import { Drill } from '@src/shared-state/VesselSafety/drills'

/* Migrates the drill to support the `crew` and `notAssignedTo` props */
const hydrateDrills = (
  vesselDrills?: VesselDrillsData,
  drillReports?: DrillReportsData,
  drillUsers?: UserType[]
): Drill[] => {
  if (!vesselDrills || !drillReports || !drillUsers) return []

  return vesselDrills.all.map(drill => {
    const crew = drillUsers.reduce(
      (result: any, user) => {
        if (!user.id) return result // Skip if we don't have a user ID

        if (!!drill.crew && drill.crew[user.id]) {
          // If the user already exists under the new data system, use that
          return {
            ...result,
            [user.id]: drill.crew[user.id],
          }
        }

        const key = `${drill.id}${user.id}`
        const lastDrill = drillReports.byDrillAndUser[key]

        if (!lastDrill?.dateDue || !lastDrill?.report?.dateCompleted) return result

        return {
          ...result,
          [user.id]: {
            dateDue: lastDrill.dateDue,
            dateLastCompleted: lastDrill.report.dateCompleted,
          },
        }
      },
      {} as Record<string, Record<string, number>>
    )

    return { ...drill, crew } as Drill
  })
}

interface UseSafetyCountProps {
  vesselId?: string
}

/**
 * Custom hook to get the safety counts for a vessel.
 *
 * @param vesselId
 * @returns An object containing the safety check count, safety equipment expiry count, and drill count.
 */
export const useSafetyCount = ({ vesselId }: UseSafetyCountProps) => {
  const users = sharedState.users.use() // TODO - Permissions on this?

  const safetyCheckItems = sharedState.safetyCheckItems.use(1)
  const safetyEquipmentItems = sharedState.safetyEquipmentItems.use(1)
  const vesselDrills = sharedState.vesselDrills.use(1)
  const drillReports = sharedState.drillReports.use(1)

  const safetyCheckCount = useMemo(() => {
    if (!safetyCheckItems?.all || !warnDays) {
      return {
        ...emptyStatusBattery,
      }
    }
    const warnThreshold = warnDays.safetyEquipmentChecks[0]

    return safetyCheckItems?.all.reduce(
      (acc, item) => {
        if (item?.hasFault) {
          return {
            ...acc,
            Critical: acc.Critical + 1,
          }
        }

        const updatedAcc = calculateStatusBatteryCount(item.dateDue, warnThreshold, acc)
        return {
          ...acc,
          ...updatedAcc,
        }
      },
      { ...emptyStatusBattery }
    )
  }, [safetyCheckItems, warnDays])

  const safetyEquipmentExpiryCount = useMemo(() => {
    const warnThreshold = warnDays.safetyEquipmentExpiries[0]

    const serviceableEquipmentCount = safetyEquipmentItems?.all.servicable.reduce(
      (acc, item) => {
        if (item?.hasFault) {
          return {
            ...acc,
            Critical: acc.Critical + 1,
          }
        }

        const updatedAcc = calculateStatusBatteryCount(item.dateDue, warnThreshold, acc)
        return {
          ...acc,
          ...updatedAcc,
        }
      },
      { ...emptyStatusBattery }
    )

    const expiringEquipmentCount = safetyEquipmentItems?.all.expiring.reduce(
      (acc, item) => {
        if (item?.hasFault) {
          return {
            ...acc,
            Critical: acc.Critical + 1,
          }
        }

        const updatedAcc = calculateStatusBatteryCount(item.dateDue, warnThreshold, acc)
        return {
          ...acc,
          ...updatedAcc,
        }
      },
      { ...emptyStatusBattery }
    )

    return {
      ...emptyStatusBattery,
      Critical: (serviceableEquipmentCount?.Critical ?? 0) + (expiringEquipmentCount?.Critical ?? 0),
      Error: (serviceableEquipmentCount?.Error ?? 0) + (expiringEquipmentCount?.Error ?? 0),
      Warning: (serviceableEquipmentCount?.Warning ?? 0) + (expiringEquipmentCount?.Warning ?? 0),
      Attention: (serviceableEquipmentCount?.Attention ?? 0) + (expiringEquipmentCount?.Attention ?? 0),
      Minor: (serviceableEquipmentCount?.Minor ?? 0) + (expiringEquipmentCount?.Minor ?? 0),
      Ok: (serviceableEquipmentCount?.Ok ?? 0) + (expiringEquipmentCount?.Ok ?? 0),
    }
  }, [safetyEquipmentItems, warnDays])

  const drillUsers = useMemo(() => {
    if (!vesselId || !users?.byVesselId) return []

    return users?.byVesselId[vesselId]
      .filter(user => user.isStaff) // We don't want to show non-staff users
      .filter(user => user.crewVesselIds === undefined || user.crewVesselIds.includes(vesselId)) // If list is undefined, just treat the user as crew (safe default)
  }, [vesselId, users])

  const newDrillsModel = useMemo(
    () => hydrateDrills(vesselDrills, drillReports, drillUsers),
    [vesselDrills, drillReports, drillUsers]
  )

  const drillCount = useMemo(() => {
    return drillUsers.reduce(
      (acc, user) => {
        const userDrillData = newDrillsModel.reduce(
          (childAcc, drill) => {
            const crewData =
              drill.crew && user.id ? drill?.crew[user.id] : { dateDue: undefined, dateLastCompleted: undefined }

            const isDrillUnassigned = (!!user.id && drill.notAssignedTo?.includes(user.id)) ?? false

            if (isDrillUnassigned) {
              return childAcc
            }

            if (!crewData?.dateDue || !crewData.dateLastCompleted) {
              return {
                ...childAcc,
                Attention: childAcc.Attention + 1,
              }
            }

            const updatedAcc = calculateStatusBatteryCount(crewData.dateDue, warnDays.drills[0], childAcc)
            return {
              ...childAcc,
              ...updatedAcc,
            }
          },
          {
            ...emptyStatusBattery,
          }
        )

        return {
          ...acc,
          Critical: acc.Critical + userDrillData.Critical,
          Error: acc.Error + userDrillData.Error,
          Warning: acc.Warning + userDrillData.Warning,
          Attention: acc.Attention + userDrillData.Attention,
          Ok: acc.Ok + userDrillData.Ok,
        }
      },
      {
        ...emptyStatusBattery,
      }
    )
  }, [drillUsers, newDrillsModel])

  return {
    safetyCheckCount,
    safetyEquipmentExpiryCount,
    drillCount,
  }
}
