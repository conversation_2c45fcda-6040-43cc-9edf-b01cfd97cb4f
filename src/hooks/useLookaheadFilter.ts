import { useMemo } from 'react'
import { useLogger } from '@src/providers/ServiceProvider'
import { addInterval } from '@src/lib/datesAndTime'
import { DateTime } from 'luxon'

export interface useLookaheadFilterProps<T> {
  items: T[]
  getDateField: (data: T) => string | undefined
  lookaheadValue?: string
}

export const useLookaheadFilter = <T>({ items, getDateField, lookaheadValue }: useLookaheadFilterProps<T>) => {
  const logger = useLogger('useLookaheadFilter')
  const filteredValues = useMemo(() => {
    // Don't filter if it's not set
    if (!lookaheadValue) return items

    const filteredItems = items
      .filter(item => {
        const dateString = getDateField(item)

        if (!dateString) return false

        const dateDue = DateTime.fromISO(dateString)

        const thresholdDate = addInterval(DateTime.now(), lookaheadValue)

        return dateDue < thresholdDate
      })
      .sort((a, b) => {
        const aDate = getDateField(a)
        const bDate = getDateField(b)

        if (!aDate || !bDate) return 0

        return aDate.localeCompare(bDate)
      })

    logger.debug('Recalculated new list of items based on new lookahead value', {
      lookaheadValue,
      filteredItems,
    })

    return filteredItems
  }, [items, lookaheadValue, getDateField])

  return filteredValues
}
