import { sharedState } from '@src/shared-state/shared-state'
import { useMemo } from 'react'
import { warnDays } from '../lib/datesAndTime'
import { calculateStatusBatteryCount, emptyStatusBattery } from '../lib/statusBattery'

/**
 * Custom hook to get the vessel register counts.
 *
 * @returns An object containing the vessel certificates count, vessel documents count, and SOP count.
 */
export const useVesselRegisterCount = () => {
  const vesselCertificates = sharedState.vesselCertificates.use(3)
  const vesselDocuments = sharedState.vesselDocuments.use(3)
  const SOPs = sharedState.vesselSOPs.use(3)

  const vesselCertificatesCount = useMemo(() => {
    if (!vesselCertificates) {
      return {
        ...emptyStatusBattery,
      }
    }

    return vesselCertificates?.all.reduce(
      (acc, certificate) => {
        return calculateStatusBatteryCount(certificate.dateExpires, warnDays.vesselCertificates[0], acc)
      },
      {
        ...emptyStatusBattery,
      }
    )
  }, [vesselCertificates])

  const vesselDocumentsCount = useMemo(() => {
    if (!vesselDocuments) {
      return {
        ...emptyStatusBattery,
      }
    }

    return vesselDocuments?.all.reduce(
      (acc, certificate) => {
        return calculateStatusBatteryCount(certificate.dateExpires, warnDays.vesselDocuments[0], acc)
      },
      {
        ...emptyStatusBattery,
      }
    )
  }, [vesselDocuments])

  const SOPCount = useMemo(() => {
    if (!SOPs) {
      return {
        ...emptyStatusBattery,
        Ok: 0,
      }
    }

    return {
      ...emptyStatusBattery,
      Ok: Object.keys(SOPs.byId).length,
    }
  }, [SOPs])

  return {
    vesselCertificatesCount,
    vesselDocumentsCount,
    SOPCount,
  }
}
