import { UserType } from '@src/shared-state/Core/user'
import { Drill } from '@src/shared-state/VesselSafety/drills'
import { sharedState } from '@src/shared-state/shared-state'
import { useMemo } from 'react'
import { VesselDrillsData } from '@src/shared-state/VesselSafety/vesselDrills'
import { UsersData } from '@src/shared-state/Core/users'
import { getDateDifferenceInDays, warnDays } from '@src/lib/datesAndTime'
import { DateTime } from 'luxon'
import { SeaStatusType } from '@src/types/Common'
import { DrillReport, DrillReportsData } from '@src/shared-state/VesselSafety/drillReports'

export enum TrainingStatus {
  Overdue = 'overdue',
  Upcoming = 'upcoming',
  Missing = 'missing',
  Ok = 'ok',
  Unassigned = 'unassigned',
}

export type DrillData = {
  status: TrainingStatus
  dateDue?: string
  dateLastCompleted?: string
  daysRemaining?: number
}

export type DrillsData = Record<string, DrillData>

export type UserDrillsData = {
  user: UserType
  drillsData: DrillsData
  /** The 'highest' status of ANY of the user's drills */
  hasOverdue: boolean
}

export type UserDrillWithReports = {
  drillData: DrillData
  reports: DrillReport[]
}

export type DrillsMatrixData = {
  users: UserDrillsData[]
  drills: Drill[]
}

function getDrillUsers(vesselId: string, users?: UserType[]) {
  if (!users) return []

  return users.filter(user => {
    return user.state === 'active' && user.crewVesselIds?.includes(vesselId) && user.isStaff
  })
}

function getVesselDrills(drills?: Drill[]) {
  if (!drills) return []

  return drills.filter(drill => drill.state === 'active')
}

function getUserDrillsData(drillUsers: UserType[], vesselDrills: Drill[]): UserDrillsData[] {
  return drillUsers.map(user => {
    const drillsData = getDrillsData(user, vesselDrills)

    return {
      user,
      drillsData: drillsData,
      hasOverdue: Object.values(drillsData).some(drillData => drillData.status === TrainingStatus.Overdue),
    }
  })
}

function getDrillsData(user: UserType, vesselDrills: Drill[]): DrillsData {
  const result: DrillsData = {}

  vesselDrills.forEach(drill => {
    // MISSING
    if (!drill.crew || !user.id) {
      result[drill.id] = {
        status: TrainingStatus.Missing,
      }
      return
    }

    // UNASSIGNED
    if (drill.notAssignedTo?.includes(user.id)) {
      result[drill.id] = {
        status: TrainingStatus.Unassigned,
      }
      return
    }

    const userData = drill.crew[user.id]

    // MISSING
    if (!userData?.dateDue || !userData?.dateLastCompleted) {
      result[drill.id] = { status: TrainingStatus.Missing }
      return
    }

    const daysRemaining = getDateDifferenceInDays(DateTime.fromISO(userData.dateDue), sharedState.todayMillis.current)

    result[drill.id] = {
      status:
        daysRemaining < 0
          ? TrainingStatus.Overdue
          : daysRemaining < warnDays.drills[0]
            ? TrainingStatus.Upcoming
            : TrainingStatus.Ok,
      dateCompleted: userData.dateDue,
      dateLastCompleted: userData.dateLastCompleted,
      daysRemaining,
    }
  })

  return result
}

export const useDrillsMatrix = (
  vesselId: string,
  vesselDrills?: Drill[],
  users?: UserType[]
): DrillsMatrixData | undefined => {
  const drillUsers = useMemo(() => getDrillUsers(vesselId, users), [users])

  const drills = useMemo(() => getVesselDrills(vesselDrills), [vesselDrills])

  const userDrillsData = useMemo(() => getUserDrillsData(drillUsers, drills), [drillUsers, vesselDrills])

  return {
    users: userDrillsData,
    drills,
  }
}

export const useUserDrills = (vesselDrills: Drill[], user?: UserType): UserDrillsData | undefined => {
  // if (!user) return undefined;

  const drills = useMemo(() => getVesselDrills(vesselDrills), [vesselDrills])

  const userDrillsData = useMemo(() => getUserDrillsData(user ? [user] : [], drills), [user, vesselDrills])

  return user ? userDrillsData[0] : undefined
}

export const useUserDrillWithReports = (
  drillId: string,
  vesselDrills: Drill[],
  drillReports?: DrillReportsData,
  user?: UserType
): UserDrillWithReports | undefined => {
  const userDrills = useUserDrills(vesselDrills, user)

  const userDrill = useMemo(() => {
    return userDrills?.drillsData[drillId]
  }, [userDrills])

  const drillReportsForDrill = useMemo(() => {
    if (!drillReports || !user?.id) return undefined

    const reports = drillReports.allReports?.filter(
      x => x.drillIds && x.state === 'active' && x.drillIds.includes(drillId)
    )

    return reports.filter(x => x.crewInvolvedIds?.includes(user?.id))
  }, [drillReports, drillId, user])

  if (!userDrill) return undefined

  return {
    drillData: userDrill,
    reports: drillReportsForDrill ?? [],
  }
}
