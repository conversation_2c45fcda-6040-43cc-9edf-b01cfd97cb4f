import { sharedState } from '@src/shared-state/shared-state'
import { useMemo } from 'react'

/**
 * Custom hook to get licensee settings.
 *
 * @returns An object containing the hasTimeTrackingEnabled property.
 */
export const useLicenseeSettings = () => {
  const licenseeSettings = sharedState.licenseeSettings.use()

  const hasTimeTrackingEnabled = useMemo(() => {
    return licenseeSettings?.hasMaintenanceTaskTime ?? false
  }, [licenseeSettings])

  return {
    hasTimeTrackingEnabled,
  }
}
