import React from 'react'
import { FlatList, FlatListProps } from 'react-native'

interface ScrollablePageLayoutProps<T> extends Omit<FlatListProps<T>, 'data' | 'renderItem' | 'keyExtractor'> {}

/**
 * This component is used to wrap the content of a page that needs to be scrollable for specially
 * large Table components and data
 *
 * Notes: This flat list loads the children as a single item
 *
 * @constructor
 */
export const ScrollablePageLayout = ({ children, ...props }: ScrollablePageLayoutProps<T>) => {
  return (
    <FlatList
      {...props}
      /** This is magic is to load the flat list where all the children are loaded as a single item */
      data={['pageLayout'] as any[]}
      renderItem={() => (
        <>
          {/** The page content is loaded here */}
          {children}
        </>
      )}
    />
  )
}
