import { SeaScrollable } from '@src/components/_legacy/SeaScrollableArea/SeaScrollableArea'
import { sharedState, SharedStateConfig } from '@src/shared-state/shared-state'
import React from 'react'
import { View, StatusBar } from 'react-native'

export type LayoutMode = 'standard' | 'centeredBox' | 'superAdmin'

export const layoutModeConfig: SharedStateConfig<LayoutMode> = {
  isAlwaysActive: true,
  default: 'centeredBox',
}

export const selectedSectionTabConfig: SharedStateConfig<string> = {
  isAlwaysActive: true,
  notes: 'Is changed by clicking within the sidebar or clicking tabs within a base page.',
}

export const selectedSectionConfig: SharedStateConfig<string> = {
  isAlwaysActive: true,
  notes: 'Is changed by clicking within the sidebar.',
}

export const scrollableContentConfig: SharedStateConfig<SeaScrollable> = {
  isAlwaysActive: true,
  notes: "Stores the current active page's scrollable area enabling scrollToTop & scrollToTarget.",
}

export const showBlankScreenConfig: SharedStateConfig<boolean> = {
  isAlwaysActive: true,
  default: false,
  notes: 'Used to show a blank loading screen while the app is restarting or reloading itself.',
}

export const scrollPageToTop = () => {
  sharedState.selectedSectionTab.set(undefined)
  sharedState.selectedSection.set(undefined)
}

// Main Layout Component
const AppLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <View style={{ flex: 1 }}>
      <StatusBar barStyle={'light-content'} backgroundColor={'#373946'} />

      {children}
    </View>
  )
}

export default AppLayout
