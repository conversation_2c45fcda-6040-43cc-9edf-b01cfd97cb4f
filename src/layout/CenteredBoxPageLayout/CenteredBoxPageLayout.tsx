import { sharedState } from '@src/shared-state/shared-state'
import React, { ReactNode, useEffect } from 'react'
import { View, Image, ScrollView, useWindowDimensions } from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { styles } from './CenteredBoxPageLayoutStyle'

interface CenteredBoxPageLayoutProps {
  children: ReactNode
  boxMode?: 'form' | 'message'
}

const CenteredBoxPageLayout: React.FC<CenteredBoxPageLayoutProps> = ({ children, boxMode = 'form' }) => {
  const { height } = useWindowDimensions()

  useEffect(() => {
    sharedState.layoutMode.set('centeredBox')
  }, [])

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView contentContainerStyle={{ flex: 1 }} style={styles.scrollView}>
        <View style={styles.container}>
          <View style={styles.contentWrapper}>
            <Image source={require('@assets/images/seaflux.png')} style={styles.logo} resizeMode="contain" />
            <View style={boxMode === 'form' ? styles.formBox : boxMode === 'message' ? styles.messageBox : ''}>
              {children}
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

export default CenteredBoxPageLayout
