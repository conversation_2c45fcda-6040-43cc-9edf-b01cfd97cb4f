import { StyleSheet, Dimensions } from 'react-native'
import { colors, fonts } from '@src/theme/globalStyle'

const screenWidth = Dimensions.get('window').width

export const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.header, // Assuming you have a colors object with a header color
  },
  scrollView: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16, // px-4 is typically 16px
  },
  contentWrapper: {
    width: '100%',
    maxWidth: 400,
  },
  logo: {
    width: 300,
    maxWidth: '90%',
    height: 80, // h-20 is typically 80px
    marginBottom: 32, // mb-8 is typically 32px
    alignSelf: 'center',
  },
  formBox: {
    backgroundColor: 'white',
    padding: 24, // p-6 is typically 24px
    borderRadius: 8, // rounded-lg is typically 8px
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  messageBox: {
    // No direct equivalent for text-white, as it would typically be applied to Text components
  },
  messageText: {
    fontFamily: fonts.sea, // Assuming you have a fonts object with a sea property
    fontSize: 18, // text-lg is typically 18px
    color: 'white', // Adding this here instead of in messageBox
  },
})
