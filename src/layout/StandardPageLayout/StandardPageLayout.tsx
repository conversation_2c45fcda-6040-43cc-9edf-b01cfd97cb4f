import React, { ReactNode, useEffect } from 'react'
import { View, StyleSheet, ScrollView, FlatList } from 'react-native'
import { sharedState } from '@src/shared-state/shared-state'
import SeaFooter from '@src/components/_legacy/SeaFooter/SeaFooter'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { colors } from '@src/theme/colors'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'

interface StandardPageLayoutProps {
  children: ReactNode
}

const StandardPageLayout: React.FC<StandardPageLayoutProps> = ({ children }) => {
  useEffect(() => {
    sharedState.layoutMode.set('standard')
  }, [])

  // Determine layout based on screen width
  const { isDesktopWidth, isTabletWidth, isMobileWidth } = useDeviceWidth()

  return (
    <View
      style={[
        styles.container,
        isDesktopWidth && {
          paddingTop: 12,
          paddingLeft: 12,
        },
      ]}>
      <View
        style={[
          styles.wrapper,
          isDesktopWidth && {
            borderTopLeftRadius: 36,
          },
        ]}>
        <View
          style={[
            styles.content,
            isMobileWidth && styles.mobileContent,
            isTabletWidth && styles.tabletContent,
            isDesktopWidth && styles.desktopContent,
          ]}>
          {children}
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexGrow: 1,
    minHeight: '100%',
    backgroundColor: colors.white,
  },
  wrapper: {
    flex: 1,
    backgroundColor: colors.background.primary,
    borderColor: colors.borderColor,
    borderWidth: 2,
  },
  content: {
    flex: 1,
    flexGrow: 1,
    minHeight: '100%',
  },

  // Mobile styles
  mobileContainer: {
    marginLeft: 0,
  },
  mobileContent: {
    padding: 0,
  },

  // Tablet styles
  tabletContainer: {
    marginLeft: 60, // Adjust based on your side bar width
  },
  tabletContent: {
    padding: 10,
  },

  // Desktop styles
  desktopContainer: {
    marginLeft: 240, // Adjust based on your side menu width
  },
  desktopContent: {
    padding: 20,
  },
})

export default StandardPageLayout
