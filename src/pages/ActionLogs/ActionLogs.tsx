import { View, Text, ScrollView } from 'react-native'
import React, { useMemo } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { useGlobalSearchParams } from 'expo-router'
import { ActionLogEntry, maxOfflineActionLogDays } from '@src/shared-state/General/actionLog'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaTable, SeaTableColumn } from '@src/components/_atoms/SeaTable/SeaTable'
import { formatDateLonger, truncateText } from '@src/lib/util'
import { formatAction } from '@src/components/_molecules/ActionLog/ActionLog'
import { renderVesselsList } from '@src/shared-state/Core/vessels'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { useActionLog } from '@src/shared-state/General/useActionLog'

export default function ActionLogs() {
  const { vesselId } = useGlobalSearchParams()
  const actionLog =
    useActionLog(
      vesselId ? true : false, // This should only be true if this is visible
      maxOfflineActionLogDays,
      vesselId as string | undefined // Todo! Not sure why vesselId could be a string[]
    ) ?? []
  const vessels = sharedState.vessels.use()
  const licenseeSettings = sharedState.licenseeSettings.use()
  const divisions = sharedState.divisions.use()

  const { styles } = useStyles(styleSheet)

  const vesselName = useMemo(() => {
    if (!vessels || !vesselId) return ''
    const vessel = vessels.all.find(v => v.id === vesselId)
    return vessel ? vessel.name : ''
  }, [vessels, vesselId])

  const buildColumns = useMemo(() => {
    return [
      {
        label: 'Date',
        value: x => formatDateLonger(x.when),
        style: { fontWeight: 'bold' },
        width: 120,
      },
      {
        label: 'Action',
        value: x => formatAction(x, licenseeSettings),
      },
      {
        label: 'Detail',
        value: x => x.detail ?? 'No details provided',
      },
      ...(!vesselId
        ? [
            {
              label: 'Vessel / Facilities',
              value: (x: ActionLogEntry) => renderVesselsList(x.vesselIds, vessels, divisions),
            },
          ]
        : []),
      {
        label: 'Done By',
        value: x => renderFullNameForUserId(x.userId),
      },
    ] as SeaTableColumn<ActionLogEntry>[]
  }, [licenseeSettings, vessels, divisions, vesselId])

  const rows = useMemo(() => buildRows(actionLog), [actionLog])

  return (
    <ScrollView style={styles.container}>
      <SeaPageCard
        titleComponent={<SeaPageCardTitle title={vesselName ? `Action Logs: ${vesselName}` : 'Action Logs'} />}
      />

      {/* Table View */}
      <View style={styles.tableView}>
        <ScrollView>
          <SeaTable
            columns={buildColumns}
            showGroupedTable
            rows={rows}
            sortFunction={(a, b) => {
              if (!a.when || !b.when) return 0
              return new Date(b.when).getTime() - new Date(a.when).getTime()
            }}
          />
        </ScrollView>
      </View>
    </ScrollView>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}))

const buildRows = (items: ActionLogEntry[]) => {
  return items.map(item => ({
    data: item,
    group: (item: ActionLogEntry) => formatDateLonger(item.when),
  }))
}
