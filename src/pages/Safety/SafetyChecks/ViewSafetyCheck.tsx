import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { ScrollView, StyleSheet, View } from 'react-native'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { formatDateShort, formatShortTimeDurationHrsMinsView, warnDays } from '@src/lib/datesAndTime'
import { formatInterval, formatValue } from '@src/lib/util'
import { renderCategoryName } from '@src/lib/categories'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SafetyCheckHistoryTable } from '@src/components/_organisms/Safety/SafetyChecks/SafetyChecksHistoryTable'
import {
  SafetyCheckCompleted,
  useCompletedSafetyCheckItems,
} from '@src/shared-state/VesselSafety/useCompletedSafetyCheckItems'
import { ModifySafetyCheckDrawer } from '@src/components/_organisms/Safety/SafetyChecks/ModifySafetyCheckDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { useLicenseeSettings } from '@src/hooks/useLicenseeSettings'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { usePathname, useRouter } from 'expo-router'
import { Routes } from '@src/navigation/constants'
import { getRoutePath } from '@src/navigation/utils'
import { SeaEmptyDivider } from '@src/components/_atoms/SeaDividers/SeaEmptyDivider'
import { useLogger, useServiceContainer } from '@src/providers/ServiceProvider'
import { DeleteSafetyCheckDto, DeleteSafetyCheckUseCase } from '@src/domain/use-cases/safety/DeleteSafetyCheckUseCase'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { ModifyCompletedSafetyCheckDrawer } from '@src/components/_organisms/Safety/SafetyChecks/ModifyCompletedSafetyCheckDrawer'

const DESKTOP_ITEMS_WIDTH = '100%'

interface ViewSafetyCheckProps {
  itemId: string
  vesselId: string
}

const ViewSafetyCheck = ({ itemId, vesselId }: ViewSafetyCheckProps) => {
  const pathname = usePathname()
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  const logger = useLogger('ViewSafetyCheck', { userId, licenseeId, pathname })

  // Global State
  const safetyChecks = sharedState.safetyCheckItems.use()
  const safetyCheckCategories = sharedState.safetyCheckCategories.use()
  const vesselSafetyItems = sharedState.vesselSafetyItems.use()
  const vesselLocations = sharedState.vesselLocations.use()

  // Internal State
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)

  const [isCompleteModalVisible, setIsCompleteModalVisible] = useState(false)
  const [completionModalMode, setCompletionModalMode] = useState(DrawerMode.Create)
  const [completedSafetyCheckId, setCompletedSafetyCheckId] = useState<string | undefined>(undefined)
  const [completedSafetyCheck, setCompletedSafetyCheck] = useState<SafetyCheckCompleted | undefined>(undefined)

  // Hooks
  const { hasTimeTrackingEnabled } = useLicenseeSettings()
  const { isMobileWidth, isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()
  const router = useRouter()
  const services = useServiceContainer()

  const item = safetyChecks?.byId[itemId]
  const historyItems = useCompletedSafetyCheckItems(item)

  const itemName = useMemo(() => {
    return renderCategoryName(item?.itemId, vesselSafetyItems)
  }, [item, vesselSafetyItems])
  const isCritical = useMemo(() => {
    return vesselSafetyItems?.byId[item?.itemId as string]?.isCritical ?? false
  }, [item, vesselSafetyItems])

  const handleDelete = useCallback(() => {
    if (!item || !vesselId || !userId || !licenseeId) {
      console.error('Vessel ID, Licensee ID, Item, or User ID is not available')
      return
    }

    const dto: DeleteSafetyCheckDto = {
      id: item.id,
      itemName: itemName,
      vesselId,
    }

    const deleteSafetyCheck = services.get(DeleteSafetyCheckUseCase)

    deleteSafetyCheck
      .execute(dto, userId, licenseeId)
      .then(() =>
        router.navigate({
          pathname: getRoutePath(Routes.SAFETY_EQUIPMENT_CHECKS),
          params: {
            vesselId,
          },
        })
      )
      .catch(err => console.error(`Error deleting safety check\n ${err.message}`))
  }, [item, vesselId, userId, licenseeId, itemName, services, router])

  const onDelete = useCallback(async () => {
    logger.debug("Clicked 'Delete' secondary action")
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  // Loading state
  if (!safetyChecks || !item) {
    logger.debug('Showing loading state', { safetyChecks, item })
    return <SeaLoadingSpinner variant={'propellor'} />
  }

  return (
    <RequirePermissions role={'safetyEquipmentList'} level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView showsVerticalScrollIndicator={false} style={styles.container}>
        <SeaPageCard
          primaryActionButton={
            <SeaButton
              onPress={() => {
                logger.debug("Clicked 'Complete' primary action")
                setCompletionModalMode(DrawerMode.Create)
                setIsCompleteModalVisible(true)
              }}
              variant={SeaButtonVariant.Primary}
              label={'Complete'}
              iconOptions={{ icon: 'check' }}
            />
          }
          secondaryActionButton={[
            <SeaDeleteButton key={'Delete'} onPress={onDelete} />,
            <SeaEditButton
              key={'Edit'}
              label={isMobileWidth ? undefined : 'Edit'}
              onPress={() => {
                logger.debug("Clicked 'Edit' secondary action")
                setIsEditModalVisible(true)
              }}
            />,
          ]}
          titleComponent={
            <SeaPageCardTitle
              title={itemName}
              additionalElements={
                item.dateDue ? (
                  <WhenDueStatus
                    whenDue={item.dateDue}
                    warnDaysThreshold={warnDays.safetyEquipmentChecks[0]}
                    hasFault={item.hasFault ?? false}
                  />
                ) : undefined
              }
              files={item.files}
            />
          }>
          <SeaPageCardContentSection>
            {/*    /!** Card 1 - START *!/*/}
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Location'}
                      iconOptions={{ icon: 'location_on' }}
                      showIcon={true}
                      value={renderCategoryName(item.locationId, vesselLocations)}
                    />
                    <SeaLabelValue
                      label={'Category'}
                      iconOptions={{ icon: 'category' }}
                      showIcon={true}
                      value={renderCategoryName(item.categoryId, safetyCheckCategories)}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Critical Equipment'}
                      iconOptions={{ icon: 'flag' }}
                      showIcon={true}
                      value={isCritical ? 'Yes' : 'No'}
                    />
                    <SeaLabelValue
                      label={'Interval'}
                      iconOptions={{ icon: 'update' }}
                      showIcon={true}
                      value={formatInterval(item.interval)}
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Next Check'}
                      iconOptions={{ icon: 'calendar_month' }}
                      showIcon={true}
                      value={formatDateShort(item.dateDue)}
                    />
                    <SeaLabelValue
                      label={'Assigned To'}
                      iconOptions={{ icon: 'person' }}
                      showIcon={true}
                      value={
                        item.assignedTo && item.assignedTo?.length > 0
                          ? item.assignedTo.map(x => renderFullNameForUserId(x)).join(', ')
                          : '-'
                      }
                    />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={'50%'} gap={isDesktopWidth ? 5 : 0}>
                    {hasTimeTrackingEnabled && (
                      <SeaLabelValue
                        label={'Estimated Time'}
                        iconOptions={{ icon: 'timer' }}
                        showIcon={true}
                        value={item.estimatedTime ? formatShortTimeDurationHrsMinsView(item.estimatedTime) : '-'}
                      />
                    )}
                  </SeaStack>
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>

          <SeaEmptyDivider />

          <SeaPageCardContentSection>
            {/*    /!** Card 1 - START *!/*/}
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack
                  isCollapsible={true}
                  width={DESKTOP_ITEMS_WIDTH}
                  direction={'row'}
                  gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue label={'Task'} value={formatValue(item.description)} layout={'vertical'} />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>
        <View style={styles.historyView}>
          <SeaTypography variant={'cardTitle'}>History</SeaTypography>
        </View>

        <SafetyCheckHistoryTable
          items={historyItems ?? []}
          hasTimeTrackingEnabled={hasTimeTrackingEnabled}
          onRowPress={row => {
            setCompletionModalMode(DrawerMode.Edit)
            setCompletedSafetyCheck(row)
            setCompletedSafetyCheckId(row.id)
            setIsCompleteModalVisible(true)
          }}
        />
      </ScrollView>

      {isCompleteModalVisible && (
        <ModifyCompletedSafetyCheckDrawer
          mode={completionModalMode}
          safetyCheck={item}
          completedSafetyCheckId={completedSafetyCheckId}
          completedSafetyCheck={completedSafetyCheck}
          visible={isCompleteModalVisible}
          onClose={() => {
            setIsCompleteModalVisible(false)
            setCompletedSafetyCheckId(undefined)
          }}
        />
      )}

      {isEditModalVisible && (
        <ModifySafetyCheckDrawer
          selectedItemId={item.id}
          mode={DrawerMode.Edit}
          visible={isEditModalVisible}
          onClose={() => setIsEditModalVisible(false)}
        />
      )}
    </RequirePermissions>
  )
}

const styles = StyleSheet.create({
  container: {
    height: '100%',
  },
  historyView: {
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
})

export default ViewSafetyCheck
