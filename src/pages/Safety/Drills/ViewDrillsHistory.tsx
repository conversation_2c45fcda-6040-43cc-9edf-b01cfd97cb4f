import { sharedState } from '@src/shared-state/shared-state'
import React, { useMemo, useState } from 'react'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { DrillHistoryTable } from '@src/components/_organisms/Safety/Drills/DrillHistoryTable'
import { ModifyDrillReportDrawer } from '@src/components/_organisms/Safety/Drills/ModifyDrillReportDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { DrillReport } from '@src/shared-state/VesselSafety/drillReports'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

const DESKTOP_ITEMS_WIDTH = '60%'

interface ViewDrillsHistoryProps {
  vesselId: string
}

export const ViewDrillsHistory = ({ vesselId }: ViewDrillsHistoryProps) => {
  // Global State
  const vesselDrills = sharedState.vesselDrills.use()
  const vesselDrillReports = sharedState.drillReports.use()

  // Internal State
  const [selectedItem, setSelectedItem] = useState<DrillReport | undefined>()
  const [isDrillReportDrawerOpen, setIsDrillReportDrawerOpen] = useState(false)

  // Hooks
  const drills = useMemo(() => vesselDrills?.all.filter(x => x.state === 'active'), [vesselDrills])

  const drillReports = useMemo(() => {
    return vesselDrillReports?.allReports.filter(x => x.state === 'active' && x.vesselId)
  }, [vesselDrillReports, vesselId])

  const router = useRouter()

  const handleRowPress = (report: DrillReport) => {
    return router.navigate({
      pathname: getRoutePath(Routes.DRILLS_VIEW_DRILL_REPORT),
      params: {
        vesselId,
        drillReportId: report.id,
      },
    })
  }

  // Loading state
  if (!drillReports || !drills) {
    return <SeaLoadingSpinner variant={'lifeBuoy'} />
  }

  return (
    <RequirePermissions role={'drills'} level={permissionLevels.VIEW} showDenial={true}>
      <SeaPageCard
        titleComponent={<SeaPageCardTitle title="Drill History" />}
        primaryActionButton={
          <SeaButton
            onPress={() => {
              setSelectedItem(undefined)
              setIsDrillReportDrawerOpen(true)
            }}
            variant={SeaButtonVariant.Primary}
            label={'Add Drill Report'}
            iconOptions={{ icon: 'description' }}
          />
        }></SeaPageCard>

      <DrillHistoryTable
        showCompletedBy
        showDrillsCompleted
        drillReports={drillReports}
        vesselDrills={drills}
        onRowPress={report => handleRowPress(report)}
      />
      <ModifyDrillReportDrawer
        mode={selectedItem ? DrawerMode.Edit : DrawerMode.Create}
        selectedItem={selectedItem}
        visible={isDrillReportDrawerOpen}
        onClose={() => setIsDrillReportDrawerOpen(false)}
      />
    </RequirePermissions>
  )
}
