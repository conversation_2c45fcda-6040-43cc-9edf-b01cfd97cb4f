import { sharedState } from '@src/shared-state/shared-state'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import React, { useState } from 'react'
import { DrillsTable } from '@src/components/_organisms/Safety/Drills/DrillsTable'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { useDrillsMatrix } from '@src/hooks/useDrillsMatrix'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { useRouter } from 'expo-router'
import { SeaHistoryButton } from '@src/components/_molecules/IconButtons/SeaHistoryButton'
import { ModifyDrillReportDrawer } from '@src/components/_organisms/Safety/Drills/ModifyDrillReportDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { ModifyDrillDrawer } from '@src/components/_organisms/Safety/Drills/ModifyDrillDrawer'

export type DrillsPageProps = {
  vesselId: string
  visible: boolean
  headerSubNavigation: SubNav[]
}

export const Drills = ({ vesselId, visible, headerSubNavigation }: DrillsPageProps) => {
  const vesselDrillsData = sharedState.vesselDrills.use(visible)
  const licenseeUsers = sharedState.users.use(visible)

  const drillMatrixData = useDrillsMatrix(vesselId, vesselDrillsData?.all, licenseeUsers?.all)

  const [isCreateDrillReportDrawerVisible, setIsCreateDrillReportDrawerVisible] = useState(false)
  const [isCreateDrillDrawerVisible, setIsCreateDrillDrawerVisible] = useState(false)

  const { isMobileWidth } = useDeviceWidth()

  const router = useRouter()

  const onUserPress = (userId?: string) => {
    return router.navigate({
      pathname: getRoutePath(Routes.DRILLS_VIEW_USER_DRILLS),
      params: {
        userId,
        vesselId,
      },
    })
  }

  const onDrillPress = (drillId?: string) => {
    return router.navigate({
      pathname: getRoutePath(Routes.DRILLS_VIEW_DRILL),
      params: {
        drillId,
        vesselId,
      },
    })
  }

  const onCellPress = (userId?: string, drillId?: string) => {
    return router.navigate({
      pathname: getRoutePath(Routes.DRILLS_VIEW_USER_DRILL),
      params: {
        userId,
        drillId,
        vesselId,
      },
    })
  }

  return (
    <>
      <ScrollablePageLayout>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title="Drills" />}
          primaryActionButton={
            <SeaButton
              onPress={() => setIsCreateDrillReportDrawerVisible(true)}
              variant={SeaButtonVariant.Primary}
              label={'Complete'}
              iconOptions={{ icon: 'check' }}
            />
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
            <RequirePermissions role={'drills'} level={permissionLevels.CREATE} key={'add-drill'}>
              <SeaAddButton onPress={() => setIsCreateDrillDrawerVisible(true)} />
            </RequirePermissions>,
            <SeaHistoryButton
              key={'drill-history'}
              label={!isMobileWidth ? 'History' : ''}
              onPress={() => {
                return router.navigate({
                  pathname: getRoutePath(Routes.DRILLS_VIEW_DRILLS_HISTORY),
                  params: {
                    vesselId,
                  },
                })
              }}
            />,
          ]}
          subNav={headerSubNavigation}
        />
        {drillMatrixData ? (
          <>
            <DrillsTable
              drillMatrixData={drillMatrixData}
              onRowPress={userId => onUserPress(userId)}
              onColumnPress={drillId => onDrillPress(drillId)}
              onCellPress={(userId, drillId) => onCellPress(userId, drillId)}
            />
            <SeaSpacer height={50} />
          </>
        ) : (
          <SeaLoadingSpinner />
        )}
      </ScrollablePageLayout>
      <ModifyDrillReportDrawer
        mode={DrawerMode.Create}
        visible={isCreateDrillReportDrawerVisible}
        onClose={() => setIsCreateDrillReportDrawerVisible(false)}
      />
      <ModifyDrillDrawer
        mode={DrawerMode.Create}
        visible={isCreateDrillDrawerVisible}
        onClose={() => setIsCreateDrillDrawerVisible(false)}
      />
    </>
  )
}

export default Drills
