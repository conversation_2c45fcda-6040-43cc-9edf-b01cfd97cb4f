import { sharedState } from '@src/shared-state/shared-state'
import React, { useMemo, useState } from 'react'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { useUserDrillWithReports } from '@src/hooks/useDrillsMatrix'
import { TrainingStatusBadge } from '@src/components/_organisms/Safety/Drills/DrillStatusBadge'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatDateShort, formatInterval } from '@src/lib/datesAndTime'
import { DrillHistoryTable } from '@src/components/_organisms/Safety/Drills/DrillHistoryTable'
import { ModifyDrillReportDrawer } from '@src/components/_organisms/Safety/Drills/ModifyDrillReportDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { StyleSheet, View } from 'react-native'
import { useDeviceWidth } from '@src/hooks/useDevice'

const DESKTOP_ITEMS_WIDTH = '100%'

interface ViewUserDrillProps {
  userId: string
  drillId: string
  vesselId: string
}

export const ViewUserDrill = ({ userId, drillId, vesselId }: ViewUserDrillProps) => {
  // Global State
  const users = sharedState.users.use()
  const vesselDrillsData = sharedState.vesselDrills.use()
  const drillReportsData = sharedState.drillReports.use()

  const [isDrillReportDrawerOpen, setIsDrillReportDrawerOpen] = useState(false)
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  // Hooks
  const vesselDrills = useMemo(() => vesselDrillsData?.all.filter(x => x.state === 'active') ?? [], [vesselDrillsData])
  const user = useMemo(() => users?.byId[userId], [users])
  const drill = useMemo(() => vesselDrillsData?.byId?.[drillId], [vesselDrillsData, drillId])

  const userDrillWithReports = useUserDrillWithReports(drillId, vesselDrills, drillReportsData, user)

  const userDrill = useMemo(() => userDrillWithReports?.drillData, [userDrillWithReports])

  // Loading state
  if (!user || !drill || !userDrill || !userDrillWithReports) {
    return <SeaLoadingSpinner variant={'lifeBuoy'} />
  }

  return (
    <RequirePermissions role={'drills'} level={permissionLevels.VIEW} showDenial={true}>
      <SeaPageCard
        titleComponent={
          <SeaPageCardTitle
            title={`${renderFullNameForUserId(userId)} - ${drill?.name}`}
            additionalElements={
              <TrainingStatusBadge status={userDrill.status} daysRemaining={userDrill.daysRemaining} />
            }
          />
        }
        primaryActionButton={
          <SeaButton
            onPress={() => setIsDrillReportDrawerOpen(true)}
            variant={SeaButtonVariant.Primary}
            label={'Complete Drill Report'}
            iconOptions={{ icon: 'check' }}
          />
        }>
        <SeaPageCardContentSection>
          <SeaStack direction={'column'} align={'start'} gap={10} width={isLargeDesktopWidth ? '70%' : '100%'}>
            <SeaStack align={'start'} width={DESKTOP_ITEMS_WIDTH}>
              <SeaStack direction={'column'} align={'start'} width={DESKTOP_ITEMS_WIDTH}>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaLabelValue
                    iconOptions={{ icon: 'update' }}
                    showIcon={true}
                    label={'Drill Interval'}
                    value={formatInterval(drill.interval)}
                  />
                  <SeaLabelValue
                    iconOptions={{ icon: 'calendar_month' }}
                    showIcon={true}
                    label={'Next Due'}
                    value={formatDateShort(userDrill.dateDue)}
                  />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaStack>
        </SeaPageCardContentSection>
      </SeaPageCard>

      <View style={styles.titleContainer}>
        <SeaTypography variant={'cardTitle'}>Drill History</SeaTypography>
      </View>
      <DrillHistoryTable
        vesselDrills={vesselDrills}
        drillReports={userDrillWithReports.reports}
        onRowPress={() => console.log('TODO: Not implemented')}
      />

      {isDrillReportDrawerOpen && (
        <ModifyDrillReportDrawer
          mode={DrawerMode.Create}
          selectedDrillId={drillId}
          selectedUserId={userId}
          visible={isDrillReportDrawerOpen}
          onClose={() => setIsDrillReportDrawerOpen(false)}
        />
      )}
    </RequirePermissions>
  )
}

const styles = StyleSheet.create({
  titleContainer: {
    paddingLeft: 10,
  },
})
