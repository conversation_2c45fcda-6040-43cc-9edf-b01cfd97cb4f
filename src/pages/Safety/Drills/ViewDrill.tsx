import { sharedState } from '@src/shared-state/shared-state'
import React, { useCallback, useMemo, useState } from 'react'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatInterval } from '@src/lib/datesAndTime'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { DrillHistoryTable } from '@src/components/_organisms/Safety/Drills/DrillHistoryTable'
import { ModifyDrillReportDrawer } from '@src/components/_organisms/Safety/Drills/ModifyDrillReportDrawer'
import { ModifyDrillDrawer } from '@src/components/_organisms/Safety/Drills/ModifyDrillDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { DrillReport } from '@src/shared-state/VesselSafety/drillReports'
import { ScrollView, StyleSheet, View } from 'react-native'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { useRouter } from 'expo-router'
import { DeleteDrillDto, DeleteDrillUseCase } from '@src/domain/use-cases/safety/DeleteDrillUseCase'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'

const DESKTOP_ITEMS_WIDTH = '60%'

interface ViewDrillProps {
  drillId: string
  vesselId: string
}

export const ViewDrill = ({ drillId, vesselId }: ViewDrillProps) => {
  // Global State
  const vesselDrills = sharedState.vesselDrills.use()
  const vesselDrillReports = sharedState.drillReports.use()
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  // Internal State
  const [selectedItem, setSelectedItem] = useState<DrillReport | undefined>()
  const [isDrillReportDrawerOpen, setIsDrillReportDrawerOpen] = useState(false)
  const [isDrillDrawerOpen, setIsDrillDrawerOpen] = useState(false)

  // Hooks
  const services = useServiceContainer()
  const router = useRouter()

  const drill = useMemo(() => vesselDrills?.byId[drillId], [drillId, vesselDrills])

  const unassignedUsers = useMemo(() => drill?.notAssignedTo?.map(x => renderFullNameForUserId(x)).join(', '), [drill])

  const drillReports = useMemo(() => {
    return vesselDrillReports?.allReports.filter(x => x.state === 'active' && x.drillIds?.includes(drillId))
  }, [vesselDrillReports, drillId])

  const handleDelete = useCallback(() => {
    if (!drill || !vesselId || !userId || !licenseeId) {
      console.error('Required data not available for deletion')
      return
    }

    const dto: DeleteDrillDto = {
      id: drill.id,
      name: drill.name,
      vesselId,
    }

    const deleteDrillUseCase = services.get(DeleteDrillUseCase)

    deleteDrillUseCase
      .execute(dto, userId, licenseeId)
      .then(() =>
        router.navigate({
          pathname: getRoutePath(Routes.DRILLS),
          params: { vesselId },
        })
      )
      .catch(err => console.error(`Error deleting drill\n ${err.message}`))
  }, [drill, vesselId, userId, licenseeId, services, router])

  const onDelete = useCallback(async () => {
    await deleteIfConfirmed({
      itemType: 'drill',
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  // Loading state
  if (!drill) {
    return <SeaLoadingSpinner variant={'lifeBuoy'} />
  }

  return (
    <RequirePermissions role={'drills'} level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView>
        <SeaPageCard
          primaryActionButton={
            <SeaButton
              onPress={() => {
                setSelectedItem(undefined)
                setIsDrillReportDrawerOpen(true)
              }}
              variant={SeaButtonVariant.Primary}
              label={'Complete Drill Report'}
              iconOptions={{ icon: 'check' }}
            />
          }
          secondaryActionButton={[
            <RequirePermissions role={'drills'} level={permissionLevels.FULL} key={'delete-drill'}>
              <SeaDeleteButton onPress={onDelete} />
            </RequirePermissions>,
            <RequirePermissions role={'drills'} level={permissionLevels.EDIT} key={'delete-drill'}>
              <SeaEditButton key={'edit-drill'} onPress={() => setIsDrillDrawerOpen(true)} />,
            </RequirePermissions>,
          ]}
          titleComponent={<SeaPageCardTitle title={`Drill - ${drill.name}`} />}>
          <SeaStack direction={'column'} align={'start'} gap={10}>
            {/** Card 1 - START */}
            <SeaStack
              align={'start'}
              style={{
                width: '100%',
              }}>
              <SeaStack
                direction={'column'}
                align={'start'}
                style={{
                  width: '100%',
                }}>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue label={'Interval'} value={formatInterval(drill.interval)} />
                  <SeaLabelValue label={'Excluded Users'} value={unassignedUsers} />
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaStack>
        </SeaPageCard>

        <View style={styles.titleContainer}>
          <SeaTypography variant={'cardTitle'}>Drill History</SeaTypography>
        </View>
        <DrillHistoryTable
          showCompletedBy
          vesselDrills={vesselDrills?.all ?? []}
          drillReports={drillReports ?? []}
          onRowPress={report => {
            console.log('TODO: Not implemented')
            // setSelectedItem(report);
            // setIsDrillReportDrawerOpen(true);
          }}
        />
        {isDrillReportDrawerOpen && (
          <ModifyDrillReportDrawer
            mode={selectedItem ? DrawerMode.Edit : DrawerMode.Create}
            selectedItem={selectedItem}
            selectedDrillId={selectedItem ? undefined : drill.id}
            visible={isDrillReportDrawerOpen}
            onClose={() => setIsDrillReportDrawerOpen(false)}
          />
        )}
        {isDrillDrawerOpen && (
          <RequirePermissions role={'drills'} level={permissionLevels.EDIT} showDenial={true}>
            <ModifyDrillDrawer
              mode={DrawerMode.Edit}
              selectedDrill={drill}
              visible={isDrillDrawerOpen}
              onClose={() => setIsDrillDrawerOpen(false)}
            />
          </RequirePermissions>
        )}
      </ScrollView>
    </RequirePermissions>
  )
}

const styles = StyleSheet.create({
  titleContainer: {
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
})
