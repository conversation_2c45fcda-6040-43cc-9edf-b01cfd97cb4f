import { sharedState } from '@src/shared-state/shared-state'
import React, { useMemo, useState } from 'react'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import {
  SeaPageCard,
  SeaPageCardContentSection,
  SeaPageCardTitle,
} from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { formatDateShort } from '@src/lib/datesAndTime'
import { ModifyDrillReportDrawer } from '@src/components/_organisms/Safety/Drills/ModifyDrillReportDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { DrillReport } from '@src/shared-state/VesselSafety/drillReports'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { ScrollView, StyleSheet, Text } from 'react-native'
import { useDeviceWidth } from '@src/hooks/useDevice'

const DESKTOP_ITEMS_WIDTH = '100%'

interface ViewDrillReportProps {
  vesselId: string
  drillReportId: string
}

export const ViewDrillReport = ({ vesselId, drillReportId }: ViewDrillReportProps) => {
  // Global State
  const vesselDrills = sharedState.vesselDrills.use()
  const vesselDrillReports = sharedState.drillReports.use()

  const drillReport = useMemo(() => {
    return vesselDrillReports?.byId[drillReportId]
  }, [drillReportId, vesselDrillReports])

  const title = useMemo(() => `Drill Report by ${renderFullNameForUserId(drillReport?.addedBy)}`, [drillReport])

  // Internal State
  const [isDrillReportDrawerOpen, setIsDrillReportDrawerOpen] = useState(false)
  const { isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  // Loading state
  if (!drillReport) {
    return <SeaLoadingSpinner variant={'lifeBuoy'} />
  }

  return (
    <RequirePermissions role={'drills'} level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView showsVerticalScrollIndicator={false} style={styles.container}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={title} />}
          primaryActionButton={
            <SeaButton
              onPress={() => {
                alert('Coming soon!')
              }}
              variant={SeaButtonVariant.Primary}
              label={'Edit Report'}
              iconOptions={{ icon: 'edit' }}
            />
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'export-report'} />,
            <SeaDeleteButton key={'delete-report'} />,
          ]}>
          <SeaPageCardContentSection>
            <SeaStack
              direction={isLargeDesktopWidth ? 'row' : 'column'}
              gap={10}
              justify="start"
              align="start"
              width={'100%'}
              style={{ flex: 1 }}>
              <SeaStack
                direction="column"
                gap={isDesktopWidth ? 20 : 10}
                align={'start'}
                width={isLargeDesktopWidth ? '70%' : '100%'}>
                <SeaStack direction="column" align={'start'} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Date Completed'} value={formatDateShort(drillReport.dateCompleted)} />
                    <SeaLabelValue label={'Location'} value={drillReport.location} />
                  </SeaStack>

                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue
                      label={'Crew Involved'}
                      value={drillReport.crewInvolvedIds.map(x => renderFullNameForUserId(x)).join(', ')}
                    />
                    <SeaLabelValue
                      label={'Drills Completed'}
                      value={drillReport.drillIds.map(x => vesselDrills?.byId[x].name).join(', ')}
                    />
                  </SeaStack>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Scenario'} value={drillReport.scenario ?? ''} />
                    <SeaLabelValue label={'Equipment'} value={drillReport.equipment ?? ''} />
                  </SeaStack>
                  <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH} gap={isDesktopWidth ? 5 : 0}>
                    <SeaLabelValue label={'Further Training'} value={drillReport.furtherTraining ?? ''} />
                    <SeaLabelValue
                      label={'Modification to Current Procedures'}
                      value={drillReport.modification ?? ''}
                    />
                  </SeaStack>
                </SeaStack>
                <SeaTypography variant={'subtitle'}>Signed Off By</SeaTypography>
                <SeaStack direction={'column'} align={'start'}>
                  <SeaTypography variant={'value'}>TODO</SeaTypography>
                </SeaStack>
              </SeaStack>
            </SeaStack>
          </SeaPageCardContentSection>
        </SeaPageCard>
        {/*<ModifyDrillReportDrawer*/}
        {/*  mode={selectedItem ? DrawerMode.Edit : DrawerMode.Create}*/}
        {/*  selectedItem={selectedItem}*/}
        {/*  visible={isDrillReportDrawerOpen}*/}
        {/*  onClose={() => setIsDrillReportDrawerOpen(false)}*/}
        {/*/>*/}
      </ScrollView>
    </RequirePermissions>
  )
}

const styles = StyleSheet.create({
  container: {
    height: '100%',
  },
})
