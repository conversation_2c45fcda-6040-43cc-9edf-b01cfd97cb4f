import React, { useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { UserDrillsTable } from '@src/components/_organisms/Safety/Drills/UserDrillsTable'
import { useDrillsMatrix } from '@src/hooks/useDrillsMatrix'
import { ModifyAssignedDrillsDrawer } from '@src/components/_organisms/Safety/Drills/ModifyAssignedDrillsDrawer'

interface ViewUserDrillsProps {
  vesselId: string
  userId: string
}

export const ViewUserDrills = ({ userId, vesselId }: ViewUserDrillsProps) => {
  // Global State
  const users = sharedState.users.use()
  const vesselDrills = sharedState.vesselDrills.use()

  const [isModifyAssignedDrillsDrawerVisible, setIsModifyAssignedDrillsDrawerVisible] = useState(false)

  // Hooks
  const drillUser = useMemo(() => {
    return users?.byId[userId]
  }, [users, userId])

  const drillMatrixData = useDrillsMatrix(vesselId, vesselDrills?.all, drillUser ? [drillUser] : [])

  const drills = drillMatrixData?.drills
  const userDrillsData = useMemo(
    () => drillMatrixData?.users.find(x => x.user.id === userId),
    [drillMatrixData, userId]
  )

  // Loading state
  if (!drillUser || !drills || !userDrillsData) {
    console.log('MISSING', { drillUser, drills, userDrillsData })
    return <SeaLoadingSpinner variant={'lifeBuoy'} />
  }

  return (
    <RequirePermissions role={'drills'} level={permissionLevels.VIEW} showDenial={true}>
      <SeaPageCard
        titleComponent={<SeaPageCardTitle title={`${renderFullNameForUserId(userId)}  - Assigned Drills`} />}
        primaryActionButton={
          <SeaButton
            onPress={() => setIsModifyAssignedDrillsDrawerVisible(true)}
            variant={SeaButtonVariant.Primary}
            label={'Edit Assigned Drills'}
            iconOptions={{ icon: 'edit' }}
          />
        }
      />

      <UserDrillsTable drills={drills} userDrillsData={userDrillsData} />
      {isModifyAssignedDrillsDrawerVisible && (
        <ModifyAssignedDrillsDrawer
          visible={isModifyAssignedDrillsDrawerVisible}
          selectedUserId={userId}
          onClose={() => setIsModifyAssignedDrillsDrawerVisible(false)}
        />
      )}
    </RequirePermissions>
  )
}
