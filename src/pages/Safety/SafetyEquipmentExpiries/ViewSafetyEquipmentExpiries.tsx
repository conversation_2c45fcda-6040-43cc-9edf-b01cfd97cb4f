import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { ScrollView, StyleSheet, Text } from 'react-native'
import { SeaPageCard } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { formatDateShort, warnDays } from '@src/lib/datesAndTime'
import { formatInterval } from '@src/lib/util'
import { renderCategoryName } from '@src/lib/categories'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import {
  SafetyEquipmentTaskCompleted,
  useCompletedSafetyEquipmentItems,
} from '@src/shared-state/VesselSafety/useCompletedSafetyEquipmentItems'
import { SafetyEquipmentExpiryHistoryTable } from '@src/components/_organisms/Safety/SafetyEquipmentExpiries/SafetyEquipmentExpiryHistoryTable'
import { ModifySafetyEquipmentExpiryDrawer } from '@src/components/_organisms/Safety/SafetyEquipmentExpiries/ModifySafetyEquipmentExpiryDrawer'
import { ModifyCompletedSafetyEquipmentExpiryDrawer } from '@src/components/_organisms/Safety/SafetyEquipmentExpiries/ModifyCompletedSafetyEquipmentExpiryDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { deleteIfConfirmed } from '@src/managers/ConfirmDialogManager/ConfirmDialogManager'
import { useServiceContainer } from '@src/providers/ServiceProvider'
import { useRouter } from 'expo-router'
import { Routes } from '@src/navigation/constants'
import { getRoutePath } from '@src/navigation/utils'
import {
  DeleteSafetyEquipmentExpiryDto,
  DeleteSafetyEquipmentExpiryUseCase,
} from '@src/domain/use-cases/safety/DeleteSafetyEquipmentExpiryUseCase'
import { useDeviceWidth } from '@src/hooks/useDevice'

const DESKTOP_ITEMS_WIDTH = '60%'

interface SafetyEquipmentExpiriesProps {
  itemId: string
  vesselId: string
}

const ViewSafetyEquipmentExpiry = ({ itemId, vesselId }: SafetyEquipmentExpiriesProps) => {
  // Global State
  const safetyEquipmentItems = sharedState.safetyEquipmentItems.use()
  const vesselSafetyItems = sharedState.vesselSafetyItems.use()
  const vesselLocations = sharedState.vesselLocations.use()
  const userId = sharedState.userId.use()
  const licenseeId = sharedState.licenseeId.use()

  // Internal State
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [isCompletionModalVisible, setIsCompletionModalVisible] = useState(false)
  const [completionModalMode, setCompletionModalMode] = useState<DrawerMode>(DrawerMode.Create)
  const [selectedCompletion, setSelectedCompletion] = useState<SafetyEquipmentTaskCompleted | undefined>()

  // Hooks
  const item = safetyEquipmentItems?.byId[itemId]
  const completedSafetyEquipmentItems = useCompletedSafetyEquipmentItems(item)
  const { isMobileWidth } = useDeviceWidth()
  const router = useRouter()
  const services = useServiceContainer()

  const itemName = useMemo(() => {
    return renderCategoryName(item?.itemId, vesselSafetyItems)
  }, [item, vesselSafetyItems])
  const isCritical = useMemo(() => {
    return vesselSafetyItems?.byId[itemId]?.isCritical ?? false
  }, [item, vesselSafetyItems])

  const handleDelete = useCallback(() => {
    if (!item || !vesselId || !userId || !licenseeId) {
      console.error('Vessel ID, Licensee ID, Item, or User ID is not available')
      return
    }

    const dto: DeleteSafetyEquipmentExpiryDto = {
      id: item.id,
      itemName: itemName,
      vesselId,
    }

    const deleteSafetyEquipmentExpiry = services.get(DeleteSafetyEquipmentExpiryUseCase)

    deleteSafetyEquipmentExpiry
      .execute(dto, userId, licenseeId)
      .then(() =>
        router.navigate({
          pathname: getRoutePath(Routes.SAFETY_EQUIPMENT_EXPIRIES),
          params: {
            vesselId,
          },
        })
      )
      .catch(err => console.error(`Error deleting safety equipment expiry\n ${err.message}`))
  }, [item, vesselId, userId, licenseeId, itemName, services, router])

  const onDelete = useCallback(async () => {
    await deleteIfConfirmed({
      onConfirmed: handleDelete,
    })
  }, [handleDelete])

  const onHistoryItemPress = () => {
    alert('Not implemented yet')
  }

  // Loading state
  if (!safetyEquipmentItems || !item) {
    return <Text>Loading...</Text>
  }

  return (
    <RequirePermissions role={'safetyEquipmentList'} level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView showsVerticalScrollIndicator={false} style={styles.container}>
        <SeaPageCard
          title={itemName}
          titleAdditionalElements={
            item.dateDue ? (
              <WhenDueStatus
                whenDue={item.dateDue}
                warnDaysThreshold={warnDays.safetyEquipmentExpiries[0]}
                hasFault={item.hasFault ?? false}
              />
            ) : (
              <></>
            )
          }
          primaryActionButton={
            item.type !== 'nonExpiring' ? (
              <SeaButton
                onPress={() => {
                  setSelectedCompletion(undefined)
                  setCompletionModalMode(DrawerMode.Create)
                  setIsCompletionModalVisible(true)
                }}
                variant={SeaButtonVariant.Primary}
                label={'Complete'}
                iconOptions={{ icon: 'check' }}
              />
            ) : (
              <></>
            )
          }
          secondaryActionButton={[
            <SeaDownloadButton
              key={'download'}
              // TODO: Add export pdf
              onPress={() => alert('This functionality is not implemented yet')}
            />,
            <SeaDeleteButton key={'Delete'} onPress={onDelete} />,
            <SeaEditButton
              key={'Edit'}
              label={isMobileWidth ? undefined : 'Edit'}
              onPress={() => {
                setIsEditModalVisible(true)
              }}
            />,
          ]}>
          <SeaStack direction={'column'} align={'start'} gap={10}>
            {/** Card 1 - START */}
            <SeaStack
              align={'start'}
              style={{
                width: '100%',
              }}>
              <SeaStack
                direction={'column'}
                align={'start'}
                style={{
                  width: '100%',
                }}>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue label={'Type'} value={item.type.toUpperCase()} />
                  <SeaLabelValue label={'Quantity'} value={item.quantity} />
                </SeaStack>

                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue label={'Location'} value={renderCategoryName(item.locationId, vesselLocations)} />
                  <SeaLabelValue label={'Critical Equipment'} value={isCritical ? 'Yes' : 'No'} />
                </SeaStack>
              </SeaStack>
            </SeaStack>

            {/** Card 2 - START */}
            <SeaSpacer height={5} />
            <>
              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                <SeaLabelValue label={'Interval'} value={formatInterval(item.interval)} />
                <SeaLabelValue label={'Notification'} value={item.emailReminder ? 'Yes' : 'No'} />
              </SeaStack>
              <SeaStack isCollapsible={true} width={'30%'} align={'start'}>
                <SeaLabelValue label={'Service Due'} value={formatDateShort(item.dateDue)} />
              </SeaStack>
            </>

            {/** Card 3 - START */}
            <SeaSpacer height={5} />
            <>
              <SeaTypography variant={'cardTitle'}>Service Task</SeaTypography>
              <SeaStack direction={'column'} align={'start'}>
                <SeaTypography variant={'value'}>{item.description}</SeaTypography>
              </SeaStack>
            </>
          </SeaStack>
        </SeaPageCard>
        <View style={styles.historyView}>
          <SeaTypography variant={'cardTitle'}>History</SeaTypography>
        </View>

        <SafetyEquipmentExpiryHistoryTable items={completedSafetyEquipmentItems} onPress={onHistoryItemPress} />
      </ScrollView>

      {isCompletionModalVisible && (
        <ModifyCompletedSafetyEquipmentExpiryDrawer
          mode={completionModalMode}
          selectedItem={item}
          existingCompletion={selectedCompletion}
          visible={isCompletionModalVisible}
          onClose={() => {
            setIsCompletionModalVisible(false)
            setSelectedCompletion(undefined)
          }}
        />
      )}

      {isEditModalVisible && (
        <ModifySafetyEquipmentExpiryDrawer
          mode={DrawerMode.Edit}
          selectedItem={item}
          visible={isEditModalVisible}
          onClose={() => setIsEditModalVisible(false)}
        />
      )}
    </RequirePermissions>
  )
}

const styles = StyleSheet.create({
  container: {
    height: '100%',
  },
  historyView: {
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
})

export default ViewSafetyEquipmentExpiry
