import React, { useEffect, useMemo, useState } from 'react'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { DangerousGood } from '@src/shared-state/HealthSafety/dangerousGoods'
import { StyleSheet, Text, View } from 'react-native'
import { sharedState } from '@src/shared-state/shared-state'
import { logPageView } from '@src/lib/firebase'
import { extractSearchTerms, formatValue, formatVessels } from '@src/lib/util'
import { warnDays } from '@src/lib/datesAndTime'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { EditDangerousGoodsRegister } from '@src/components/_organisms/HealthSafety/DangerousGoodsRegister/EditDangerousGoodsRegister'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { VesselFilterDropdown } from '@src/components/_molecules/VesselFilterDropdown/VesselFilterDropdown'
import { SeaFilterSearch } from '@src/components/_atoms/SeaFilterSearch/SeaFilterSearch'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import {
  SeaTableIconCalendar,
  SeaTableIconLocation,
  SeaTableIconVessel,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

interface DangerousGoodsRegisterProps {
  headerSubNavigation?: SubNav[]
}

export const DangerousGoodsRegister = ({ headerSubNavigation }: DangerousGoodsRegisterProps) => {
  // Shared Data
  const dangerousGoods = sharedState.dangerousGoods.use()
  const user = sharedState.user.use()
  const vessels = sharedState.vessels.use()
  const vesselId = sharedState.vesselId.use()

  // Hooks
  const router = useRouter()
  const [addDangerousGoodVisible, setAddDangerousGoodVisible] = useState(false)
  const [filterVesselIds, setFilterVesselIds] = useState<string[]>(user?.vesselIds ?? [])
  const [searchValue, setSearchValue] = useState('')
  // TODO: Export
  // const [exportType, setExportType] = useState<ExportType>();

  useEffect(() => {
    logPageView('HealthSafety/DangerousGoodsRegister')
  }, [])

  useEffect(() => {
    user?.vesselIds && setFilterVesselIds(user?.vesselIds)
  }, [user?.vesselIds])

  const filteredDangerousGoods = useMemo(() => {
    let _dangerousGoods: DangerousGood[] = []

    // Filter by search value
    const terms = extractSearchTerms(searchValue, true)

    if (filterVesselIds) {
      const doneDangerousGood = {} as Record<string, true>
      filterVesselIds.forEach(vesselId => {
        dangerousGoods?.byVesselId[vesselId]?.forEach(dangerousGood => {
          if (!doneDangerousGood[dangerousGood.id]) {
            doneDangerousGood[dangerousGood.id] = true
            let isMatch = true
            if (terms.length > 0) {
              for (const termsItem of terms) {
                if (!dangerousGood.searchText?.includes(termsItem, 0)) {
                  isMatch = false
                  break
                }
              }
            }
            if (isMatch) {
              _dangerousGoods.push(dangerousGood)
            }
          }
        })
      })

      if (filterVesselIds.length > 1) {
        _dangerousGoods.sort((a, b) => {
          return a.name.localeCompare(b.name)
        })
      }
    } else {
      _dangerousGoods = dangerousGoods?.all ?? []
    }
    return _dangerousGoods
  }, [searchValue, dangerousGoods, filterVesselIds])

  /**
   * Get the columns for the Table
   *
   * @return SeaTableColumn<DangerousGood>[]
   */
  const tableColumns = useMemo((): SeaTableColumn<DangerousGood>[] => {
    return [
      {
        label: '',
        width: 60,
        render: item => <SeaTableImage files={item?.imageFiles} />,
        compactModeOptions: {
          isThumbnail: true,
        },
      },
      {
        label: 'Chemical',
        value: item => item.name,
        compactModeOptions: {
          rowPosition: CompactRowPosition.Title,
        },
      },
      {
        label: 'Vessels / Facilities',
        icon: () => <SeaTableIconVessel />,
        value: item => formatVessels(item.vesselIds, vessels),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
            name: 'Vessel',
          },
        },
      },
      {
        label: 'Quantity',
        value: item => formatValue(item.quantity),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
      {
        label: 'Location',
        icon: () => <SeaTableIconLocation />,
        value: item => formatValue(item.location),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Hazardous',
        value: item => (item.isHazardous ? 'Yes' : 'No'),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'DG Class',
        value: item => formatValue(item.class),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'SDS',
        render: item => {
          if (item?.msdsFiles.length > 0) {
            return <SeaTableImage files={item.msdsFiles} />
          }
          return <Text>-</Text>
        },
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Expiry',
        icon: () => <SeaTableIconCalendar />,
        value: item =>
          item.dateExpires && (
            <WhenDueStatus
              whenDue={item.dateExpires}
              warnDaysThreshold={warnDays.safetyEquipmentChecks[0]}
              hasFault={false}
            />
          ),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
    ]
  }, [vessels])

  /**
   * Get the rows for the Table
   *
   * @param items - the filtered list of risks
   * @return SeaTableRow<Riskt>[]
   */
  const tableRows = useMemo(() => {
    return filteredDangerousGoods.map(item => {
      return {
        data: item,
        onPress: (item: DangerousGood) => {
          return router.navigate({
            pathname: getRoutePath(Routes.DANGEROUS_GOODS_REGISTER_VIEW),
            params: {
              dangerousGoodId: item.id,
            },
          })
        },
      }
    })
  }, [router, filteredDangerousGoods])

  const newDangerousGoodData: DangerousGood = {
    vesselIds: vesselId ? [vesselId] : [],
    name: '',
    quantity: '',
    location: '',
    isHazardous: false,
    class: '',
    imageFiles: [],
    msdsFiles: [],
    state: 'active',
    dateExpires: '',
  }

  return (
    <ScrollablePageLayout>
      <RequirePermissions role="dangerousGoodsRegister" level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={'Dangerous Goods Register'} />}
          primaryActionButton={
            <RequirePermissions
              key="dangerousGoodsRegister"
              role="dangerousGoodsRegister"
              level={permissionLevels.CREATE}>
              <SeaAddButton
                label={'Add New'}
                variant={SeaButtonVariant.Primary}
                onPress={() => setAddDangerousGoodVisible(true)}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Row */}
        <SeaStack direction={'row'} justify={'between'} gap={10}>
          <SeaStack style={{ flexWrap: 'wrap', flexShrink: 1 }}>
            <VesselFilterDropdown vesselIds={filterVesselIds} setVesselIds={setFilterVesselIds} />
          </SeaStack>
          <SeaFilterSearch value={searchValue} onChangeText={setSearchValue} />
        </SeaStack>

        {/* Table View */}
        <View style={styles.tableView}>
          {/* TODO: Filter Tags */}
          <SeaTable columns={tableColumns} rows={tableRows} />
        </View>
        {addDangerousGoodVisible && (
          <EditDangerousGoodsRegister
            dangerousGood={newDangerousGoodData}
            visible={addDangerousGoodVisible}
            onClose={() => setAddDangerousGoodVisible(false)}
            isNew={true}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  tableView: {
    marginTop: 16,
  },
})
