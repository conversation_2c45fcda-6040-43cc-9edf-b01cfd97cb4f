import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { Risk } from '@src/shared-state/HealthSafety/risks'
import { logPageView } from '@src/lib/firebase'
import { extractSearchTerms, formatVessels } from '@src/lib/util'
import { formatDate, getDayOffset, getToday, warnDays } from '@src/lib/datesAndTime'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { StyleSheet, View } from 'react-native'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { RiskRatingStatus } from '@src/components/_molecules/RiskRatingStatus/RiskRatingStatus'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { useRouter } from 'expo-router'
import { EditRiskRegister } from '@src/components/_organisms/HealthSafety/RiskRegister/EditRiskRegister'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaFilterSearch } from '@src/components/_atoms/SeaFilterSearch/SeaFilterSearch'
import { renderCategoryName } from '@src/lib/categories'
import { VesselFilterDropdown } from '@src/components/_molecules/VesselFilterDropdown/VesselFilterDropdown'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconCalendar, SeaTableIconVessel } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

interface RiskRegisterProps {
  headerSubNavigation?: SubNav[]
}

export const RiskRegister = ({ headerSubNavigation }: RiskRegisterProps) => {
  // Shared Data
  const user = sharedState.user.use()
  const vessels = sharedState.vessels.use()
  const vesselId = sharedState.vesselId.use()
  const risks = sharedState.risks.use()
  const riskCategories = sharedState.riskCategories.use()
  const todayMillis = sharedState.todayMillis.use()!

  // Hooks
  const router = useRouter()
  const [addRiskVisible, setAddRiskVisible] = useState(false)
  const [filterVesselIds, setFilterVesselIds] = useState<string[]>(user?.vesselIds ?? [])
  const [searchValue, setSearchValue] = useState('')
  // TODO: Set Risk Model
  // const [riskMatrixModal, setRiskMatrixModal] = useState({
  //   show: false,
  //   allowEdit: true,
  //   level: 1,
  //   likelihoodId: 0,
  //   consequenceId: 0,
  //   type: "",
  // });
  // TODO: Settings Screen
  // const [showRiskRegisterSettings, setShowRiskRegisterSettings] = useState(false);
  // TODO: Export
  // const [exportType, setExportType] = useState<ExportType>();

  useEffect(() => {
    logPageView('HealthSafety/RiskRegistry')
  }, [])

  useEffect(() => {
    user?.vesselIds && setFilterVesselIds(user?.vesselIds)
  }, [user?.vesselIds])

  const filteredRisks = useMemo(() => {
    if (!risks) {
      return []
    }
    let _filteredRisks: Risk[] = []
    const terms = extractSearchTerms(searchValue, true)
    if (filterVesselIds) {
      const doneRisk = {} as Record<string, true>
      filterVesselIds.forEach(vesselId => {
        if (risks?.byVesselId[vesselId]) {
          Object.keys(risks.byVesselId[vesselId].byCategoryId).forEach(categoryId => {
            risks.byVesselId[vesselId].byCategoryId[categoryId].forEach(risk => {
              if (!doneRisk[risk.id]) {
                doneRisk[risk.id] = true
                let isMatch = true
                if (terms.length > 0) {
                  for (const termsItem of terms) {
                    if (!risk?.searchText?.includes(termsItem, 0)) {
                      isMatch = false
                      break
                    }
                  }
                }
                if (isMatch) {
                  _filteredRisks.push(risk)
                }
              }
            })
          })
        }
      })
    } else {
      _filteredRisks = risks.all ?? []
    }
    return _filteredRisks
  }, [risks, filterVesselIds, searchValue])

  const today = useMemo(() => {
    return getToday()
  }, [todayMillis])

  const dayUpcoming = useMemo(() => {
    return getDayOffset(warnDays.riskRegister[0])
  }, [todayMillis])

  const renderWhenDueClass = useCallback(
    (dateDue: string) => {
      if (dateDue < today) {
        return 'fail'
      } else if (dateDue < dayUpcoming) {
        return 'warn'
      }
      return ''
    },
    [today, dayUpcoming]
  )

  // TODO: Display Settings and Matrix
  // @jira: https://sea-flux.atlassian.net/browse/SF-350
  // const onViewRiskMatrix = () => {
  //   setRiskMatrixModal({
  //     show: true,
  //     allowEdit: true,
  //     level: 1,
  //     likelihoodId: 0,
  //     consequenceId: 0,
  //     type: "",
  //   });
  // };
  //
  // const onEditSettings = () => {
  //   setShowRiskRegisterSettings(true);
  // };

  /**
   * Get the columns for the Table
   *
   * @return SeaTableColumn<Risk>[]
   */
  const tableColumns = useMemo((): SeaTableColumn<Risk>[] => {
    return [
      {
        label: '',
        width: 60,
        render: item => <SeaTableImage files={item?.files} />,
        compactModeOptions: {
          isThumbnail: true,
        },
      },
      {
        label: 'Hazard',
        value: item => item.name,
        compactModeOptions: {
          rowPosition: CompactRowPosition.Title,
        },
      },
      {
        label: 'Risks',
        value: item => item.risks,
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
      {
        label: 'Pre Risk Rating',
        value: item => (
          <RiskRatingStatus likelihoodId={item.preControls.likelihood} consequenceId={item.preControls.consequence} />
        ),
        compactModeOptions: {
          rowPosition: CompactRowPosition.TopRightCorner,
        },
      },
      {
        label: 'Controls',
        value: item => item.controls,
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
      {
        label: 'Post Risk Rating',
        render: item => (
          <RiskRatingStatus likelihoodId={item.postControls.likelihood} consequenceId={item.postControls.consequence} />
        ),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Vessels',
        icon: () => <SeaTableIconVessel />,
        value: item => formatVessels(item.vesselIds, vessels),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Review Date',
        icon: (item, isCompactView) => {
          if (!renderWhenDueClass(item.dateDue) && !isCompactView) {
            return <SeaTableIconCalendar />
          }
          return <></>
        },
        value: (item, isCompactView) => {
          if (renderWhenDueClass(item.dateDue) && !isCompactView) {
            return (
              <WhenDueStatus whenDue={item.dateDue} warnDaysThreshold={warnDays.riskRegister[0]} hasFault={false} />
            )
          }
          return formatDate(item.dateDue)
        },
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Medium,
          },
        },
      },
    ]
  }, [])

  /**
   * Get the rows for the Table
   *
   * @param items - the filtered list of risks
   * @return SeaTableRow<Riskt>[]
   */
  const tableRows = useMemo((): SeaTableRow<Risk>[] => {
    return filteredRisks.map(item => {
      return {
        data: item,
        onPress: (item: Risk) => {
          return router.navigate({
            pathname: getRoutePath(Routes.RISK_ASSESSMENT_VIEW),
            params: {
              riskId: item.id,
            },
          })
        },
        group: (item: Risk) => renderCategoryName(item.categoryId, riskCategories),
      }
    })
  }, [router, filteredRisks, riskCategories])

  const newRiskData: Risk = {
    vesselIds: vesselId ? [vesselId] : [],
    categoryId: '',
    controls: '',
    dateDue: '',
    dateLastReviewed: '',
    interval: '',
    name: '',
    // TODO: Make a new category ID
    preControls: {},
    postControls: {},
    risks: '',
    shouldReportToManagement: false,
    state: 'active',
    whoResponsible: '',
  }

  return (
    <ScrollablePageLayout>
      <RequirePermissions role="hazardRegister" level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={'Risk Assessments'} />}
          primaryActionButton={
            <RequirePermissions key="hazardRegister" role="hazardRegister" level={permissionLevels.CREATE}>
              <SeaAddButton
                key={'add'}
                variant={SeaButtonVariant.Primary}
                label={'Complete'}
                onPress={() => setAddRiskVisible(true)}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
            <RequirePermissions key="hazardRegister" role="hazardRegister" level={permissionLevels.EDIT}>
              <SeaSettingsButton key={'settings'} onPress={() => alert('This functionality is not completed yet')} />
            </RequirePermissions>,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Row */}
        <SeaStack direction={'row'} justify={'between'} gap={10}>
          <SeaStack style={{ flexWrap: 'wrap', flexShrink: 1 }}>
            <VesselFilterDropdown vesselIds={filterVesselIds} setVesselIds={setFilterVesselIds} />
          </SeaStack>
          <SeaFilterSearch value={searchValue} onChangeText={setSearchValue} />
        </SeaStack>

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable
            columns={tableColumns}
            rows={tableRows}
            showGroupedTable={true}
            sortGroupsByTitle={(a, b) => a.localeCompare(b)}
            sortFunction={(a, b) =>
              renderCategoryName(a.categoryId, riskCategories).localeCompare(
                renderCategoryName(b.categoryId, riskCategories)
              )
            }
          />
        </View>
        {addRiskVisible && (
          <EditRiskRegister
            risk={newRiskData}
            visible={addRiskVisible}
            onClose={() => setAddRiskVisible(false)}
            isNew={true}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  tableView: {
    marginTop: 16,
  },
})
