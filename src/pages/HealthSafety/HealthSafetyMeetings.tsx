import React, { useEffect, useMemo, useState } from 'react'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
  SeaTableRow,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { Pressable, StyleSheet, View } from 'react-native'
import { sharedState } from '@src/shared-state/shared-state'
import { SafetyMeetingReport } from '@src/shared-state/HealthSafety/safetyMeetingReports'
import { logPageView } from '@src/lib/firebase'
import { formatMonthDayTime, formatMonthISO, formatMonthLonger } from '@src/lib/datesAndTime'
import { formatValue, formatVessels } from '@src/lib/util'
import { renderFullNameForUserId } from '@src/shared-state/Core/users'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { useRouter } from 'expo-router'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'
import { SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { EditHealthSafetyMeetings } from '@src/components/_organisms/HealthSafety/HealthSafetyMeetings/EditHealthSafetyMeetings'
import { VesselFilterDropdown } from '@src/components/_molecules/VesselFilterDropdown/VesselFilterDropdown'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import {
  SeaTableIconCalendar,
  SeaTableIconPerson,
  SeaTableIconVessel,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

interface HealthSafetyMeetingsProps {
  headerSubNavigation?: SubNav[]
}

export const HealthSafetyMeetings = ({ headerSubNavigation }: HealthSafetyMeetingsProps) => {
  // Shared Data
  const user = sharedState.user.use()
  const vessels = sharedState.vessels.use()
  const vesselId = sharedState.vesselId.use()
  const safetyMeetingReports = sharedState.safetyMeetingReports.use()
  const safetyMeetingJobs = sharedState.safetyMeetingJobs.use()

  // Hooks
  const router = useRouter()
  const [addMeetingVisible, setAddMeetingVisible] = useState(false)
  const [filterVesselIds, setFilterVesselIds] = useState<string[]>(user?.vesselIds ?? [])
  // TODO: Setting Screen
  // const [showEditSettingsModal, setShowEditSettingsModal] = useState(false);
  // TODO: Export
  // const [exportType, setExportType] = useState<ExportType>();

  useEffect(() => {
    logPageView('HealthSafety/HealthSafetyMeetings')
  }, [])

  useEffect(() => {
    user?.vesselIds && setFilterVesselIds(user?.vesselIds)
  }, [user?.vesselIds])

  // // Convert array of meeting reports to be categorised
  // const categoriseMeetingReports = (reports: SafetyMeetingReport[]) => {
  //   const result = {
  //     categories: [] as string[],
  //     byCategory: {} as Record<string, SafetyMeetingReport[]>,
  //   };
  //   if (reports && reports.length > 0) {
  //     reports.forEach((report) => {
  //       const whenMeeting = formatMonthISO(report.whenMeeting);
  //       if (result.byCategory[whenMeeting] === undefined) {
  //         result.byCategory[whenMeeting] = [];
  //         result.categories.push(whenMeeting);
  //       }
  //       result.byCategory[whenMeeting].push(report);
  //     });
  //   }
  //   return result;
  // };

  const filteredMeetingReports = useMemo(() => {
    if (!safetyMeetingReports) {
      return []
    }

    // Filter by selected Vessels
    let _safetyMeetingReports: SafetyMeetingReport[] = []
    if (filterVesselIds) {
      const hasReport = {} as Record<string, true>
      filterVesselIds.forEach(vesselId => {
        safetyMeetingReports?.byVesselId[vesselId]?.forEach(report => {
          if (!hasReport[report.id]) {
            hasReport[report.id] = true
            _safetyMeetingReports.push(report)
          }
        })
      })
    } else {
      _safetyMeetingReports = safetyMeetingReports?.all ?? []
    }

    return _safetyMeetingReports
  }, [safetyMeetingReports, filterVesselIds])

  // TODO: Display Settings and Matrix
  // @jira: https://sea-flux.atlassian.net/browse/SF-350
  // const onViewSettings = () => {
  //   setShowEditSettingsModal(true);
  // };

  const onPressJobLink = (jobId: string) => {
    // TODO: Navigate to Job View screen
    // TODO: Navigate to Job View screen
    // TODO: Navigate to Job View screen
    router.navigate({
      pathname: getRoutePath(Routes.JOBLIST),
      params: {
        jobId: jobId,
      },
    })
  }

  /**
   * Get the rows for the Table
   *
   * @param items - the filtered list
   * @return SeaTableRow<SafetyMeetingReport>[]
   */
  const tableRows = useMemo(() => {
    return filteredMeetingReports.map(item => {
      return {
        data: item,
        onPress: (item: SafetyMeetingReport) => {
          router.navigate({
            pathname: getRoutePath(Routes.HEALTH_SAFETY_MEETING_VIEW),
            params: {
              safetyMeetingReportId: item.id,
            },
          })
        },
        group: (item: SafetyMeetingReport) => formatMonthLonger(item.whenMeeting),
      }
    })
  }, [router, filteredMeetingReports])

  /**
   * Get the columns for the Table
   *
   * @return SeaTableColumn<>[]
   */
  const tableColumns = useMemo((): SeaTableColumn<SafetyMeetingReport>[] => {
    return [
      {
        label: 'Date',
        icon: () => <SeaTableIconCalendar />,
        value: item => formatMonthDayTime(item.whenMeeting),
        compactModeOptions: {
          rowPosition: CompactRowPosition.Title,
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
          },
        },
      },
      {
        label: 'Vessels / Facilities',
        icon: () => <SeaTableIconVessel />,
        value: item => formatVessels(item.vesselIds, vessels),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
            name: 'Vessels',
          },
        },
      },
      {
        label: 'Notes',
        value: item => formatValue(item.notes),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
          },
        },
      },
      {
        label: 'Personnel Present',
        icon: () => <SeaTableIconPerson />,
        value: item => item?.personnelPresentIds?.map(id => renderFullNameForUserId(id) || '-')?.join(', '),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
            name: 'Present',
          },
        },
      },
      {
        label: 'Jobs Created',
        value: item => {
          let hasJobs = false
          return (
            <SeaStack direction={'column'} align={'start'}>
              {/* TODO: Sree - will need to change - also duplicated in `HealthSafetyMeetings` */}
              {safetyMeetingJobs &&
                item?.jobIds?.map((jobId: string) => {
                  if (safetyMeetingJobs?.byId[jobId]) {
                    hasJobs = true
                    return (
                      <Pressable key={jobId} onPress={() => onPressJobLink(jobId)}>
                        <SeaTypography variant={'link'}>{safetyMeetingJobs.byId[jobId].task}</SeaTypography>
                      </Pressable>
                    )
                  }
                })}
              {!hasJobs && <SeaTypography variant={'input'}>-</SeaTypography>}
            </SeaStack>
          )
        },
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: '',
        width: 60,
        render: item => <SeaTableImage files={item?.files} />,
        compactModeOptions: {
          // isThumbnail: true,
          hideRow: true,
        },
      },
    ]
  }, [])

  const newSafetyMeetingReportData: SafetyMeetingReport = {
    vesselIds: vesselId ? [vesselId] : [],
    personnelPresentIds: [],
    personnelPresentNames: [],
    files: [],
    jobIds: [],
    notes: '',
    sendToCrew: false,
    signature: '',
    state: 'active',
    type: '',
    whenMeeting: 0,
  }

  return (
    <ScrollablePageLayout>
      <RequirePermissions role="healthSafetyMeetings" level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={'Health & Safety Meetings'} />}
          primaryActionButton={
            <RequirePermissions key="healthSafetyMeetings" role="healthSafetyMeetings" level={permissionLevels.CREATE}>
              <SeaAddButton
                label={'Create'}
                variant={SeaButtonVariant.Primary}
                onPress={() => setAddMeetingVisible(true)}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
            <RequirePermissions key="healthSafetyMeetings" role="healthSafetyMeetings" level={permissionLevels.EDIT}>
              <SeaSettingsButton key={'settings'} onPress={() => alert('This functionality is not completed yet')} />
            </RequirePermissions>,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Filter Row */}
        <SeaStack direction={'row'} justify={'between'} gap={10}>
          <SeaStack style={{ flexWrap: 'wrap', flexShrink: 1 }}>
            <VesselFilterDropdown vesselIds={filterVesselIds} setVesselIds={setFilterVesselIds} />
          </SeaStack>
        </SeaStack>

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable
            columns={tableColumns}
            rows={tableRows}
            showGroupedTable={true}
            sortGroupsByTitle={(a, b) => formatMonthISO(b).localeCompare(formatMonthISO(a))}
            sortFunction={(a, b) => {
              return b.whenMeeting - a.whenMeeting
            }}
          />
        </View>

        {addMeetingVisible && (
          <EditHealthSafetyMeetings
            safetyMeetingReport={newSafetyMeetingReportData}
            visible={addMeetingVisible}
            onClose={() => setAddMeetingVisible(false)}
            isNew={true}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  tableView: {
    marginTop: 16,
  },
})
