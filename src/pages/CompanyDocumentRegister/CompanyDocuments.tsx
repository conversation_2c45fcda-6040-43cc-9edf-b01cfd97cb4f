import { View, Text } from 'react-native'
import React, { useMemo, useState } from 'react'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { sharedState } from '@src/shared-state/shared-state'
import { CompanyDocument } from '@src/shared-state/CompanyDocuments/companyDocuments'
import { CategoriesData, renderCategoryName } from '@src/lib/categories'
import { formatDateShort, formatInterval, warnDays } from '@src/lib/datesAndTime'
import { WhenDueStatus } from '@src/components/_molecules/WhenDueStatus/WhenDueStatus'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { useCompanyDocumentsSubNav } from '@src/hooks/useSubNav'
import { EditCompanyDocumentsDrawer } from '@src/components/_organisms/CompanyDocumentRegister/CompanyDocuments/EditCompanyDocumentsDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { colors } from '@src/theme/colors'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import {
  SeaTableIconCalendar,
  SeaTableIconInterval,
  SeaTableIconMail,
} from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'

export function CompanyDocuments() {
  const vessel = sharedState.vessel.use()
  const companyDocuments = sharedState.companyDocuments.use()
  const companyDocumentCategories = sharedState.companyDocumentCategories.use()

  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false)

  const { styles } = useStyles(styleSheet)
  const router = useRouter()

  const data = useMemo(() => {
    if (!companyDocuments) return []

    return companyDocuments.all
  }, [companyDocuments])

  const handlePress = (item: CompanyDocument) => {
    router.navigate({
      pathname: getRoutePath(Routes.COMPANY_DOCUMENTS_VIEW),
      params: {
        vesselId: vessel?.id,
        documentId: item.id,
      },
    })
  }

  const columns = useMemo(() => buildColumns(), [vessel])
  const rows = useMemo(
    () => buildRows(data, item => handlePress(item), companyDocumentCategories),
    [data, companyDocumentCategories]
  )

  return (
    <ScrollablePageLayout>
      <RequirePermissions role="companyDocuments" level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title="Company Documents" />}
          primaryActionButton={
            <RequirePermissions role="companyDocuments" level={permissionLevels.CREATE}>
              <SeaAddButton
                onPress={() => setIsVisibleAddDrawer(true)}
                variant={SeaButtonVariant.Primary}
                label={'Add New'}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <RequirePermissions key={'settings'} role="companyDocuments" level={permissionLevels.CREATE}>
              <SeaSettingsButton onPress={() => alert('This functionality is not completed yet')} />
            </RequirePermissions>,
          ]}
          subNav={useCompanyDocumentsSubNav(vessel?.id, Routes.COMPANY_DOCUMENTS)}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable columns={columns} rows={rows} showGroupedTable />
        </View>

        {isVisibleAddDrawer && (
          <EditCompanyDocumentsDrawer
            onClose={() => setIsVisibleAddDrawer(false)}
            visible={isVisibleAddDrawer}
            type={DrawerMode.Create}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const buildColumns = () => {
  return [
    {
      label: '',
      width: 60,
      render: row => <SeaTableImage files={row.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
    {
      label: 'Name',
      value: (row: CompanyDocument) => row.title,
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Expiry',
      icon: () => <SeaTableIconCalendar />,
      value: (row: CompanyDocument) => formatDateShort(row.dateExpires),
      width: 120,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Large,
        },
      },
    },
    {
      label: 'Interval',
      icon: () => <SeaTableIconInterval />,
      value: (row: CompanyDocument) => formatInterval(row.interval),
      width: 120,
      compactModeOptions: {
        hideRow: true,
      },
    },
    {
      label: 'Status',
      render: (row: CompanyDocument, isCompactView) =>
        row.dateExpires ? (
          <WhenDueStatus
            whenDue={row.dateExpires}
            warnDaysThreshold={warnDays.companyDocuments[0]}
            hasFault={false}
            compact={isCompactView}
          />
        ) : (
          <></>
        ),
      width: 150,
      compactModeOptions: {
        rowPosition: CompactRowPosition.TopRightCorner,
      },
    },
    {
      label: 'Reminder',
      render: (row: CompanyDocument) => (row.emailReminder ? <SeaTableIconMail /> : null),
      width: 100,
      compactModeOptions: {
        hideRow: true,
      },
    },
  ] as SeaTableColumn<CompanyDocument>[]
}

const buildRows = (
  items: CompanyDocument[],
  onPress: (item: CompanyDocument) => void,
  companyDocumentCategories?: CategoriesData
) => {
  return items.map(item => ({
    data: item,
    onPress: (item: CompanyDocument) => onPress(item),
    group: (item: CompanyDocument) => renderCategoryName(item.categoryId, companyDocumentCategories),
  }))
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}))
