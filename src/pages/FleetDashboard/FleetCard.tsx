import {
  SeaCard,
  SeaCardBody,
  SeaCardContent,
  SeaCardHeader,
  SeaCardThumbnail,
} from '@src/components/_atoms/SeaCard/SeaCard'
import { SeaIcon } from '@src/components/_atoms/SeaIcon/SeaIcon'
import { LinkType, SeaStatusBattery } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBattery'
import { SeaStatusBatteryPopover } from '@src/components/_atoms/SeaStatusBattery/SeaStatusBatteryPopover'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { Vessel } from '@src/shared-state/Core/vessel'
import { colors } from '@src/theme/globalStyle'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaStatusType } from '@src/types/Common'
import React, { useMemo, useRef, useState } from 'react'
import { Linking, Text, TouchableOpacity, View } from 'react-native'

interface FleetCardProps {
  vessel: Vessel
  containerWidth: number
  onPress?: () => void
}
export const FleetCard: React.FC<FleetCardProps> = ({ vessel, containerWidth, onPress }) => {
  const [visiblePopovers, setVisiblePopovers] = useState<Record<string, boolean>>({})
  const { styles } = useStyles(styleSheet)
  const { isTabletWidth, isDesktopWidth, isLargeDesktopWidth } = useDeviceWidth()

  const anchorRefs = useRef<Record<string, React.RefObject<View>>>({
    [SeaStatusType.Error]: React.createRef(),
    [SeaStatusType.Warning]: React.createRef(),
    [SeaStatusType.Attention]: React.createRef(),
    [SeaStatusType.Ok]: React.createRef(),
    [SeaStatusType.Critical]: React.createRef(),
  })

  const togglePopover = (status: string) => {
    setVisiblePopovers(prev => ({
      ...prev,
      [status]: !prev[status],
    }))
  }

  const numberOfColumns = useMemo(() => {
    if (isLargeDesktopWidth) return 3
    if (isTabletWidth || isDesktopWidth) return 2
    return 1
  }, [isTabletWidth, isDesktopWidth])

  return (
    <TouchableOpacity
      key={vessel.id}
      style={{
        width: containerWidth / numberOfColumns - 20,
        minWidth: 120,
        maxWidth: 500,
      }}
      onPress={onPress}>
      <SeaCard>
        <SeaCardThumbnail files={vessel.images} onPress={onPress}>
          {vessel.marineTrafficLink && (
            <TouchableOpacity onPress={() => Linking.openURL(vessel.marineTrafficLink!)} style={styles.mapMarker}>
              <SeaIcon icon="location_on" color="grey" fill={false} />
            </TouchableOpacity>
          )}
        </SeaCardThumbnail>
        <SeaCardBody>
          <SeaCardHeader
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: 10,
            }}>
            <SeaTypography
              variant="cardTitle"
              containerStyle={{
                flex: 1,
              }}
              numberOfLines={1}
              ellipsizeMode="tail">
              {vessel.name}
            </SeaTypography>

            {/* TODO: Need to add the backend logic for this */}
            {/* <SeaStack gap={8} direction="row" align="center">
              {!vessel.isShoreFacility && (
                <SeaStack
                  gap={4}
                  direction="row"
                  align="center"
                  style={styles.badge}
                >
                  <SeaIcon
                    icon="location_on"
                    fill={false}
                    size={12}
                    color={theme.colors.grey}
                  />
                  <SeaTypography variant="body">At Sea</SeaTypography>
                </SeaStack>
              )}
              <SeaStack>
                <SeaIcon
                  icon="person"
                  fill={false}
                  size={20}
                  color={theme.colors.grey}
                />
                <SeaTypography variant="body">3</SeaTypography>
              </SeaStack>
            </SeaStack> */}
          </SeaCardHeader>
          <SeaCardContent>
            <SeaStatusBattery
              statuses={[
                {
                  status: SeaStatusType.Error,
                  type: LinkType.Popover,
                  count: 20,
                  onShowPopover: () => togglePopover(SeaStatusType.Error),
                  popover: (
                    <SeaStatusBatteryPopover
                      isVisible={visiblePopovers[SeaStatusType.Error] || false}
                      onClose={() => togglePopover(SeaStatusType.Error)}
                      anchorRef={anchorRefs.current[SeaStatusType.Error] ?? null}>
                      <Text>This is Error popover!</Text>
                    </SeaStatusBatteryPopover>
                  ),
                },
                {
                  status: SeaStatusType.Warning,
                  count: 14,
                  type: LinkType.Popover,
                  onShowPopover: () => togglePopover(SeaStatusType.Warning),
                  popover: (
                    <SeaStatusBatteryPopover
                      isVisible={visiblePopovers[SeaStatusType.Warning] || false}
                      onClose={() => togglePopover(SeaStatusType.Warning)}
                      anchorRef={anchorRefs.current[SeaStatusType.Warning] ?? null}>
                      <Text>This is Warning popover!</Text>
                    </SeaStatusBatteryPopover>
                  ),
                },
                {
                  status: SeaStatusType.Attention,
                  count: 12,
                  onShowPopover: () => togglePopover(SeaStatusType.Attention),
                  type: LinkType.Popover,
                  popover: (
                    <SeaStatusBatteryPopover
                      isVisible={visiblePopovers[SeaStatusType.Attention] || false}
                      onClose={() => togglePopover(SeaStatusType.Attention)}
                      anchorRef={anchorRefs.current[SeaStatusType.Attention] ?? null}>
                      <Text>This is Attention popover!</Text>
                    </SeaStatusBatteryPopover>
                  ),
                },
                {
                  status: SeaStatusType.Ok,
                  count: 10,
                  type: LinkType.Popover,
                  onShowPopover: () => togglePopover(SeaStatusType.Ok),
                  popover: (
                    <SeaStatusBatteryPopover
                      isVisible={visiblePopovers[SeaStatusType.Ok] || false}
                      onClose={() => togglePopover(SeaStatusType.Ok)}
                      anchorRef={anchorRefs.current[SeaStatusType.Ok] ?? null}>
                      <Text>This is Ok popover!</Text>
                    </SeaStatusBatteryPopover>
                  ),
                },
              ]}
            />
          </SeaCardContent>
        </SeaCardBody>
      </SeaCard>
    </TouchableOpacity>
  )
}

const styleSheet = createStyleSheet(theme => ({
  mapMarker: {
    position: 'absolute',
    top: 8,
    left: 8,
    borderRadius: 30,
    width: 30,
    height: 30,
    backgroundColor: theme.colors.white,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },

  badge: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.colors.borderColor,
  },
}))
