import type { Meta, StoryObj } from '@storybook/react'
import FleetDashboard from '@src/pages/FleetDashboard/FleetDashboard'
import { createDrawerNavigator } from '@react-navigation/drawer'

const Drawer = createDrawerNavigator()

const meta = {
  title: 'Screens/Fleet Dashboard',
  component: FleetDashboard,
  argTypes: {
    onPress: { action: 'pressed the button' },
  },
  args: {},
  decorators: [
    Story => {
      return (
        <Drawer.Navigator>
          <Drawer.Screen key={'Fleet Dashboard'} name={'Fleet Dashboard'} component={Story} />
        </Drawer.Navigator>
      )
    },
  ],
} satisfies Meta<typeof FleetDashboard>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
}
