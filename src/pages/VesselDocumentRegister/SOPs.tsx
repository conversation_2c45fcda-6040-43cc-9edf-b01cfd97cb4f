import { View, Text } from 'react-native'
import React, { useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { useRouter } from 'expo-router'
import { SOP } from '@src/shared-state/VesselDocuments/vesselSOPS'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { CategoriesData, renderCategoryName } from '@src/lib/categories'
import { formatDateShort } from '@src/lib/datesAndTime'
import { EditSOPDrawer } from '@src/components/_organisms/VesselDocumentRegister/SOPs/EditSOPDrawer'
import { DrawerMode } from '@src/components/_atoms/SeaDrawer/SeaDrawer'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconCalendar } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'
import { SeaAddButton } from '@src/components/_molecules/IconButtons/SeaAddButton'

interface SOPProps {
  headerSubNavigation?: SubNav[]
}
export function SOPs({ headerSubNavigation }: SOPProps) {
  const vessel = sharedState.vessel.use()
  const vesselSopCategories = sharedState.vesselSOPCategories.use()
  const SOPs = sharedState.vesselSOPs.use()

  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false)

  const { styles } = useStyles(styleSheet)
  const router = useRouter()

  const data = useMemo(() => {
    if (!SOPs) return []

    const allOptions = SOPs.categoryIds.reduce((acc, categoryId) => {
      if (!SOPs.byCategoryId[categoryId]) return acc
      const data = SOPs.byCategoryId[categoryId]

      acc.push(...data)
      return acc
    }, [] as SOP[])

    return allOptions
  }, [SOPs])

  const handlePress = (item: SOP) => {
    router.navigate({
      pathname: getRoutePath(Routes.STANDARD_OPERATING_PROCEDURES_VIEW),
      params: {
        vesselId: vessel?.id,
        sopId: item.id,
      },
    })
  }

  const columns = useMemo(() => buildColumns(), [vessel])
  const rows = useMemo(
    () => buildRows(data, item => handlePress(item), vesselSopCategories),
    [data, vesselSopCategories]
  )

  return (
    <ScrollablePageLayout>
      <RequirePermissions role="standardOperatingProcedures" level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title="Standard Operating Procedures" />}
          primaryActionButton={
            <RequirePermissions role="standardOperatingProcedures" level={permissionLevels.CREATE}>
              <SeaAddButton
                onPress={() => setIsVisibleAddDrawer(true)}
                variant={SeaButtonVariant.Primary}
                label={'Add New'}
              />
            </RequirePermissions>
          }
          secondaryActionButton={[
            <RequirePermissions key={'settings'} role="standardOperatingProcedures" level={permissionLevels.CREATE}>
              <SeaSettingsButton onPress={() => alert('This functionality is not completed yet')} />
            </RequirePermissions>,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          <SeaTable columns={columns} rows={rows} showGroupedTable />
        </View>

        {isVisibleAddDrawer && (
          <EditSOPDrawer
            onClose={() => setIsVisibleAddDrawer(false)}
            visible={isVisibleAddDrawer}
            type={DrawerMode.Create}
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const buildColumns = () => {
  return [
    {
      label: '',
      width: 60,
      render: row => <SeaTableImage files={row.files} />,
      compactModeOptions: {
        isThumbnail: true,
      },
    },
    {
      label: 'Title',
      value: (row: SOP) => row.title,
      compactModeOptions: {
        rowPosition: CompactRowPosition.Title,
      },
    },
    {
      label: 'Issue Date',
      icon: () => <SeaTableIconCalendar />,
      value: (row: SOP) => formatDateShort(row.dateIssued),
      width: 120,
      compactModeOptions: {
        label: {
          show: true,
          valueWidth: CompactValueWidth.Medium,
        },
      },
    },
  ] as SeaTableColumn<SOP>[]
}

const buildRows = (items: SOP[], onPress: (item: SOP) => void, vesselDocumentCategories?: CategoriesData) => {
  return items.map(item => ({
    data: item,
    onPress: (item: SOP) => onPress(item),
    group: (item: SOP) => renderCategoryName(item.categoryId, vesselDocumentCategories),
  }))
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}))
