import React, { ReactNode } from 'react'
import StandardPageLayout from '@src/layout/StandardPageLayout/StandardPageLayout'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { ScrollView, View } from 'react-native'
import { SeaSelector } from '@src/components/_atoms/SeaSelector/SeaSelector'
import { getRouteConfig, getRoutePath } from '@src/navigation/utils'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { useLocalSearchParams, useRouter } from 'expo-router'
import { Routes } from '@src/navigation/constants'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'

interface CrewProps {
  visible: boolean
  children?: ReactNode
}

export const Crew = ({ visible, children }: CrewProps) => {
  // Styles
  const { styles } = useStyles(styleSheet)

  // Hooks
  const { isDesktopWidth } = useDeviceWidth()

  // Navigation
  const { vesselId, tab } = useLocalSearchParams()
  const router = useRouter()

  const baseRouteConfig = getRouteConfig(Routes.CREW_DASHBOARD)

  const handleTabPress = (tabName: string) => {
    return router.navigate({
      pathname: getRoutePath(tabName),
      params: {
        vesselId: vesselId,
        tab: tabName,
      },
    })
  }

  return (
    <View style={styles.container}>
      <ScrollablePageLayout>
        {/**
              TODO: Render tab content once we have finalised a few things
              {renderTabContent()}
            */}
        {children}
      </ScrollablePageLayout>
    </View>
  )
}

const styleSheet = createStyleSheet(() => ({
  container: {
    flex: 1,
  },
}))
