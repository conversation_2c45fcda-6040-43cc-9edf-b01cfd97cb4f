import { View, ScrollView } from 'react-native'
import React from 'react'
import { useGlobalSearchParams, useRouter } from 'expo-router'

import { SeaPageCard, SeaPageCardTitle, SubNavType } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import SeaFileImage from '@src/components/_atoms/SeaFileImage/SeaFileImage'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

import { VesselDashboardModules } from '@src/components/_organisms/VesselDashboard/VesselDashboard'
import { Metrics } from '@src/components/_organisms/VesselDashboard/Metrics'

import PeopleOnBoard from '@src/components/_organisms/VesselDashboard/PeopleOnBoard'
import { ActionLogs } from '@src/components/_organisms/VesselDashboard/ActionLogs'

export function VesselDashboard() {
  const router = useRouter()

  const vessel = sharedState.vessel.use()

  const { vesselId } = useGlobalSearchParams()

  const { isDesktopWidth } = useDeviceWidth()

  return (
    <ScrollView style={{ flex: 1 }} contentContainerStyle={{ flexGrow: 1 }}>
      <SeaStack direction="column" gap={20} style={{ flex: 1, width: '100%' }}>
        <SeaPageCard
          primaryActionButton={
            <SeaButton
              label="Settings"
              variant={SeaButtonVariant.Secondary}
              iconOptions={{
                icon: 'settings',
                size: 20,
                fill: false,
              }}
            />
          }
          secondaryActionButton={[
            <SeaButton key="vessel-details" label="Vessel Details" variant={SeaButtonVariant.Tertiary} />,
          ]}
          subNav={[
            {
              title: 'Start New Voyage',
              onPress: () =>
                router.navigate({
                  pathname: getRoutePath(Routes.VOYAGE),
                  params: {
                    vesselId: vesselId,
                  },
                }),
              iconOptions: {
                icon: 'directions_boat',
                size: 20,
                fill: false,
              },
            },
            {
              title: 'Add Job List Task',
              onPress: () =>
                router.navigate({
                  pathname: getRoutePath(Routes.JOBLIST),
                  params: {
                    vesselId: vesselId,
                    mode: 'add',
                  },
                }),
              iconOptions: {
                icon: 'construction',
                size: 20,
                fill: false,
              },
            },
            {
              title: 'Log Risk Assessment',
              onPress: () =>
                router.navigate({
                  pathname: getRoutePath(Routes.RISK_ASSESSMENT),
                  params: {
                    vesselId: vesselId,
                    openForm: 'true',
                  },
                }),
              iconOptions: {
                icon: 'warning',
                size: 20,
                fill: false,
              },
            },
            {
              title: 'Complete Form',
              onPress: () =>
                router.navigate({
                  pathname: getRoutePath(Routes.CUSTOM_FORMS),
                  params: {
                    vesselId: vesselId,
                    openForm: 'true',
                  },
                }),
              iconOptions: {
                icon: 'list_alt',
                size: 20,
                fill: false,
              },
            },
            {
              title: 'Log Incident',
              onPress: () =>
                router.navigate({
                  pathname: getRoutePath(Routes.INCIDENT_REPORT),
                  params: {
                    vesselId: vesselId,
                    openForm: 'true',
                  },
                }),
              iconOptions: {
                icon: 'emergency_home',
                size: 20,
                fill: false,
              },
            },
          ]}
          subNavType={SubNavType.Button}
          style={{
            margin: 0,
          }}
          titleComponent={
            <SeaPageCardTitle
              title={vessel?.name}
              additionalElements={
                <SeaTypography variant="value" textStyle={{ margin: 0 }} containerStyle={{ margin: 0 }}>
                  {vessel?.areaOfOperation}
                </SeaTypography>
              }
              files={vessel?.images}
            />
          }
        />

        <Metrics />

        <SeaStack
          direction={isDesktopWidth ? 'row' : 'column'}
          gap={20}
          style={{ width: '100%' }}
          justify="center"
          align="start">
          <VesselDashboardModules />

          <SeaStack
            style={{
              maxWidth: isDesktopWidth ? '40%' : '100%',
              width: '100%',
              ...(isDesktopWidth ? { flex: 1 } : {}),
            }}
            direction="column"
            gap={20}
            justify="start"
            align="start">
            <PeopleOnBoard />
            <ActionLogs />
          </SeaStack>
        </SeaStack>
      </SeaStack>
    </ScrollView>
  )
}
