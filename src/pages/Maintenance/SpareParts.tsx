import { SeaFilterTags, SeaFilterTagsValue } from '@src/components/_atoms/SeaFilterTags/SeaFilterTags'
import {
  CompactRowPosition,
  CompactValueWidth,
  SeaTable,
  SeaTableColumn,
} from '@src/components/_atoms/SeaTable/SeaTable'
import SeaIcon from '@src/components/_legacy/SeaIcon/SeaIcon'
import { MaintenanceFilters } from '@src/components/_organisms/Maintenance/MaintenanceSchedule/MaintenanceFilters'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { renderCategoryName } from '@src/lib/categories'
import { formatValue, getCurrencyFromRegion } from '@src/lib/util'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { sharedState } from '@src/shared-state/shared-state'
import { SparePart } from '@src/shared-state/VesselMaintenance/spareParts'
import { colors } from '@src/theme/colors'
import React, { useCallback, useMemo, useState } from 'react'
import { ScrollView, StyleSheet, View } from 'react-native'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { usePermission } from '@src/hooks/usePermission'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { EditSparePartDrawer } from '@src/components/_organisms/Maintenance/SparePart/EditSparePartDrawer'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { SeaTableImage } from '@src/components/_atoms/SeaTable/SeaTableImage'
import { SeaTableIconFlag, SeaTableIconLocation } from '@src/components/_molecules/TableIcons/SeaTableCustomIcons'

const SparePartsPermissions = {
  maintenanceSchedule: { level: permissionLevels.CREATE },
}

interface SparePartsProps {
  headerSubNavigation: SubNav[]
}

export const SpareParts = ({ headerSubNavigation }: SparePartsProps) => {
  const vessel = sharedState.vessel.use()
  const spareParts = sharedState.spareParts.use()
  const equipment = sharedState.equipment.use()
  const vesselLocations = sharedState.vesselLocations.use()
  const licenseeSettings = sharedState.licenseeSettings.use()
  const vesselSystems = sharedState.vesselSystems.use()

  const router = useRouter()

  const [searchValue, setSearchValue] = useState('')
  const [systemFilter, setSystemFilter] = useState('')
  const [equipmentFilter, setEquipmentFilter] = useState('')
  const [locationFilter, setLocationFilter] = useState('')
  const [isVisibleAddDrawer, setIsVisibleAddDrawer] = useState(false)

  const [seaFilterTagsValue, setSeaFilterTagsValue] = useState<Partial<SeaFilterTagsValue>>({
    all: { isActive: true },
    overdue: { isActive: false },
    critical: { isActive: false },
  })

  const modulePermissions = usePermission<typeof SparePartsPermissions>({
    modules: SparePartsPermissions,
  })

  const handleRow = useCallback((item: SparePart) => {
    router.navigate({
      pathname: getRoutePath(Routes.SPARE_PARTS_LIST_VIEW),
      params: {
        vesselId: vessel?.id,
        sparePartId: item.id,
      },
    })
  }, [])

  const checkContainsCritical = useCallback(
    (equipmentIds: string[] | undefined) => {
      if (!equipmentIds) return false

      return equipmentIds?.some(id => equipment?.byId[id]?.isCritical) ?? false
    },
    [equipment?.byId]
  )

  const seaTableColumns = useMemo(() => {
    if (!equipment || !licenseeSettings || !vesselLocations) return []

    return [
      {
        label: '',
        width: 60,
        render: sparePart => <SeaTableImage files={sparePart.files} />,
        compactModeOptions: {
          isThumbnail: true,
        },
      },
      {
        label: 'Item',
        value: sparePart => sparePart.item,
        style: { fontWeight: 'bold' },
        compactModeOptions: {
          rowPosition: CompactRowPosition.Title,
        },
      },
      {
        label: 'Quantity',
        value: sparePart => {
          return formatValue(sparePart.quantity)
        },
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
          },
        },
      },
      ...(seaFilterTagsValue.overdue
        ? [
            {
              label: 'Order Quantity',
              value: (sparePart: SparePart) => {
                return formatValue(sparePart.orderQuantity)
              },
              compactModeOptions: {
                hideRow: true,
              },
            },
          ]
        : []),
      {
        label: 'Equipment',
        value: sparePart => formatValue(sparePart.equipmentList),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Location',
        icon: () => <SeaTableIconLocation />,
        value: sparePart => formatValue(renderCategoryName(sparePart.locationId, vesselLocations)),
        compactModeOptions: {
          label: {
            show: true,
            valueWidth: CompactValueWidth.Large,
          },
        },
      },
      {
        label: 'Part #',
        value: sparePart => formatValue(sparePart.partNum),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Unit Price',
        value: sparePart => getCurrencyFromRegion(licenseeSettings?.region) + ' ' + formatValue(sparePart.unitPrice),
        compactModeOptions: {
          hideRow: true,
        },
      },
      {
        label: 'Critical',
        render: sparePart => {
          const isCritical = checkContainsCritical(sparePart.equipmentIds)
          return isCritical ? <SeaTableIconFlag color={colors.status.critical} /> : <></>
        },
        compactModeOptions: {
          rowPosition: CompactRowPosition.BottomRightCorner,
        },
      },
    ] as SeaTableColumn<SparePart>[]
  }, [equipment, checkContainsCritical, licenseeSettings, vesselLocations, seaFilterTagsValue])

  const systemFilterOptions = useMemo(() => {
    if (!spareParts?.filterOptions) return []

    const options = spareParts.filterOptions.systemIds.map((id: string) => ({
      label: renderCategoryName(id, vesselSystems),
      value: id,
    }))

    return [
      {
        label: 'All',
        value: '',
      },
      ...options,
    ]
  }, [spareParts])

  const equipmentFilterOptions = useMemo(() => {
    if (!spareParts?.filterOptions) return []

    const options = spareParts.filterOptions.equipmentIds.map((id: string) => ({
      label: equipment?.byId[id].equipment ?? '',
      value: id,
    }))

    const filteredOptions = options.filter(option => {
      if (systemFilter) {
        const _equipment = equipment?.byId[option.value]
        return _equipment?.systemId === systemFilter
      }
      return true
    })

    return [
      {
        label: 'All',
        value: '',
      },
      ...filteredOptions,
    ]
  }, [spareParts, systemFilter])

  const locationFilterOptions = useMemo(() => {
    if (!spareParts?.filterOptions) return []
    const options = spareParts.filterOptions.locationIds.map((id: string) => ({
      label: renderCategoryName(id, vesselLocations),
      value: id,
    }))

    return [
      {
        label: 'All',
        value: '',
      },
      ...options,
    ]
  }, [spareParts, vesselLocations])

  const allData = useMemo(() => {
    const data = spareParts?.all

    setSeaFilterTagsValue(prev => ({
      ...prev,
      all: { isActive: prev.all?.isActive ?? false, count: data?.length ?? 0 },
    }))

    return data
  }, [spareParts])

  const overDueData = useMemo(() => {
    const data = spareParts?.all.filter(sparePart => sparePart.orderQuantity && Number(sparePart.orderQuantity) > 0)

    setSeaFilterTagsValue(prev => ({
      ...prev,
      overdue: {
        isActive: prev.overdue?.isActive ?? false,
        count: data?.length ?? 0,
      },
    }))

    return data
  }, [spareParts])

  const criticalData = useMemo(() => {
    const data = spareParts?.all.filter(sparePart => checkContainsCritical(sparePart.equipmentIds))

    setSeaFilterTagsValue(prev => ({
      ...prev,
      critical: {
        isActive: prev.critical?.isActive ?? false,
        count: data?.length ?? 0,
      },
    }))

    return data
  }, [spareParts])

  // Filter categories]
  const filteredTasks = useMemo(() => {
    let filteredJobs: SparePart[] = []

    if (seaFilterTagsValue.all?.isActive) {
      filteredJobs = allData ?? []
    } else {
      if (seaFilterTagsValue.overdue?.isActive) {
        filteredJobs = overDueData ?? []
      }

      if (seaFilterTagsValue.critical?.isActive) {
        filteredJobs = criticalData ?? []
      }
    }

    // Filter by system
    if (systemFilter) {
      filteredJobs = filteredJobs.filter(sparePart => sparePart.systemId === systemFilter)
    }

    // Filter by equipment
    if (equipmentFilter) {
      filteredJobs = filteredJobs.filter(sparePart => sparePart.equipmentIds?.includes(equipmentFilter))
    }

    //Filter by location
    if (locationFilter) {
      filteredJobs = filteredJobs.filter(sparePart => sparePart.locationId === locationFilter)
    }

    // Filter by search
    if (searchValue) {
      filteredJobs = filteredJobs.filter(sparePart => {
        const itemName = sparePart.item?.toLowerCase() ?? ''
        const partNumber = sparePart.partNum?.toLowerCase() ?? ''

        return itemName.includes(searchValue.toLowerCase()) || partNumber.includes(searchValue.toLowerCase())
      })
    }

    return filteredJobs
  }, [
    allData,
    overDueData,
    criticalData,
    seaFilterTagsValue,
    searchValue,
    systemFilter,
    equipmentFilter,
    locationFilter,
  ])

  const rows = useMemo(() => buildRows(filteredTasks ?? [], (item: SparePart) => handleRow(item)), [filteredTasks])

  return (
    <ScrollablePageLayout>
      <RequirePermissions role="sparePartsList" level={permissionLevels.VIEW} showDenial={true}>
        <View style={styles.container}>
          <SeaPageCard
            titleComponent={
              <SeaPageCardTitle title={vessel?.isShoreFacility ? 'Inventory List' : 'Spare Parts List'} />
            }
            primaryActionButton={
              modulePermissions.maintenanceSchedule ? (
                <SeaButton
                  onPress={() => setIsVisibleAddDrawer(true)}
                  variant={SeaButtonVariant.Primary}
                  label={'Add New'}
                  iconOptions={{ icon: 'add' }}
                />
              ) : undefined
            }
            secondaryActionButton={[
              <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
            ]}
            subNav={headerSubNavigation}
          />

          <View
            style={{
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
            }}>
            <ScrollView
              horizontal
              style={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                paddingVertical: 10,
              }}>
              <SeaStack direction="row" justify="between" gap={10} style={{ width: '100%' }}>
                {/* Filter Tags */}
                <SeaFilterTags value={seaFilterTagsValue} onChange={value => setSeaFilterTagsValue(value)} />

                {/* Filter Row */}
                <MaintenanceFilters
                  searchFilter={{
                    searchValue: searchValue,
                    setSearchValue: setSearchValue,
                  }}
                  systemFilter={{
                    value: systemFilter,
                    setValue: setSystemFilter,
                    options: systemFilterOptions,
                  }}
                  equipmentFilter={{
                    value: equipmentFilter,
                    setValue: setEquipmentFilter,
                    options: equipmentFilterOptions,
                  }}
                  locationFilter={{
                    value: locationFilter,
                    setValue: setLocationFilter,
                    options: locationFilterOptions,
                  }}
                />
              </SeaStack>
            </ScrollView>
          </View>

          {/* Table View */}
          <View style={styles.tableView}>
            <SeaTable columns={seaTableColumns} showGroupedTable={false} rows={rows} />
          </View>
        </View>
        {isVisibleAddDrawer && (
          <EditSparePartDrawer
            onClose={() => setIsVisibleAddDrawer(false)}
            visible={isVisibleAddDrawer}
            type="create"
          />
        )}
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const buildRows = (items: SparePart[], onPress: (item: SparePart) => void) => {
  return items.map(item => ({
    data: item,
    // status: getStatus(item),
    onPress: (item: SparePart) => onPress(item),
  }))
}

const styles = StyleSheet.create({
  container: {},
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  filterRow: {
    // backgroundColor: "magenta",
  },
  title: {
    // backgroundColor: "magenta"
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  tableView: {
    marginTop: 16,
  },
})
