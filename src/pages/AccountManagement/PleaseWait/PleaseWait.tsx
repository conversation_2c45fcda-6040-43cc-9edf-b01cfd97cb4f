// PleaseWait.tsx
import React, { useEffect, useState } from 'react'
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native'

import SeaButton from '@src/components/_legacy/SeaButton/SeaButton'
import { colors } from '@src/theme/globalStyle'
import { refreshApp } from '@src/App'

interface PleaseWaitProps {
  message: string
}

const PleaseWait: React.FC<PleaseWaitProps> = ({ message }) => {
  const [showButton, setShowButton] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowButton(true)
    }, 10000) // Set timeout for 10 seconds
    return () => clearTimeout(timer) // Cleanup the timer
  }, [])

  const handleReload = () => {
    refreshApp('Restart pressed after authentication took longer than 10s')
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.spinnerContainer}>
          <ActivityIndicator size="large" color="#ffffff" style={styles.spinner} />
        </View>
        <Text style={styles.message}>{message}</Text>
        {showButton && message === 'Authenticating...' && (
          <View style={styles.buttonContainer}>
            <Text style={styles.warningText}>Authentication is taking longer than expected...</Text>
            <SeaButton onPress={handleReload}>Restart Sea Flux</SeaButton>
          </View>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.header,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinnerContainer: {
    paddingLeft: 8,
  },
  spinner: {
    opacity: 0.5,
    // marginTop: -16,
    // marginLeft: 12,
  },
  message: {
    fontSize: 18,
    color: 'white',
    textAlign: 'center',
  },
  buttonContainer: {
    alignItems: 'center',
  },
  warningText: {
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
})

export default PleaseWait
