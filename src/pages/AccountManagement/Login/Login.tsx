import React, { useCallback, useState } from 'react'
import { Dimensions, Image, Platform, Pressable, StyleSheet, View } from 'react-native'
import { useFormik } from 'formik'
import { functions } from '@src/lib/firebase'
import { httpsCallable } from '@src/lib/firebase/services/functions.service'
import { collection, firestore, getDocs, query, where } from '@src/lib/firebase/services/firestore.service'
import { sharedState } from '@src/shared-state/shared-state'
import { loginUser } from '@src/shared-state/Core/user'
import { colors } from '@src/theme/globalStyle'
import Yup from '@src/lib/yup'
import { useLocalSearchParams, useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaTextInput } from '@src/components/_atoms/_inputs/SeaTextInput/SeaTextInput'
import { SeaPasswordInput } from '@src/components/_atoms/_inputs/SeaPasswordInput/SeaPasswordInput'
import { SeaButton } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { SeaPasscodeModal } from '@src/components/_atoms/SeaPasscodeModal/SeaPasscodeModal'
import { useLogger } from '@src/providers/ServiceProvider'
import { SaveStateAction, setSavingStateManager } from '@src/managers/SavingStateManager/SavingStateManager'

/** Load the images */
const logoImage = require('@assets/sea-flux-logo-full-blue.svg')
const iosStoreImage = require('@assets/ios-app-store-badge-white.png')
const androidStoreImage = require('@assets/google-app-store-badge.png')

const Login = () => {
  const router = useRouter()
  const [formError, setFormError] = useState('')
  const [passcode, setPasscode] = useState('')
  const [passcodeError, setPasscodeError] = useState('')
  const [showPasscodeModal, setShowPasscodeModal] = useState(false)
  const [loading, setLoading] = useState(false)

  const logger = useLogger('Login')

  const { styles: s } = useStyles(styleSheet)

  const getLogoWidth = () => {
    const screenWidth = Dimensions.get('window').width
    if (screenWidth < 400) return 300
    if (screenWidth < 600) return 350
    return 400
  }

  const { d } = useLocalSearchParams()

  const performSignIn = useCallback(async (email: string, password: string) => {
    logger.debug('Performing Sign In')
    const result = await httpsCallable(
      functions,
      'signIn'
    )({
      email,
      password,
      passcode,
    })

    if (result.data.error) {
      throw result.data.error
    }

    return result.data
  }, [])

  const processLoginResult = useCallback(
    async (loginData: any) => {
      const userSnapshot = await loginUser(loginData.token)
      logger.debug(`Processing login result for user ${userSnapshot.user.uid}`, {
        userSnapshot,
      })

      const userDocs = await getDocs(query(collection(firestore, 'users'), where('uid', '==', userSnapshot.user.uid)))

      logger.debug('Received user docs', { userDocs })

      return userDocs
    },
    [logger]
  )

  const navigateAfterLogin = (userDocs: any) => {
    setTimeout(() => {
      resetForm() // (If we come back here later, we want inputs to be blank)
    }, 2000)

    setShowPasscodeModal(false)
    setPasscode('')
    setPasscodeError('')
    const qs = { d }

    if (userDocs.docs && userDocs.docs.length > 0 && userDocs.docs[0].data().isSuperAdmin) {
      logger.debug(`Logged in as superAdmin! navigate to ${qs?.d ? String(qs.d) : '/admin'}`)
      router.navigate(getRoutePath(Routes.ADMIN))
    } else {
      logger.debug(`Logged in as user! navigate to ${qs?.d ? String(qs.d) : '/fleet'}`)
      router.navigate(getRoutePath(Routes.HOME))
    }
  }

  const handlePasscodeError = (error: string) => {
    logger.debug('Error with passcode', { error })
    if (error === 'PasscodeInvalid') {
      setPasscodeError('Passcode is invalid.\nPlease check your email for the most recent passcode we sent you.')
    } else if (error === 'PasscodeExpired') {
      setFormError('The passcode you entered had expired.')
      setShowPasscodeModal(false)
      setPasscode('')
      setPasscodeError('')
    } else {
      setPasscodeError('There was an error processing your passcode.')
    }
  }

  const handleEmailPasscodeRequirement = (data: any) => {
    setPasscode('')
    setPasscodeError('')
    setShowPasscodeModal(true)

    // Make sure password hasn't been wiped by Password Manager
    setFieldValue('password', data.password)
    setTimeout(() => {
      setFieldValue('password', data.password)
    }, 500)
    setTimeout(() => {
      setFieldValue('password', data.password)
    }, 1000)
    setTimeout(() => {
      setFieldValue('password', data.password)
    }, 2000)
  }

  const handleLoginError = (error: any, data: any) => {
    logger.error('Error logging in', { error })

    if (typeof error === 'string' && error.startsWith('Passcode')) {
      handlePasscodeError(error)
    } else {
      setShowPasscodeModal(false)
      setPasscode('')
      setLoading(false)

      const badLoginCodes = [
        'NoAccount',
        'BadAccount',
        'AccountArchived',
        'AccountDeactivated',
        'AccountDeleted',
        'LoginDisabled',
        'BadPassword',
      ]

      if (error === 'RequireEmailPasscode') {
        handleEmailPasscodeRequirement(data)
      } else if (badLoginCodes.includes(error)) {
        setFormError('Unable to login with provided credentials')
      } else if (error === 'SendEmailFailed') {
        setFormError('There was a problem sending a passcode to your email address')
      } else if (typeof error === 'object' && error.code === 'functions/internal') {
        setFormError('You must be online to login. Please check your internet connection.')
      } else if (error.code || error.message) {
        setFormError(`${error.message ?? ''} ${error.code ? `(Error Code: ${error.code})` : ''}`)
      } else {
        setFormError('There was an error logging you in')
      }
    }
  }

  const handleLoginSubmit = async (data: any) => {
    try {
      setSavingStateManager({
        action: SaveStateAction.SAVING,
        message: 'Logging in...',
      })
      setLoading(true)
      setFormError('')
      sharedState.userPending.set(true)

      const loginData = await performSignIn(data.email, data.password)
      const userDocs = await processLoginResult(loginData)
      setSavingStateManager({
        action: SaveStateAction.SUCCESS,
        message: 'Logging in...',
        onCloseTimeout: 200,
        hideFollowOnToast: true,
      })
      navigateAfterLogin(userDocs)
    } catch (error) {
      handleLoginError(error, data)
      setSavingStateManager({
        action: SaveStateAction.ERROR,
        message: 'Failed!',
        hideFollowOnToast: true,
      })
    } finally {
      setLoading(false)
    }
  }

  const { values, handleChange, handleBlur, handleSubmit, setFieldValue, errors, touched, resetForm } = useFormik({
    initialValues: {
      email: '<EMAIL>',
      password: 'hihihi',
    },
    validationSchema: Yup.object({
      email: Yup.string().min(3).max(200).email().required(),
      password: Yup.string().min(5).max(32).required(),
    }),
    onSubmit: handleLoginSubmit,
  })

  return (
    <View style={s.container}>
      <View style={s.logoContainer}>
        <Image source={logoImage} style={[s.logo, { width: getLogoWidth() }]} resizeMode="contain" />
      </View>
      <View style={s.contentContainer}>
        <SeaStack direction={'column'} gap={10} width={'100%'} style={s.formContainer}>
          <SeaTextInput
            label="EMAIL"
            placeholder={''}
            value={values.email}
            onChangeText={handleChange('email')}
            onBlur={() => handleBlur('email')}
            keyboardType="email-address"
            autoComplete="email"
            hasError={!!(touched.email && errors.email)}
            errorText={touched.email ? errors.email : ''}
            style={s.emailInput}
          />
          <SeaPasswordInput
            label="PASSWORD"
            placeholder={''}
            value={values.password}
            onChangeText={handleChange('password')}
            onBlur={() => handleBlur('password')}
            autoComplete="password"
            hasError={!!(touched.password && errors.password)}
            errorText={touched.password ? errors.password : ''}
            style={s.passwordInput}
          />

          {!!formError && (
            <SeaTypography variant="body" textStyle={{ color: colors.danger, textAlign: 'center' }}>
              {formError}
            </SeaTypography>
          )}

          <SeaSpacer height={5} />

          <SeaButton label="Log In" viewStyle={{ width: 160 }} onPress={handleSubmit} />

          <Pressable
            onPress={() => {
              alert('Forgot Password functionality coming soon!')
              // TODO: Setup Screen & Nav
              // router.navigate(getRoutePath(Routes.ForgotPassword));
            }}>
            <SeaTypography variant={'link'}>Forgot Password?</SeaTypography>
          </Pressable>

          <SeaSpacer height={10} />

          <View style={s.termsContainer}>
            <SeaTypography variant={'body'} textStyle={{ textAlign: 'center' }} numberOfLines={3}>
              By signing in, you agree to our Terms and Conditions and Privacy Policy.
            </SeaTypography>
          </View>

          {Platform.OS === 'web' && (
            <SeaStack direction={'row'} gap={20} justify={'between'}>
              <Image source={iosStoreImage} resizeMode="contain" style={{ width: 132 }} />
              <Image source={androidStoreImage} style={{ width: 149 }} resizeMode="contain" />
            </SeaStack>
          )}

          <SeaSpacer height={50} />
        </SeaStack>
      </View>

      <SeaPasscodeModal
        visible={showPasscodeModal}
        onClose={() => setShowPasscodeModal(false)}
        passcode={passcode}
        onPasscodeChange={setPasscode}
        onSubmit={handleSubmit}
        error={passcodeError}
        loading={loading}
      />
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.white,
  },
  contentContainer: {
    borderRadius: 16,
    borderWidth: 2,
    borderColor: theme.colors.borderColor,
    backgroundColor: theme.colors.background.primary,
    maxWidth: 500,
    minWidth: 300,
    minHeight: 450,
    paddingHorizontal: 30,
    marginHorizontal: 40,
    paddingTop: 40,
    paddingBottom: 40,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
  },
  logoContainer: {
    // backgroundColor: "magenta",
    maxWidth: 600,
    height: 100,
    // width: "100%",
    // height: 100,
    // display: "flex",
    // justifyContent: "center",
    // alignItems: "center",
  },
  logo: {
    height: 100,
  },
  formContainer: {},
  emailInput: {
    width: '100%',
  },
  passwordInput: {
    width: '100%',
  },
  termsContainer: {
    maxWidth: 350,
  },
}))

export default Login
