import React, { useEffect, useState } from 'react'
import { View, Text, StyleSheet, Button } from 'react-native'
import { useRoute } from '@react-navigation/native'
import { logPageView } from '@src/lib/firebase'
import { sharedState } from '@src/shared-state/shared-state'
import CenteredBoxPageLayout from '@src/layout/CenteredBoxPageLayout/CenteredBoxPageLayout'
import { useRouter } from 'expo-router'
import { Routes } from '@src/navigation/constants'

const RestartRequired: React.FC = () => {
  const route = useRoute()
  const [restartReason, setRestartReason] = useState<string | undefined>()
  const router = useRouter()

  useEffect(() => {
    // Process query string
    const params = route.params as { reason?: string }
    if (params.reason) {
      setRestartReason(params.reason)
    }
    logPageView('RestartRequired')
    sharedState.showBlankScreen.set(false)
  }, [route.params])

  const handleLogBackIn = () => {
    console.log('handleLogBackIn pressed')
    // Navigate to login screen
    // Assuming you have a navigation setup
    router.navigate(Routes.LOGIN)
  }

  return (
    <CenteredBoxPageLayout boxMode="message">
      {restartReason === 'loggedOut' && (
        <Text style={styles.message}>
          You have successfully logged out of Sea-Flux.
          {'\n'}Please restart the app to log back in.
        </Text>
      )}
      {restartReason === 'offlineFail' && (
        <Text style={styles.message}>
          There was a problem authenticating your account while offline.
          {'\n'}Please restart the app to try again.
        </Text>
      )}
      {restartReason === 'sessionTimeout' && (
        <>
          <Text style={styles.message}>
            Your current Sea-Flux session has expired.
            {'\n'}Please restart the app to log back in.
          </Text>
          <View style={styles.buttonContainer}>
            <Button title="Log Back In" onPress={handleLogBackIn} color="#4076B0" />
          </View>
        </>
      )}
      {restartReason === 'indexedDbError' && (
        <View>
          <Text style={styles.message}>
            Unfortunately, a critical error has occurred preventing Sea-Flux from operating. This has most likely been
            caused by indexedDb failing which is a known issue with older versions of iOS.
          </Text>
          <Text style={styles.message}>
            To prevent this from happening in the future, we recommend upgrading your device to at least iOS 17 or
            higher (if your device supports it).
          </Text>
          <Text style={styles.message}>
            For now, restarting the app should resolve the problem. However, if this issue persists, you might need to
            restart your entire device.
          </Text>
        </View>
      )}
    </CenteredBoxPageLayout>
  )
}

const styles = StyleSheet.create({
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 10,
  },
  buttonContainer: {
    marginTop: 50,
  },
})

export default RestartRequired
