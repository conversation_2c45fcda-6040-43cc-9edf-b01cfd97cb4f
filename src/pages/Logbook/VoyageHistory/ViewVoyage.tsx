import React, { useEffect, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { ScrollView, StyleSheet, View } from 'react-native'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { formatDateShort } from '@src/lib/datesAndTime'
import { SeaEditButton } from '@src/components/_molecules/IconButtons/SeaEditButton'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { SeaLabelValue } from '@src/components/_atoms/SeaLabelValue/SeaLabelValue'
import { SeaSpacer } from '@src/components/_atoms/SeaSpacer/SeaSpacer'
import { SeaDeleteButton } from '@src/components/_molecules/IconButtons/SeaDeleteButton'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { Voyage } from '@src/shared-state/VesselLogbook/voyages'

const DESKTOP_ITEMS_WIDTH = '60%'

interface ViewVoyageProps {
  voyageId: string
}

export const ViewVoyage = ({ voyageId }: ViewVoyageProps) => {
  // Global State
  const voyagesData = sharedState.voyages.use()

  // Internal State
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)

  // Hooks
  const voyage = useMemo(() => {
    return voyagesData?.all.find(v => v.id === voyageId)
  }, [voyageId, voyagesData])

  const title = useMemo(() => {
    return voyage?.name ?? 'Voyage Details'
  }, [voyage])

  useEffect(() => {
    console.debug('VOYAGE', { voyage })
  }, [voyage])

  const onEditVoyage = () => {
    setIsEditModalVisible(true)
  }

  const onDeleteVoyage = () => {
    alert('Delete voyage functionality coming soon!')
  }

  const onExportVoyage = () => {
    alert('Export voyage functionality coming soon!')
  }

  // Loading state
  if (!voyage) {
    return <SeaLoadingSpinner variant={'lifeBuoy'} />
  }

  return (
    <RequirePermissions role={'logbook'} level={permissionLevels.VIEW} showDenial={true}>
      <ScrollView showsVerticalScrollIndicator={false} style={styles.container}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title={title} />}
          primaryActionButton={
            <SeaButton
              onPress={onEditVoyage}
              variant={SeaButtonVariant.Primary}
              label={'Edit Voyage'}
              iconOptions={{ icon: 'edit' }}
            />
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'export-voyage'} onPress={onExportVoyage} />,
            <SeaDeleteButton key={'delete-voyage'} onPress={onDeleteVoyage} />,
            <SeaEditButton key={'edit-voyage'} onPress={onEditVoyage} />,
          ]}>
          <SeaTypography variant={'title'}>{title}</SeaTypography>
          <SeaStack direction={'column'} align={'start'} gap={10}>
            {/** Card 1 - START */}
            <SeaStack
              align={'start'}
              style={{
                width: '100%',
              }}>
              <SeaStack
                direction={'column'}
                align={'start'}
                style={{
                  width: '100%',
                }}>
                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue
                    label={'Voyage Name'}
                    iconOptions={{ icon: 'directions_boat_filled' }}
                    showIcon={true}
                    value={voyage.name ?? 'Unnamed Voyage'}
                  />
                  <SeaLabelValue label={'Status'} iconOptions={{ icon: 'flag' }} showIcon={true} value={voyage.state} />
                </SeaStack>

                <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                  <SeaLabelValue
                    label={'Departure'}
                    iconOptions={{ icon: 'location_on' }}
                    showIcon={true}
                    value={voyage.whenDeparted ? formatDateShort(voyage.whenDeparted) : 'Not departed'}
                  />
                  <SeaLabelValue
                    label={'Arrival'}
                    iconOptions={{ icon: 'place' }}
                    showIcon={true}
                    value={voyage.whenArrived ? formatDateShort(voyage.whenArrived) : 'Not arrived'}
                  />
                </SeaStack>
              </SeaStack>
            </SeaStack>

            {/** Card 2 - START */}
            <SeaSpacer height={5} />
            <>
              <SeaStack isCollapsible={true} width={DESKTOP_ITEMS_WIDTH}>
                <SeaLabelValue
                  label={'Departure From'}
                  iconOptions={{ icon: 'anchor' }}
                  showIcon={true}
                  value={voyage.departureFrom ?? 'Not specified'}
                />
                <SeaLabelValue
                  label={'Destination To'}
                  iconOptions={{ icon: 'anchor' }}
                  showIcon={true}
                  value={voyage.destinationTo ?? 'Not specified'}
                />
              </SeaStack>
            </>

            {/** Card 3 - START */}
            <SeaSpacer height={5} />
            <>
              <SeaTypography variant={'subtitle'}>Voyage Notes</SeaTypography>
              <SeaStack direction={'column'} align={'start'}>
                <SeaTypography variant={'value'}>{voyage.voyageNotes ?? 'No notes available'}</SeaTypography>
              </SeaStack>
            </>
          </SeaStack>
        </SeaPageCard>

        {/* TODO: Add voyage history/entries table here */}
        <View style={styles.historyContainer}>
          <SeaTypography variant={'cardTitle'}>Voyage Entries</SeaTypography>
          <SeaStack direction={'column'} align={'start'}>
            <SeaTypography variant={'value'}>Voyage entries functionality coming soon...</SeaTypography>
          </SeaStack>
        </View>
      </ScrollView>

      {/* TODO: Add edit voyage drawer/modal here */}
      {isEditModalVisible && (
        <View>
          {/* EditVoyageDrawer component would go here */}
          <SeaTypography variant={'value'}>Edit voyage functionality coming soon...</SeaTypography>
        </View>
      )}
    </RequirePermissions>
  )
}

const styles = StyleSheet.create({
  container: {
    height: '100%',
  },
  historyContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
})

export default ViewVoyage
