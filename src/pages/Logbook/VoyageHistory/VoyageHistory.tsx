import React, { use<PERSON>allback, useMemo, useState } from 'react'
import { sharedState } from '@src/shared-state/shared-state'
import { RequirePermissions } from '@src/components/hoc/RequirePermissions'
import { permissionLevels } from '@src/shared-state/Core/userPermissions'
import { SeaPageCard, SeaPageCardTitle, SubNav } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { SeaDownloadButton } from '@src/components/_molecules/IconButtons/SeaDownloadButton'
import { SeaSettingsButton } from '@src/components/_molecules/IconButtons/SeaSettingsButton'
import { SeaFilterSearch } from '@src/components/_atoms/SeaFilterSearch/SeaFilterSearch'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaLoadingSpinner } from '@src/components/_atoms/SeaLoadingSpinner/SeaLoadingSpinner'
import { VoyageHistoryTable } from '@src/components/_organisms/Logbook/VoyageHistory/VoyageHistoryTable'
import { Voyage } from '@src/shared-state/VesselLogbook/voyages'
import { extractSearchTerms } from '@src/lib/util'
import { formatDateShort } from '@src/lib/datesAndTime'
import { debounce } from 'lodash'
import { ScrollablePageLayout } from '@src/layout/ScrollablePageLayout/ScrollablePageLayout'
import { useDeviceWidth } from '@src/hooks/useDevice'
import { SeaTypography } from '@src/components/_atoms/SeaTypography/SeaTypography'
import { View, StyleSheet } from 'react-native'
import { useLogger } from '@src/providers/ServiceProvider'
import { useRouter } from 'expo-router'
import { getRoutePath } from '@src/navigation/utils'
import { Routes } from '@src/navigation/constants'

interface VoyageHistoryProps {
  vesselId: string
  visible: boolean
  headerSubNavigation?: SubNav[]
}

export const VoyageHistory: React.FC<VoyageHistoryProps> = ({ vesselId, visible, headerSubNavigation }) => {
  const logger = useLogger('VoyageHistory')
  const { isMobileWidth, isTabletWidth } = useDeviceWidth()
  const router = useRouter()

  // Shared State
  const voyagesData = sharedState.voyages.use(visible)

  // Filter voyages to only show completed ones for history
  const completedVoyages = useMemo(() => {
    logger.debug('Voyage data changed', { voyagesData })

    if (!voyagesData?.all) return []
    return voyagesData.all.filter(voyage => voyage.state === 'completed')
  }, [voyagesData])

  const handleVoyageSelect = (voyage: Voyage) => {
    return router.navigate({
      pathname: getRoutePath(Routes.VOYAGE_HISTORY_VIEW),
      params: {
        voyageId: voyage.id,
        vesselId,
      },
    })
  }

  const handleStartNewVoyage = () => {
    // TODO: Navigate to start new voyage page
    alert('Start New Voyage functionality coming soon!')
  }

  // Loading state
  if (!completedVoyages) {
    return <SeaLoadingSpinner variant={'lifeBuoy'} />
  }

  return (
    <ScrollablePageLayout>
      <RequirePermissions role={'logbook'} level={permissionLevels.VIEW} showDenial={true}>
        <SeaPageCard
          titleComponent={<SeaPageCardTitle title="Ship's Logbook" />}
          primaryActionButton={
            <SeaButton
              onPress={handleStartNewVoyage}
              variant={SeaButtonVariant.Primary}
              label={'Start Voyage'}
              iconOptions={{ icon: 'add' }}
            />
          }
          secondaryActionButton={[
            <SeaDownloadButton key={'download'} onPress={() => alert('This functionality is not completed yet')} />,
            <SeaSettingsButton key={'settings'} onPress={() => alert('This functionality is not completed yet')} />,
          ]}
          subNav={headerSubNavigation}
        />

        {/* Table View */}
        <View style={styles.tableView}>
          {completedVoyages.length > 0 ? (
            <VoyageHistoryTable voyages={completedVoyages} onVoyageSelect={handleVoyageSelect} />
          ) : (
            <View style={styles.emptyState}>
              <SeaTypography variant={'subtitle'}>No completed voyages found</SeaTypography>
            </View>
          )}
        </View>
      </RequirePermissions>
    </ScrollablePageLayout>
  )
}

const styles = StyleSheet.create({
  filterRow: {
    paddingHorizontal: 12,
  },
  filterTextContainer: {
    paddingHorizontal: 12,
  },
  tableView: {
    marginTop: 16,
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
})
