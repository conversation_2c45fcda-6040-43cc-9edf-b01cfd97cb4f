import { ScrollView, View } from 'react-native'
import React, { useMemo, useState } from 'react'
import { SeaStack } from '@src/components/_atoms/SeaStack/SeaStack'
import { SeaPageCard, SeaPageCardTitle } from '@src/components/_molecules/SeaPageCard/SeaPageCard'
import { SeaButton, SeaButtonVariant } from '@src/components/_atoms/SeaButton/SeaButton'
import { sharedState } from '@src/shared-state/shared-state'
import { SeaTable, SeaTableColumn } from '@src/components/_atoms/SeaTable/SeaTable'
import { Engine } from '@src/shared-state/VesselMaintenance/engines'
import { formatDateShort } from '@src/lib/datesAndTime'
import { createStyleSheet, useStyles } from '@src/theme/styles'
import { EditEngineHoursDrawer } from '@src/components/_organisms/Engine/EditEngineHoursDrawer'

export default function EngineHours() {
  const engines = sharedState.engines.use()

  const { styles } = useStyles(styleSheet)

  const [isEditDrawerVisible, setIsEditDrawerVisible] = useState(false)

  const columns = useMemo(() => buildColumns(), [])
  const rows = useMemo(() => buildRows(engines?.all ?? []), [engines])

  return (
    <>
      <ScrollView style={{ flex: 1 }}>
        <SeaStack direction="column" gap={20} style={{ flex: 1, width: '100%' }}>
          <SeaPageCard
            titleComponent={<SeaPageCardTitle title="Engine Hours" />}
            primaryActionButton={
              <SeaButton
                label="Update hours"
                variant={SeaButtonVariant.Primary}
                onPress={() => setIsEditDrawerVisible(true)}
              />
            }
            style={{
              margin: 0,
            }}
          />
        </SeaStack>

        <View style={styles.tableView}>
          <ScrollView>
            <SeaTable columns={columns} rows={rows} />
          </ScrollView>
        </View>
      </ScrollView>
      {isEditDrawerVisible && (
        <EditEngineHoursDrawer visible={isEditDrawerVisible} onClose={() => setIsEditDrawerVisible(false)} />
      )}
    </>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
  },
  tableView: {
    marginTop: 16,
  },
}))

const buildColumns = () => {
  return [
    {
      label: 'Engine/Equipment',
      value: x => x.name,
      style: { fontWeight: 'bold' },
    },
    {
      label: 'Current Hours',
      value: x => (x.hours ? `${x.hours}hrs` : 'N/A'),
    },
    {
      label: 'Last Updated',
      value: x => formatDateShort(x.whenUpdated),
    },
    {
      label: 'Hours Over Range',
      value: x => '50hrs',
    },
  ] as SeaTableColumn<Engine>[]
}
const buildRows = (items: Engine[]) => {
  return items.map(item => ({
    data: item,
  }))
}
